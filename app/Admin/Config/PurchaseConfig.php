<?php

namespace App\Admin\Config;

/**
 * 采购管理全局配置常量类
 */
class PurchaseConfig
{
    // 角色配置
    public const ROLE_SUPPLIER = '仕入先';
    public const ROLE_CLIENT = '得意先';
    public const ROLE_OTHER = 'その他';

    // 贸易条件
    public const INCOTERMS_DEFAULT = '貿易外';
    public const INCOTERMS_CIP = 'CIP';
    public const INCOTERMS_DDP = 'DDP';

    // 货币配置
    public const CURRENCY_USD = '美元';
    public const CURRENCY_JPY = '日元';
    public const CURRENCY_CNY = '人民币';

    // 纳期选项
    public const DELIVERY_TERM_SHORTEST = 1; // 最短
    public const DELIVERY_TERM_NEXT_MONTH = 2; // 下个月月末
    public const DELIVERY_TERM_NEGOTIABLE = 3; // 別途調整

    // 维护配置
    public const MAINTENANCE_1YEAR = '1年間 先出しセンドバック';
    public const MAINTENANCE_3YEAR = '3年間 先出しセンドバック';
    public const MAINTENANCE_5YEAR = '5年間 先出しセンドバック';
    public const MAINTENANCE_NONE = '保守なし';

    // 仓库位置状态常量定义
    public const LOCATION_STATUS_AVAILABLE = '可用';
    public const LOCATION_STATUS_OCCUPIED = '已占用';
    public const LOCATION_STATUS_DISABLED = '禁用';

    // 仓库状态常量定义
    public const WAREHOUSE_STATUS_IDLE = '空闲';
    public const WAREHOUSE_STATUS_NORMAL = '正常';
    public const WAREHOUSE_STATUS_FULL = '爆仓';
    public const WAREHOUSE_STATUS_CLOSED = '关闭';

    // 产品状态常量定义
    public const PRODUCT_STATUS_ON_SALE = '在售';
    public const PRODUCT_STATUS_DISCONTINUED = '停售';

    // 产品属性常量定义
    public const PRODUCT_ZOKUSEI_NORMAL = '通常製品';
    public const PRODUCT_ZOKUSEI_RR = 'RR製品';
    public const PRODUCT_ZOKUSEI_INSPECTION = '検証機';

    // RR制品在库状态常量定义
    public const STOCK_STATUS_IN = 1;      // 在库
    public const STOCK_STATUS_OUT = 2;     // RMA出库
    public const STOCK_STATUS_SCRAPPED = 3; // 报废

    /**
     * 获取纳期选项映射
     * @return array
     */
    public static function getDeliveryTerms(): array
    {
        return [
            self::DELIVERY_TERM_SHORTEST => '最短',
            self::DELIVERY_TERM_NEXT_MONTH => '下个月月末',
            self::DELIVERY_TERM_NEGOTIABLE => '別途調整',
        ];
    }

    /**
     * 获取RR制品在库状态映射
     * @return array
     */
    public static function getStockStatus(): array
    {
        return [
            self::STOCK_STATUS_IN      => '在库',
            self::STOCK_STATUS_OUT     => 'RMA出库',
            self::STOCK_STATUS_SCRAPPED => '报废',
        ];
    }

    /**
     * 获取角色映射
     * @return array
     */
    public static function getRoles(): array
    {
        return [
            'supplier' => self::ROLE_SUPPLIER,
            'client'   => self::ROLE_CLIENT,
            'other'    => self::ROLE_OTHER,
        ];
    }

    /**
     * 获取贸易条件映射
     * @return array
     */
    public static function getIncoterms(): array
    {
        return [
            '-'    => self::INCOTERMS_DEFAULT,
            'CIP'   => self::INCOTERMS_CIP,
            'DDP' => self::INCOTERMS_DDP,
        ];
    }

    /**
     * 获取货币映射
     * @return array
     */
    public static function getCurrencies(): array
    {
        return [
            'USD' => self::CURRENCY_USD,
            'JPY' => self::CURRENCY_JPY,
            'CNY' => self::CURRENCY_CNY,
        ];
    }

    /**
     * 获取维护配置映射
     * @return array
     */
    public static function getMaintenance(): array
    {
        return [
            'MAINTENANCE_1YEAR' => self::MAINTENANCE_1YEAR,
            'MAINTENANCE_3YEAR' => self::MAINTENANCE_3YEAR,
            'MAINTENANCE_5YEAR' => self::MAINTENANCE_5YEAR,
            'MAINTENANCE_NONE' => self::MAINTENANCE_NONE,
        ];
    }

    /**
     * 获取仓库位置状态映射
     * @return array
     */
    public static function getLocationStatus(): array
    {
        return [
            'available' => self::LOCATION_STATUS_AVAILABLE,
            'occupied' => self::LOCATION_STATUS_OCCUPIED,
            'disabled' => self::LOCATION_STATUS_DISABLED
        ];
    }

    /**
     * 获取仓库状态映射
     * @return array
     */
    public static function getWarehouseStatus(): array
    {
        return [
            'idle' => self::WAREHOUSE_STATUS_IDLE,
            'normal' => self::WAREHOUSE_STATUS_NORMAL,
            'full' => self::WAREHOUSE_STATUS_FULL,
            'closed' => self::WAREHOUSE_STATUS_CLOSED
        ];
    }

    /**
     * 获取产品状态映射
     * @return array
     */
    public static function getProductStatus(): array
    {
        return [
            'on_sale' => self::PRODUCT_STATUS_ON_SALE,
            'discontinued' => self::PRODUCT_STATUS_DISCONTINUED,
        ];
    }

    /**
     * 获取产品属性映射
     * @return array
     */
    public static function getProductZokusei(): array
    {
        return [
            'normal' => self::PRODUCT_ZOKUSEI_NORMAL,
            'rr' => self::PRODUCT_ZOKUSEI_RR,
            'inspection' => self::PRODUCT_ZOKUSEI_INSPECTION,
        ];
    }

    /**
     * 获取产品属性反向映射（从中文值到英文键）
     * @return array
     */
    public static function getProductZokuseiReverse(): array
    {
        return [
            self::PRODUCT_ZOKUSEI_NORMAL => 'normal',
            self::PRODUCT_ZOKUSEI_RR => 'rr',
            self::PRODUCT_ZOKUSEI_INSPECTION => 'inspection',
            // 兼容旧的默认值
            '常规' => 'normal',
        ];
    }

    /**
     * 规范化产品属性值（将任何格式转换为标准的英文键）
     * @param string $value
     * @return string
     */
    public static function normalizeProductZokusei(string $value): string
    {
        // 如果已经是标准的英文键，直接返回
        if (in_array($value, ['normal', 'rr', 'inspection'])) {
            return $value;
        }
        
        // 尝试从反向映射中获取
        $reverseMap = self::getProductZokuseiReverse();
        return $reverseMap[$value] ?? 'normal';
    }
}
