<?php

declare(strict_types=1);

namespace App\Admin\Actions\H3c;

use App\Models\TecH3cCustomerTemporaryProductModel;
use App\Models\TecH3cProductListModel;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

/**
 * 将临时商品转为正式商品
 */
class TecH3cConvertToFormalProductAction extends RowAction
{
    use HasPermissions;

    /**
     * 标题
     *
     * @return string
     */
    public function title()
    {
        return '转正式';
    }



    /**
     * 设置按钮样式
     *
     * @return string
     */
    public function html()
    {
        return <<<HTML
<a href="javascript:void(0);" class="{$this->getElementClass()} btn btn-sm btn-success" title="升级为正式产品" style="color: white; margin-left: 5px;">
    <i class="fa fa-level-up-alt"></i>
</a>
HTML;
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @return Response
     */
    public function handle(Request $request)
    {
        $tempProductId = $this->getKey();
        $tempProduct = TecH3cCustomerTemporaryProductModel::find($tempProductId);
        
        if (!$tempProduct) {
            return $this->response()->error('临时商品不存在');
        }
        
        if ($tempProduct->is_reused) {
            return $this->response()->error('该临时商品已经转为正式商品');
        }
        
        if (!$tempProduct->category_id) {
            return $this->response()->error('请先为临时商品设置产品分类');
        }

        if (!$tempProduct->major_category_id) {
            return $this->response()->error('请先为临时商品设置主分类');
        }
        
        try {
            // 获取分类信息来确定主分类
            $category = \App\Models\TecH3cProductCategoryModel::find($tempProduct->category_id);
            if (!$category) {
                return $this->response()->error('产品分类不存在');
            }

            // 创建正式商品
            $formalProduct = TecH3cProductListModel::create([
                'major_category_id' => $tempProduct->major_category_id,
                'category_id' => $tempProduct->category_id,
                'product' => $tempProduct->product_name,
                'product_bom' => $tempProduct->product_model ?: '-',
                'product_zokusei' => 'normal',
                'unit' => $tempProduct->product_unit ?: 1, // 单位ID，默认为1
                'is_rr_product' => false,
                'is_inspection_machine' => false,
                'need_incentive_calc' => true,
                'incoterms' => 'CIP', // 默认贸易条件
                'standard_price' => $tempProduct->unit_price ?: 0,
                'standard_selling_price' => $tempProduct->unit_price ?: 0,
                'cip_cost' => 1000, // 默认CIP成本
                'standard_profit_rate' => 15, // 默认利润率
                'spec' => $tempProduct->description,
                'status' => 'on_sale',
            ]);
            
            // 更新临时商品状态
            $tempProduct->update([
                'is_reused' => true,
                'formal_product_id' => $formalProduct->id,
            ]);
            
            return $this->response()
                ->success('成功转为正式商品')
                ->refresh();
                
        } catch (\Exception $e) {
            return $this->response()->error('转换失败：' . $e->getMessage());
        }
    }

    /**
     * 确认弹窗
     *
     * @return string|array|void
     */
    public function confirm()
    {
        return [
            '确认升级为正式产品？',
            '此操作将创建一个新的正式产品，并标记临时产品为已复用状态。'
        ];
    }

    /**
     * 权限判断
     *
     * @param Model|Authenticatable|HasPermissions|null $user
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true; // 根据需要调整权限
    }
}
