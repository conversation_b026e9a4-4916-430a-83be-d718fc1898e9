<?php

namespace App\Admin\Actions\H3c;

use App\Models\TecH3cCustomerTemporaryProductModel;
use Dcat\Admin\Grid\BatchAction;

/**
 * H3C临时商品清理未使用批量操作
 */
class TecH3cCleanupTempProductBatchAction extends BatchAction
{
    protected $title = '清理未使用';

    public function handle()
    {
        $ids = $this->getKey();

        // 检查是否有报价单引用这些临时商品
        $referencedIds = \DB::table('t_customer_quote_items_h3c')
            ->whereIn('temp_product_id', $ids)
            ->whereNotNull('temp_product_id')
            ->pluck('temp_product_id')
            ->unique()
            ->toArray();

        // 过滤掉有引用的临时商品
        $safeToDeleteIds = array_diff($ids, $referencedIds);

        if (empty($safeToDeleteIds)) {
            return $this->response()->warning('选中的临时商品都有报价单引用，无法清理')->refresh();
        }

        $count = TecH3cCustomerTemporaryProductModel::whereIn('id', $safeToDeleteIds)
            ->where('usage_count', '<=', 0)  // 只清理从未使用过的临时商品
            ->where('is_reused', false)
            ->delete();

        $skippedCount = count($referencedIds);
        $message = "成功清理 {$count} 个临时商品";
        if ($skippedCount > 0) {
            $message .= "，跳过 {$skippedCount} 个有引用的商品";
        }

        return $count > 0 ?
            $this->response()->success($message)->refresh() :
            $this->response()->info('没有符合条件的临时商品可清理')->refresh();
    }
}
