<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use App\Models\TecSndContactModel;

class TecTecSndContactCopyAction extends BatchAction
{
    protected $title = '复制联系人';

    public function match($key)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return false;
        }

        // 只有选择单条数据时才显示
        return $this->grid->getSelectedKeys()->count() === 1;
    }

    public function handle(Request $request)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return $this->response()->error('回收站中不允许复制联系人');
        }

        // 获取选中的记录 ID
        $keys = $this->getKey() ?? [];
        
        if (count($keys) !== 1) {
            return $this->response()
                ->error('请选择单条数据进行复制')
                ->refresh();
        }

        $id = reset($keys);  // 获取数组的第一个元素

        try {
            // 获取原始联系人
            $originalContact = TecSndContactModel::findOrFail($id);

            // 创建新联系人，复制原始联系人的所有属性
            $newContact = $originalContact->replicate();

            // 修改联系人名称，添加 -copy 后缀
            $newContact->last_name = $originalContact->last_name . '-copy';

            // 保存新联系人
            $newContact->save();

            return $this->response()
                ->success('成功复制联系人')
                ->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('复制联系人失败：' . $e->getMessage());
        }
    }

    public function confirm()
    {
        return ['确认要复制选中的联系人吗？', '将创建一个新的联系人副本'];
    }
}
