<?php

namespace App\Admin\Actions\Grid\WarehouseLocation;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use App\Models\TecWarehouseLocationModel;

class TecWarehouseLocationBulkRestoreAction extends BatchAction
{
    protected $title = '批量恢复';

    public function render()
    {
        // 始终显示批量恢复按钮
        return parent::render();
    }

    public function handle(Request $request)
    {
        // 获取选中的记录 ID
        $keys = $this->getKey();
        
        // 如果没有选中记录，则不执行任何操作
        if (empty($keys)) {
            return $this->response()->error('请选择要恢复的记录');
        }

        $successCount = 0;
        $failedCount = 0;
        $errorMessages = [];

        foreach ($keys as $id) {
            try {
                // 使用自定义的 restoreById 方法
                TecWarehouseLocationModel::restoreById($id);
                $successCount++;
            } catch (\Exception $e) {
                $failedCount++;
                $errorMessages[] = "ID {$id}: " . $e->getMessage();
            }
        }

        $message = "成功恢复 {$successCount} 条记录";
        if ($failedCount > 0) {
            $message .= "，{$failedCount} 条记录恢复失败：" . implode('; ', $errorMessages);
        }

        return $this->response()
            ->success($message)
            ->refresh();
    }

    public function confirm()
    {
        return ['确认批量恢复', '您确定要恢复选中的记录吗？'];
    }
}
