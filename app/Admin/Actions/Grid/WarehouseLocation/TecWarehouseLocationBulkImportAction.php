<?php

namespace App\Admin\Actions\Grid\WarehouseLocation;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Widgets\Modal;
use App\Models\TecWarehouseModel;
use App\Models\TecWarehouseLocationModel;

class TecWarehouseLocationBulkImportAction extends AbstractTool
{
    protected $title = '批量追加';

    public function render()
    {
        $warehouses = TecWarehouseModel::select('id', 'name')->get();
        $areas = TecWarehouseLocationModel::distinct()
            ->select('area_number')
            ->where('area_number', '!=', '0')
            ->where('area_number', '!=', '')
            ->get();

        $modal = Modal::make()
            ->lg()
            ->title($this->title)
            ->body(view('admin.h3c.tec_h3c_warehouse_location_import', compact('warehouses', 'areas')))
            ->button('<button class="btn btn-primary"><i class="fa fa-files-o"></i> ' . $this->title . '</button>');

        return $modal->render();
    }

    public function icon()
    {
        return 'feather icon-layers';
    }
}