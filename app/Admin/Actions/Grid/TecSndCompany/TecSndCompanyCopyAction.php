<?php

namespace App\Admin\Actions\Grid\TecSndCompany;

use App\Models\TecSndCompanyModel;
use Illuminate\Http\Request;
use Dcat\Admin\Grid\BatchAction;

class TecSndCompanyCopyAction extends BatchAction
{
    protected $title = '复制公司';

    public function match($key)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return false;
        }

        // 只有选择单条数据时才显示
        return $this->grid->getSelectedKeys()->count() === 1;
    }

    public function handle(Request $request)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return $this->response()->error('回收站中不允许复制公司');
        }

        // 获取选中的记录 ID
        $keys = $this->getKey() ?? [];
        
        if (count($keys) !== 1) {
            return $this->response()
                ->error('请选择单条数据进行复制')
                ->refresh();
        }

        $id = reset($keys);  // 获取数组的第一个元素

        try {
            // 获取原始公司
            $originalCompany = TecSndCompanyModel::findOrFail($id);

            // 创建新公司，复制原始公司的所有属性
            $newCompany = $originalCompany->replicate();

            // 修改公司名称，添加 -copy 后缀
            $newCompany->name = $originalCompany->name . '-copy';
            if ($originalCompany->abbreviation) {
                $newCompany->abbreviation = $originalCompany->abbreviation . '-copy';
            }

            // 保存新公司
            $newCompany->save();

            return $this->response()
                ->success('成功复制公司')
                ->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('复制公司失败：' . $e->getMessage());
        }
    }

    public function confirm()
    {
        return ['确认要复制选中的公司吗？', '将创建一个新的公司副本'];
    }
}
