<?php

/*
 * // +----------------------------------------------------------------------
 * // | erp
 * // +----------------------------------------------------------------------
 * // | Copyright (c) 2006~2020 erp All rights reserved.
 * // +----------------------------------------------------------------------
 * // | Licensed ( LICENSE-1.0.0 )
 * // +----------------------------------------------------------------------
 * // | Author: yxx <<EMAIL>>
 * // +----------------------------------------------------------------------
 */

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Grid;
use Illuminate\Support\Str;

class TecBatchCreatePro extends BatchCreatePro
{
    public function getOrderId(): int
    {
        // 对于 TecH3cPurchaseOrderController 等特殊场景
        if (method_exists($this->parent, 'getOrderId')) {
            $orderId = $this->parent->getOrderId();
            \Log::info('TecBatchCreatePro Debug: Parent getOrderId', ['orderId' => $orderId]);
            return $orderId;
        }

        // 尝试从当前控制器获取订单ID
        $controller = request()->route()->getController();
        if (property_exists($controller, 'order') && $controller->order) {
            $orderId = $controller->order->id;
            \Log::info('TecBatchCreatePro Debug: Controller Order', ['orderId' => $orderId]);
            return $orderId;
        }

        // 记录详细的调试信息
        \Log::info('TecBatchCreatePro Debug: Order ID Retrieval Failed', [
            'parent' => $this->parent ? get_class($this->parent) : 'No Parent',
            'controller' => get_class(request()->route()->getController()),
            'route_params' => request()->route()->parameters(),
        ]);

        // 如果以上方法都失败，返回 0
        return 0;
    }
}
