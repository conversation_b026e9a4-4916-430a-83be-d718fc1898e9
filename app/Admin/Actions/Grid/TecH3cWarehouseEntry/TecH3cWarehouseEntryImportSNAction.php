<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cWarehouseEntry;

use App\Models\TecH3cWarehouseEntrySNModel;
use App\Models\TecH3cWarehouseEntryModel;
use App\Imports\TecH3cWarehouseEntrySNImport;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;

class TecH3cWarehouseEntryImportSNAction extends AbstractTool
{
    protected $title = '导入SN';

    /**
     * 渲染模态框表单
     */
    public function html()
    {
        // 获取可选的入库单
        $entries = TecH3cWarehouseEntryModel::where('status', '!=', TecH3cWarehouseEntryModel::STATUS_COMPLETED)
            ->pluck('entry_no', 'id')
            ->toArray();

        $options = collect($entries)->map(function ($entry_no, $id) {
            return "<option value='{$id}'>{$entry_no}</option>";
        })->join('');

        return <<<HTML
        <div class="btn-group">
            <button class="btn btn-primary" data-toggle="modal" data-target="#import-sn-modal">
                <i class="feather icon-upload"></i> 导入SN
            </button>
        </div>

        <div class="modal fade" id="import-sn-modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">导入SN</h4>
                        <button type="button" class="close" data-dismiss="modal">×</button>
                    </div>
                    <form action="{$this->getHandleRoute()}" method="post" enctype="multipart/form-data" pjax-container>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>选择入库单</label>
                                <select name="entry_id" class="form-control" required>
                                    <option value="">请选择入库单</option>
                                    {$options}
                                </select>
                            </div>
                            <div class="form-group">
                                <label>选择Excel文件</label>
                                <input type="file" name="file" class="form-control" accept=".xlsx,.xls" required/>
                                <small class="form-text text-muted">
                                    请上传包含以下列的Excel文件：<br>
                                    - sn: SN编号（必填）<br>
                                    - product_id: 产品ID（必填）<br>
                                    - remarks: 备注（选填）
                                </small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-primary">导入</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
HTML;
    }

    /**
     * 处理导入请求
     */
    public function handle(Request $request)
    {
        try {
            // 验证请求
            $request->validate([
                'file' => 'required|file|mimes:xlsx,xls',
                'entry_id' => 'required|exists:t_warehouse_entries_h3c,id'
            ]);

            // 开始导入
            DB::beginTransaction();

            // 使用导入类处理Excel文件
            Excel::import(new TecH3cWarehouseEntrySNImport($request->input('entry_id')), $request->file('file'));

            DB::commit();

            return $this->response()
                ->success('导入成功')
                ->refresh();
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            DB::rollBack();
            $failures = $e->failures();
            $errors = collect($failures)->map(function ($failure) {
                return "第{$failure->row()}行: " . implode(', ', $failure->errors());
            })->join('<br>');
            
            return $this->response()->error('导入失败：<br>' . $errors);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('SN导入失败：' . $e->getMessage());
            return $this->response()->error('导入失败：' . $e->getMessage());
        }
    }

    /**
     * 获取处理路由
     */
    public function getHandleRoute()
    {
        return admin_url('h3c/warehouse-entries/sn/import');
    }

    /**
     * 权限判断
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * 返回请求方法
     */
    public function getMethod()
    {
        return 'POST';
    }
} 