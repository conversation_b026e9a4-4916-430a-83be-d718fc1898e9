<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cWarehouseEntry;

use App\Models\TecH3cWarehouseEntryModel;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Yxx\LaravelQuick\Exceptions\Api\ApiRequestException;
use Dcat\Admin\Admin;

class TecH3cWarehouseEntryPrintAction extends AbstractTool
{
    /**
     * @return string
     */
    protected $title = '打印入库单';

    public function __construct($title = null)
    {
        parent::__construct();
        $this->title = '<i class="fa fa-print"></i> ' . $this->title;
        Admin::script($this->script());
    }

    public function getHandleRoute()
    {
        $key = request()->route('r_h3c_warehouse_entry');
        return admin_url("r_warehouse_entries_h3c/{$key}/print");
    }

    protected function script()
    {
        return <<<JS
        $(function () {
            $('.grid-print-btn').on('click', function() {
                var url = $(this).data('url');
                var id = $(this).data('key');

                if (!id) {
                    Dcat.error('未找到入库单ID');
                    return;
                }

                // 直接在新窗口中打开打印页面（类似客户报价单的方式）
                var printWindow = window.open(url, '_blank');
                if (!printWindow) {
                    Dcat.error('请允许浏览器打开新窗口');
                }
            });
        });
        JS;
    }

    public function handle(Request $request)
    {
        try {
            // 从请求中获取 ID
            $id = $request->get('_key');
            
            Log::info('开始获取打印入库单数据', [
                'entry_id' => $id,
                'request_path' => $request->path(),
                'full_url' => $request->fullUrl(),
                'request_all' => $request->all()
            ]);
            
            if (empty($id)) {
                throw new ApiRequestException('入库单ID不能为空！');
            }
            
            // 获取入库单数据
            $entry = TecH3cWarehouseEntryModel::with([
                'items.productlist',
                'items.orderItem.unitInfo',
                'items.location',
                'warehouse',
                'order'
            ])->findOrFail($id);

            // 获取公司信息
            $companyInfo = \App\Models\TecH3cCompanyInfoModel::first();

            Log::info('打印入库单数据获取成功', [
                'entry_id' => $entry->id,
                'entry_no' => $entry->entry_no,
                'items_count' => $entry->items->count()
            ]);

            // 返回确认视图
            return view('admin.h3c.tec_h3c_warehouse_entry_confirm', compact('entry', 'companyInfo'));
            
        } catch (\Exception $e) {
            Log::error('获取打印入库单数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_all' => $request->all()
            ]);
            return $this->response()->error('获取打印入库单数据失败：' . $e->getMessage());
        }
    }

    protected function html()
    {
        // 添加按钮的 class 和 data 属性
        $this->setHtmlAttribute([
            'data-url' => $this->getHandleRoute(),
            'data-action' => 'print',
            'class' => 'btn btn-primary btn-mini grid-print-btn',
        ]);

        return <<<HTML
<div class="btn-group pull-right" style="margin-right: 5px">
    <button {$this->formatHtmlAttributes()}>
        {$this->title}
    </button>
</div>
HTML;
    }

    protected function parameters(): array
    {
        return [
            '_key' => request()->route('r_h3c_warehouse_entry'),
        ];
    }
} 