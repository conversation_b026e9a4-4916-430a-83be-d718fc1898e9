<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cWarehouseEntry;

use App\Models\TecH3cWarehouseEntrySNModel;
use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TecH3cWarehouseEntryBatchInvalidSNAction extends BatchAction
{
    protected $title = '批量设置无效';

    public function confirm()
    {
        return ['确认要将选中的SN设置为无效吗？'];
    }

    public function handle(Request $request)
    {
        try {
            $keys = $this->getKey();
            
            if (empty($keys)) {
                return $this->response()->error('请选择要操作的SN');
            }

            DB::transaction(function () use ($keys) {
                foreach ($keys as $key) {
                    $sn = TecH3cWarehouseEntrySNModel::find($key);
                    if ($sn && $sn->status === TecH3cWarehouseEntrySNModel::STATUS_PENDING) {
                        $sn->status = TecH3cWarehouseEntrySNModel::STATUS_INVALID;
                        $sn->updated_by = Admin::user()->id;
                        $sn->save();
                    }
                }
            });

            return $this->response()
                ->success('操作成功')
                ->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('操作失败：' . $e->getMessage());
        }
    }

    public function authorize($user): bool
    {
        return true;
    }
} 