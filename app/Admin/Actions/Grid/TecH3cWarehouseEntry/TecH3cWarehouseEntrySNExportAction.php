<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cWarehouseEntry;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;

/**
 * 自定义导出 SN 列表按钮
 */
class TecH3cWarehouseEntrySNExportAction extends AbstractTool
{
    /** 按钮标题 */
    protected $title = '导出 SN 列表';

    /** 渲染按钮标题 */
    public function title()
    {
        return '<i class="fa fa-download"></i> ' . $this->title;
    }

    /** 按钮 HTML */
    protected function html(): string
    {
        $id = 'h3c-sn-export-btn';
        $this->setHtmlAttribute([
            'class' => 'btn btn-primary',
            'data-action' => 'export-sn',
            'id'    => $id,
            'title' => '导出 SN 列表'
        ]);
        $attributes = $this->formatHtmlAttributes();
        return "<button {$attributes}>{$this->title()}</button>";
    }

    /** 渲染按钮及脚本 */
    public function render(): string
    {
        return $this->html() . $this->script();
    }

    /** 构建导出 URL */
    protected function buildExportUrl(): string
    {
        $url = admin_url('r_warehouse_entries_h3c/sn/export');
        if ($queries = request()->all()) {
            $url .= '?' . http_build_query($queries);
        }
        return $url;
    }

    /** 初始化导出按钮脚本 */
    protected function script(): string
    {
        $exportUrl = $this->buildExportUrl();
        return <<<JS
<script>
$(function() {
    $('#h3c-sn-export-btn').off('click').on('click', function() {
        Dcat.NP.start();
        var params = {};
        $('.grid-filter-form').find('input, select').each(function() {
            var name = $(this).attr('name');
            var value = $(this).val();
            if (name && value !== null && value !== undefined) {
                params[name] = value;
            }
        });
        var url = '{$exportUrl}';
        var query = $.param(params);
        if (query) {
            url += (url.indexOf('?') >= 0 ? '&' : '?') + query;
        }
        window.location.href = url;
        Dcat.NP.done();
    });
});
</script>
JS;
    }
}
