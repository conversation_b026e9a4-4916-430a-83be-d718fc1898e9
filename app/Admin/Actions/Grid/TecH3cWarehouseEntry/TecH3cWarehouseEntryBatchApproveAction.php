<?php

namespace App\Admin\Actions\Grid\TecH3cWarehouseEntry;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class TecH3cWarehouseEntryBatchApproveAction extends BatchAction
{
    protected $title = '批量审核通过';

    public function handle(Request $request)
    {
        $ids = $this->getKey();
        
        return $request->json([
            'method' => 'post',
            'url' => '/admin/r_warehouse_entries_h3c/batch-review',
            'data' => [
                'ids' => $ids,
                'action' => 'approve',
                '_token' => csrf_token(),
            ],
        ]);
    }

    public function confirm()
    {
        return ['确认要审核通过选中的入库单吗？'];
    }
} 