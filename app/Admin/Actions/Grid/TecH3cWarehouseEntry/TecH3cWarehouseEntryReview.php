<?php

namespace App\Admin\Actions\Grid\TecH3cWarehouseEntry;

use App\Models\TecH3cWarehouseEntryModel;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TecH3cWarehouseEntryReview extends RowAction
{
    protected $title = '审核';

    public function confirm()
    {
        return ['确认要审核通过此入库单吗？'];
    }

    public function handle(Request $request)
    {
        $id = $this->getKey();
        $entry = TecH3cWarehouseEntryModel::find($id);

        if (!$entry) {
            return $this->response()->error('入库单不存在');
        }

        if ($entry->review_status !== TecH3cWarehouseEntryModel::REVIEW_STATUS_PENDING) {
            return $this->response()->error('只能审核待审核状态的入库单');
        }

        try {
            DB::transaction(function () use ($entry) {
                // 更新入库单状态
                $entry->review_status = TecH3cWarehouseEntryModel::REVIEW_STATUS_APPROVED;
                $entry->save();

                // 更新库存（这里需要实现具体的库存更新逻辑）
                // TODO: 实现库存更新逻辑
            });

            return $this->response()
                ->success('审核成功')
                ->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('审核失败：' . $e->getMessage());
        }
    }
} 