<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cCustomerIncentiveOut;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TecH3cCustomerIncentiveOutExportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '导出';

    public function title()
    {
        return '<i class="fa fa-download"></i> ' . $this->title;
    }

    protected function html()
    {
        // 生成唯一ID
        $id = 'h3c-customer-incentive-out-export-btn';
        
        // 设置HTML属性
        $this->setHtmlAttribute([
            'class' => 'btn btn-primary waves-effect',
            'data-action' => 'export',
            'id' => $id,
            'title' => 'インセンティブ支出数据导出'
        ]);
        
        $attributes = $this->formatHtmlAttributes();
        
        // 记录日志
        Log::info('生成インセンティブ支出导出按钮HTML', [
            'id' => $id,
            'attributes' => $attributes,
        ]);
        
        return <<<HTML
<button {$attributes}>{$this->title()}</button>
HTML;
    }

    /**
     * 构建导出URL
     *
     * @return string
     */
    protected function buildExportUrl()
    {
        $url = admin_url('r_h3c_customer_incentive_outs/export');
        
        // 附加当前查询参数
        if ($queries = request()->all()) {
            // 移除无关参数
            foreach (['_pjax', '_token', '_method'] as $key) {
                if (isset($queries[$key])) {
                    unset($queries[$key]);
                }
            }
            
            if (!empty($queries)) {
                $url .= '?' . http_build_query($queries);
            }
        }
        
        Log::info('构建インセンティブ支出导出URL', [
            'url' => $url,
            'queries' => $queries ?? [],
        ]);
        
        return $url;
    }

    /**
     * 渲染方法
     */
    public function render()
    {
        Log::info('开始渲染インセンティブ支出导出按钮');
        
        return $this->html() . $this->script();
    }

    /**
     * 初始化导出功能的JavaScript
     *
     * @return string
     */
    protected function script()
    {
        $exportUrl = $this->buildExportUrl();
        
        return <<<JS
<script>
$(function () {
    console.log('初始化インセンティブ支出导出按钮 #h3c-customer-incentive-out-export-btn');
    
    $('#h3c-customer-incentive-out-export-btn').off('click').on('click', function() {
        console.log('インセンティブ支出导出按钮被点击');
        
        // 显示加载提示
        Dcat.NP.start();
        
        // 获取当前过滤条件（手动序列化，确保日期等格式正确）
        var filterParams = {};
        console.log('查找过滤器表单:', $('.grid-filter-form').length);
        
        $('.grid-filter-form').find('input, select').each(function() {
            var input = $(this);
            var name = input.attr('name');
            var value = input.val();
            
            console.log('检查过滤器字段:', name, value);
            
            // 检查是否为日期范围
            if (name && (name.indexOf('request_date') === 0 || name.indexOf('business_date') === 0 || name.indexOf('date') === 0)) {
                // 如果是日期范围字段
                if (Array.isArray(value) && value.length === 2) {
                    filterParams[name+'[start]'] = value[0];
                    filterParams[name+'[end]'] = value[1];
                    console.log('转换日期范围:', name, value[0], value[1]);
                } else if (value) {
                    filterParams[name] = value;
                }
            } else if (name && value !== null && value !== undefined && value !== '') {
                filterParams[name] = value;
            }
        });
        
        console.log('过滤条件:', filterParams);
        
        // 构建URL参数
        var queryParams = $.param(filterParams);
        
        // 构建完整URL
        var url = '{$exportUrl}';
        if (queryParams) {
            url += (url.indexOf('?') >= 0 ? '&' : '?') + queryParams;
        }
        console.log('导出URL:', url);
        
        // 直接触发下载
        try {
            var a = document.createElement('a');
            a.href = url;
            a.target = '_blank';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            Dcat.NP.done();
            Dcat.success('导出请求已发送，请稍候...');
        } catch (e) {
            Dcat.NP.done();
            console.error('导出请求异常:', e);
            Dcat.error('导出请求异常: ' + (e.message || '未知错误'));
        }
    });
});
</script>
JS;
    }

    /**
     * 处理导出请求
     *
     * @param Request $request
     * @return mixed
     */
    public function handle(Request $request)
    {
        // 请注意：此方法通常不会被调用，因为我们是通过URL直接访问控制器的导出方法
        return $this->response()->success('正在导出...')->refresh();
    }
}
