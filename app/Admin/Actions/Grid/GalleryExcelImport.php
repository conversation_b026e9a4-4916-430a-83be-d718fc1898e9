<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Form\AbstractTool;
use Illuminate\Http\Request;

class GalleryExcelImport extends AbstractTool
{
    protected $title = 'EXCEL批量导入';

    public function html()
    {
        $modal = Modal::make()
            ->lg()
            ->title($this->title())
            ->body($this->form())
            ->button('<button class="btn btn-primary"><i class="fa fa-upload"></i> ' . $this->title() . '</button>');

        return $modal->render();
    }

    protected function form()
    {
        // $actionUrl = route('admin.gallery.import'); // 生成正确的URL
        return view('admin.import-excel-form')->render();
    }

    public function handle(Request $request)
    {
        // 此方法可以不实现，所有逻辑都在控制器中处理
        return $this->response()->success('操作成功')->refresh();
    }
}
