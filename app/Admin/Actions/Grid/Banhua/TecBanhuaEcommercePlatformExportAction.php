<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaEcommercePlatformModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Dcat\Admin\Http\JsonResponse;

/**
 * 阪画电商平台数据导出操作
 */
class TecBanhuaEcommercePlatformExportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-download"></i> 导出';

    /**
     * 处理请求
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|JsonResponse
     */
    public function handle(Request $request)
    {
        try {
            // 获取筛选条件
            $platformName = $request->get('platform_name');
            $platformCode = $request->get('platform_code');
            $shopName = $request->get('shop_name');
            $status = $request->get('status');
            
            // 记录导出条件
            Log::info('导出电商平台数据', [
                'platform_name' => $platformName,
                'platform_code' => $platformCode,
                'shop_name' => $shopName,
                'status' => $status
            ]);
            
            // 构建查询
            $query = TecBanhuaEcommercePlatformModel::query();
            
            // 应用筛选条件
            if (!empty($platformName)) {
                $query->where('platform_name', 'like', "%{$platformName}%");
            }
            
            if (!empty($platformCode)) {
                $query->where('platform_code', 'like', "%{$platformCode}%");
            }
            
            if (!empty($shopName)) {
                $query->where('shop_name', 'like', "%{$shopName}%");
            }
            
            if ($status !== null && $status !== '') {
                $query->where('status', $status);
            }
            
            // 获取数据
            $platforms = $query->get();
            
            // 如果没有数据，返回错误信息
            if ($platforms->isEmpty()) {
                return response()->json([
                    'status' => false,
                    'message' => '没有符合条件的数据可导出',
                    'type' => 'error'
                ]);
            }
            
            // 准备CSV数据
            $headers = [
                'ID', '平台名称', '平台代码', '平台简称', '店铺名称', '店铺网址', 
                '负责人', '联系电话', '联系邮箱', 'API密钥', '应用ID', '状态', '备注',
                '创建时间', '更新时间'
            ];
            
            $rows = [];
            $rows[] = $headers;
            
            foreach ($platforms as $platform) {
                $rows[] = [
                    $platform->id,
                    $platform->platform_name,
                    $platform->platform_code,
                    $platform->short_name,
                    $platform->shop_name,
                    $platform->shop_url,
                    $platform->manager_name,
                    $platform->contact_phone,
                    $platform->contact_email,
                    $platform->api_key,
                    $platform->app_id,
                    $platform->status == 1 ? '启用' : '禁用',
                    $platform->remarks,
                    $platform->created_at ? $platform->created_at->format('Y-m-d H:i:s') : '',
                    $platform->updated_at ? $platform->updated_at->format('Y-m-d H:i:s') : '',
                ];
            }
            
            // 生成CSV文件
            $filename = 'ecommerce_platforms_' . date('YmdHis') . '.csv';
            $tempPath = storage_path('app/public/' . $filename);
            
            // 创建临时文件目录（如果不存在）
            if (!is_dir(dirname($tempPath))) {
                mkdir(dirname($tempPath), 0755, true);
            }
            
            // 写入CSV文件
            $file = fopen($tempPath, 'w');
            
            // 添加UTF-8 BOM，以便Excel正确识别中文
            fputs($file, chr(0xEF) . chr(0xBB) . chr(0xBF));
            
            foreach ($rows as $row) {
                fputcsv($file, $row);
            }
            
            fclose($file);
            
            // 返回文件下载响应
            if (method_exists('Illuminate\Support\Facades\Response', 'download')) {
                $headers = [
                    'Content-Type' => 'text/csv; charset=UTF-8',
                    'Content-Description' => 'File Transfer',
                    'Content-Disposition' => 'attachment; filename=' . $filename,
                    'Content-Transfer-Encoding' => 'binary',
                ];
                
                return response()->download($tempPath, $filename, $headers)->deleteFileAfterSend(true);
            }
            
            // 如果上面的方法不可用，使用另一种方式
            if (file_exists($tempPath)) {
                return response()->file($tempPath, [
                    'Content-Type' => 'text/csv; charset=UTF-8',
                    'Content-Disposition' => 'attachment; filename=' . $filename,
                ])->deleteFileAfterSend(true);
            }
            
            return response()->json([
                'status' => false,
                'message' => '导出文件创建失败',
                'type' => 'error'
            ]);
            
        } catch (\Exception $e) {
            Log::error('导出电商平台数据失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '导出失败: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }
    
    /**
     * 自定义HTML，使用与图库导出相同的格式
     * 
     * @return string
     */
    public function html()
    {
        return <<<HTML
<a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline export-ecommerce" style="margin-right:3px">
    <i class="fa fa-download"></i><span class="d-none d-sm-inline">&nbsp; 导出</span>
</a>
<script>
$(function () {
    $('.export-ecommerce').on('click', function () {
        // 获取当前页面的所有筛选参数
        var params = {};
        var queryString = window.location.search.substring(1);
        var pairs = queryString.split('&');
        
        for (var i = 0; i < pairs.length; i++) {
            var pair = pairs[i].split('=');
            if (pair[0] && pair[0] !== '_pjax') {
                params[pair[0]] = decodeURIComponent(pair[1] || '');
            }
        }
        
        // 构建导出URL
        var exportUrl = '{$this->getHandleRoute()}';
        var queryParams = [];
        
        for (var key in params) {
            if (params.hasOwnProperty(key)) {
                queryParams.push(key + '=' + encodeURIComponent(params[key]));
            }
        }
        
        if (queryParams.length > 0) {
            exportUrl += '?' + queryParams.join('&');
        }
        
        // 显示加载中提示
        Dcat.loading();
        
        // 使用AJAX请求导出数据
        $.ajax({
            url: exportUrl,
            type: 'GET',
            success: function(response) {
                if (response.status === false) {
                    Dcat.error(response.message);
                } else {
                    // 执行下载操作
                    window.location.href = exportUrl;
                }
                
                // 关闭加载提示
                setTimeout(function() {
                    Dcat.loading(false);
                }, 1000);
            },
            error: function() {
                Dcat.error('导出失败，请稍后重试');
                Dcat.loading(false);
            }
        });
    });
});
</script>
HTML;
    }
    
    /**
     * 获取处理URL
     *
     * @return string
     */
    public function getHandleRoute()
    {
        return admin_url('banhua/ecommerce-export');
    }
} 