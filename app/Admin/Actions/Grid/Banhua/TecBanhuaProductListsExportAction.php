<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaProductListsModel;
use App\Models\Banhua\TecBanhuaProductCategoryModel;
use App\Models\Banhua\TecBanhuaProductUnitModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class TecBanhuaProductListsExportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-download"></i> 导出产品';

    /**
     * 处理请求
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function handle(Request $request)
    {
        try {
            // 获取筛选条件
            $categoryId = $request->get('category_id');
            $code = $request->get('code');
            $name = $request->get('name');
            $pid = $request->get('pid');
            $jan = $request->get('jan');
            $sku = $request->get('sku');
            $status = $request->get('status');
            
            // 记录导出条件
            Log::info('导出产品数据', [
                'category_id' => $categoryId,
                'code' => $code,
                'name' => $name,
                'pid' => $pid,
                'jan' => $jan,
                'sku' => $sku,
                'status' => $status
            ]);
            
            // 构建查询
            $query = TecBanhuaProductListsModel::query();
            
            // 应用筛选条件
            if (!empty($categoryId)) {
                $query->where('category_id', $categoryId);
            }
            
            if (!empty($code)) {
                $query->where('code', 'like', "%{$code}%");
            }
            
            if (!empty($name)) {
                $query->where('name', 'like', "%{$name}%");
            }
            
            if (!empty($pid)) {
                $query->where('pid', 'like', "%{$pid}%");
            }
            
            if (!empty($jan)) {
                $query->where('jan', 'like', "%{$jan}%");
            }
            
            if (!empty($sku)) {
                $query->where('sku', 'like', "%{$sku}%");
            }

            if (isset($status)) {
                $query->where('status', $status);
            }
            
            // 预加载关联数据
            $query->with(['category', 'unit']);
            
            // 获取数据
            $products = $query->get();
            
            // 如果没有数据，返回错误信息
            if ($products->isEmpty()) {
                return $this->response()->error('没有符合条件的数据可导出');
            }
            
            // 准备导出数据
            $headers = [
                'ID', '编码', '名称', 'SKU', '图库ID', 'JAN', '尺寸', '价格', '成本', '库存', '分类', '单位', '状态', '图片URL', '描述', '排序', '创建时间', '更新时间'
            ];
            
            $rows = [];
            $rows[] = $headers;
            
            foreach ($products as $product) {
                // 获取状态文本
                $statusText = $product->status == 1 ? '上架' : '下架';
                
                $rows[] = [
                    $product->id,
                    $product->code,
                    $product->name,
                    $product->sku,
                    $product->pid,
                    $product->jan,
                    $product->size,
                    $product->price,
                    $product->cost,
                    $product->stock,
                    $product->category ? $product->category->title : '',
                    $product->unit ? $product->unit->unit_name : '',
                    $statusText,
                    $product->image,
                    $product->description,
                    $product->sort,
                    $product->created_at,
                    $product->updated_at
                ];
            }
            
            // 生成CSV文件
            $filename = 'banhua_products_export_' . date('YmdHis') . '.csv';
            $tempFile = storage_path('app/public/' . $filename);
            
            // 创建CSV文件
            $fp = fopen($tempFile, 'w');
            
            // 添加BOM头，解决中文乱码问题
            fwrite($fp, "\xEF\xBB\xBF");
            
            // 写入数据
            foreach ($rows as $row) {
                fputcsv($fp, $row);
            }
            
            fclose($fp);
            
            // 记录导出成功
            Log::info('产品导出成功', [
                'filename' => $filename,
                'count' => count($rows) - 1
            ]);
            
            // 返回文件下载响应
            return response()->download($tempFile, $filename, [
                'Content-Type' => 'text/csv; charset=UTF-8',
            ])->deleteFileAfterSend(true);
            
        } catch (\Exception $e) {
            Log::error('产品导出失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->response()->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 渲染按钮
     *
     * @return string
     */
    public function render()
    {
        // 自定义按钮样式
        return <<<HTML
<a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline export-products" style="margin-right:3px">
    <i class="fa fa-download"></i><span class="d-none d-sm-inline">&nbsp; 导出</span>
</a>
<script>
$(function () {
    $('.export-products').on('click', function () {
        // 获取当前页面的所有筛选参数
        var params = {};
        var queryString = window.location.search.substring(1);
        var pairs = queryString.split('&');
        
        for (var i = 0; i < pairs.length; i++) {
            var pair = pairs[i].split('=');
            if (pair[0] && pair[0] !== '_pjax') {
                params[pair[0]] = decodeURIComponent(pair[1] || '');
            }
        }
        
        // 构建导出URL
        var exportUrl = '{$this->getHandleRoute()}';
        var queryParams = [];
        
        for (var key in params) {
            if (params.hasOwnProperty(key)) {
                queryParams.push(key + '=' + encodeURIComponent(params[key]));
            }
        }
        
        if (queryParams.length > 0) {
            exportUrl += '?' + queryParams.join('&');
        }
        
        // 显示加载中提示
        Dcat.loading();
        
        // 下载文件
        window.location.href = exportUrl;
        
        // 延迟关闭加载提示
        setTimeout(function() {
            Dcat.loading(false);
        }, 2000);
    });
});
</script>
HTML;
    }
    
    /**
     * 获取处理URL
     *
     * @return string
     */
    public function getHandleRoute()
    {
        return admin_url('banhua/product-lists-export');
    }
} 