<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaConsumableStockModel;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\TecWarehouseModel;
use App\Models\Banhua\TecBanhuaLocationModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;

class TecBanhuaConsumableStockExportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-download"></i> 导出Excel';

    /**
     * 存储导出任务进度的缓存键前缀
     */
    protected $progressCachePrefix = 'consumable_stock_export_progress_';
    
    /**
     * 渲染按钮
     *
     * @return string
     */
    public function render()
    {
        // 自定义按钮样式
        return <<<HTML
<div class="btn-group" role="group">
    <a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline export-consumable-stock" data-include-images="0" style="margin-right:3px">
        <i class="fa fa-download"></i><span class="d-none d-sm-inline">&nbsp; 导出Excel</span>
    </a>
</div>
<!-- 导出进度对话框 -->
<div class="modal fade" id="exportProgressModal" tabindex="-1" role="dialog" aria-labelledby="exportProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportProgressModalLabel">导出进度</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3" id="exportStatusText">正在准备导出数据...</div>
                <div class="progress">
                    <div id="exportProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                </div>
                <div class="mt-3 small text-muted" id="exportDetailText"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>
<script>
$(function () {
    // 创建导出进度检查函数
    var checkExportProgress = function(taskId, includeImages) {
        var progressCheckCount = 0;
        var progressInterval = setInterval(function() {
            $.ajax({
                url: '{$this->getHandleRoute()}/progress',
                data: { task_id: taskId },
                success: function(response) {
                    if (response.status) {
                        // 更新进度条
                        var progress = response.data.progress || 0;
                        var status = response.data.status || '处理中';
                        var detail = response.data.detail || '';
                        
                        $('#exportProgressBar').css('width', progress + '%').attr('aria-valuenow', progress);
                        $('#exportStatusText').text(status);
                        $('#exportDetailText').text(detail);
                        
                        // 如果完成，停止检查并下载文件
                        if (response.data.completed) {
                            clearInterval(progressInterval);
                            
                            // 关闭进度条
                            setTimeout(function() {
                                $('#exportProgressModal').modal('hide');
                                
                                // 如果有下载URL，触发下载
                                if (response.data.download_url) {
                                    window.location.href = response.data.download_url;
                                }
                            }, 1000);
                        }
                    } else {
                        // 出错时显示错误信息
                        $('#exportStatusText').text('导出出错: ' + (response.message || '未知错误'));
                        $('#exportDetailText').text('请稍后重试或联系管理员');
                        clearInterval(progressInterval);
                    }
                },
                error: function() {
                    progressCheckCount++;
                    
                    // 如果连续5次检查失败，显示错误信息
                    if (progressCheckCount >= 5) {
                        $('#exportStatusText').text('无法获取导出进度');
                        $('#exportDetailText').text('服务器可能正忙，但导出仍在后台进行，请稍后查看导出结果');
                        clearInterval(progressInterval);
                    }
                }
            });
        }, 2000); // 每2秒检查一次进度
    };
    
    // 导出按钮点击事件
    $('.export-consumable-stock').on('click', function () {
        // 获取是否包含图片选项
        var includeImages = $(this).data('include-images');
        
        // 获取当前页面的所有筛选参数
        var params = {};
        var queryString = window.location.search.substring(1);
        var pairs = queryString.split('&');
        
        for (var i = 0; i < pairs.length; i++) {
            var pair = pairs[i].split('=');
            if (pair[0] && pair[0] !== '_pjax') {
                params[pair[0]] = decodeURIComponent(pair[1] || '');
            }
        }
        
        // 添加是否包含图片参数
        params['include_images'] = includeImages;
        
        // 显示进度条对话框
        $('#exportProgressModal').modal('show');
        $('#exportProgressBar').css('width', '0%').attr('aria-valuenow', 0);
        $('#exportStatusText').text('正在准备导出Excel...');
        $('#exportDetailText').text('初始化导出任务...');
        
        // 发起导出请求
        $.ajax({
            url: '{$this->getHandleRoute()}/start',
            type: 'POST',
            data: params,
            success: function(response) {
                if (response.status && response.data.task_id) {
                    // 开始检查进度
                    checkExportProgress(response.data.task_id, includeImages);
                } else {
                    // 显示错误信息
                    $('#exportStatusText').text('启动导出任务失败: ' + (response.message || '未知错误'));
                    $('#exportDetailText').text('请稍后重试');
                }
            },
            error: function(xhr) {
                var errorMsg = '启动导出任务失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg += ': ' + xhr.responseJSON.message;
                }
                $('#exportStatusText').text(errorMsg);
                $('#exportDetailText').text('请稍后重试');
            }
        });
    });
});
</script>
HTML;
    }
    
    /**
     * 启动导出任务
     */
    public function start(Request $request)
    {
        try {
            // 生成任务ID
            $taskId = uniqid('export_');
            
            // 保存任务初始状态
            $this->updateProgress($taskId, [
                'progress' => 0,
                'status' => '初始化导出任务',
                'detail' => '准备数据中...',
                'completed' => false,
                'include_images' => $request->get('include_images', '0') === '1',
                'params' => $request->all()
            ]);
            
            // 在后台处理导出任务
            $this->dispatchExportJob($taskId, $request->all());
            
            return response()->json([
                'status' => true,
                'message' => '导出任务已启动',
                'data' => [
                    'task_id' => $taskId
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('启动导出任务失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '启动导出任务失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取导出进度
     */
    public function progress(Request $request)
    {
        $taskId = $request->get('task_id');
        
        if (!$taskId) {
            return response()->json([
                'status' => false,
                'message' => '缺少任务ID'
            ]);
        }
        
        $progress = $this->getProgress($taskId);
        
        if (!$progress) {
            return response()->json([
                'status' => false,
                'message' => '找不到任务进度信息'
            ]);
        }
        
        return response()->json([
            'status' => true,
            'data' => $progress
        ]);
    }
    
    /**
     * 在后台处理导出任务
     */
    protected function dispatchExportJob($taskId, $params)
    {
        // 直接在当前请求中处理，避免额外的复杂性
        // 在实际生产环境中，应该使用队列处理
        $this->updateProgress($taskId, [
            'progress' => 5,
            'status' => '正在处理导出请求',
            'detail' => '加载数据中...',
            'completed' => false
        ]);
        
        try {
            // 调用处理方法
            $result = $this->handleExport($taskId, $params);
            
            // 更新进度为完成
            $this->updateProgress($taskId, [
                'progress' => 100,
                'status' => '导出完成',
                'detail' => '文件已准备好，即将下载',
                'completed' => true,
                'download_url' => $result['download_url'] ?? null
            ]);
            
            return true;
        } catch (\Exception $e) {
            // 记录错误
            Log::error('导出处理失败', [
                'task_id' => $taskId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 更新进度为失败
            $this->updateProgress($taskId, [
                'progress' => 100,
                'status' => '导出失败',
                'detail' => '错误: ' . $e->getMessage(),
                'completed' => true,
                'error' => true
            ]);
            
            return false;
        }
    }
    
    /**
     * 更新导出进度
     */
    protected function updateProgress($taskId, $data)
    {
        $cacheKey = $this->progressCachePrefix . $taskId;
        $expireTime = 3600; // 1小时过期
        
        // 使用文件缓存，确保在多进程间可以共享
        \Cache::put($cacheKey, $data, $expireTime);
    }
    
    /**
     * 获取导出进度
     */
    protected function getProgress($taskId)
    {
        $cacheKey = $this->progressCachePrefix . $taskId;
        return \Cache::get($cacheKey);
    }
    
    /**
     * 处理导出请求
     *
     * @param string $taskId 任务ID
     * @param array $params 请求参数
     * @return array 处理结果
     */
    protected function handleExport($taskId, $params)
    {
        // 设置更长的执行时间限制和内存限制
        set_time_limit(300); // 5分钟
        ini_set('memory_limit', '1G');
        
        // 获取筛选条件
        $warehouseId = $params['warehouse_id'] ?? null;
        $locationId = $params['location_id'] ?? null;
        $skuFilter = $params['sku_filter'] ?? null;
        $stockFilter = $params['stock_filter'] ?? null;
        $includeImages = ($params['include_images'] ?? '0') === '1'; // 是否包含图片
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 10,
            'status' => '正在查询数据',
            'detail' => '应用筛选条件...',
            'completed' => false
        ]);
        
        // 构建查询
        $query = TecBanhuaConsumableStockModel::query();
        
        // 应用筛选条件
        if (!empty($warehouseId)) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        if (!empty($locationId)) {
            $query->where('location_id', $locationId);
        }
        
        if (!empty($skuFilter)) {
            $query->whereHas('sku', function ($skuQuery) use ($skuFilter) {
                $skuQuery->where('sku', 'like', "%{$skuFilter}%");
            });
        }
        
        if (!empty($stockFilter)) {
            if ($stockFilter == 'zero') {
                $query->where('stock', 0);
            } elseif ($stockFilter == 'positive') {
                $query->where('stock', '>', 0);
            }
        }
        
        // 优化：先获取SKU IDs，然后分批处理
        $stockIds = $query->pluck('id')->toArray();
        $totalCount = count($stockIds);
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 20,
            'status' => '找到 ' . $totalCount . ' 条记录',
            'detail' => '正在加载详细数据...',
            'completed' => false
        ]);
        
        // 如果没有数据，抛出异常
        if ($totalCount === 0) {
            throw new \Exception('没有符合条件的数据可导出');
        }
        
        // 处理请求的主要逻辑
        return $this->processExport($taskId, $stockIds, $includeImages);
    }
    
    /**
     * 处理导出逻辑
     */
    protected function processExport($taskId, $stockIds, $includeImages)
    {
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 30,
            'status' => '正在加载数据',
            'detail' => '正在准备Excel文件...',
            'completed' => false
        ]);
        
        // 批量加载数据，避免内存问题
        $batchSize = 500;
        $stocks = [];
        $totalBatches = ceil(count($stockIds) / $batchSize);
        
        for ($i = 0; $i < $totalBatches; $i++) {
            // 更新进度
            $progress = 30 + ($i / $totalBatches) * 20;
            $this->updateProgress($taskId, [
                'progress' => $progress,
                'status' => '正在加载数据',
                'detail' => '已加载 ' . min(($i + 1) * $batchSize, count($stockIds)) . ' / ' . count($stockIds) . ' 条记录',
                'completed' => false
            ]);
            
            // 获取当前批次的ID
            $batchIds = array_slice($stockIds, $i * $batchSize, $batchSize);
            
            // 加载数据
            $batchStocks = TecBanhuaConsumableStockModel::with([
                'sku.gallery', 'warehouse', 'location'
            ])->whereIn('id', $batchIds)->get();
            
            $stocks = array_merge($stocks, $batchStocks->all());
        }
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 50,
            'status' => '正在生成Excel文件',
            'detail' => '处理 ' . count($stocks) . ' 条记录',
            'completed' => false
        ]);
        
        // 生成Excel文件
        return $this->generateExcel($taskId, $stocks, $includeImages);
    }
    
    /**
     * 生成Excel文件
     */
    protected function generateExcel($taskId, $stocks, $includeImages)
    {
        // 创建临时目录存储图片
        $tempDir = storage_path('app/temp/consumable_stock_export/' . $taskId);
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0777, true);
        }
        
        $tempImageDir = $tempDir . '/images';
        if (!file_exists($tempImageDir)) {
            mkdir($tempImageDir, 0777, true);
        }
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 55,
            'status' => '正在创建Excel文档',
            'detail' => '正在处理数据...',
            'completed' => false
        ]);
        
        // 预加载属性和属性值
        $attrs = \App\Models\Banhua\TecBanhuaAttrModel::with('values')->get();
        $attrValuesMap = [];
        
        // 构建属性值映射
        foreach ($attrs as $attr) {
            foreach ($attr->values as $value) {
                $attrValuesMap[$value->id] = [
                    'attr_name' => $attr->name,
                    'value_name' => $value->name
                ];
            }
        }
        
        // 创建新的Excel文档
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('消耗品库存');
        
        // 设置表头
        $headers = [
            'ID', 'SKU编码', 'SKU名称', '属性组合', 
            '仓库', '库位', '库存数量', 
            '创建时间', '更新时间'
        ];
        
        // 如果包含图片，添加图片列
        if ($includeImages) {
            $headers[] = '图片';
            
            // 处理图片
            $this->updateProgress($taskId, [
                'progress' => 60,
                'status' => '正在处理图片',
                'detail' => '下载和处理SKU图片...',
                'completed' => false
            ]);
            
            $this->processImages($taskId, $stocks, $tempImageDir);
        }
        
        // 设置表头样式
        $headerStyle = [
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'DDDDDD'],
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];
        
        // 写入表头
        foreach ($headers as $col => $header) {
            $sheet->setCellValueByColumnAndRow($col + 1, 1, $header);
        }
        
        // 应用表头样式
        $lastCol = Coordinate::stringFromColumnIndex(count($headers));
        $sheet->getStyle("A1:{$lastCol}1")->applyFromArray($headerStyle);
        
        // 如果包含图片，设置图片列宽
        if ($includeImages) {
            $sheet->getColumnDimension($lastCol)->setWidth(15);
            $sheet->getRowDimension(1)->setRowHeight(30);
        }
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 65,
            'status' => '正在写入数据',
            'detail' => '将数据写入Excel...',
            'completed' => false
        ]);
        
        // 写入数据
        $row = 2;
        $total = count($stocks);
        $processed = 0;
        
        foreach ($stocks as $stock) {
            // 更新进度，每10%更新一次
            $processed++;
            if ($processed % max(1, intval($total / 10)) === 0) {
                $progressPercent = 65 + ($processed / $total) * 25;
                $this->updateProgress($taskId, [
                    'progress' => $progressPercent,
                    'status' => '正在写入数据',
                    'detail' => "已处理 {$processed} / {$total} 条记录",
                    'completed' => false
                ]);
            }
            
            // 获取SKU属性组合
            $attrValuesText = '';
            if (!empty($stock->attr_value_ids)) {
                // 使用库存记录自身的attr_value_ids计算属性组合
                $valueIds = is_array($stock->attr_value_ids) 
                    ? $stock->attr_value_ids 
                    : explode(',', (string)$stock->attr_value_ids);
                    
                $attrValues = [];
                foreach ($valueIds as $id) {
                    $id = trim($id);
                    if (!empty($id) && isset($attrValuesMap[$id])) {
                        $attrValues[] = $attrValuesMap[$id]['value_name'];
                    }
                }
                $attrValuesText = implode(', ', $attrValues);
            } else if ($stock->sku && !empty($stock->sku->attr_value_ids)) {
                // 备用方法：从SKU获取属性值
                $attrValues = [];
                foreach ($stock->sku->attr_value_ids as $valueId) {
                    if (isset($attrValuesMap[$valueId])) {
                        $attrValues[] = $attrValuesMap[$valueId]['value_name'];
                    }
                }
                $attrValuesText = implode(', ', $attrValues);
            }
            
            // 写入数据
            $sheet->setCellValueByColumnAndRow(1, $row, $stock->id);
            $sheet->setCellValueByColumnAndRow(2, $row, $stock->sku ? $stock->sku->sku : '');
            $sheet->setCellValueByColumnAndRow(3, $row, $stock->sku ? $stock->sku->name : '');
            $sheet->setCellValueByColumnAndRow(4, $row, $attrValuesText);
            $sheet->setCellValueByColumnAndRow(5, $row, $stock->warehouse ? $stock->warehouse->name : '');
            $sheet->setCellValueByColumnAndRow(6, $row, $stock->location ? $stock->location->location_name : '');
            $sheet->setCellValueByColumnAndRow(7, $row, $stock->stock);
            $sheet->setCellValueByColumnAndRow(8, $row, $stock->created_at ? $stock->created_at->format('Y-m-d H:i:s') : '');
            $sheet->setCellValueByColumnAndRow(9, $row, $stock->updated_at ? $stock->updated_at->format('Y-m-d H:i:s') : '');
            
            // 如果包含图片，添加图片
            if ($includeImages) {
                // 获取图片路径
                $imagePath = '';
                if ($stock->sku) {
                    $skuId = $stock->sku->id;
                    $imageFile = $tempImageDir . '/' . $skuId . '.jpg';
                    if (file_exists($imageFile)) {
                        $imagePath = $imageFile;
                    }
                }
                
                // 如果有图片，插入到Excel
                if (!empty($imagePath)) {
                    $drawing = new Drawing();
                    $drawing->setName('SKU图片');
                    $drawing->setDescription('SKU图片');
                    $drawing->setPath($imagePath);
                    $drawing->setCoordinates($lastCol . $row);
                    $drawing->setWidth(100);
                    $drawing->setHeight(100);
                    $drawing->setWorksheet($sheet);
                    
                    // 调整行高以适应图片
                    $sheet->getRowDimension($row)->setRowHeight(100);
                }
            }
            
            $row++;
        }
        
        // 自动调整列宽
        foreach (range('A', $lastCol) as $col) {
            if ($col !== $lastCol || !$includeImages) { // 不调整图片列
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
        }
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 90,
            'status' => '正在保存Excel文件',
            'detail' => '即将完成...',
            'completed' => false
        ]);
        
        // 保存Excel文件
        $excelFilename = 'consumable_stock_export_' . date('YmdHis') . '.xlsx';
        $excelPath = $tempDir . '/' . $excelFilename;
        
        $writer = new Xlsx($spreadsheet);
        $writer->save($excelPath);
        
        // 将文件移动到公共目录
        $publicPath = 'exports/consumable_stock/' . $excelFilename;
        Storage::disk('public')->put($publicPath, file_get_contents($excelPath));
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 95,
            'status' => '导出完成',
            'detail' => '文件已生成，准备下载',
            'completed' => true,
            'download_url' => Storage::disk('public')->url($publicPath)
        ]);
        
        // 保存缓存键，用于后续清理
        $cacheKeys = \Cache::get('banhua_consumable_stock_export_keys', []);
        $cacheKeys[] = $this->progressCachePrefix . $taskId;
        \Cache::put('banhua_consumable_stock_export_keys', $cacheKeys, 3600);
        
        // 返回结果
        return [
            'status' => true,
            'download_url' => Storage::disk('public')->url($publicPath)
        ];
    }
    
    /**
     * 处理图片
     */
    protected function processImages($taskId, $stocks, $tempImageDir)
    {
        // 统计需要处理的图片数
        $skusWithImage = 0;
        $skuImageMap = [];
        
        foreach ($stocks as $stock) {
            if ($stock->sku) {
                $skuId = $stock->sku->id;
                
                // 如果已经处理过这个SKU，跳过
                if (isset($skuImageMap[$skuId])) {
                    continue;
                }
                
                // 获取图片URL
                $imageUrl = '';
                if ($stock->sku->gallery && !empty($stock->sku->gallery->path)) {
                    $imageUrl = $stock->sku->gallery->path;
                } elseif (!empty($stock->sku->image)) {
                    $imageUrl = $stock->sku->image;
                }
                
                if (!empty($imageUrl)) {
                    $skuImageMap[$skuId] = $imageUrl;
                    $skusWithImage++;
                }
            }
        }
        
        // 如果没有图片，返回
        if ($skusWithImage === 0) {
            return;
        }
        
        // 下载图片
        $processed = 0;
        foreach ($skuImageMap as $skuId => $imageUrl) {
            // 更新进度
            $processed++;
            $progressPercent = 60 + ($processed / $skusWithImage) * 5;
            $this->updateProgress($taskId, [
                'progress' => $progressPercent,
                'status' => '正在处理图片',
                'detail' => "已处理 {$processed} / {$skusWithImage} 张图片",
                'completed' => false
            ]);
            
            try {
                // 下载图片
                $imageContent = @file_get_contents($imageUrl);
                
                if ($imageContent) {
                    // 保存图片
                    $imagePath = $tempImageDir . '/' . $skuId . '.jpg';
                    file_put_contents($imagePath, $imageContent);
                    
                    // 裁剪图片到合适大小
                    $this->resizeImage($imagePath, 100, 100);
                }
            } catch (\Exception $e) {
                Log::warning('下载图片失败', [
                    'sku_id' => $skuId,
                    'image_url' => $imageUrl,
                    'error' => $e->getMessage()
                ]);
                // 继续处理下一张图片
                continue;
            }
        }
    }
    
    /**
     * 裁剪图片到指定大小
     */
    protected function resizeImage($imagePath, $width, $height)
    {
        try {
            // 如果GD库可用，使用GD库裁剪图片
            if (extension_loaded('gd')) {
                // 获取图片信息
                $imageInfo = getimagesize($imagePath);
                
                if (!$imageInfo) {
                    return false;
                }
                
                // 创建原图
                $srcImage = null;
                switch ($imageInfo[2]) {
                    case IMAGETYPE_JPEG:
                        $srcImage = imagecreatefromjpeg($imagePath);
                        break;
                    case IMAGETYPE_PNG:
                        $srcImage = imagecreatefrompng($imagePath);
                        break;
                    case IMAGETYPE_GIF:
                        $srcImage = imagecreatefromgif($imagePath);
                        break;
                    default:
                        return false;
                }
                
                // 创建目标图
                $dstImage = imagecreatetruecolor($width, $height);
                
                // 调整PNG/GIF的透明度
                if ($imageInfo[2] === IMAGETYPE_PNG || $imageInfo[2] === IMAGETYPE_GIF) {
                    imagecolortransparent($dstImage, imagecolorallocate($dstImage, 0, 0, 0));
                    imagealphablending($dstImage, false);
                    imagesavealpha($dstImage, true);
                }
                
                // 缩放图片
                imagecopyresampled(
                    $dstImage, $srcImage,
                    0, 0, 0, 0,
                    $width, $height, $imageInfo[0], $imageInfo[1]
                );
                
                // 保存图片
                switch ($imageInfo[2]) {
                    case IMAGETYPE_JPEG:
                        imagejpeg($dstImage, $imagePath, 90);
                        break;
                    case IMAGETYPE_PNG:
                        imagepng($dstImage, $imagePath);
                        break;
                    case IMAGETYPE_GIF:
                        imagegif($dstImage, $imagePath);
                        break;
                }
                
                // 释放资源
                imagedestroy($srcImage);
                imagedestroy($dstImage);
                
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('裁剪图片失败', [
                'image_path' => $imagePath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 获取处理路由
     */
    public function getHandleRoute()
    {
        return '/admin/banhua/consumable-stock-export';
    }
    
    /**
     * 下载导出文件
     */
    public function download(Request $request)
    {
        $filePath = $request->get('file');
        
        if (empty($filePath)) {
            return response()->json([
                'status' => false,
                'message' => '缺少文件路径参数'
            ]);
        }
        
        // 检查文件是否存在
        if (!Storage::disk('public')->exists($filePath)) {
            return response()->json([
                'status' => false,
                'message' => '文件不存在或已被删除'
            ]);
        }
        
        // 获取文件名
        $filename = basename($filePath);
        
        // 下载文件
        return Storage::disk('public')->download($filePath, $filename);
    }
} 