<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaImageGalleryModel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Http\JsonResponse;

/**
 * 阪画图库数据导入操作
 */
class TecBanhuaImageGalleryImportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-upload"></i> 导入';

    /**
     * 处理导入请求
     *
     * @param Request $request
     * @return JsonResponse|mixed
     */
    public function handle(Request $request = null)
    {
        if (!$request || !$request->hasFile('file')) {
            admin_toastr('请选择要导入的文件', 'error');
            return redirect()->back();
        }
        
        Log::info('开始导入阪画图库数据');
        
        try {
            $file = $request->file('file');
            
            // 验证文件类型
            $validator = Validator::make(['file' => $file], [
                'file' => 'required|mimes:xlsx,xls',
            ]);
            
            if ($validator->fails()) {
                return Response::error('文件格式不正确，请上传Excel文件(.xlsx或.xls)');
            }
            
            // 读取Excel文件
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            
            // 确保有表头
            if (count($rows) < 2) {
                return Response::error('Excel文件格式不正确，至少需要包含表头和一行数据');
            }
            
            // 获取表头
            $headers = $rows[0];
            
            // 验证必要的列
            $requiredColumns = ['图库ID', '图片名称', '图片路径'];
            $headerMap = [];
            
            foreach ($requiredColumns as $required) {
                $found = false;
                foreach ($headers as $index => $header) {
                    if (trim($header) === $required) {
                        $headerMap[$required] = $index;
                        $found = true;
                        break;
                    }
                }
                if (!$found) {
                    return Response::error("Excel文件缺少必要的列：{$required}");
                }
            }
            
            // 可选列
            foreach (['备注'] as $optional) {
                foreach ($headers as $index => $header) {
                    if (trim($header) === $optional) {
                        $headerMap[$optional] = $index;
                        break;
                    }
                }
            }
            
            Log::info('解析表头成功', [
                'header_map' => $headerMap,
            ]);
            
            // 开始导入数据
            $total = 0;
            $success = 0;
            $skipped = 0;
            $errors = [];
            
            DB::beginTransaction();
            
            try {
                // 准备批量插入的数据
                $batchData = [];
                $updateData = [];
                
                // 跳过表头
                for ($i = 1; $i < count($rows); $i++) {
                    $row = $rows[$i];
                    $total++;
                    
                    if (empty($row[$headerMap['图库ID']])) {
                        $skipped++;
                        $errors[] = "第" . ($i + 1) . "行：图库ID不能为空";
                        continue;
                    }
                    
                    if (empty($row[$headerMap['图片名称']])) {
                        $skipped++;
                        $errors[] = "第" . ($i + 1) . "行：图片名称不能为空";
                        continue;
                    }
                    
                    if (empty($row[$headerMap['图片路径']])) {
                        $skipped++;
                        $errors[] = "第" . ($i + 1) . "行：图片路径不能为空";
                        continue;
                    }
                    
                    // 准备数据
                    $data = [
                        'image_id' => trim($row[$headerMap['图库ID']]),
                        'name' => trim($row[$headerMap['图片名称']]),
                        'path' => trim($row[$headerMap['图片路径']]),
                        'remark' => isset($headerMap['备注']) ? trim($row[$headerMap['备注']] ?? '') : '',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                    
                    // 将数据添加到批量操作数组
                    $batchData[] = $data;
                }
                
                // 先检查哪些记录已存在
                if (!empty($batchData)) {
                    $existingImageIds = TecBanhuaImageGalleryModel::whereIn('image_id', array_column($batchData, 'image_id'))
                        ->pluck('image_id')
                        ->toArray();
                    
                    // 分离新增和更新的数据
                    $insertData = [];
                    foreach ($batchData as $data) {
                        if (in_array($data['image_id'], $existingImageIds)) {
                            // 更新现有记录
                            TecBanhuaImageGalleryModel::where('image_id', $data['image_id'])
                                ->update([
                                    'name' => $data['name'],
                                    'path' => $data['path'],
                                    'remark' => $data['remark'],
                                    'updated_at' => now(),
                                ]);
                            Log::info('更新图库记录', [
                                'image_id' => $data['image_id'],
                                'name' => $data['name'],
                            ]);
                            $success++;
                        } else {
                            // 添加到新增数据数组
                            $insertData[] = $data;
                        }
                    }
                    
                    // 批量插入新记录
                    if (!empty($insertData)) {
                        // 分批插入，每批最多100条
                        foreach (array_chunk($insertData, 100) as $chunk) {
                            TecBanhuaImageGalleryModel::insert($chunk);
                            $success += count($chunk);
                            Log::info('批量插入图库记录', [
                                'count' => count($chunk),
                            ]);
                        }
                    }
                }
                
                DB::commit();
                
                Log::info('导入阪画图库数据完成', [
                    'total' => $total,
                    'success' => $success,
                    'skipped' => $skipped
                ]);
                
                // 构建结果消息
                $message = "导入完成，共处理 {$total} 条记录，成功导入 {$success} 条";
                
                if ($skipped > 0) {
                    $message .= "，跳过 {$skipped} 条";
                }
                
                if (!empty($errors)) {
                    $message .= "<br>错误记录：<br>" . implode("<br>", array_slice($errors, 0, 5));
                    if (count($errors) > 5) {
                        $message .= "<br>...共 " . count($errors) . " 条错误记录";
                    }
                }
                
                // 修复静态调用错误
                return (new JsonResponse())->success($message)->refresh();
                
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('导入过程中出错，已回滚事务', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // 修复静态调用错误
                return (new JsonResponse())->error('导入过程中出错：' . $e->getMessage());
            }
            
        } catch (\Exception $e) {
            Log::error('导入阪画图库数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 修复静态调用错误
            return (new JsonResponse())->error('导入失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 设置HTML属性
     *
     * @return array|string[]
     */
    protected function getElementAttributes()
    {
        return [
            'class' => 'btn btn-default',
            'style' => 'margin-right:3px',
        ];
    }
    
    /**
     * 渲染按钮
     *
     * @return string
     */
    public function render()
    {
        // 自定义按钮和上传表单
        $token = csrf_token();
        
        return <<<HTML
<a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline import-gallery" style="margin-right:3px">
    <i class="fa fa-upload"></i><span class="d-none d-sm-inline">&nbsp; 导入</span>
</a>

<div class="modal fade" id="import-gallery-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导入图库数据</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    请先下载导入模板，按照模板格式填写数据后再上传。
                    <button type="button" class="btn btn-primary btn-sm download-template" style="margin-left:10px;">
                        <i class="fa fa-download"></i> 下载Excel导入模板
                    </button>
                </div>
                <form id="import-gallery-form" action="{$this->getHandleRoute()}" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="_token" value="{$token}">
                    <div class="form-group">
                        <label for="file">选择文件</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".csv,.xlsx,.xls">
                        <small class="form-text text-muted">
                            支持Excel格式(.xlsx, .xls)和CSV格式文件，文件大小不超过10MB。必须包含以下列：图库ID、图片名称、图片路径。
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white btn-mini" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary btn-mini" id="submit-import">导入</button>
            </div>
        </div>
    </div>
</div>

<script>
$(function () {
    // 点击导入按钮显示模态框
    $('.import-gallery').on('click', function () {
        $('#import-gallery-modal').modal('show');
    });
    
    // 下载模板按钮点击事件
    $('.download-template').on('click', function() {
        // 显示加载中
        Dcat.loading();
        
        // 直接打开新窗口下载，避免使用iframe
        window.open('{$this->getTemplateUrl()}', '_blank');
        
        // 延迟关闭加载提示
        setTimeout(function() {
            Dcat.loading(false);
        }, 1000);
    });
    
    // 提交导入表单
    $('#submit-import').on('click', function () {
        var fileInput = $('#file');
        
        if (fileInput.val() === '') {
            Dcat.error('请选择要导入的文件');
            return;
        }
        
        // 显示加载中
        Dcat.loading();
        
        // 提交表单
        var formData = new FormData($('#import-gallery-form')[0]);
        
        $.ajax({
            url: '{$this->getHandleRoute()}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                // 关闭加载中提示
                Dcat.loading(false);
                
                // 输出调试信息到控制台
                console.log('导入响应数据:', response);
                
                // 提取消息内容
                var message = '';
                if (response.message) {
                    message = response.message;
                } else if (response.data && response.data.message) {
                    message = response.data.message;
                }
                
                // 根据消息内容判断类型
                if (message) {
                    // 检查消息中是否包含特定关键词
                    if (message.indexOf('未新增任何数据') >= 0 || message.indexOf('已存在') >= 0) {
                        // 显示警告消息
                        Dcat.warning(message);
                    } else if (message.indexOf('失败') >= 0 || message.indexOf('错误') >= 0) {
                        // 显示错误消息
                        Dcat.error(message);
                    } else if (message.indexOf('成功') >= 0 || message.indexOf('新增') >= 0) {
                        // 显示成功消息
                        Dcat.success(message);
                    } else {
                        // 默认显示为信息消息
                        Dcat.info(message);
                    }
                } else {
                    // 没有消息内容时显示默认提示
                    Dcat.info('导入处理完成');
                }
                
                // 关闭弹窗
                $('#import-gallery-modal').modal('hide');
                
                // 延迟刷新页面
                setTimeout(function () {
                    Dcat.reload();
                }, 1500);
            },
            error: function (xhr) {
                Dcat.loading(false);
                
                // 输出错误信息到控制台
                console.error('导入错误:', xhr);
                
                var message = '导入失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                
                Dcat.error(message);
            }
        });
    });
});
</script>
HTML;
    }
    
    /**
     * 获取处理路由
     *
     * @return string
     */
    protected function getHandleRoute()
    {
        return admin_url('banhua/gallery-import');
    }
    
    /**
     * 获取模板下载URL
     *
     * @return string
     */
    public function getTemplateUrl()
    {
        return admin_url('banhua/gallery-import-template');
    }
} 