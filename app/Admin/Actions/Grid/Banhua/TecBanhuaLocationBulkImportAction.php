<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Widgets\Modal;
use App\Models\TecWarehouseModel;
use App\Models\Banhua\TecBanhuaLocationModel;

class TecBanhuaLocationBulkImportAction extends AbstractTool
{
    protected $title = '批量追加';

    public function render()
    {
        $warehouses = TecWarehouseModel::select('id', 'name')->get();
        $areas = TecBanhuaLocationModel::distinct()
            ->select('area_number')
            ->where('area_number', '!=', '0')
            ->where('area_number', '!=', '')
            ->get();

        $modal = Modal::make()
            ->lg()
            ->title($this->title)
            ->body(view('admin.banhua.tec_banhua_location_import', compact('warehouses', 'areas')))
            ->button('<button class="btn btn-primary"><i class="fa fa-files-o"></i> ' . $this->title . '</button>');

        return $modal->render();
    }

    public function icon()
    {
        return 'feather icon-layers';
    }
} 