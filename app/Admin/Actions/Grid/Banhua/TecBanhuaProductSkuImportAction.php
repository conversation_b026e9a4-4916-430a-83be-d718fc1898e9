<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\Banhua\TecBanhuaProductCategoryModel;
use App\Models\Banhua\TecBanhuaProductUnitModel;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class TecBanhuaProductSkuImportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-upload"></i> 导入SKU';

    /**
     * 日志记录函数
     *
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 日志上下文
     * @return void
     */
    protected function log($level, $message, array $context = [])
    {
        // 同时记录到普通日志和专用日志
        Log::channel('sku_import')->{$level}($message, $context);
        Log::{$level}($message, $context);
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Request $request)
    {
        try {
            // 设置更长的执行时间限制
            ini_set('max_execution_time', 600); // 设置为10分钟
            set_time_limit(600); // 同时使用set_time_limit确保PHP执行时间也被延长
            
            // 记录导入开始日志
            $this->log('info', '开始SKU导入处理', [
                'file_info' => $request->hasFile('file') ? [
                    'name' => $request->file('file')->getClientOriginalName(),
                    'size' => $request->file('file')->getSize(),
                    'type' => $request->file('file')->getClientMimeType()
                ] : '未接收到文件'
            ]);
            
            // 验证请求
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:10240', // 最大10MB
            ], [
                'file.required' => '请选择要导入的文件',
                'file.file' => '上传的文件无效',
                'file.mimes' => '请上传CSV或Excel格式的文件',
                'file.max' => '文件大小不能超过10MB',
            ]);
            
            if ($validator->fails()) {
                $errorMsg = $validator->errors()->first();
                $this->log('warning', 'SKU导入验证失败', ['error' => $errorMsg]);
                return response()->json([
                    'message' => $errorMsg,
                    'status' => false,
                    'refresh' => false
                ]);
            }
            
            // 获取上传的文件
            $file = $request->file('file');
            $path = $file->getRealPath();
            $extension = strtolower($file->getClientOriginalExtension());
            
            $this->log('info', 'SKU导入文件信息', [
                'original_name' => $file->getClientOriginalName(),
                'path' => $path,
                'extension' => $extension,
                'size' => $file->getSize()
            ]);
            
            // 根据文件类型选择不同的处理方式
            if (in_array($extension, ['xlsx', 'xls'])) {
                // Excel文件处理
                $this->log('info', '开始处理Excel文件导入');
                return $this->handleExcelFile($path, $extension);
            } else {
                // CSV文件处理
                $this->log('info', '开始处理CSV文件导入');
                return $this->handleCsvFile($path);
            }
            
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            $this->log('error', 'SKU导入失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => '导入失败: ' . $e->getMessage(),
                'status' => false,
                'refresh' => false
            ]);
        }
    }
    
    /**
     * 处理Excel文件导入
     *
     * @param string $path 文件路径
     * @param string $extension 文件扩展名
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleExcelFile($path, $extension)
    {
        try {
            // 设置更长的执行时间限制
            ini_set('max_execution_time', 600); // 设置为10分钟
            set_time_limit(600); // 同时使用set_time_limit确保PHP执行时间也被延长
            
            // 根据扩展名选择不同的读取器
            if ($extension === 'xlsx') {
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                $this->log('info', '使用XLSX读取器');
            } else {
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xls();
                $this->log('info', '使用XLS读取器');
            }
            
            // 只读取数据，不读取格式
            $reader->setReadDataOnly(true);
            
            // 实现更强大的工作表选择逻辑
            $spreadsheet = $reader->load($path);
            
            // 获取所有工作表名称以便诊断
            $sheetNames = $spreadsheet->getSheetNames();
            $this->log('info', '检测到Excel工作表列表', [
                'sheet_names' => $sheetNames,
                'sheet_count' => count($sheetNames)
            ]);
            
            // 尝试按照以下顺序选择工作表：
            // 1. 名为"导入模板"的工作表
            // 2. 第一个工作表（索引0）
            $worksheet = null;
            if (in_array('导入模板', $sheetNames)) {
                $worksheet = $spreadsheet->getSheetByName('导入模板');
                $this->log('info', '找到并选择了"导入模板"工作表');
            } else {
                $worksheet = $spreadsheet->getSheet(0); // 默认第一个工作表
                $this->log('info', '未找到"导入模板"工作表，使用第一个工作表', [
                    'sheet_name' => $worksheet->getTitle()
                ]);
            }
            
            // 获取工作表的行数和列数
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
            
            $this->log('info', 'Excel文件基本信息', [
                'sheet_name' => $worksheet->getTitle(),
                'row_count' => $highestRow,
                'column_count' => $highestColumnIndex
            ]);
            
            // 扫描前15行，找到包含"SKU"的那一行作为表头
            $headerRow = 8; // 默认表头在第8行
            $foundHeader = false;
            
            for ($row = 1; $row <= min(15, $highestRow); $row++) {
                // 取出前10列的内容
                $rowContents = [];
                for ($col = 1; $col <= min(10, $highestColumnIndex); $col++) {
                    $cellValue = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                    if ($cellValue !== null) {
                        $rowContents[$col] = $cellValue;
                    }
                }
                
                // 记录每一行的内容，便于诊断
                $this->log('debug', "扫描第{$row}行内容", [
                    'row_contents' => $rowContents
                ]);
                
                // 检查是否包含"SKU"单元格
                if (in_array('SKU', $rowContents)) {
                    $headerRow = $row;
                    $foundHeader = true;
                    $this->log('info', "找到表头行，位于第{$row}行");
                    break;
                }
            }
            
            if (!$foundHeader) {
                $this->log('warning', '未找到包含"SKU"的表头行，使用默认的第8行');
            }
            
            // 读取表头行
            $headers = [];
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $value = $worksheet->getCellByColumnAndRow($col, $headerRow)->getValue();
                if ($value) {
                    $headers[] = trim($value);
                }
            }
            
            $this->log('info', 'Excel表头行信息', [
                'header_row' => $headerRow,
                'headers' => $headers,
                'header_count' => count($headers)
            ]);
            
            // 验证Excel格式
            $requiredColumns = ['SKU', '名称', '图库ID', '分类', '单位'];
            $missingColumns = array_diff($requiredColumns, $headers);
            
            if (!empty($missingColumns)) {
                $this->log('warning', 'Excel文件缺少必要的列', [
                    'missing_columns' => $missingColumns,
                    'found_headers' => $headers
                ]);
                return response()->json([
                    'message' => 'Excel文件缺少必要的列: ' . implode(', ', $missingColumns),
                    'status' => false,
                    'refresh' => false
                ]);
            }
            
            // 获取列索引
            $columnIndexes = [];
            foreach ($headers as $index => $header) {
                $columnIndexes[trim($header)] = $index;
            }
            
            // 开始导入
            DB::beginTransaction();
            
            $totalRows = 0;
            $successCount = 0;
            $errorRows = [];
            $existingSkus = [];
            
            // 缓存分类和单位数据
            $categories = TecBanhuaProductCategoryModel::pluck('id', 'title')->toArray();
            $units = TecBanhuaProductUnitModel::pluck('id', 'unit_name')->toArray();
            
            // 预先加载所有属性和属性值数据到内存，优化查询
            $allAttrs = DB::table('t_banhua_attrs')->get();
            $attrMap = [];
            foreach ($allAttrs as $attr) {
                $attrMap[$attr->id] = $attr;
            }
            
            // 一次性查询所有属性值，减少数据库交互
            $allAttrValues = DB::table('t_banhua_attr_values')->get();
            
            // 构建高效的查找表
            $attrValuesByExactName = [];
            $attrValuesByLowerName = [];
            $attrValuesByAttrId = [];
            
            foreach ($allAttrValues as $value) {
                // 添加属性名称信息
                $value->attr_name = isset($attrMap[$value->attr_id]) ? $attrMap[$value->attr_id]->name : '';
                
                // 建立多种索引方式
                $attrValuesByExactName[$value->name] = $value;
                $attrValuesByLowerName[strtolower(trim($value->name))] = $value;
                
                // 按属性ID分组
                if (!isset($attrValuesByAttrId[$value->attr_id])) {
                    $attrValuesByAttrId[$value->attr_id] = [];
                }
                $attrValuesByAttrId[$value->attr_id][$value->name] = $value;
            }
            
            $this->log('info', '属性和属性值数据加载完成', [
                'attr_count' => count($allAttrs),
                'attr_value_count' => count($allAttrValues)
            ]);
            
            // 从表头中找出所有可能的属性列
            $attrColumns = [];
            foreach ($columnIndexes as $header => $index) {
                foreach ($allAttrs as $attr) {
                    // 如果表头与属性名完全匹配或包含
                    if ($header == $attr->name || strpos($header, $attr->name) !== false) {
                        $attrColumns[$header] = $attr->name;
                        break;
                    }
                }
            }
            
            $this->log('info', '检测到的属性列', [
                'attr_columns' => $attrColumns
            ]);
            
            // 缓存图库ID和图片路径的映射
            $galleryImages = DB::table('t_banhua_image_gallery')
                ->whereNotNull('path')
                ->whereNotNull('id')
                ->pluck('path', 'id')
                ->toArray();
            
            $this->log('info', '图库数据加载完成', [
                'image_count' => count($galleryImages)
            ]);
            
            $this->log('info', '获取分类和单位数据', [
                'category_count' => count($categories),
                'unit_count' => count($units),
            ]);
            
            // 从表头的下一行开始读取数据
            $startDataRow = $headerRow + 1;
            $this->log('info', "将从第{$startDataRow}行开始读取数据");
            
            // 批量插入数据准备
            $batchSize = 100; // 增加批量处理大小到100条
            $skuBatch = [];
            
            // 从数据行开始读取数据
            for ($row = $startDataRow; $row <= $highestRow; $row++) {
                // 检查是否为空行
                $isEmpty = true;
                for ($col = 1; $col <= $highestColumnIndex; $col++) {
                    if ($worksheet->getCellByColumnAndRow($col, $row)->getValue() !== null) {
                        $isEmpty = false;
                        break;
                    }
                }
                
                if ($isEmpty) {
                    continue;
                }
                
                $totalRows++;
                
                try {
                    // 读取行数据
                    $rowData = [];
                    for ($col = 1; $col <= $highestColumnIndex; $col++) {
                        $rowData[] = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                    }
                    
                    // 检查行数据是否完整
                    if (count($rowData) < count($headers)) {
                        $errorRows[] = [
                            'row' => $row,
                            'error' => '数据列数不足',
                            'data' => implode(',', $rowData)
                        ];
                        $this->log('warning', "Excel第{$row}行数据列数不足", [
                            'expected' => count($headers),
                            'actual' => count($rowData)
                        ]);
                        continue;
                    }
                    
                    try {
                        // 获取SKU数据
                        $skuCode = trim($rowData[$columnIndexes['SKU']]);
                        $name = trim($rowData[$columnIndexes['名称']]);
                        $pid = trim($rowData[$columnIndexes['图库ID']]);
                        $categoryName = trim($rowData[$columnIndexes['分类']]);
                        $unitName = trim($rowData[$columnIndexes['单位']]);
                        
                        // 可选字段
                        $jan = isset($columnIndexes['JAN']) ? trim($rowData[$columnIndexes['JAN']]) : '';
                        $price = isset($columnIndexes['价格']) ? floatval($rowData[$columnIndexes['价格']]) : 0;
                        
                        // 获取图片路径
                        $image = '';
                        if (isset($columnIndexes['图片']) && !empty($rowData[$columnIndexes['图片']])) {
                            $image = trim($rowData[$columnIndexes['图片']]);
                        } else if (isset($columnIndexes['图片URL']) && !empty($rowData[$columnIndexes['图片URL']])) {
                            $image = trim($rowData[$columnIndexes['图片URL']]);
                        } else if (!empty($pid) && isset($galleryImages[$pid])) {
                            // 从图库中获取图片路径
                            $image = $galleryImages[$pid];
                        }
                        
                        // 验证必填字段
                        if (empty($skuCode) || empty($name) || empty($categoryName) || empty($unitName)) {
                            $errorRows[] = [
                                'row' => $row,
                                'error' => 'SKU、名称、分类或单位不能为空',
                                'data' => implode(',', $rowData)
                            ];
                            continue;
                        }
                        
                        // 获取分类ID
                        $categoryId = $categories[$categoryName] ?? null;
                        if (!$categoryId) {
                            $errorRows[] = [
                                'row' => $row,
                                'error' => "分类 '{$categoryName}' 不存在",
                                'data' => implode(',', $rowData)
                            ];
                            continue;
                        }
                        
                        // 获取单位ID
                        $unitId = $units[$unitName] ?? null;
                        if (!$unitId) {
                            $errorRows[] = [
                                'row' => $row,
                                'error' => "单位 '{$unitName}' 不存在",
                                'data' => implode(',', $rowData)
                            ];
                            continue;
                        }
                        
                        // 解析属性值
                        $attrValueIds = [];
                        $attrValuesText = '';
                        
                        // 从表格属性列中获取属性值
                        foreach ($attrColumns as $header => $attrName) {
                            if (isset($columnIndexes[$header]) && !empty($rowData[$columnIndexes[$header]])) {
                                $attrValue = trim($rowData[$columnIndexes[$header]]);
                                if (!empty($attrValue)) {
                                    // 查找属性值ID
                                    if (isset($attrValuesByExactName[$attrValue])) {
                                        $value = $attrValuesByExactName[$attrValue];
                                        $attrValueIds[] = $value->id;
                                        $attrValuesText .= "{$attrName}:{$attrValue}, ";
                                    } else {
                                        $lowerPart = strtolower($attrValue);
                                        if (isset($attrValuesByLowerName[$lowerPart])) {
                                            $value = $attrValuesByLowerName[$lowerPart];
                                            $attrValueIds[] = $value->id;
                                            $attrValuesText .= "{$attrName}:{$attrValue}, ";
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 直接处理属性组合列 - 优化检索流程
                        $attrComboString = '';
                        $attrValueIds = [];
                        $attrValuesText = '';
                        
                        // 首先尝试从属性组合列获取
                        if (isset($columnIndexes['属性组合']) && !empty($rowData[$columnIndexes['属性组合']])) {
                            $attrComboString = trim($rowData[$columnIndexes['属性组合']]);
                        } 
                        // 如果没有属性组合列，尝试从属性值列获取
                        else if (isset($columnIndexes['属性值']) && !empty($rowData[$columnIndexes['属性值']])) {
                            $attrComboString = trim($rowData[$columnIndexes['属性值']]);
                        }
                        
                        // 如果有属性字符串，处理它
                        if (!empty($attrComboString)) {
                            // 分割属性字符串
                            $attrParts = array_map('trim', explode(',', $attrComboString));
                            
                            foreach ($attrParts as $part) {
                                $part = trim($part);
                                if (empty($part)) continue;
                                
                                // 使用优化的查找逻辑
                                if (isset($attrValuesByExactName[$part])) {
                                    $value = $attrValuesByExactName[$part];
                                    $attrValueIds[] = $value->id;
                                    $attrValuesText .= "{$value->attr_name}:{$part}, ";
                                } else {
                                    $lowerPart = strtolower($part);
                                    if (isset($attrValuesByLowerName[$lowerPart])) {
                                        $value = $attrValuesByLowerName[$lowerPart];
                                        $attrValueIds[] = $value->id;
                                        $attrValuesText .= "{$value->attr_name}:{$part}, ";
                                    } else if ($totalRows % 100 === 0) {
                                        // 只在找不到匹配时记录警告，且减少日志频率
                                        $this->log('warning', '未能匹配属性值', [
                                            'row' => $totalRows,
                                            'sku' => $skuCode,
                                            'attr_part' => $part
                                        ]);
                                    }
                                }
                            }
                        }
                        
                        // 减少日志记录频率
                        if ($totalRows % 100 === 0) {
                            $this->log('debug', '提取的属性值', [
                                'row' => $totalRows,
                                'sku' => $skuCode,
                                'attr_value_ids' => $attrValueIds
                            ]);
                        }
                        
                        // 确保属性值ID是唯一的
                        $attrValueIds = array_unique($attrValueIds);
                        
                        // 检查SKU和属性值组合是否已存在
                        $skuAttrHash = TecBanhuaProductSkuModel::generateSkuAttrHash($skuCode, $attrValueIds);
                        
                        if (TecBanhuaProductSkuModel::where('sku_attr_hash', $skuAttrHash)->exists()) {
                            $existingSkus[] = $skuCode . "[" . $attrValuesText . "]";
                            continue;
                        }
                        
                        // 准备批量插入的数据
                        $skuBatch[] = [
                            'sku' => $skuCode,
                            'name' => $name,
                            'pid' => $pid,
                            'jan' => $jan ?? null,
                            'price' => $price ?? 0,
                            'image' => $image ?? '',
                            'category_id' => $categoryId,
                            'unit_id' => $unitId,
                            'attr_value_ids' => json_encode($attrValueIds),
                            'sku_attr_hash' => $skuAttrHash,
                            'created_at' => now(),
                            'updated_at' => now()
                        ];
                        
                        // 当达到批量大小时执行批量插入
                        if (count($skuBatch) >= $batchSize) {
                            try {
                                $this->log('info', '执行批量插入', ['batch_size' => count($skuBatch)]);
                                
                                DB::table('t_banhua_product_skus')->insert($skuBatch);
                                $successCount += count($skuBatch);
                                
                                $this->log('info', '批量插入成功', ['count' => count($skuBatch)]);
                                // 清空批次数据
                                $skuBatch = [];
                            } catch (\Exception $batchEx) {
                                $this->log('error', '批量插入失败', [
                                    'error' => $batchEx->getMessage()
                                ]);
                                throw $batchEx;
                            }
                        }
                        
                    } catch (\Exception $dataEx) {
                        $this->log('error', '读取数据时出错', [
                            'row' => $row,
                            'error' => $dataEx->getMessage()
                        ]);
                        throw $dataEx;
                    }
                    
                } catch (\Exception $e) {
                    $errorRows[] = [
                        'row' => $row,
                        'error' => $e->getMessage(),
                        'data' => implode(',', $rowData ?? [])
                    ];
                    
                    $this->log('error', '导入SKU行数据失败', [
                        'row' => $row,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // 处理最后一批数据
            if (!empty($skuBatch)) {
                try {
                    $this->log('info', '执行最后一批批量插入', ['batch_size' => count($skuBatch)]);
                    
                    DB::table('t_banhua_product_skus')->insert($skuBatch);
                    $successCount += count($skuBatch);
                    
                    $this->log('info', '最后一批批量插入成功', ['count' => count($skuBatch)]);
                } catch (\Exception $batchEx) {
                    $this->log('error', '最后一批批量插入失败', [
                        'error' => $batchEx->getMessage()
                    ]);
                    throw $batchEx;
                }
            }
            
            // 提交事务
            DB::commit();
            
            // 生成导入报告
            if ($successCount > 0) {
                $message = "导入完成：总共 {$totalRows} 行，成功新增 {$successCount} 行";
            } else {
                $message = "导入完成：未新增任何数据";
            }
            
            if (!empty($existingSkus)) {
                $message .= "，" . count($existingSkus) . " 个SKU已存在（已跳过）";
            }
            
            if (!empty($errorRows)) {
                $message .= "，" . count($errorRows) . " 行导入失败";
                
                // 记录详细错误信息
                $this->log('warning', 'SKU导入存在错误行', [
                    'error_count' => count($errorRows)
                ]);
            }
            
            // 记录导入结果
            $this->log('info', 'Excel导入结果', [
                'total' => $totalRows,
                'success' => $successCount,
                'existing' => count($existingSkus),
                'errors' => count($errorRows)
            ]);
            
            // 构建自定义响应数据
            $responseData = [
                'message' => $message,
                'status' => true,
                'refresh' => true,
                'statistics' => [
                    'total' => $totalRows,
                    'success' => $successCount,
                    'existing' => count($existingSkus),
                    'errors' => count($errorRows)
                ]
            ];
            
            // 根据是否有成功导入的数据返回不同类型的响应
            if ($successCount > 0) {
                return response()->json($responseData);
            } else if (count($existingSkus) > 0 && count($errorRows) == 0) {
                // 所有记录都是已存在的，没有错误
                return response()->json($responseData);
            } else if (count($errorRows) > 0) {
                // 有错误的记录
                return response()->json($responseData);
            } else {
                // 没有记录需要导入
                return response()->json($responseData);
            }
            
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            $this->log('error', 'Excel导入失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => '导入失败: ' . $e->getMessage(),
                'status' => false,
                'refresh' => false
            ]);
        }
    }
    
    /**
     * 处理CSV文件导入
     *
     * @param string $path 文件路径
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleCsvFile($path)
    {
        try {
            // 设置更长的执行时间限制
            ini_set('max_execution_time', 600); // 设置为10分钟
            set_time_limit(600); // 同时使用set_time_limit确保PHP执行时间也被延长
            
            $this->log('info', '开始读取CSV文件', ['file_path' => $path]);
            
            // 读取CSV文件
            $handle = fopen($path, 'r');
            if ($handle === false) {
                $this->log('error', '无法打开CSV文件', ['path' => $path]);
                return response()->json([
                    'message' => '无法打开CSV文件',
                    'status' => false,
                    'refresh' => false
                ]);
            }
            
            // 检查BOM标记并跳过
            $bom = "\xef\xbb\xbf";
            $firstThreeBytes = fread($handle, 3);
            if ($firstThreeBytes !== $bom) {
                // 如果不是BOM标记，回到文件开头
                rewind($handle);
                $this->log('info', 'CSV文件没有BOM标记，重置文件指针到开头');
            } else {
                $this->log('info', 'CSV文件有BOM标记，已跳过');
            }
            
            // 读取前15行数据，用于检测表头行
            $rowsToCheck = [];
            $headerRowIndex = -1;
            $foundSKUHeader = false;
            
            for ($i = 0; $i < 15; $i++) {
                $row = fgetcsv($handle);
                if ($row === false) {
                    break; // 文件结束
                }
                
                // 保存行数据以便后续处理
                $rowsToCheck[] = $row;
                
                // 检查是否包含"SKU"
                if (!$foundSKUHeader && in_array('SKU', $row)) {
                    $headerRowIndex = $i;
                    $foundSKUHeader = true;
                    $this->log('info', "在CSV文件的第".($i+1)."行找到表头行");
                }
            }
            
            // 如果没有找到表头，则视第一行为表头
            if (!$foundSKUHeader) {
                $headerRowIndex = 0;
                $this->log('warning', "未在CSV文件中找到包含'SKU'的表头行，使用第一行作为表头");
            }
            
            // 使用找到的行作为表头
            $headers = $rowsToCheck[$headerRowIndex];
            
            $this->log('info', 'CSV表头信息', [
                'header_row_index' => $headerRowIndex+1, // 1-based for logging
                'headers' => $headers,
                'header_count' => count($headers)
            ]);
            
            // 验证CSV格式
            $requiredColumns = ['SKU', '名称', '图库ID', '分类', '单位'];
            $missingColumns = array_diff($requiredColumns, $headers);
            
            if (!empty($missingColumns)) {
                $this->log('warning', 'CSV文件缺少必要的列', [
                    'missing_columns' => $missingColumns,
                    'found_headers' => $headers
                ]);
                fclose($handle);
                return response()->json([
                    'message' => 'CSV文件缺少必要的列: ' . implode(', ', $missingColumns),
                    'status' => false,
                    'refresh' => false
                ]);
            }
            
            // 获取列索引
            $columnIndexes = [];
            foreach ($headers as $index => $header) {
                $columnIndexes[trim($header)] = $index;
            }
            
            // 开始导入
            DB::beginTransaction();
            
            $totalRows = 0;
            $successCount = 0;
            $errorRows = [];
            $existingSkus = [];
            
            // 缓存分类和单位数据
            $categories = TecBanhuaProductCategoryModel::pluck('id', 'title')->toArray();
            $units = TecBanhuaProductUnitModel::pluck('id', 'unit_name')->toArray();
            
            // 预先加载所有属性和属性值数据到内存，优化查询
            $allAttrs = DB::table('t_banhua_attrs')->get();
            $attrMap = [];
            foreach ($allAttrs as $attr) {
                $attrMap[$attr->id] = $attr;
            }
            
            // 一次性查询所有属性值，减少数据库交互
            $allAttrValues = DB::table('t_banhua_attr_values')->get();
            
            // 构建高效的查找表
            $attrValuesByExactName = [];
            $attrValuesByLowerName = [];
            $attrValuesByAttrId = [];
            
            foreach ($allAttrValues as $value) {
                // 添加属性名称信息
                $value->attr_name = isset($attrMap[$value->attr_id]) ? $attrMap[$value->attr_id]->name : '';
                
                // 建立多种索引方式
                $attrValuesByExactName[$value->name] = $value;
                $attrValuesByLowerName[strtolower(trim($value->name))] = $value;
                
                // 按属性ID分组
                if (!isset($attrValuesByAttrId[$value->attr_id])) {
                    $attrValuesByAttrId[$value->attr_id] = [];
                }
                $attrValuesByAttrId[$value->attr_id][$value->name] = $value;
            }
            
            $this->log('info', '属性和属性值数据加载完成', [
                'attr_count' => count($allAttrs),
                'attr_value_count' => count($allAttrValues)
            ]);
            
            // 从表头中找出所有可能的属性列
            $attrColumns = [];
            foreach ($columnIndexes as $header => $index) {
                foreach ($allAttrs as $attr) {
                    // 如果表头与属性名完全匹配或包含
                    if ($header == $attr->name || strpos($header, $attr->name) !== false) {
                        $attrColumns[$header] = $attr->name;
                        break;
                    }
                }
            }
            
            // 缓存图库ID和图片路径的映射
            $galleryImages = DB::table('t_banhua_image_gallery')
                ->whereNotNull('path')
                ->whereNotNull('id')
                ->pluck('path', 'id')
                ->toArray();
            
            $this->log('info', '获取分类和单位数据', [
                'category_count' => count($categories),
                'unit_count' => count($units),
                'categories' => array_keys($categories),
                'units' => array_keys($units)
            ]);
            
            // 批量插入数据准备
            $batchSize = 100; // 增加批量处理大小到100条
            $skuBatch = [];
            
            // 处理已经读取的行中的数据行（跳过表头行）
            for ($i = 0; $i < count($rowsToCheck); $i++) {
                // 跳过表头行
                if ($i == $headerRowIndex) {
                    continue;
                }
                
                $row = $rowsToCheck[$i];
                
                // 检查是否为空行
                $isEmpty = true;
                foreach ($row as $cell) {
                    if (!empty(trim($cell))) {
                        $isEmpty = false;
                        break;
                    }
                }
                
                if ($isEmpty) {
                    continue;
                }
                
                $totalRows++;
                
                // 处理该行数据，收集批处理数据
                $this->collectCsvRowData($row, $columnIndexes, $categories, $units, $galleryImages, $totalRows, $successCount, $errorRows, $existingSkus, $skuBatch, $attrValuesByExactName, $attrValuesByLowerName, $attrColumns, $allAttrValues);
                
                // 当达到批量大小时执行批量插入
                if (count($skuBatch) >= $batchSize) {
                    try {
                        $this->log('info', '执行批量插入', ['batch_size' => count($skuBatch)]);
                        
                        DB::table('t_banhua_product_skus')->insert($skuBatch);
                        $successCount += count($skuBatch);
                        
                        $this->log('info', '批量插入成功', ['count' => count($skuBatch)]);
                        // 清空批次数据
                        $skuBatch = [];
                    } catch (\Exception $batchEx) {
                        $this->log('error', '批量插入失败', [
                            'error' => $batchEx->getMessage()
                        ]);
                        throw $batchEx;
                    }
                }
            }
            
            // 继续读取剩余的行
            while (($row = fgetcsv($handle)) !== false) {
                // 检查是否为空行
                $isEmpty = true;
                foreach ($row as $cell) {
                    if (!empty(trim($cell))) {
                        $isEmpty = false;
                        break;
                    }
                }
                
                if ($isEmpty) {
                    continue;
                }
                
                $totalRows++;
                
                // 处理该行数据，收集批处理数据
                $this->collectCsvRowData($row, $columnIndexes, $categories, $units, $galleryImages, $totalRows, $successCount, $errorRows, $existingSkus, $skuBatch, $attrValuesByExactName, $attrValuesByLowerName, $attrColumns, $allAttrValues);
                
                // 当达到批量大小时执行批量插入
                if (count($skuBatch) >= $batchSize) {
                    try {
                        $this->log('info', '执行批量插入', ['batch_size' => count($skuBatch)]);
                        
                        DB::table('t_banhua_product_skus')->insert($skuBatch);
                        $successCount += count($skuBatch);
                        
                        $this->log('info', '批量插入成功', ['count' => count($skuBatch)]);
                        // 清空批次数据
                        $skuBatch = [];
                    } catch (\Exception $batchEx) {
                        $this->log('error', '批量插入失败', [
                            'error' => $batchEx->getMessage()
                        ]);
                        throw $batchEx;
                    }
                }
            }
            
            fclose($handle);
            
            // 处理最后一批数据
            if (!empty($skuBatch)) {
                try {
                    $this->log('info', '执行最后一批批量插入', ['batch_size' => count($skuBatch)]);
                    
                    DB::table('t_banhua_product_skus')->insert($skuBatch);
                    $successCount += count($skuBatch);
                    
                    $this->log('info', '最后一批批量插入成功', ['count' => count($skuBatch)]);
                } catch (\Exception $batchEx) {
                    $this->log('error', '最后一批批量插入失败', [
                        'error' => $batchEx->getMessage()
                    ]);
                    throw $batchEx;
                }
            }
            
            // 提交事务
            DB::commit();
            
            // 生成导入报告
            if ($successCount > 0) {
                $message = "导入完成：总共 {$totalRows} 行，成功新增 {$successCount} 行";
            } else {
                $message = "导入完成：未新增任何数据";
            }
            
            if (!empty($existingSkus)) {
                $message .= "，" . count($existingSkus) . " 个SKU已存在（已跳过）";
            }
            
            if (!empty($errorRows)) {
                $message .= "，" . count($errorRows) . " 行导入失败";
                
                // 记录详细错误信息
                $this->log('warning', 'SKU导入存在错误行', [
                    'error_rows' => $errorRows
                ]);
            }
            
            // 记录导入结果
            $this->log('info', 'CSV导入结果', [
                'total' => $totalRows,
                'success' => $successCount,
                'existing' => count($existingSkus),
                'errors' => count($errorRows)
            ]);
            
            // 构建自定义响应数据
            $responseData = [
                'message' => $message,
                'status' => true,
                'refresh' => true,
                'statistics' => [
                    'total' => $totalRows,
                    'success' => $successCount,
                    'existing' => count($existingSkus),
                    'errors' => count($errorRows)
                ]
            ];
            
            // 根据是否有成功导入的数据返回不同类型的响应
            if ($successCount > 0) {
                return response()->json($responseData);
            } else if (count($existingSkus) > 0 && count($errorRows) == 0) {
                // 所有记录都是已存在的，没有错误
                return response()->json($responseData);
            } else if (count($errorRows) > 0) {
                // 有错误的记录
                return response()->json($responseData);
            } else {
                // 没有记录需要导入
                return response()->json($responseData);
            }
            
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            $this->log('error', 'CSV导入失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->response()->error('导入失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 收集CSV行数据，准备批量插入
     * 
     * @param array $row 行数据
     * @param array $columnIndexes 列索引
     * @param array $categories 分类数据
     * @param array $units 单位数据
     * @param array $galleryImages 图库图片数据
     * @param int $totalRows 总行数引用
     * @param int &$successCount 成功数引用
     * @param array &$errorRows 错误行引用
     * @param array &$existingSkus 已存在SKU引用
     * @param array &$skuBatch 收集批量插入的数据
     * @param array $attrValuesByExactName 按名称索引的属性值
     * @param array $attrValuesByLowerName 按小写名称索引的属性值
     * @param array $attrColumns 属性列
     * @param array $allAttrValues 所有属性值
     * @return void
     */
    protected function collectCsvRowData($row, $columnIndexes, $categories, $units, $galleryImages, $totalRows, &$successCount, &$errorRows, &$existingSkus, &$skuBatch, $attrValuesByExactName, $attrValuesByLowerName, $attrColumns, $allAttrValues)
    {
        try {
            // 检查行数据是否完整
            if (count($row) < count($columnIndexes)) {
                $errorRows[] = [
                    'row' => $totalRows,
                    'error' => '数据列数不足',
                    'data' => implode(',', $row)
                ];
                return;
            }
            
            // 获取SKU数据
            $skuCode = trim($row[$columnIndexes['SKU']]);
            $name = trim($row[$columnIndexes['名称']]);
            $pid = trim($row[$columnIndexes['图库ID']]);
            $categoryName = trim($row[$columnIndexes['分类']]);
            $unitName = trim($row[$columnIndexes['单位']]);
            
            // 减少日志输出，只在需要时记录
            if ($totalRows % 100 === 0) {
                $this->log('debug', '处理行数据', [
                    'row' => $totalRows,
                    'sku' => $skuCode
                ]);
            }
            
            // 验证必填字段
            if (empty($skuCode) || empty($name) || empty($categoryName) || empty($unitName)) {
                $errorRows[] = [
                    'row' => $totalRows,
                    'error' => 'SKU、名称、分类或单位不能为空',
                    'data' => implode(',', $row)
                ];
                return;
            }
            
            // 获取分类ID
            $categoryId = $categories[$categoryName] ?? null;
            if (!$categoryId) {
                $errorRows[] = [
                    'row' => $totalRows,
                    'error' => "分类 '{$categoryName}' 不存在",
                    'data' => implode(',', $row)
                ];
                return;
            }
            
            // 获取单位ID
            $unitId = $units[$unitName] ?? null;
            if (!$unitId) {
                $errorRows[] = [
                    'row' => $totalRows,
                    'error' => "单位 '{$unitName}' 不存在",
                    'data' => implode(',', $row)
                ];
                return;
            }
            
            // 获取可选字段
            $jan = isset($columnIndexes['JAN']) ? trim($row[$columnIndexes['JAN']]) : '';
            $price = isset($columnIndexes['价格']) ? trim($row[$columnIndexes['价格']]) : 0;
            
            // 获取图片路径
            $image = '';
            if (isset($columnIndexes['图片']) && !empty($row[$columnIndexes['图片']])) {
                $image = trim($row[$columnIndexes['图片']]);
            } else if (isset($columnIndexes['图片URL']) && !empty($row[$columnIndexes['图片URL']])) {
                $image = trim($row[$columnIndexes['图片URL']]);
            } else if (!empty($pid) && isset($galleryImages[$pid])) {
                // 从图库中获取图片路径
                $image = $galleryImages[$pid];
            }
            
            // 处理价格
            if (!is_numeric($price)) {
                $price = 0;
            }
            
            // 解析属性值
            $attrValueIds = [];
            $attrValuesText = '';
            
            // 从表格属性列中获取属性值
            foreach ($attrColumns as $header => $attrName) {
                if (isset($columnIndexes[$header]) && !empty($row[$columnIndexes[$header]])) {
                    $attrValue = trim($row[$columnIndexes[$header]]);
                    if (!empty($attrValue)) {
                        // 查找属性值ID
                        if (isset($attrValuesByExactName[$attrValue])) {
                            $value = $attrValuesByExactName[$attrValue];
                            $attrValueIds[] = $value->id;
                            $attrValuesText .= "{$attrName}:{$attrValue}, ";
                        } else {
                            $lowerPart = strtolower($attrValue);
                            if (isset($attrValuesByLowerName[$lowerPart])) {
                                $value = $attrValuesByLowerName[$lowerPart];
                                $attrValueIds[] = $value->id;
                                $attrValuesText .= "{$attrName}:{$attrValue}, ";
                            }
                        }
                    }
                }
            }
            
            // 直接处理属性组合列 - 优化检索流程
            $attrComboString = '';
            $attrValueIds = [];
            $attrValuesText = '';
            
            // 首先尝试从属性组合列获取
            if (isset($columnIndexes['属性组合']) && !empty($row[$columnIndexes['属性组合']])) {
                $attrComboString = trim($row[$columnIndexes['属性组合']]);
            } 
            // 如果没有属性组合列，尝试从属性值列获取
            else if (isset($columnIndexes['属性值']) && !empty($row[$columnIndexes['属性值']])) {
                $attrComboString = trim($row[$columnIndexes['属性值']]);
            }
            
            // 如果有属性字符串，处理它
            if (!empty($attrComboString)) {
                // 分割属性字符串
                $attrParts = array_map('trim', explode(',', $attrComboString));
                
                foreach ($attrParts as $part) {
                    $part = trim($part);
                    if (empty($part)) continue;
                    
                    // 使用优化的查找逻辑
                    if (isset($attrValuesByExactName[$part])) {
                        $value = $attrValuesByExactName[$part];
                        $attrValueIds[] = $value->id;
                        $attrValuesText .= "{$value->attr_name}:{$part}, ";
                    } else {
                        $lowerPart = strtolower($part);
                        if (isset($attrValuesByLowerName[$lowerPart])) {
                            $value = $attrValuesByLowerName[$lowerPart];
                            $attrValueIds[] = $value->id;
                            $attrValuesText .= "{$value->attr_name}:{$part}, ";
                        } else if ($totalRows % 100 === 0) {
                            // 只在找不到匹配时记录警告，且减少日志频率
                            $this->log('warning', '未能匹配属性值', [
                                'row' => $totalRows,
                                'sku' => $skuCode,
                                'attr_part' => $part
                            ]);
                        }
                    }
                }
            }
            
            // 减少日志记录频率
            if ($totalRows % 100 === 0) {
                $this->log('debug', '提取的属性值', [
                    'row' => $totalRows,
                    'sku' => $skuCode,
                    'attr_value_ids' => $attrValueIds
                ]);
            }
            
            // 确保属性值ID是唯一的
            $attrValueIds = array_unique($attrValueIds);
            
            // 检查SKU和属性值组合是否已存在
            $skuAttrHash = TecBanhuaProductSkuModel::generateSkuAttrHash($skuCode, $attrValueIds);
            
            if (TecBanhuaProductSkuModel::where('sku_attr_hash', $skuAttrHash)->exists()) {
                $existingSkus[] = $skuCode . "[" . $attrValuesText . "]";
                $this->log('info', 'SKU和属性值组合已存在', [
                    'row' => $totalRows,
                    'sku' => $skuCode,
                    'attr_values' => $attrValuesText,
                    'hash' => $skuAttrHash
                ]);
                return;
            }
            
            // 添加到批量插入数据
            $skuBatch[] = [
                'sku' => $skuCode,
                'name' => $name,
                'pid' => $pid,
                'jan' => $jan ?? null,
                'price' => $price ?? 0,
                'image' => $image ?? '',
                'category_id' => $categoryId,
                'unit_id' => $unitId,
                'attr_value_ids' => json_encode($attrValueIds),
                'sku_attr_hash' => $skuAttrHash,
                'created_at' => now(),
                'updated_at' => now()
            ];
            
            $this->log('info', '收集SKU数据用于批量插入', [
                'row' => $totalRows,
                'sku' => $skuCode,
                'attr_value_ids' => json_encode($attrValueIds),
                'batch_size' => count($skuBatch)
            ]);
            
        } catch (\Exception $e) {
            $errorRows[] = [
                'row' => $totalRows,
                'error' => $e->getMessage(),
                'data' => implode(',', $row)
            ];
            
            $this->log('error', '处理CSV行数据失败', [
                'row' => $totalRows,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 渲染按钮
     *
     * @return string
     */
    public function render()
    {
        // 自定义按钮和上传表单
        $token = csrf_token();
        
        return <<<HTML
<a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline import-sku" style="margin-right:3px">
    <i class="fa fa-upload"></i><span class="d-none d-sm-inline">&nbsp; 导入</span>
</a>

<div class="modal fade" id="import-sku-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导入SKU数据</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    请先下载导入模板，按照模板格式填写数据后再上传。
                    <button type="button" class="btn btn-primary btn-sm download-template" style="margin-left:10px;">
                        <i class="fa fa-download"></i> 下载Excel导入模板
                    </button>
                </div>
                <form id="import-sku-form" action="{$this->getHandleRoute()}" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="_token" value="{$token}">
                    <div class="form-group">
                        <label for="file">选择文件</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".csv,.xlsx,.xls">
                        <small class="form-text text-muted">
                            支持Excel格式(.xlsx, .xls)和CSV格式文件，文件大小不超过10MB。必须包含以下列：SKU、名称、图库ID、分类、单位。
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white btn-mini" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary btn-mini" id="submit-import">导入</button>
            </div>
        </div>
    </div>
</div>

<script>
$(function () {
    // 点击导入按钮显示模态框
    $('.import-sku').on('click', function () {
        $('#import-sku-modal').modal('show');
    });
    
    // 下载模板按钮点击事件
    $('.download-template').on('click', function() {
        // 显示加载中
        Dcat.loading();
        
        // 直接打开新窗口下载，避免使用iframe
        window.open('{$this->getTemplateUrl()}', '_blank');
        
        // 延迟关闭加载提示
        setTimeout(function() {
            Dcat.loading(false);
        }, 1000);
    });
    
    // 提交导入表单
    $('#submit-import').on('click', function () {
        var fileInput = $('#file');
        
        if (fileInput.val() === '') {
            Dcat.error('请选择要导入的文件');
            return;
        }
        
        // 显示加载中
        Dcat.loading();
        
        // 提交表单
        var formData = new FormData($('#import-sku-form')[0]);
        
        $.ajax({
            url: '{$this->getHandleRoute()}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                // 关闭加载中提示
                Dcat.loading(false);
                
                // 输出调试信息到控制台
                console.log('导入响应数据:', response);
                
                // 提取消息内容
                var message = '';
                if (response.message) {
                    message = response.message;
                } else if (response.data && response.data.message) {
                    message = response.data.message;
                }
                
                // 根据消息内容判断类型
                if (message) {
                    // 检查消息中是否包含特定关键词
                    if (message.indexOf('未新增任何数据') >= 0 || message.indexOf('已存在') >= 0) {
                        // 显示警告消息
                        Dcat.warning(message);
                    } else if (message.indexOf('失败') >= 0 || message.indexOf('错误') >= 0) {
                        // 显示错误消息
                        Dcat.error(message);
                    } else if (message.indexOf('成功') >= 0 || message.indexOf('新增') >= 0) {
                        // 显示成功消息
                        Dcat.success(message);
                    } else {
                        // 默认显示为信息消息
                        Dcat.info(message);
                    }
                } else {
                    // 没有消息内容时显示默认提示
                    Dcat.info('导入处理完成');
                }
                
                // 关闭弹窗
                $('#import-sku-modal').modal('hide');
                
                // 延迟刷新页面
                setTimeout(function () {
                    Dcat.reload();
                }, 1500);
            },
            error: function (xhr) {
                Dcat.loading(false);
                
                // 输出错误信息到控制台
                console.error('导入错误:', xhr);
                
                var message = '导入失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                
                Dcat.error(message);
            }
        });
    });
});
</script>
HTML;
    }
    
    /**
     * 获取处理URL
     *
     * @return string
     */
    public function getHandleRoute()
    {
        return admin_url('banhua/sku-import');
    }
    
    /**
     * 获取模板下载URL
     *
     * @return string
     */
    public function getTemplateUrl()
    {
        return admin_url('banhua/sku-import-template');
    }
} 