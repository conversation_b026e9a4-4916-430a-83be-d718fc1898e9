<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaConsumableStockModel;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\TecWarehouseModel;
use App\Models\Banhua\TecBanhuaLocationModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Schema;

class TecBanhuaConsumableStockImportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-upload"></i> 导入';

    /**
     * 处理请求
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Request $request)
    {
        try {
            // 设置更长的执行时间限制
            ini_set('max_execution_time', 300); // 设置为5分钟
            set_time_limit(300); // 同时使用set_time_limit确保PHP执行时间也被延长
            
            // 验证请求
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:10240', // 最大10MB
                'duplicate_strategy' => 'required|in:skip,overwrite,merge',
            ], [
                'file.required' => '请选择要导入的文件',
                'file.file' => '上传的文件无效',
                'file.mimes' => '请上传CSV或Excel格式的文件',
                'file.max' => '文件大小不能超过10MB',
                'duplicate_strategy.required' => '请选择重复记录处理方式',
                'duplicate_strategy.in' => '重复记录处理方式无效',
            ]);
            
            if ($validator->fails()) {
                $errorMsg = $validator->errors()->first();
                Log::warning('消耗品库存导入验证失败', ['error' => $errorMsg]);
                return response()->json([
                    'status' => false,
                    'message' => $errorMsg
                ]);
            }

            // 获取重复记录处理策略
            $duplicateStrategy = $request->input('duplicate_strategy', 'skip');
            Log::info('消耗品库存导入 - 重复记录处理策略', ['strategy' => $duplicateStrategy]);
            
            // 验证文件是否存在
            if (!$request->hasFile('file')) {
                return response()->json(['status' => false, 'message' => '请选择要导入的文件']);
            }

            $file = $request->file('file');
            $extension = strtolower($file->getClientOriginalExtension());
            $path = $file->getRealPath();
            
            // 记录导入开始
            Log::info('开始导入消耗品库存数据', [
                'file' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'extension' => $extension,
                'path' => $path
            ]);
            
            // 读取Excel/CSV文件
            try {
                if (in_array($extension, ['xlsx', 'xls'])) {
                    // 根据扩展名选择不同的读取器
                    if ($extension === 'xlsx') {
                        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                    } else {
                        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xls();
                    }
                    
                    // 只读取数据，不读取格式
                    $reader->setReadDataOnly(true);
                    
                    // 加载Excel文件
                    $spreadsheet = $reader->load($path);
                    
                    // 获取所有工作表名称
                    $sheetNames = $spreadsheet->getSheetNames();
                    Log::info('检测到Excel工作表列表', [
                        'sheet_names' => $sheetNames,
                        'sheet_count' => count($sheetNames)
                    ]);
                    
                    // 尝试按照以下顺序选择工作表：
                    // 1. 名为"导入模板"的工作表
                    // 2. 第一个工作表（索引0）
                    $worksheet = null;
                    if (in_array('导入模板', $sheetNames)) {
                        $worksheet = $spreadsheet->getSheetByName('导入模板');
                        Log::info('找到并选择了"导入模板"工作表');
                    } else {
                        $worksheet = $spreadsheet->getSheet(0); // 默认第一个工作表
                        Log::info('未找到"导入模板"工作表，使用第一个工作表', [
                            'sheet_name' => $worksheet->getTitle()
                        ]);
                    }
                    
                    // 获取工作表的行数和列数
                    $highestRow = $worksheet->getHighestRow();
                    $highestColumn = $worksheet->getHighestColumn();
                    $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
                    
                    Log::info('Excel文件基本信息', [
                        'sheet_name' => $worksheet->getTitle(),
                        'row_count' => $highestRow,
                        'column_count' => $highestColumnIndex
                    ]);
                    
                    // 扫描前15行，找到包含"SKU编码"的那一行作为表头
                    $headerRow = 8; // 默认表头在第8行
                    $foundHeader = false;
                    
                    for ($row = 1; $row <= min(15, $highestRow); $row++) {
                        // 取出前10列的内容
                        $rowContents = [];
                        for ($col = 1; $col <= min(10, $highestColumnIndex); $col++) {
                            $cellValue = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                            if ($cellValue !== null) {
                                $rowContents[$col] = trim((string)$cellValue);
                            }
                        }
                        
                        // 记录每行内容便于调试
                        Log::info("扫描第{$row}行内容", ['row_contents' => $rowContents]);
                        
                        // 检查是否是真正的表头行（多个单元格有值，且不是说明文本）
                        $filledCells = count(array_filter($rowContents, function($value) {
                            return !empty($value) && !preg_match('/^\d+\.\s/', $value); // 排除以"数字."开头的说明文本
                        }));
                        
                        // 检查是否包含"SKU编码"单元格或类似内容，且不是说明文本
                        $hasSkuColumn = false;
                        foreach ($rowContents as $value) {
                            // 如果是单独的"SKU编码"字段（不是说明文本的一部分）
                            if ($value === 'SKU编码' || (strpos($value, 'SKU编码') !== false && !preg_match('/^\d+\.\s/', $value))) {
                                $hasSkuColumn = true;
                                break;
                            }
                        }
                        
                        // 如果找到了看起来像表头的行
                        if ($filledCells >= 3 && $hasSkuColumn) {
                            $headerRow = $row;
                            $foundHeader = true;
                            Log::info("找到表头行，位于第{$row}行", ['header_content' => $rowContents]);
                            break;
                        }
                    }
                    
                    if (!$foundHeader) {
                        Log::warning('未找到真正的表头行，使用默认的第8行');
                        // 强制使用第8行作为表头
                        $headerRow = 8;
                        
                        // 确认第8行内容
                        $rowContents = [];
                        for ($col = 1; $col <= $highestColumnIndex; $col++) {
                            $cellValue = $worksheet->getCellByColumnAndRow($col, $headerRow)->getValue();
                            if ($cellValue !== null) {
                                $rowContents[$col] = trim((string)$cellValue);
                            }
                        }
                        Log::info("强制使用第8行作为表头", ['header_content' => $rowContents]);
                    }
                    
                    // 读取表头行
                    $headers = [];
                    for ($col = 1; $col <= $highestColumnIndex; $col++) {
                        $value = $worksheet->getCellByColumnAndRow($col, $headerRow)->getValue();
                        if ($value !== null) {
                            $headers[] = trim((string)$value);
                        }
                    }
                    
                    Log::info('Excel表头行信息', [
                        'header_row' => $headerRow,
                        'headers' => $headers,
                        'header_count' => count($headers)
                    ]);
                    
                    // 从表头的下一行开始读取数据
                    $startDataRow = $headerRow + 1;
                    Log::info("将从第{$startDataRow}行开始读取数据");
                    
                    // 读取所有数据行
                    $rows = [];
                    for ($row = $startDataRow; $row <= $highestRow; $row++) {
                        // 检查是否为空行
                        $isEmpty = true;
                        $rowData = [];
                        
                        for ($col = 1; $col <= $highestColumnIndex; $col++) {
                            $value = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                            $rowData[] = $value;
                            if ($value !== null && $value !== '') {
                                $isEmpty = false;
                            }
                        }
                        
                        if (!$isEmpty) {
                            $rows[] = $rowData;
                        }
                    }
                } else {
                    // 处理CSV文件
                    $allRows = array_map('str_getcsv', file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES));
                    
                    // 第一行作为表头
                    $headers = $allRows[0];
                    // 剩余行作为数据
                    $rows = array_slice($allRows, 1);
                    
                    Log::info('CSV文件信息', [
                        'header' => $headers,
                        'data_rows' => count($rows)
                    ]);
                }
                
                // 检查数据是否为空
                if (empty($rows)) {
                    return response()->json(['status' => false, 'message' => '文件中没有有效数据']);
                }
                
            } catch (\Exception $e) {
                Log::error('读取导入文件失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                return response()->json(['status' => false, 'message' => '读取文件失败: ' . $e->getMessage()]);
            }
            
            // 开始处理数据导入
            Log::info('开始处理消耗品库存数据导入', [
                'headers' => $headers,
                'data_rows' => count($rows)
            ]);
            
            // 查找必要字段的索引
            $skuIndex = array_search('SKU编码', $headers);
            $attrIndex = array_search('属性组合', $headers);
            $warehouseIndex = array_search('仓库', $headers);
            $locationIndex = array_search('库位', $headers);
            $stockIndex = array_search('库存数量', $headers);
            
            // 检查是否找到必要字段
            if ($skuIndex === false || $warehouseIndex === false || $locationIndex === false || $stockIndex === false) {
                return response()->json([
                    'status' => false,
                    'message' => '导入文件缺少必要字段：SKU编码、仓库、库位、库存数量'
                ]);
            }
            
            // 预加载所有SKU
            $skuMap = [];
            $skus = TecBanhuaProductSkuModel::all(['id', 'sku', 'name', 'attr_value_ids'])->keyBy('sku');
            
            // 预加载所有仓库
            $warehouseMap = [];
            $warehouses = TecWarehouseModel::all(['id', 'name'])->keyBy('name');
            
            // 预加载所有库位
            $locationMap = [];
            $locations = TecBanhuaLocationModel::with('warehouse')
                ->get(['id', 'area_number', 'shelf_number', 'level_number', 'warehouse_id'])
                ->each(function ($location) use (&$locationMap) {
                    $locationName = $location->area_number . '区' . $location->shelf_number . '架' . $location->level_number . '层';
                    $warehouseId = $location->warehouse_id;
                    
                    if (!isset($locationMap[$warehouseId])) {
                        $locationMap[$warehouseId] = [];
                    }
                    
                    $locationMap[$warehouseId][$locationName] = $location->id;
                });
            
            // 记录库位映射信息
            Log::info('库位映射信息', [
                'warehouse_count' => count($locationMap),
                'location_sample' => array_slice($locationMap, 0, 1, true)
            ]);
            
            // 预加载所有属性值映射（ID => 详情）
            $attrValueMap = [];
            $attrs = \App\Models\Banhua\TecBanhuaAttrModel::with('values')->get();
            foreach ($attrs as $attr) {
                foreach ($attr->values as $value) {
                    $attrValueMap[$value->id] = [
                        'attr_id' => $attr->id,
                        'attr_name' => $attr->name,
                        'value_name' => $value->name
                    ];
                }
            }
            
            // 记录属性值映射信息
            Log::info('属性值映射信息', [
                'attr_value_count' => count($attrValueMap),
                'attr_value_sample' => array_slice($attrValueMap, 0, 5, true)
            ]);
            
            // 处理每行数据
            $importedCount = 0;
            $skippedCount = 0;
            $errorCount = 0;
            $errors = [];
            
            DB::beginTransaction();
            
            try {
                foreach ($rows as $index => $row) {
                    $rowNum = $index + 1;
                    
                    // 获取当前行的字段值
                    $skuCode = $row[$skuIndex] ?? null;
                    $attrText = $attrIndex !== false ? ($row[$attrIndex] ?? null) : null;
                    $warehouseName = $row[$warehouseIndex] ?? null;
                    $locationName = $row[$locationIndex] ?? null;
                    $stockValue = $row[$stockIndex] ?? null;
                    
                    // 记录当前处理的行
                    Log::info("处理第{$rowNum}行数据", [
                        'sku_code' => $skuCode,
                        'attr_text' => $attrText,
                        'warehouse' => $warehouseName,
                        'location' => $locationName,
                        'stock' => $stockValue
                    ]);
                    
                    // 检查必要字段是否有值
                    if (empty($skuCode) || empty($warehouseName) || empty($locationName) || $stockValue === null) {
                        $skippedCount++;
                        $errors[] = "第{$rowNum}行：缺少必要字段值";
                        Log::warning("第{$rowNum}行：缺少必要字段值", [
                            'sku_code' => $skuCode,
                            'warehouse' => $warehouseName,
                            'location' => $locationName,
                            'stock' => $stockValue
                        ]);
                        continue;
                    }
                    
                    // 转换库存数量为整数
                    $stockValue = intval($stockValue);
                    if ($stockValue < 0) {
                        $skippedCount++;
                        $errors[] = "第{$rowNum}行：库存数量不能为负数";
                        Log::warning("第{$rowNum}行：库存数量不能为负数", ['stock' => $stockValue]);
                        continue;
                    }
                    
                    // 查找SKU
                    $sku = null;
                    $skuId = null;
                    
                    if (!isset($skuMap[$skuCode])) {
                        $sku = $skus->get($skuCode);
                        if ($sku) {
                            $skuId = $sku->id;
                            $skuMap[$skuCode] = $skuId;
                            Log::info("找到SKU", ['sku_code' => $skuCode, 'sku_id' => $skuId]);
                        } else {
                            Log::warning("未找到SKU", ['sku_code' => $skuCode]);
                        }
                    } else {
                        $skuId = $skuMap[$skuCode];
                        Log::info("从缓存中获取SKU", ['sku_code' => $skuCode, 'sku_id' => $skuId]);
                    }
                    
                    // 如果没找到SKU，尝试通过属性组合确定
                    if (!$skuId && !empty($attrText)) {
                        Log::info("尝试通过属性组合确定SKU", ['sku_code' => $skuCode, 'attr_text' => $attrText]);
                        
                        // 获取具有相同SKU编码的所有SKU
                        $candidateSkus = TecBanhuaProductSkuModel::where('sku', $skuCode)->get();
                        
                        Log::info("找到候选SKU", [
                            'sku_code' => $skuCode, 
                            'candidate_count' => $candidateSkus->count(),
                            'candidates' => $candidateSkus->map(function($item) {
                                return [
                                    'id' => $item->id,
                                    'attr_value_ids' => $item->attr_value_ids
                                ];
                            })->toArray()
                        ]);
                        
                        if ($candidateSkus->count() > 0) {
                            // 解析属性文本 - 增强版，支持多种格式
                            // 处理可能的格式：
                            // 1. "30*40, 5CM" 
                            // 2. "30×40, 5CM"
                            // 3. "30*40,5CM" (无空格)
                            // 4. "30*40 5CM" (空格分隔)
                            
                            // 首先尝试按逗号分隔
                            $attrValues = array_map('trim', explode(',', $attrText));
                            
                            // 如果只有一个元素，可能是使用空格分隔的
                            if (count($attrValues) === 1 && strpos($attrValues[0], ' ') !== false) {
                                $attrValues = array_map('trim', explode(' ', $attrValues[0]));
                            }
                            
                            Log::info("解析属性文本", ['attr_text' => $attrText, 'attr_values' => $attrValues]);
                            
                            // 规范化属性值，处理常见的变体
                            $normalizedAttrValues = [];
                            foreach ($attrValues as $value) {
                                $normalized = $value;
                                
                                // 替换 × 为 *
                                $normalized = str_replace('×', '*', $normalized);
                                
                                // 处理尺寸格式，确保数字和单位之间有空格
                                // 例如：将 "5CM" 转换为 "5 CM"
                                $normalized = preg_replace('/(\d+)([A-Za-z]+)/', '$1 $2', $normalized);
                                
                                $normalizedAttrValues[] = $normalized;
                            }
                            
                            Log::info("规范化后的属性值", ['original' => $attrValues, 'normalized' => $normalizedAttrValues]);
                            
                            // 查找属性值ID - 使用更灵活的匹配
                            $attrValueIds = [];
                            $unmatchedValues = [];
                            
                            foreach ($normalizedAttrValues as $value) {
                                $lowerValue = strtolower($value);
                                $found = false;
                                
                                // 直接匹配
                                if (isset($attrValueMap[$lowerValue])) {
                                    $attrValueIds[] = $attrValueMap[$lowerValue]['id'];
                                    $found = true;
                                    Log::info("直接匹配到属性值", [
                                        'value' => $value,
                                        'id' => $attrValueMap[$lowerValue]['id']
                                    ]);
                                } else {
                                    // 尝试部分匹配
                                    foreach ($attrValueMap as $key => $attrValue) {
                                        // 检查是否是部分匹配
                                        if (strpos($key, $lowerValue) !== false || strpos($lowerValue, $key) !== false) {
                                            $attrValueIds[] = $attrValue['id'];
                                            $found = true;
                                            Log::info("部分匹配到属性值", [
                                                'value' => $value,
                                                'matched_key' => $key,
                                                'id' => $attrValue['id']
                                            ]);
                                            break;
                                        }
                                    }
                                }
                                
                                if (!$found) {
                                    $unmatchedValues[] = $value;
                                    Log::warning("未找到匹配的属性值", ['value' => $value]);
                                }
                            }
                            
                            // 如果有未匹配的属性值，尝试直接使用原始属性文本进行匹配
                            if (!empty($unmatchedValues)) {
                                // 尝试将整个属性文本作为一个整体进行匹配
                                $lowerFullText = strtolower($attrText);
                                foreach ($attrValueMap as $key => $attrValue) {
                                    if (strpos($lowerFullText, $key) !== false || strpos($key, $lowerFullText) !== false) {
                                        $attrValueIds[] = $attrValue['id'];
                                        Log::info("使用完整文本匹配到属性值", [
                                            'full_text' => $attrText,
                                            'matched_key' => $key,
                                            'id' => $attrValue['id']
                                        ]);
                                    }
                                }
                            }
                            
                            // 对属性值ID进行排序，以便进行准确比较
                            sort($attrValueIds);
                            Log::info("属性值ID排序后", ['attr_value_ids' => $attrValueIds]);
                            
                            // 查找匹配的SKU
                            $matchFound = false;
                            foreach ($candidateSkus as $candidate) {
                                $candidateAttrIds = $candidate->attr_value_ids;
                                Log::info("比较候选SKU属性", [
                                    'candidate_id' => $candidate->id,
                                    'candidate_attr_ids' => $candidateAttrIds,
                                    'input_attr_ids' => $attrValueIds
                                ]);
                                
                                if (is_array($candidateAttrIds)) {
                                    sort($candidateAttrIds);
                                    
                                    // 精确匹配
                                    if ($attrValueIds == $candidateAttrIds) {
                                        $skuId = $candidate->id;
                                        $skuMap[$skuCode] = $skuId;
                                        $matchFound = true;
                                        Log::info("找到精确匹配的SKU", ['sku_code' => $skuCode, 'sku_id' => $skuId]);
                                        break;
                                    } 
                                    // 部分匹配 - 如果输入的属性值是候选SKU属性值的子集
                                    else if (!empty($attrValueIds) && count(array_intersect($attrValueIds, $candidateAttrIds)) == count($attrValueIds)) {
                                        $skuId = $candidate->id;
                                        $skuMap[$skuCode] = $skuId;
                                        $matchFound = true;
                                        Log::info("找到部分匹配的SKU", [
                                            'sku_code' => $skuCode, 
                                            'sku_id' => $skuId,
                                            'input_attr_ids' => $attrValueIds,
                                            'candidate_attr_ids' => $candidateAttrIds,
                                            'matched_count' => count(array_intersect($attrValueIds, $candidateAttrIds))
                                        ]);
                                        break;
                                    } else {
                                        Log::info("属性值不匹配", [
                                            'candidate_attr_ids' => $candidateAttrIds,
                                            'input_attr_ids' => $attrValueIds
                                        ]);
                                    }
                                } else {
                                    Log::warning("候选SKU的属性值ID不是数组", [
                                        'candidate_id' => $candidate->id,
                                        'attr_value_ids' => $candidateAttrIds
                                    ]);
                                }
                            }
                            
                            // 如果没有找到匹配，但只有一个候选SKU，则使用它
                            if (!$matchFound && $candidateSkus->count() === 1 && empty($attrValueIds)) {
                                $skuId = $candidateSkus->first()->id;
                                $skuMap[$skuCode] = $skuId;
                                Log::info("使用唯一候选SKU", ['sku_code' => $skuCode, 'sku_id' => $skuId]);
                            }
                        }
                    }
                    
                    // 如果还是找不到SKU，记录错误并跳过
                    if (!$skuId) {
                        $skippedCount++;
                        $errors[] = "第{$rowNum}行：找不到对应的SKU - {$skuCode}" . (!empty($attrText) ? " ({$attrText})" : "");
                        Log::warning("第{$rowNum}行：找不到对应的SKU", [
                            'sku_code' => $skuCode,
                            'attr_text' => $attrText
                        ]);
                        continue;
                    }
                    
                    // 查找仓库
                    $warehouse = null;
                    $warehouseId = null;
                    
                    if (!isset($warehouseMap[$warehouseName])) {
                        $warehouse = $warehouses->get($warehouseName);
                        if ($warehouse) {
                            $warehouseId = $warehouse->id;
                            $warehouseMap[$warehouseName] = $warehouseId;
                            Log::info("找到仓库", ['warehouse_name' => $warehouseName, 'warehouse_id' => $warehouseId]);
                        } else {
                            // 尝试模糊匹配仓库名称
                            $lowerWarehouseName = strtolower($warehouseName);
                            foreach ($warehouses as $name => $wh) {
                                $lowerName = strtolower($name);
                                if (strpos($lowerName, $lowerWarehouseName) !== false || 
                                    strpos($lowerWarehouseName, $lowerName) !== false) {
                                    $warehouseId = $wh->id;
                                    $warehouseMap[$warehouseName] = $warehouseId;
                                    Log::info("通过模糊匹配找到仓库", [
                                        'input_name' => $warehouseName,
                                        'matched_name' => $name,
                                        'warehouse_id' => $warehouseId
                                    ]);
                                    break;
                                }
                            }
                            
                            // 如果还是找不到仓库，但系统中只有一个仓库，则使用它
                            if (!$warehouseId && $warehouses->count() === 1) {
                                $warehouse = $warehouses->first();
                                $warehouseId = $warehouse->id;
                                $warehouseMap[$warehouseName] = $warehouseId;
                                Log::info("使用系统唯一仓库", [
                                    'input_name' => $warehouseName,
                                    'warehouse_name' => $warehouse->name,
                                    'warehouse_id' => $warehouseId
                                ]);
                            }
                            
                            if (!$warehouseId) {
                                Log::warning("未找到仓库", [
                                    'warehouse_name' => $warehouseName,
                                    'available_warehouses' => $warehouses->keys()->toArray()
                                ]);
                            }
                        }
                    } else {
                        $warehouseId = $warehouseMap[$warehouseName];
                        Log::info("从缓存中获取仓库", ['warehouse_name' => $warehouseName, 'warehouse_id' => $warehouseId]);
                    }
                    
                    // 如果找不到仓库，记录错误并跳过
                    if (!$warehouseId) {
                        $skippedCount++;
                        $errors[] = "第{$rowNum}行：找不到仓库 - {$warehouseName}";
                        Log::warning("第{$rowNum}行：找不到仓库", ['warehouse_name' => $warehouseName]);
                        continue;
                    }
                    
                    // 查找库位
                    $locationId = null;
                    
                    if (isset($locationMap[$warehouseId][$locationName])) {
                        $locationId = $locationMap[$warehouseId][$locationName];
                        Log::info("找到库位", [
                            'warehouse_id' => $warehouseId,
                            'location_name' => $locationName,
                            'location_id' => $locationId
                        ]);
                    } else {
                        // 尝试更灵活的库位匹配
                        $normalizedLocationName = $locationName;
                        
                        // 替换可能的特殊字符
                        $normalizedLocationName = str_replace('*', '层', $normalizedLocationName);
                        
                        // 检查是否包含"区"、"架"、"层"
                        if (strpos($normalizedLocationName, '区') === false) {
                            $normalizedLocationName = preg_replace('/^(.+?)(?=[架层]|$)/', '$1区', $normalizedLocationName);
                        }
                        if (strpos($normalizedLocationName, '架') === false) {
                            $normalizedLocationName = preg_replace('/区(.+?)(?=[层]|$)/', '区$1架', $normalizedLocationName);
                        }
                        if (strpos($normalizedLocationName, '层') === false) {
                            $normalizedLocationName .= '层';
                        }
                        
                        Log::info("尝试规范化库位名称", [
                            'original' => $locationName,
                            'normalized' => $normalizedLocationName
                        ]);
                        
                        // 使用规范化后的名称再次尝试查找
                        if (isset($locationMap[$warehouseId][$normalizedLocationName])) {
                            $locationId = $locationMap[$warehouseId][$normalizedLocationName];
                            Log::info("使用规范化名称找到库位", [
                                'warehouse_id' => $warehouseId,
                                'original_name' => $locationName,
                                'normalized_name' => $normalizedLocationName,
                                'location_id' => $locationId
                            ]);
                        } else {
                            // 尝试模糊匹配
                            $availableLocations = isset($locationMap[$warehouseId]) ? $locationMap[$warehouseId] : [];
                            foreach ($availableLocations as $locName => $locId) {
                                // 检查是否是部分匹配
                                if (strpos($locName, $locationName) !== false || 
                                    strpos($locationName, $locName) !== false ||
                                    strpos($locName, $normalizedLocationName) !== false || 
                                    strpos($normalizedLocationName, $locName) !== false) {
                                    $locationId = $locId;
                                    Log::info("通过模糊匹配找到库位", [
                                        'warehouse_id' => $warehouseId,
                                        'input_name' => $locationName,
                                        'normalized_name' => $normalizedLocationName,
                                        'matched_name' => $locName,
                                        'location_id' => $locationId
                                    ]);
                                    break;
                                }
                            }
                            
                            // 如果还是找不到，但该仓库只有一个库位，则使用它
                            if (!$locationId && count($availableLocations) === 1) {
                                $locationId = reset($availableLocations);
                                $locName = key($availableLocations);
                                Log::info("使用仓库唯一库位", [
                                    'warehouse_id' => $warehouseId,
                                    'warehouse_name' => $warehouseName,
                                    'location_name' => $locName,
                                    'location_id' => $locationId
                                ]);
                            }
                            
                            if (!$locationId) {
                                Log::warning("未找到库位", [
                                    'warehouse_id' => $warehouseId,
                                    'location_name' => $locationName,
                                    'normalized_name' => $normalizedLocationName,
                                    'available_locations' => array_keys($availableLocations)
                                ]);
                            }
                        }
                    }
                    
                    // 如果找不到库位，记录错误并跳过
                    if (!$locationId) {
                        $skippedCount++;
                        $errors[] = "第{$rowNum}行：找不到库位 - {$locationName} (在仓库 {$warehouseName} 中)";
                        Log::warning("第{$rowNum}行：找不到库位", [
                            'warehouse_name' => $warehouseName,
                            'location_name' => $locationName
                        ]);
                        continue;
                    }
                    
                    // 标准化属性值ID
                    $attrValueIds = $this->normalizeAttrValueIds($attrText, $attrValueMap);

                    // 检查是否已存在相同的记录（唯一性条件使用attr_value_ids）
                    $existingRecord = TecBanhuaConsumableStockModel::where([
                        'sku_id' => $skuId,
                        'warehouse_id' => $warehouseId,
                        'location_id' => $locationId,
                        'attr_value_ids' => $attrValueIds
                    ])->first();
                    
                    // 根据重复策略处理数据
                    if ($existingRecord) {
                        switch ($duplicateStrategy) {
                            case 'skip':
                                $skippedCount++;
                                Log::info("跳过重复记录", [
                                    'row' => $rowNum,
                                    'sku_code' => $skuCode,
                                    'attr_text' => $attrText,
                                    'warehouse' => $warehouseName,
                                    'location' => $locationName,
                                    'existing_record_id' => $existingRecord->id
                                ]);
                                continue 2; // 跳过当前行，继续下一行
                            
                            case 'overwrite':
                                $existingRecord->stock = $stockValue;
                                $existingRecord->save();
                                $importedCount++;
                                Log::info("覆盖现有记录", [
                                    'row' => $rowNum,
                                    'sku_code' => $skuCode,
                                    'attr_text' => $attrText,
                                    'warehouse' => $warehouseName,
                                    'location' => $locationName,
                                    'old_stock' => $existingRecord->stock,
                                    'new_stock' => $stockValue
                                ]);
                                break;
                            
                            case 'merge':
                                $existingRecord->stock += $stockValue;
                                $existingRecord->save();
                                $importedCount++;
                                Log::info("合并记录数据（库存累加）", [
                                    'row' => $rowNum,
                                    'sku_code' => $skuCode,
                                    'attr_text' => $attrText,
                                    'warehouse' => $warehouseName,
                                    'location' => $locationName,
                                    'old_stock' => $existingRecord->stock - $stockValue,
                                    'added_stock' => $stockValue,
                                    'new_stock' => $existingRecord->stock
                                ]);
                                break;
                        }
                    } else {
                        // 创建新记录
                        $newRecord = new TecBanhuaConsumableStockModel();
                        $newRecord->sku_id = $skuId;
                        $newRecord->warehouse_id = $warehouseId;
                        $newRecord->location_id = $locationId;
                        $newRecord->stock = $stockValue;
                        
                        // 添加属性组合信息（如果模型支持）
                        if (Schema::hasColumn('t_banhua_consumable_stocks', 'attr_value_ids')) {
                            $newRecord->attr_value_ids = !empty($attrValueIds) ? 
                                (is_array($attrValueIds) ? implode(',', $attrValueIds) : $attrValueIds) : 
                                null;
                        }
                        
                        // 保存记录
                        $newRecord->save();
                        $importedCount++;
                        
                        Log::info("创建新记录", [
                            'row' => $rowNum,
                            'sku_code' => $skuCode,
                            'attr_text' => $attrText,
                            'warehouse' => $warehouseName,
                            'location' => $locationName,
                            'stock' => $stockValue,
                            'record_id' => $newRecord->id
                        ]);
                    }
                }
                
                // 提交事务
                DB::commit();
                
                // 清除缓存
                \Cache::forget('banhua_consumable_stock_count');
                \Cache::forget('banhua_consumable_stock_total');
                \Cache::forget('banhua_consumable_stock_warehouse_stats');
                
                // 记录导入结果
                Log::info('消耗品库存导入完成', [
                    'total_rows' => count($rows),
                    'imported' => $importedCount,
                    'skipped' => $skippedCount,
                    'errors' => $errorCount
                ]);
                
                // 返回结果
                $message = "导入成功：共处理 " . count($rows) . " 条记录，成功导入 {$importedCount} 条，跳过 {$skippedCount} 条";
                if (!empty($errors)) {
                    $message .= "。错误明细：" . implode('; ', array_slice($errors, 0, 5));
                    if (count($errors) > 5) {
                        $message .= "... 等" . count($errors) . "个错误";
                    }
                }
                
                return response()->json([
                    'status' => true,
                    'message' => $message,
                    'data' => [
                        'total' => count($rows),
                        'imported' => $importedCount,
                        'skipped' => $skippedCount,
                        'errors' => $errors
                    ]
                ]);
                
            } catch (\Exception $e) {
                // 回滚事务
                DB::rollBack();
                
                // 记录错误
                Log::error('消耗品库存导入失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                return response()->json([
                    'status' => false,
                    'message' => '导入失败: ' . $e->getMessage()
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('消耗品库存导入处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '导入处理失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 渲染
     *
     * @return string
     */
    public function render()
    {
        // 获取路由和token
        $token = csrf_token();
        $handleRoute = $this->getHandleRoute();
        $templateRoute = $this->getTemplateRoute();
        
        // 按钮HTML
        $html = <<<HTML
<a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline import-consumable-stock" style="margin-right:3px">
    <i class="fa fa-upload"></i><span class="d-none d-sm-inline">&nbsp; 导入</span>
</a>
        
<div class="modal fade" id="import-consumable-stock-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                <h5 class="modal-title">导入消耗品库存数据</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                <div class="alert alert-info">
                    请先下载导入模板，按照模板格式填写数据后再上传。
                    <button type="button" class="btn btn-primary btn-sm download-template" style="margin-left:10px;">
                        <i class="fa fa-download"></i> 下载Excel导入模板
                    </button>
                </div>
                <form id="import-consumable-stock-form" action="{$handleRoute}" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="_token" value="{$token}">
                            <div class="form-group">
                        <label for="file">选择文件</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".csv,.xlsx,.xls">
                        <small class="form-text text-muted">
                            支持Excel格式(.xlsx, .xls)和CSV格式文件，文件大小不超过10MB。必须包含以下列：SKU编码、仓库、库位、库存数量。
                        </small>
                            </div>
                            <div class="form-group">
                        <label for="duplicate_strategy">重复记录处理方式</label>
                        <select class="form-control" id="duplicate_strategy" name="duplicate_strategy">
                            <option value="skip" selected>跳过重复记录（不修改已有数据）</option>
                            <option value="overwrite">覆盖现有记录（用新数据替换）</option>
                            <option value="merge">合并数据（库存累加，其他非空字段更新）</option>
                                </select>
                        <small class="form-text text-muted">
                            选择当导入的SKU在同一仓库同一库位已存在记录时的处理方式
                        </small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                <button type="button" class="btn btn-white btn-mini" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary btn-mini" id="submit-import-consumable-stock">导入</button>
                    </div>
                </div>
            </div>
        </div>
HTML;

        // 添加JavaScript
        \Dcat\Admin\Admin::script(<<<SCRIPT
$(function () {
    // 定义路由变量
    var handleRoute = '{$handleRoute}';
    var templateRoute = '{$templateRoute}';
    
    // 点击导入按钮显示模态框
    $('.import-consumable-stock').on('click', function () {
        $('#import-consumable-stock-modal').modal('show');
            });
            
    // 下载模板按钮点击事件
    $('.download-template').on('click', function() {
        Dcat.loading();
        // 直接打开新窗口下载，避免使用iframe
        window.open(templateRoute, '_blank');
        setTimeout(function() {
            Dcat.loading(false);
        }, 1000);
    });
    
    // 提交导入表单
    $('#submit-import-consumable-stock').on('click', function () {
        var fileInput = $('#file');
        if (fileInput.val() === '') {
            Dcat.error('请选择要导入的文件');
                    return;
                }
        Dcat.loading();
        var formData = new FormData($('#import-consumable-stock-form')[0]);
                $.ajax({
            url: handleRoute,
                    type: 'POST',
                    data: formData,
                    processData: false,
            contentType: false,
            success: function (response) {
                Dcat.loading(false);
                
                // 构建详细的结果消息
                var message = response.message || '';
                var detailMessage = '';
                
                // 如果有详细统计信息，显示在消息中
                if (response.stats) {
                    var stats = response.stats;
                    if (stats.skipped > 0) {
                        detailMessage += '<br>- 跳过了 ' + stats.skipped + ' 条重复记录';
                            }
                    if (stats.overwritten > 0) {
                        detailMessage += '<br>- 覆盖了 ' + stats.overwritten + ' 条现有记录';
                    }
                    if (stats.merged > 0) {
                        detailMessage += '<br>- 合并了 ' + stats.merged + ' 条记录数据（库存累加）';
                    }
                }
                
                // 如果有错误信息，添加到消息中
                if (response.errors && response.errors.length > 0) {
                    detailMessage += '<br><br><strong>错误详情：</strong>';
                    // 最多显示5条错误信息
                    var maxErrors = Math.min(5, response.errors.length);
                    for (var i = 0; i < maxErrors; i++) {
                        detailMessage += '<br>- ' + response.errors[i];
                    }
                    if (response.errors.length > 5) {
                        detailMessage += '<br>... 等共 ' + response.errors.length + ' 条错误';
                    }
                }
                
                // 根据操作结果显示不同颜色的消息
                if (response.status === true) {
                    // 根据主要操作类型决定消息颜色
                    if (response.stats) {
                        var stats = response.stats;
                        if (stats.failed > 0) {
                            // 有失败记录，显示红色错误消息
                            Dcat.error(message + detailMessage);
                        } else if (stats.success > 0 && stats.skipped === 0 && stats.overwritten === 0 && stats.merged === 0) {
                            // 只有新增记录，显示绿色成功消息
                            Dcat.success(message + detailMessage);
                        } else if (stats.skipped > 0 && stats.success === 0 && stats.overwritten === 0 && stats.merged === 0) {
                            // 只有跳过记录，显示蓝色信息消息
                            Dcat.info(message + detailMessage);
                        } else if (stats.overwritten > 0 && stats.success === 0 && stats.skipped === 0 && stats.merged === 0) {
                            // 只有覆盖记录，显示紫色主要消息
                            Dcat.info(message + detailMessage);
                        } else if (stats.merged > 0 && stats.success === 0 && stats.skipped === 0 && stats.overwritten === 0) {
                            // 只有合并记录，显示黄色警告消息
                            Dcat.warning(message + detailMessage);
                            } else {
                            // 混合操作，根据优先级决定颜色
                            if (stats.success > 0) {
                                // 有新增记录，优先显示绿色成功消息
                                Dcat.success(message + detailMessage);
                            } else if (stats.overwritten > 0) {
                                // 有覆盖记录，显示紫色主要消息
                                Dcat.info(message + detailMessage);
                            } else if (stats.merged > 0) {
                                // 有合并记录，显示黄色警告消息
                                Dcat.warning(message + detailMessage);
                            } else {
                                // 默认显示蓝色信息消息
                                Dcat.info(message + detailMessage);
                            }
                        }
                        } else {
                        // 没有详细统计信息，默认显示绿色成功消息
                        Dcat.success(message + detailMessage);
                    }
                } else {
                    // 导入失败，显示红色错误消息
                    Dcat.error(message + detailMessage);
                }
                
                $('#import-consumable-stock-modal').modal('hide');
                setTimeout(function () {
                    Dcat.reload();
                }, 2000);
                    },
            error: function (xhr) {
                Dcat.loading(false);
                var message = '导入失败';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                Dcat.error(message);
                    }
                });
            });
        });
SCRIPT);

        return $html;
    }

    /**
     * 获取处理路由
     *
     * @return string
     */
    public function getHandleRoute()
    {
        return admin_url('banhua/consumable-stock-import');
    }
    
    /**
     * 获取模板下载路由
     *
     * @return string
     */
    public function getTemplateRoute()
    {
        return admin_url('banhua/consumable-stock-import-template');
    }

    /**
     * 下载导入模板
     */
    public function downloadTemplate()
    {
        try {
            // 创建新的Excel文档
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            
            // ===== 第一个工作表：导入模板 =====
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('导入模板');
            
            // 添加说明文字
            $sheet->setCellValue('A1', '消耗品库存导入模板');
            $sheet->mergeCells('A1:G1');
            $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            $sheet->setCellValue('A2', '填写说明：');
            $sheet->mergeCells('A2:G2');
            $sheet->getStyle('A2')->getFont()->setBold(true);
            
            $sheet->setCellValue('A3', '1. SKU编码、仓库、库位、库存数量为必填项');
            $sheet->mergeCells('A3:G3');
            
            $sheet->setCellValue('A4', '2. 当SKU编码对应多个SKU时，属性组合也为必填项，用于确定唯一的SKU');
            $sheet->mergeCells('A4:G4');
            
            $sheet->setCellValue('A5', '3. 仓库必须填写系统中已存在的仓库名称，请参考"仓库列表"工作表');
            $sheet->mergeCells('A5:G5');
            
            $sheet->setCellValue('A6', '4. 库位必须填写系统中已存在的库位名称，请参考"库位列表"工作表');
            $sheet->mergeCells('A6:G6');
            
            $sheet->setCellValue('A7', '5. 同一个SKU在同一个仓库同一个库位只能有一条记录');
            $sheet->mergeCells('A7:G7');
            
            // 设置表头
            $headers = [
                'SKU编码', '属性组合', '仓库', '库位', '库存数量'
            ];
            
            $row = 8;
            foreach ($headers as $col => $header) {
                $sheet->setCellValueByColumnAndRow($col + 1, $row, $header);
                $sheet->getStyleByColumnAndRow($col + 1, $row)->getFont()->setBold(true);
                $sheet->getStyleByColumnAndRow($col + 1, $row)->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('CCCCCC');
            }
            
            // 获取仓库列表
            $warehouses = \App\Models\TecWarehouseModel::pluck('name', 'id')->toArray();
            
            // 预加载所有属性和属性值，构建映射
            $attrValuesMap = [];
            $attrs = \App\Models\Banhua\TecBanhuaAttrModel::with('values')->get();
            foreach ($attrs as $attr) {
                foreach ($attr->values as $value) {
                    $attrValuesMap[$value->id] = $value->name;
                }
            }
            
            // 获取示例SKU
            $skus = \App\Models\Banhua\TecBanhuaProductSkuModel::with(['gallery'])->take(3)->get();
            
            // 示例数据
            $exampleData = [];
            $i = 0;
            
            foreach ($warehouses as $warehouseId => $warehouseName) {
                // 获取该仓库的库位
                $locations = \App\Models\Banhua\TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
                    ->take(2)
                    ->get(['id', 'area_number', 'shelf_number', 'level_number'])
                    ->toArray();
                
                if (empty($locations)) {
                    continue;
                }
                
                foreach ($locations as $location) {
                    if (isset($skus[$i])) {
                        $sku = $skus[$i];
                        $locationName = $location['area_number'] . '区' . $location['shelf_number'] . '架' . $location['level_number'] . '层';
                        
                        // 获取属性组合文本
                        $attrValuesText = '';
                        if (!empty($sku->attr_value_ids)) {
                            $attrValues = [];
                            foreach ($sku->attr_value_ids as $valueId) {
                                if (isset($attrValuesMap[$valueId])) {
                                    $attrValues[] = $attrValuesMap[$valueId];
                                }
                            }
                            $attrValuesText = implode(', ', $attrValues);
                        }
                        
                        $exampleData[] = [
                            $sku->sku,
                            $attrValuesText,
                            $warehouseName,
                            $locationName,
                            100 + $i * 10
                        ];
                        $i++;
                    }
                }
            }
            
            // 如果没有足够的示例数据，添加一些默认示例
            while (count($exampleData) < 3) {
                $exampleData[] = [
                    'BH-EXAMPLE-00' . (count($exampleData) + 1),
                    'PS, 油画布',
                    '默认仓库',
                    '1区1架1层',
                    100 + count($exampleData) * 10
                ];
            }
            
            // 添加示例数据
            $row = 9;
            foreach ($exampleData as $data) {
                foreach ($data as $col => $value) {
                    $sheet->setCellValueByColumnAndRow($col + 1, $row, $value);
                }
                $row++;
            }
            
            // ===== 第二个工作表：仓库列表 =====
            $warehouseSheet = $spreadsheet->createSheet();
            $warehouseSheet->setTitle('仓库列表');
            
            // 设置仓库表头
            $warehouseSheet->setCellValue('A1', 'ID');
            $warehouseSheet->setCellValue('B1', '仓库名称');
            
            $warehouseSheet->getStyle('A1:B1')->getFont()->setBold(true);
            $warehouseSheet->getStyle('A1:B1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            
            // 填充仓库数据
            $row = 2;
            foreach ($warehouses as $id => $name) {
                $warehouseSheet->setCellValue('A' . $row, $id);
                $warehouseSheet->setCellValue('B' . $row, $name);
                $row++;
            }
            
            // ===== 第三个工作表：库位列表 =====
            $locationSheet = $spreadsheet->createSheet();
            $locationSheet->setTitle('库位列表');
            
            // 设置库位表头
            $locationSheet->setCellValue('A1', 'ID');
            $locationSheet->setCellValue('B1', '仓库ID');
            $locationSheet->setCellValue('C1', '仓库名称');
            $locationSheet->setCellValue('D1', '库位名称');
            
            $locationSheet->getStyle('A1:D1')->getFont()->setBold(true);
            $locationSheet->getStyle('A1:D1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            
            // 填充库位数据
            $row = 2;
            foreach ($warehouses as $warehouseId => $warehouseName) {
                $locations = \App\Models\Banhua\TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
                    ->get(['id', 'area_number', 'shelf_number', 'level_number'])
                    ->toArray();
                
                foreach ($locations as $location) {
                    $locationName = $location['area_number'] . '区' . $location['shelf_number'] . '架' . $location['level_number'] . '层';
                    $locationSheet->setCellValue('A' . $row, $location['id']);
                    $locationSheet->setCellValue('B' . $row, $warehouseId);
                    $locationSheet->setCellValue('C' . $row, $warehouseName);
                    $locationSheet->setCellValue('D' . $row, $locationName);
                    $row++;
                }
            }
            
            // 创建第四个工作表：SKU列表
            $skuSheet = $spreadsheet->createSheet();
            $skuSheet->setTitle('SKU列表');
            
            // 设置SKU表头
            $skuSheet->setCellValue('A1', 'ID');
            $skuSheet->setCellValue('B1', 'SKU编码');
            $skuSheet->setCellValue('C1', 'SKU名称');
            $skuSheet->setCellValue('D1', '属性组合');
            
            $skuSheet->getStyle('A1:D1')->getFont()->setBold(true);
            $skuSheet->getStyle('A1:D1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            
            // 填充SKU数据
            $row = 2;
            $skus = \App\Models\Banhua\TecBanhuaProductSkuModel::all();
            foreach ($skus as $sku) {
                // 构建属性组合文本
                $attrValuesText = '';
                if (!empty($sku->attr_value_ids)) {
                    $attrValues = [];
                    foreach ($sku->attr_value_ids as $valueId) {
                        if (isset($attrValuesMap[$valueId])) {
                            $attrValues[] = $attrValuesMap[$valueId];
                        }
                    }
                    $attrValuesText = implode(', ', $attrValues);
                }
                
                $skuSheet->setCellValue('A' . $row, $sku->id);
                $skuSheet->setCellValue('B' . $row, $sku->sku);
                $skuSheet->setCellValue('C' . $row, $sku->name);
                $skuSheet->setCellValue('D' . $row, $attrValuesText);
                $row++;
            }
            
            // 自动调整列宽
            foreach ($spreadsheet->getAllSheets() as $sheet) {
                foreach (range('A', 'Z') as $col) {
                    $sheet->getColumnDimension($col)->setAutoSize(true);
                }
            }
            
            // 确保默认打开第一个工作表
            $spreadsheet->setActiveSheetIndex(0);
            
            // 设置下载响应头
            $filename = '消耗品库存导入模板_' . date('YmdHis') . '.xlsx';
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');
            
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            Log::error('下载导入模板失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '下载失败：' . $e->getMessage()
            ]);
        }
    }

    // 标准化属性值ID数组
    private function normalizeAttrValueIds($attrText, $attrValueMap)
    {
        if (empty($attrText)) return '';
        
        // 替换全角逗号为半角，去除多余空格
        $attrText = str_replace(['，', '、', '；', ';'], ',', $attrText);
        $attrText = str_replace(['×'], '*', $attrText);
        $parts = array_filter(array_map('trim', explode(',', $attrText)));
        
        // 查找每个属性值对应的ID
        $valueIds = [];
        foreach ($parts as $part) {
            $lowerPart = strtolower($part);
            $found = false;
            
            // 直接匹配
            foreach ($attrValueMap as $valueId => $value) {
                if (strtolower($value['value_name']) === $lowerPart) {
                    $valueIds[] = $valueId;
                    $found = true;
                    break;
                }
            }
            
            // 如果直接匹配失败，尝试模糊匹配
            if (!$found) {
                foreach ($attrValueMap as $valueId => $value) {
                    if (strpos(strtolower($value['value_name']), $lowerPart) !== false || 
                        strpos($lowerPart, strtolower($value['value_name'])) !== false) {
                        $valueIds[] = $valueId;
                        $found = true;
                        break;
                    }
                }
            }
        }
        
        // 去重并排序
        $valueIds = array_unique($valueIds);
        sort($valueIds);
        
        return implode(',', $valueIds);
    }
} 