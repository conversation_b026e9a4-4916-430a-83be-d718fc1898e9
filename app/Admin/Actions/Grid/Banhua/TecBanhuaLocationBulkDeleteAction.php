<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class TecBanhuaLocationBulkDeleteAction extends BatchAction
{
    protected $title = '批量彻底删除';

    public function handle(Request $request)
    {
        // 获取选中的记录 ID
        $keys = $this->getKey();
        
        if (empty($keys)) {
            return $this->response()->error('请选择要删除的记录');
        }

        try {
            $controller = app(\App\Admin\Controllers\Banhua\TecBanhuaLocationController::class);
            $response = $controller->bulkDelete($request->merge(['ids' => $keys]));
            $result = json_decode($response->getContent(), true);

            if ($result['status']) {
                return $this->response()->success($result['message'])->refresh();
            } else {
                return $this->response()->error($result['message']);
            }
        } catch (\Exception $e) {
            return $this->response()->error('删除失败：' . $e->getMessage());
        }
    }

    public function confirm()
    {
        return ['确认要彻底删除选中的记录吗？', '此操作不可撤销'];
    }
} 