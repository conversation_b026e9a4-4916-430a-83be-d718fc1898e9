<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaSkuStockModel;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\TecWarehouseModel;
use App\Models\Banhua\TecBanhuaLocationModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class TecBanhuaSkuStockImportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-upload"></i> 导入库存';

    /**
     * 处理请求
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Request $request)
    {
        try {
            // 设置更长的执行时间限制
            ini_set('max_execution_time', 300); // 设置为5分钟
            set_time_limit(300); // 同时使用set_time_limit确保PHP执行时间也被延长
            
            // 验证请求
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:10240', // 最大10MB
                'duplicate_strategy' => 'required|in:skip,overwrite,merge',
            ], [
                'file.required' => '请选择要导入的文件',
                'file.file' => '上传的文件无效',
                'file.mimes' => '请上传CSV或Excel格式的文件',
                'file.max' => '文件大小不能超过10MB',
                'duplicate_strategy.required' => '请选择重复记录处理方式',
                'duplicate_strategy.in' => '重复记录处理方式无效',
            ]);
            
            if ($validator->fails()) {
                $errorMsg = $validator->errors()->first();
                Log::warning('SKU库存导入验证失败', ['error' => $errorMsg]);
                return response()->json([
                    'status' => false,
                    'message' => $errorMsg
                ]);
            }

            // 获取重复记录处理策略
            $duplicateStrategy = $request->input('duplicate_strategy', 'skip');
            Log::info('SKU库存导入 - 重复记录处理策略', ['strategy' => $duplicateStrategy]);
            
            // 验证文件是否存在
            if (!$request->hasFile('file')) {
                return response()->json(['status' => false, 'message' => '请选择要导入的文件']);
            }

            $file = $request->file('file');
            $extension = strtolower($file->getClientOriginalExtension());
            $path = $file->getRealPath();
            
            // 记录导入开始
            Log::info('开始导入SKU库存数据', [
                'file' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'extension' => $extension,
                'path' => $path
            ]);
            
            // 读取Excel/CSV文件
            try {
                if (in_array($extension, ['xlsx', 'xls'])) {
                    // 根据扩展名选择不同的读取器
                    if ($extension === 'xlsx') {
                        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                    } else {
                        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xls();
                    }
                    
                    // 只读取数据，不读取格式
                    $reader->setReadDataOnly(true);
                    
                    // 加载Excel文件
                    $spreadsheet = $reader->load($path);
                    
                    // 获取所有工作表名称
                    $sheetNames = $spreadsheet->getSheetNames();
                    Log::info('检测到Excel工作表列表', [
                        'sheet_names' => $sheetNames,
                        'sheet_count' => count($sheetNames)
                    ]);
                    
                    // 尝试按照以下顺序选择工作表：
                    // 1. 名为"导入模板"的工作表
                    // 2. 第一个工作表（索引0）
                    $worksheet = null;
                    if (in_array('导入模板', $sheetNames)) {
                        $worksheet = $spreadsheet->getSheetByName('导入模板');
                        Log::info('找到并选择了"导入模板"工作表');
                    } else {
                        $worksheet = $spreadsheet->getSheet(0); // 默认第一个工作表
                        Log::info('未找到"导入模板"工作表，使用第一个工作表', [
                            'sheet_name' => $worksheet->getTitle()
                        ]);
                    }
                    
                    // 获取工作表的行数和列数
                    $highestRow = $worksheet->getHighestRow();
                    $highestColumn = $worksheet->getHighestColumn();
                    $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
                    
                    Log::info('Excel文件基本信息', [
                        'sheet_name' => $worksheet->getTitle(),
                        'row_count' => $highestRow,
                        'column_count' => $highestColumnIndex
                    ]);
                    
                    // 扫描前15行，找到包含"SKU编码"的那一行作为表头
                    $headerRow = 8; // 默认表头在第8行
                    $foundHeader = false;
                    
                    for ($row = 1; $row <= min(15, $highestRow); $row++) {
                        // 取出前10列的内容
                        $rowContents = [];
                        for ($col = 1; $col <= min(10, $highestColumnIndex); $col++) {
                            $cellValue = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                            if ($cellValue !== null) {
                                $rowContents[$col] = trim((string)$cellValue);
                            }
                        }
                        
                        // 记录每行内容便于调试
                        Log::info("扫描第{$row}行内容", ['row_contents' => $rowContents]);
                        
                        // 检查是否是真正的表头行（多个单元格有值，且不是说明文本）
                        $filledCells = count(array_filter($rowContents, function($value) {
                            return !empty($value) && !preg_match('/^\d+\.\s/', $value); // 排除以"数字."开头的说明文本
                        }));
                        
                        // 检查是否包含"SKU编码"单元格或类似内容，且不是说明文本
                        $hasSkuColumn = false;
                        foreach ($rowContents as $value) {
                            // 如果是单独的"SKU编码"字段（不是说明文本的一部分）
                            if ($value === 'SKU编码' || (strpos($value, 'SKU编码') !== false && !preg_match('/^\d+\.\s/', $value))) {
                                $hasSkuColumn = true;
                                break;
                            }
                        }
                        
                        // 如果找到了看起来像表头的行
                        if ($filledCells >= 3 && $hasSkuColumn) {
                            $headerRow = $row;
                            $foundHeader = true;
                            Log::info("找到表头行，位于第{$row}行", ['header_content' => $rowContents]);
                            break;
                        }
                    }
                    
                    if (!$foundHeader) {
                        Log::warning('未找到真正的表头行，使用默认的第8行');
                        // 强制使用第8行作为表头
                        $headerRow = 8;
                        
                        // 确认第8行内容
                        $rowContents = [];
                        for ($col = 1; $col <= $highestColumnIndex; $col++) {
                            $cellValue = $worksheet->getCellByColumnAndRow($col, $headerRow)->getValue();
                            if ($cellValue !== null) {
                                $rowContents[$col] = trim((string)$cellValue);
                            }
                        }
                        Log::info("强制使用第8行作为表头", ['header_content' => $rowContents]);
                    }
                    
                    // 读取表头行
                    $headers = [];
                    for ($col = 1; $col <= $highestColumnIndex; $col++) {
                        $value = $worksheet->getCellByColumnAndRow($col, $headerRow)->getValue();
                        if ($value !== null) {
                            $headers[] = trim((string)$value);
                        }
                    }
                    
                    Log::info('Excel表头行信息', [
                        'header_row' => $headerRow,
                        'headers' => $headers,
                        'header_count' => count($headers)
                    ]);
                    
                    // 从表头的下一行开始读取数据
                    $startDataRow = $headerRow + 1;
                    Log::info("将从第{$startDataRow}行开始读取数据");
                    
                    // 读取所有数据行
                    $rows = [];
                    for ($row = $startDataRow; $row <= $highestRow; $row++) {
                        // 检查是否为空行
                        $isEmpty = true;
                        $rowData = [];
                        
                        for ($col = 1; $col <= $highestColumnIndex; $col++) {
                            $value = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                            $rowData[] = $value;
                            if ($value !== null && $value !== '') {
                                $isEmpty = false;
                            }
                        }
                        
                        if (!$isEmpty) {
                            $rows[] = $rowData;
                        }
                    }
                } else {
                    // 处理CSV文件
                    $allRows = array_map('str_getcsv', file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES));
                    
                    // 第一行作为表头
                    $headers = $allRows[0];
                    // 剩余行作为数据
                    $rows = array_slice($allRows, 1);
                    
                    Log::info('CSV文件信息', [
                        'header' => $headers,
                        'data_rows' => count($rows)
                    ]);
                }
                
                // 检查数据是否为空
                if (empty($rows)) {
                    return response()->json(['status' => false, 'message' => '文件中没有有效数据']);
                }
                
            } catch (\Exception $e) {
                Log::error('读取导入文件失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                return response()->json(['status' => false, 'message' => '读取文件失败: ' . $e->getMessage()]);
            }
            
            // 验证表头 - 使用已经找到的表头行
            $requiredHeaders = ['SKU编码', '仓库', '库位', '库存数量'];
            $foundHeaders = [];
            
            // 更灵活地匹配表头 - 先尝试精确匹配
            foreach ($requiredHeaders as $required) {
                $index = array_search($required, $headers);
                if ($index !== false) {
                    $foundHeaders[$required] = $index;
                    Log::info("精确匹配到表头: {$required}", ['column' => $index]);
                }
            }
            
            // 对于未找到的表头，尝试模糊匹配
            $remainingHeaders = array_diff($requiredHeaders, array_keys($foundHeaders));
            if (!empty($remainingHeaders)) {
                foreach ($remainingHeaders as $required) {
                    // 设置特殊匹配规则
                    $matchPatterns = [$required];
                    
                    // 特殊情况：SKU编码可能简写为SKU
                    if ($required === 'SKU编码') {
                        $matchPatterns[] = 'SKU';
                    }
                    
                    // 特殊情况：库存数量可能简写为库存
                    if ($required === '库存数量') {
                        $matchPatterns[] = '库存';
                    }
                    
                    // 尝试模糊匹配
                    foreach ($headers as $index => $header) {
                        if (!$header) continue;
                        
                        $header = trim((string)$header);
                        foreach ($matchPatterns as $pattern) {
                            if (strpos($header, $pattern) !== false) {
                                $foundHeaders[$required] = $index;
                                Log::info("模糊匹配到表头: {$required}", ['column' => $index, 'header' => $header, 'pattern' => $pattern]);
                                break 2;
                            }
                        }
                    }
                }
            }
            
            // 记录找到的表头字段
            Log::info('找到的表头字段', ['found_headers' => $foundHeaders]);
            
            $missingHeaders = array_diff($requiredHeaders, array_keys($foundHeaders));
            if (!empty($missingHeaders)) {
                Log::warning('表头缺少必要字段', ['missing' => $missingHeaders, 'found' => array_keys($foundHeaders)]);
                return response()->json([
                    'status' => false, 
                    'message' => '表头缺少必要字段：' . implode(', ', $missingHeaders) . '。请确认Excel文件第8行包含这些字段。'
                ]);
            }
            
            // 准备导入数据
            $skuColumn = $foundHeaders['SKU编码'];
            $warehouseColumn = $foundHeaders['仓库'];
            $locationColumn = $foundHeaders['库位'];
            $stockColumn = $foundHeaders['库存数量'];
            
            // 可选字段
            $attrCombinationColumn = null;
            $costPriceColumn = null;
            $salePriceColumn = null;
            $batchNoColumn = null;
            $expireDateColumn = null;
            
            // 查找可选字段
            foreach ($headers as $index => $header) {
                if (!$header) continue;
                
                $header = trim((string)$header);
                if ($header === '属性组合' || strpos($header, '属性组合') !== false) {
                    $attrCombinationColumn = $index;
                    Log::info("找到可选字段: 属性组合", ['column' => $index]);
                }
                if ($header === '成本价' || strpos($header, '成本价') !== false) {
                    $costPriceColumn = $index;
                    Log::info("找到可选字段: 成本价", ['column' => $index]);
                }
                if ($header === '销售价' || strpos($header, '销售价') !== false) {
                    $salePriceColumn = $index;
                    Log::info("找到可选字段: 销售价", ['column' => $index]);
                }
                if ($header === '批次号' || strpos($header, '批次号') !== false) {
                    $batchNoColumn = $index;
                    Log::info("找到可选字段: 批次号", ['column' => $index]);
                }
                if ($header === '有效期' || strpos($header, '有效期') !== false) {
                    $expireDateColumn = $index;
                    Log::info("找到可选字段: 有效期", ['column' => $index]);
                }
            }
            
            // 预取所有SKU，用于查询SKU ID
            $allSkus = TecBanhuaProductSkuModel::select(['id', 'sku', 'attr_value_ids'])->get();
            
            // 构建SKU映射，支持通过SKU编码和属性组合查找
            $skuMap = [];
            $skuAttrMap = [];
            
            // 预加载所有属性值
            $attrValues = \App\Models\Banhua\TecBanhuaAttrValueModel::pluck('name', 'id')->toArray();
            
            foreach ($allSkus as $sku) {
                // 基本映射：SKU编码 => ID
                if (!isset($skuMap[$sku->sku])) {
                    $skuMap[$sku->sku] = [];
                }
                $skuMap[$sku->sku][] = $sku->id;
                
                // 属性组合映射：SKU编码_属性组合 => ID
                $attrValueNames = [];
                if (!empty($sku->attr_value_ids)) {
                    foreach ($sku->attr_value_ids as $valueId) {
                        if (isset($attrValues[$valueId])) {
                            $attrValueNames[] = $attrValues[$valueId];
                        }
                    }
                }
                
                $attrCombination = implode(', ', $attrValueNames);
                $skuAttrMap[$sku->sku . '_' . $attrCombination] = $sku->id;
            }
            
            // 预取所有仓库，用于查询仓库ID
            $warehouseMap = TecWarehouseModel::pluck('id', 'name')->toArray();
            
            // 读取所有库位
            $allLocations = TecBanhuaLocationModel::with('warehouse')->get();
            
            // 构建仓库库位映射，格式: 仓库名称_库位名称 => 库位ID
            $locationMap = [];
            foreach ($allLocations as $location) {
                $warehouseName = $location->warehouse ? $location->warehouse->name : '未知仓库';
                $locationName = $location->area_number . '区' . $location->shelf_number . '架' . $location->level_number . '层';
                $key = $warehouseName . '_' . $locationName;
                $locationMap[$key] = $location->id;
            }
            
            // 开始批量导入
            DB::beginTransaction();
            
            $success = 0;
            $failed = 0;
            $skipped = 0;
            $overwritten = 0;
            $merged = 0;
            $errors = [];
            
            foreach ($rows as $index => $row) {
                $rowNumber = $index + 1; // 行号从1开始
                
                // 跳过空行
                if (empty($row[$skuColumn]) || empty($row[$warehouseColumn]) || empty($row[$locationColumn]) || !isset($row[$stockColumn])) {
                    $errors[] = "第{$rowNumber}行：数据不完整，跳过该行";
                    $failed++;
                    continue;
                }
                
                $skuCode = trim($row[$skuColumn]);
                $attrCombination = $attrCombinationColumn !== null && isset($row[$attrCombinationColumn]) ? trim($row[$attrCombinationColumn]) : '';
                $warehouseName = trim($row[$warehouseColumn]);
                $locationName = trim($row[$locationColumn]);
                $stock = intval($row[$stockColumn]);
                $costPrice = $costPriceColumn !== null && isset($row[$costPriceColumn]) ? floatval($row[$costPriceColumn]) : 0;
                $salePrice = $salePriceColumn !== null && isset($row[$salePriceColumn]) ? floatval($row[$salePriceColumn]) : 0;
                $batchNo = $batchNoColumn !== null && isset($row[$batchNoColumn]) ? trim($row[$batchNoColumn]) : null;
                $expireDate = $expireDateColumn !== null && isset($row[$expireDateColumn]) ? trim($row[$expireDateColumn]) : null;
                
                // 验证SKU是否存在
                if (!isset($skuMap[$skuCode])) {
                    $errors[] = "第{$rowNumber}行：SKU编码'{$skuCode}'不存在";
                    $failed++;
                    continue;
                }
                
                // 确定具体的SKU ID
                $skuId = null;
                
                // 如果提供了属性组合，尝试通过SKU编码+属性组合确定唯一SKU
                if (!empty($attrCombination)) {
                    $key = $skuCode . '_' . $attrCombination;
                    if (isset($skuAttrMap[$key])) {
                        $skuId = $skuAttrMap[$key];
                    } else {
                        // 尝试模糊匹配
                        foreach ($skuAttrMap as $mapKey => $mapId) {
                            if (strpos($mapKey, $skuCode . '_') === 0 && 
                                strpos($mapKey, $attrCombination) !== false) {
                                $skuId = $mapId;
                                break;
                            }
                        }
                    }
                }
                
                // 如果通过属性组合无法确定，且SKU编码对应多个SKU
                if ($skuId === null) {
                    if (count($skuMap[$skuCode]) === 1) {
                        // 只有一个匹配的SKU，直接使用
                        $skuId = $skuMap[$skuCode][0];
                    } else {
                        // 多个匹配的SKU，无法确定
                        $errors[] = "第{$rowNumber}行：SKU编码'{$skuCode}'对应多个SKU，请提供准确的属性组合以确定唯一SKU";
                        $failed++;
                        continue;
                    }
                }
                
                // 验证仓库是否存在
                if (!isset($warehouseMap[$warehouseName])) {
                    $errors[] = "第{$rowNumber}行：仓库'{$warehouseName}'不存在";
                    $failed++;
                    continue;
                }
                
                // 验证库位是否存在
                $locationKey = $warehouseName . '_' . $locationName;
                if (!isset($locationMap[$locationKey])) {
                    $errors[] = "第{$rowNumber}行：仓库'{$warehouseName}'下不存在库位'{$locationName}'";
                    $failed++;
                    continue;
                }
                
                // 获取实际ID
                $warehouseId = $warehouseMap[$warehouseName];
                $locationId = $locationMap[$locationKey];
                
                // 检查该SKU在此仓库库位是否已存在记录
                $existingStock = TecBanhuaSkuStockModel::where('sku_id', $skuId)
                    ->where('warehouse_id', $warehouseId)
                    ->where('location_id', $locationId)
                    ->first();
                
                try {
                    if ($existingStock) {
                        // 记录原始值，用于日志
                        $originalValues = [
                            'stock' => $existingStock->stock,
                            'cost_price' => $existingStock->cost_price,
                            'sale_price' => $existingStock->sale_price,
                            'batch_no' => $existingStock->batch_no,
                            'expire_date' => $existingStock->expire_date
                        ];
                        
                        // 根据不同策略处理重复记录
                        switch ($duplicateStrategy) {
                            case 'skip':
                                // 跳过重复记录
                                Log::info("跳过重复记录", [
                                    'row' => $rowNumber,
                                    'sku_code' => $skuCode,
                                    'warehouse' => $warehouseName,
                                    'location' => $locationName,
                                    'existing_values' => $originalValues,
                                    'new_values' => [
                                        'stock' => $stock,
                                        'cost_price' => $costPrice,
                                        'sale_price' => $salePrice,
                                        'batch_no' => $batchNo,
                                        'expire_date' => $expireDate
                                    ]
                                ]);
                                $skipped++;
                                continue 2; // 跳过当前行，继续下一行
                                
                            case 'overwrite':
                                // 完全覆盖现有记录
                                $existingStock->stock = $stock;
                                $existingStock->cost_price = $costPrice;
                                $existingStock->sale_price = $salePrice;
                                $existingStock->batch_no = $batchNo;
                                
                                if (!empty($expireDate)) {
                                    // 尝试解析日期格式
                                    try {
                                        $formattedDate = \Carbon\Carbon::parse($expireDate)->format('Y-m-d');
                                        $existingStock->expire_date = $formattedDate;
                                    } catch (\Exception $e) {
                                        $existingStock->expire_date = null;
                                    }
                                } else {
                                    $existingStock->expire_date = null;
                                }
                                
                                $existingStock->save();
                                $overwritten++;
                                
                                // 记录覆盖操作
                                Log::info("覆盖现有记录", [
                                    'row' => $rowNumber,
                                    'sku_code' => $skuCode,
                                    'warehouse' => $warehouseName,
                                    'location' => $locationName,
                                    'old_values' => $originalValues,
                                    'new_values' => [
                                        'stock' => $stock,
                                        'cost_price' => $costPrice,
                                        'sale_price' => $salePrice,
                                        'batch_no' => $batchNo,
                                        'expire_date' => $existingStock->expire_date
                                    ]
                                ]);
                                break;
                                
                            case 'merge':
                                // 只更新非空字段
                                if ($stock > 0) {
                                    // 库存数量进行累加而不是替换
                                    $existingStock->stock += $stock;
                                }
                                
                                if ($costPrice > 0) {
                                    $existingStock->cost_price = $costPrice;
                                }
                                
                                if ($salePrice > 0) {
                                    $existingStock->sale_price = $salePrice;
                                }
                                
                                if (!empty($batchNo)) {
                                    $existingStock->batch_no = $batchNo;
                                }
                                
                                if (!empty($expireDate)) {
                                    try {
                                        $formattedDate = \Carbon\Carbon::parse($expireDate)->format('Y-m-d');
                                        $existingStock->expire_date = $formattedDate;
                                    } catch (\Exception $e) {
                                        // 保留原有日期
                                    }
                                }
                                
                                $existingStock->save();
                                $merged++;
                                
                                // 记录合并操作
                                Log::info("合并记录数据（库存累加）", [
                                    'row' => $rowNumber,
                                    'sku_code' => $skuCode,
                                    'warehouse' => $warehouseName,
                                    'location' => $locationName,
                                    'original_values' => $originalValues,
                                    'updated_values' => [
                                        'stock' => $existingStock->stock,
                                        'cost_price' => $existingStock->cost_price,
                                        'sale_price' => $existingStock->sale_price,
                                        'batch_no' => $existingStock->batch_no,
                                        'expire_date' => $existingStock->expire_date
                                    ],
                                    'input_values' => [
                                        'stock' => $stock,
                                        'cost_price' => $costPrice,
                                        'sale_price' => $salePrice,
                                        'batch_no' => $batchNo,
                                        'expire_date' => $expireDate
                                    ]
                                ]);
                                break;
                        }
                    } else {
                        // 创建新记录
                        $newStock = new TecBanhuaSkuStockModel();
                        $newStock->sku_id = $skuId;
                        $newStock->warehouse_id = $warehouseId;
                        $newStock->location_id = $locationId;
                        $newStock->stock = $stock;
                        $newStock->cost_price = $costPrice;
                        $newStock->sale_price = $salePrice;
                        $newStock->batch_no = $batchNo;
                        
                        if (!empty($expireDate)) {
                            // 尝试解析日期格式
                            try {
                                $formattedDate = \Carbon\Carbon::parse($expireDate)->format('Y-m-d');
                                $newStock->expire_date = $formattedDate;
                            } catch (\Exception $e) {
                                $newStock->expire_date = null;
                            }
                        }
                        
                        $newStock->save();
                        $success++;
                        
                        // 记录新增操作
                        Log::info("新增库存记录", [
                            'row' => $rowNumber,
                            'sku_code' => $skuCode,
                            'warehouse' => $warehouseName,
                            'location' => $locationName,
                            'values' => [
                                'stock' => $stock,
                                'cost_price' => $costPrice,
                                'sale_price' => $salePrice,
                                'batch_no' => $batchNo,
                                'expire_date' => $newStock->expire_date
                            ]
                        ]);
                    }
                } catch (\Exception $e) {
                    $errors[] = "第{$rowNumber}行：保存失败，" . $e->getMessage();
                    $failed++;
                    Log::error('导入SKU库存失败', [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
            
            // 提交事务
            DB::commit();
            
            // 构建结果消息
            $resultMessage = "导入完成，新增：{$success}条";
            if ($skipped > 0) {
                $resultMessage .= "，跳过：{$skipped}条";
            }
            if ($overwritten > 0) {
                $resultMessage .= "，覆盖：{$overwritten}条";
            }
            if ($merged > 0) {
                $resultMessage .= "，合并：{$merged}条";
            }
            if ($failed > 0) {
                $resultMessage .= "，失败：{$failed}条";
            }
            
            // 记录导入结果
            Log::info('SKU库存导入完成', [
                'success' => $success,
                'skipped' => $skipped,
                'overwritten' => $overwritten,
                'merged' => $merged,
                'failed' => $failed,
                'errors' => $errors
            ]);
            
            // 返回导入结果
            return response()->json([
                'status' => true,
                'message' => $resultMessage,
                'errors' => $errors,
                'stats' => [
                    'success' => $success,
                    'skipped' => $skipped,
                    'overwritten' => $overwritten,
                    'merged' => $merged,
                    'failed' => $failed
                ]
            ]);
            
        } catch (\Exception $e) {
            // 发生异常，回滚事务
            if (DB::transactionLevel() > 0) {
                DB::rollBack();
            }
            
            Log::error('SKU库存导入失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '导入失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 渲染按钮
     *
     * @return string
     */
    public function render()
    {
        // 获取路由和token
        $token = csrf_token();
        $handleRoute = $this->getHandleRoute();
        $templateRoute = $this->getTemplateRoute();
        
        // 按钮HTML
        $html = <<<HTML
<a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline import-sku-stock" style="margin-right:3px">
    <i class="fa fa-upload"></i><span class="d-none d-sm-inline">&nbsp; 导入</span>
</a>

<div class="modal fade" id="import-sku-stock-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导入SKU库存数据</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    请先下载导入模板，按照模板格式填写数据后再上传。
                    <button type="button" class="btn btn-primary btn-sm download-template" style="margin-left:10px;">
                        <i class="fa fa-download"></i> 下载Excel导入模板
                    </button>
                </div>
                <form id="import-sku-stock-form" action="{$handleRoute}" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="_token" value="{$token}">
                    <div class="form-group">
                        <label for="file">选择文件</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".csv,.xlsx,.xls">
                        <small class="form-text text-muted">
                            支持Excel格式(.xlsx, .xls)和CSV格式文件，文件大小不超过10MB。必须包含以下列：SKU编码、仓库、库位、库存数量。
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="duplicate_strategy">重复记录处理方式</label>
                        <select class="form-control" id="duplicate_strategy" name="duplicate_strategy">
                            <option value="skip" selected>跳过重复记录（不修改已有数据）</option>
                            <option value="overwrite">覆盖现有记录（用新数据替换）</option>
                            <option value="merge">合并数据（库存累加，其他非空字段更新）</option>
                        </select>
                        <small class="form-text text-muted">
                            选择当导入的SKU在同一仓库同一库位已存在记录时的处理方式
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white btn-mini" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary btn-mini" id="submit-import-sku-stock">导入</button>
            </div>
        </div>
    </div>
</div>
HTML;

        // 添加JavaScript
        \Dcat\Admin\Admin::script(<<<SCRIPT
$(function () {
    // 定义路由变量
    var handleRoute = '{$handleRoute}';
    var templateRoute = '{$templateRoute}';
    
    // 点击导入按钮显示模态框
    $('.import-sku-stock').on('click', function () {
        $('#import-sku-stock-modal').modal('show');
    });
    
    // 下载模板按钮点击事件
    $('.download-template').on('click', function() {
        Dcat.loading();
        // 直接打开新窗口下载，避免使用iframe
        window.open(templateRoute, '_blank');
        setTimeout(function() {
            Dcat.loading(false);
        }, 1000);
    });
    
    // 提交导入表单
    $('#submit-import-sku-stock').on('click', function () {
        var fileInput = $('#file');
        if (fileInput.val() === '') {
            Dcat.error('请选择要导入的文件');
            return;
        }
        Dcat.loading();
        var formData = new FormData($('#import-sku-stock-form')[0]);
        $.ajax({
            url: handleRoute,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                Dcat.loading(false);
                
                // 构建详细的结果消息
                var message = response.message || '';
                var detailMessage = '';
                
                // 如果有详细统计信息，显示在消息中
                if (response.stats) {
                    var stats = response.stats;
                    if (stats.skipped > 0) {
                        detailMessage += '<br>- 跳过了 ' + stats.skipped + ' 条重复记录';
                    }
                    if (stats.overwritten > 0) {
                        detailMessage += '<br>- 覆盖了 ' + stats.overwritten + ' 条现有记录';
                    }
                    if (stats.merged > 0) {
                        detailMessage += '<br>- 合并了 ' + stats.merged + ' 条记录数据（库存累加）';
                    }
                }
                
                // 如果有错误信息，添加到消息中
                if (response.errors && response.errors.length > 0) {
                    detailMessage += '<br><br><strong>错误详情：</strong>';
                    // 最多显示5条错误信息
                    var maxErrors = Math.min(5, response.errors.length);
                    for (var i = 0; i < maxErrors; i++) {
                        detailMessage += '<br>- ' + response.errors[i];
                    }
                    if (response.errors.length > 5) {
                        detailMessage += '<br>... 等共 ' + response.errors.length + ' 条错误';
                    }
                }
                
                // 根据操作结果显示不同颜色的消息
                if (response.status === true) {
                    // 根据主要操作类型决定消息颜色
                    if (response.stats) {
                        var stats = response.stats;
                        if (stats.failed > 0) {
                            // 有失败记录，显示红色错误消息
                            Dcat.error(message + detailMessage);
                        } else if (stats.success > 0 && stats.skipped === 0 && stats.overwritten === 0 && stats.merged === 0) {
                            // 只有新增记录，显示绿色成功消息
                            Dcat.success(message + detailMessage);
                        } else if (stats.skipped > 0 && stats.success === 0 && stats.overwritten === 0 && stats.merged === 0) {
                            // 只有跳过记录，显示蓝色信息消息
                            Dcat.info(message + detailMessage);
                        } else if (stats.overwritten > 0 && stats.success === 0 && stats.skipped === 0 && stats.merged === 0) {
                            // 只有覆盖记录，显示紫色主要消息
                            Dcat.info(message + detailMessage);
                        } else if (stats.merged > 0 && stats.success === 0 && stats.skipped === 0 && stats.overwritten === 0) {
                            // 只有合并记录，显示黄色警告消息
                            Dcat.warning(message + detailMessage);
                        } else {
                            // 混合操作，根据优先级决定颜色
                            if (stats.success > 0) {
                                // 有新增记录，优先显示绿色成功消息
                                Dcat.success(message + detailMessage);
                            } else if (stats.overwritten > 0) {
                                // 有覆盖记录，显示紫色主要消息
                                Dcat.info(message + detailMessage);
                            } else if (stats.merged > 0) {
                                // 有合并记录，显示黄色警告消息
                                Dcat.warning(message + detailMessage);
                            } else {
                                // 默认显示蓝色信息消息
                                Dcat.info(message + detailMessage);
                            }
                        }
                    } else {
                        // 没有详细统计信息，默认显示绿色成功消息
                        Dcat.success(message + detailMessage);
                    }
                } else {
                    // 导入失败，显示红色错误消息
                    Dcat.error(message + detailMessage);
                }
                
                $('#import-sku-stock-modal').modal('hide');
                setTimeout(function () {
                    Dcat.reload();
                }, 2000);
            },
            error: function (xhr) {
                Dcat.loading(false);
                var message = '导入失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                Dcat.error(message);
            }
        });
    });
});
SCRIPT);
        
        return $html;
    }
    
    /**
     * 获取处理URL
     *
     * @return string
     */
    public function getHandleRoute()
    {
        return admin_url('banhua/sku-stock-import');
    }
    
    /**
     * 获取模板下载URL
     *
     * @return string
     */
    public function getTemplateRoute()
    {
        return admin_url('banhua/sku-stock-import-template');
    }
} 