<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\Banhua\TecBanhuaProductCategoryModel;
use App\Models\Banhua\TecBanhuaProductUnitModel;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class TecBanhuaProductSkuExportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-download"></i> 导出SKU';

    /**
     * 处理请求
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function handle(Request $request)
    {
        try {
            // 获取筛选条件
            $categoryId = $request->get('category_id');
            $sku = $request->get('sku');
            $pid = $request->get('pid');
            $jan = $request->get('jan');
            $attrValueFilter = $request->get('attr_value_filter');
            
            // 记录导出条件
            Log::info('导出SKU数据', [
                'category_id' => $categoryId,
                'sku' => $sku,
                'pid' => $pid,
                'jan' => $jan,
                'attr_value_filter' => $attrValueFilter
            ]);
            
            // 构建查询
            $query = TecBanhuaProductSkuModel::query();
            
            // 应用筛选条件
            if (!empty($categoryId)) {
                $query->where('category_id', $categoryId);
            }
            
            if (!empty($sku)) {
                $query->where('sku', 'like', "%{$sku}%");
            }
            
            if (!empty($pid)) {
                $query->where('pid', 'like', "%{$pid}%");
            }
            
            if (!empty($jan)) {
                $query->where('jan', 'like', "%{$jan}%");
            }
            
            if (!empty($attrValueFilter)) {
                // 尝试解析可能是逗号分隔的多个值
                $attrValues = explode(',', $attrValueFilter);
                if (count($attrValues) > 1) {
                    $query->where(function($q) use ($attrValues) {
                        foreach ($attrValues as $attrValue) {
                            $attrValue = trim($attrValue);
                            if (!empty($attrValue)) {
                                $q->orWhere(function($subQ) use ($attrValue) {
                                    // 作为字符串数组内元素
                                    $subQ->whereRaw('JSON_CONTAINS(attr_value_ids, ?)', ['["'.$attrValue.'"]']);
                                    // 作为数字数组内元素
                                    $subQ->orWhereRaw('JSON_CONTAINS(attr_value_ids, ?)', ['['.$attrValue.']']);
                                    // 作为单独字符串
                                    $subQ->orWhereRaw('JSON_CONTAINS(attr_value_ids, ?)', ['"'.$attrValue.'"']);
                                });
                            }
                        }
                    });
                } else {
                $query->where(function($q) use ($attrValueFilter) {
                    // 作为字符串数组内元素 - 最常见格式
                    $q->whereRaw('JSON_CONTAINS(attr_value_ids, ?)', ['["'.$attrValueFilter.'"]']);
                    // 作为数字数组内元素
                    $q->orWhereRaw('JSON_CONTAINS(attr_value_ids, ?)', ['['.$attrValueFilter.']']);
                    // 作为单独字符串 - 兼容特殊格式
                    $q->orWhereRaw('JSON_CONTAINS(attr_value_ids, ?)', ['"'.$attrValueFilter.'"']);
                });
                }
            }
            
            // 预加载关联数据
            $query->with(['category', 'unit', 'gallery']);
            
            // 获取数据
            $skus = $query->get();
            
            // 记录查询结果
            Log::info('SKU导出查询结果', [
                'count' => $skus->count(),
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]);
            
            // 如果没有数据，返回错误信息
            if ($skus->isEmpty()) {
                return $this->response()->error('没有符合条件的数据可导出');
            }
            
            // 预取所有图库数据，用于补充图片URL
            $galleryImages = DB::table('t_banhua_image_gallery')
                ->whereNotNull('path')
                ->whereNotNull('id')
                ->pluck('path', 'id')
                ->toArray();
            
            // 一次性查出所有属性值，避免N+1
            $allAttrValues = 
                \App\Models\Banhua\TecBanhuaAttrValueModel::all()->keyBy('id');
            
            // 准备CSV数据
            $headers = [
                'ID', 'SKU', '名称', '图库ID', 'JAN', '价格', '分类', '单位', '属性组合', '图片URL', '创建时间', '更新时间'
            ];
            
            $rows = [];
            $rows[] = $headers;
            
            foreach ($skus as $sku) {
                // 获取属性值文本
                $attrValuesText = '';
                if (!empty($sku->attr_value_ids)) {
                    $attrValueIds = $sku->attr_value_ids;
                    if (is_string($attrValueIds)) {
                        $attrValueIds = json_decode($attrValueIds, true);
                    }
                    if (is_array($attrValueIds)) {
                        $formattedValues = [];
                        foreach ($attrValueIds as $vid) {
                            if (isset($allAttrValues[$vid])) {
                                $formattedValues[] = $allAttrValues[$vid]->name;
                        }
                        }
                        $attrValuesText = implode(', ', $formattedValues);
                    }
                }
                
                // 获取图片URL
                $imageUrl = $sku->image;
                // 优先从关联的gallery获取
                if (!empty($sku->gallery) && !empty($sku->gallery->path)) {
                    $imageUrl = $sku->gallery->path;
                } 
                // 如果没有关联gallery或没有path，尝试从galleryImages中获取
                else if (empty($imageUrl) && !empty($sku->pid) && isset($galleryImages[$sku->pid])) {
                    $imageUrl = $galleryImages[$sku->pid];
                }
                
                $rows[] = [
                    $sku->id,
                    $sku->sku,
                    $sku->name,
                    $sku->pid,
                    $sku->jan,
                    $sku->price,
                    $sku->category ? $sku->category->title : '',
                    $sku->unit ? $sku->unit->unit_name : '',
                    $attrValuesText,
                    $imageUrl,
                    $sku->created_at,
                    $sku->updated_at
                ];
            }
            
            // 生成CSV文件
            $filename = 'banhua_sku_export_' . date('YmdHis') . '.csv';
            $tempFile = storage_path('app/public/' . $filename);
            
            // 创建CSV文件
            $fp = fopen($tempFile, 'w');
            
            // 添加BOM头，解决中文乱码问题
            fwrite($fp, "\xEF\xBB\xBF");
            
            // 写入数据
            foreach ($rows as $row) {
                fputcsv($fp, $row);
            }
            
            fclose($fp);
            
            // 记录导出成功
            Log::info('SKU导出成功', [
                'filename' => $filename,
                'count' => count($rows) - 1
            ]);
            
            // 返回文件下载响应
            return response()->download($tempFile, $filename, [
                'Content-Type' => 'text/csv; charset=UTF-8',
            ])->deleteFileAfterSend(true);
            
        } catch (\Exception $e) {
            Log::error('SKU导出失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->response()->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 渲染按钮
     *
     * @return string
     */
    public function render()
    {
        // 自定义按钮样式
        return <<<HTML
<a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline export-sku" style="margin-right:3px">
    <i class="fa fa-download"></i><span class="d-none d-sm-inline">&nbsp; 导出</span>
</a>
<script>
$(function () {
    $('.export-sku').on('click', function () {
        // 获取当前页面的所有筛选参数
        var params = {};
        var queryString = window.location.search.substring(1);
        var pairs = queryString.split('&');
        
        for (var i = 0; i < pairs.length; i++) {
            var pair = pairs[i].split('=');
            if (pair[0] && pair[0] !== '_pjax') {
                params[pair[0]] = decodeURIComponent(pair[1] || '');
            }
        }
        
        // 构建导出URL
        var exportUrl = '{$this->getHandleRoute()}';
        var queryParams = [];
        
        for (var key in params) {
            if (params.hasOwnProperty(key)) {
                queryParams.push(key + '=' + encodeURIComponent(params[key]));
            }
        }
        
        if (queryParams.length > 0) {
            exportUrl += '?' + queryParams.join('&');
        }
        
        // 显示加载中提示
        Dcat.loading();
        
        // 下载文件
        window.location.href = exportUrl;
        
        // 延迟关闭加载提示
        setTimeout(function() {
            Dcat.loading(false);
        }, 2000);
    });
});
</script>
HTML;
    }
    
    /**
     * 获取处理URL
     *
     * @return string
     */
    public function getHandleRoute()
    {
        return admin_url('banhua/sku-export');
    }
} 