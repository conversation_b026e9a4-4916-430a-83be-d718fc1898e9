<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use Illuminate\Support\Facades\Log;

class TecBanhuaProductSkuCopyAction extends BatchAction
{
    protected $title = '复制SKU';

    public function match($key)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return false;
        }

        // 只有选择单条数据时才显示
        return $this->grid->getSelectedKeys()->count() === 1;
    }

    public function handle(Request $request)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return $this->response()->error('回收站中不允许复制SKU');
        }

        // 获取选中的记录 ID
        $keys = $this->getKey() ?? [];
        
        if (count($keys) !== 1) {
            return $this->response()
                ->error('请选择单条数据进行复制')
                ->refresh();
        }

        $id = reset($keys);  // 获取数组的第一个元素

        try {
            // 获取原始SKU
            $originalSku = TecBanhuaProductSkuModel::findOrFail($id);

            // 创建新SKU，复制原始SKU的所有属性
            $newSku = $originalSku->replicate();

            // 修改SKU编码，添加 -copy 后缀
            $newSku->sku = $originalSku->sku . '-copy';
            
            // 记录日志
            Log::info('复制SKU', [
                'original_id' => $originalSku->id,
                'original_sku' => $originalSku->sku,
                'new_sku' => $newSku->sku
            ]);

            // 保存新SKU
            $newSku->save();

            return $this->response()
                ->success('成功复制SKU')
                ->refresh();
        } catch (\Exception $e) {
            Log::error('复制SKU失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->response()->error('复制SKU失败：' . $e->getMessage());
        }
    }

    public function confirm()
    {
        return ['确认要复制选中的SKU吗？', '将创建一个新的SKU副本'];
    }
} 