<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaEcommercePlatformModel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Http\JsonResponse;

/**
 * 阪画电商平台数据导入操作
 */
class TecBanhuaEcommercePlatformImportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-upload"></i> 导入';

    /**
     * 处理导入请求
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function handle(Request $request = null)
    {
        if (!$request || !$request->hasFile('file')) {
            return response()->json([
                'status' => false,
                'message' => '请选择要导入的文件',
                'type' => 'error'
            ]);
        }
        
        Log::info('开始导入阪画电商平台数据');
        
        try {
            $file = $request->file('file');
            
            // 验证文件类型
            $validator = Validator::make(['file' => $file], [
                'file' => 'required|mimes:xlsx,xls',
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => '文件格式不正确，请上传Excel文件(.xlsx或.xls)',
                    'type' => 'error'
                ]);
            }
            
            // 读取Excel文件
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            
            // 确保有表头
            if (count($rows) < 2) {
                return response()->json([
                    'status' => false,
                    'message' => 'Excel文件格式不正确，至少需要包含表头和一行数据'
                ]);
            }
            
            // 获取表头
            $headers = $rows[0];
            
            // 验证必要的列
            $requiredColumns = ['平台名称', '平台代码', '平台简称', '店铺名称', '店铺网址', '负责人', '状态'];
            $headerMap = [];
            
            foreach ($requiredColumns as $required) {
                $found = false;
                foreach ($headers as $index => $header) {
                    if (trim($header) === $required) {
                        $headerMap[$required] = $index;
                        $found = true;
                        break;
                    }
                }
                if (!$found) {
                    return response()->json([
                        'status' => false,
                        'message' => "Excel文件缺少必要的列：{$required}"
                    ]);
                }
            }
            
            // 可选列
            $optionalColumns = ['联系电话', '联系邮箱', 'API密钥', 'API密钥密文', '应用ID', '应用密钥', '备注'];
            foreach ($optionalColumns as $optional) {
                foreach ($headers as $index => $header) {
                    if (trim($header) === $optional) {
                        $headerMap[$optional] = $index;
                        break;
                    }
                }
            }
            
            // 开始事务
            DB::beginTransaction();
            
            try {
                $total = 0;
                $success = 0;
                $skipped = 0;
                $errors = [];
                $batchData = [];
                
                // 处理每一行数据（从第二行开始，跳过表头）
                for ($i = 1; $i < count($rows); $i++) {
                    $row = $rows[$i];
                    $total++;
                    
                    if (empty($row[$headerMap['平台名称']])) {
                        $skipped++;
                        $errors[] = "第" . ($i + 1) . "行：平台名称不能为空";
                        continue;
                    }
                    
                    if (empty($row[$headerMap['平台代码']])) {
                        $skipped++;
                        $errors[] = "第" . ($i + 1) . "行：平台代码不能为空";
                        continue;
                    }
                    
                    if (empty($row[$headerMap['平台简称']])) {
                        $skipped++;
                        $errors[] = "第" . ($i + 1) . "行：平台简称不能为空";
                        continue;
                    }
                    
                    if (empty($row[$headerMap['店铺名称']])) {
                        $skipped++;
                        $errors[] = "第" . ($i + 1) . "行：店铺名称不能为空";
                        continue;
                    }
                    
                    if (empty($row[$headerMap['店铺网址']])) {
                        $skipped++;
                        $errors[] = "第" . ($i + 1) . "行：店铺网址不能为空";
                        continue;
                    }
                    
                    if (empty($row[$headerMap['负责人']])) {
                        $skipped++;
                        $errors[] = "第" . ($i + 1) . "行：负责人不能为空";
                        continue;
                    }
                    
                    // 状态字段处理
                    $status = 1; // 默认启用
                    if (isset($headerMap['状态']) && $row[$headerMap['状态']] !== null) {
                        $statusValue = trim($row[$headerMap['状态']]);
                        if ($statusValue === '0' || strtolower($statusValue) === 'false' || $statusValue === '禁用') {
                            $status = 0;
                        }
                    }
                    
                    // 准备数据
                    $data = [
                        'platform_name' => trim($row[$headerMap['平台名称']]),
                        'platform_code' => trim($row[$headerMap['平台代码']]),
                        'short_name' => trim($row[$headerMap['平台简称']]),
                        'shop_name' => trim($row[$headerMap['店铺名称']]),
                        'shop_url' => trim($row[$headerMap['店铺网址']]),
                        'manager_name' => trim($row[$headerMap['负责人']]),
                        'contact_phone' => isset($headerMap['联系电话']) ? trim($row[$headerMap['联系电话']] ?? '') : '',
                        'contact_email' => isset($headerMap['联系邮箱']) ? trim($row[$headerMap['联系邮箱']] ?? '') : '',
                        'api_key' => isset($headerMap['API密钥']) ? trim($row[$headerMap['API密钥']] ?? '') : '',
                        'api_secret' => isset($headerMap['API密钥密文']) ? trim($row[$headerMap['API密钥密文']] ?? '') : '',
                        'app_id' => isset($headerMap['应用ID']) ? trim($row[$headerMap['应用ID']] ?? '') : '',
                        'app_secret' => isset($headerMap['应用密钥']) ? trim($row[$headerMap['应用密钥']] ?? '') : '',
                        'remarks' => isset($headerMap['备注']) ? trim($row[$headerMap['备注']] ?? '') : '',
                        'status' => $status,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                    
                    // 将数据添加到批量操作数组
                    $batchData[] = $data;
                }
                
                // 检查平台代码是否已存在
                if (!empty($batchData)) {
                    $platformCodes = array_column($batchData, 'platform_code');
                    $existingCodes = TecBanhuaEcommercePlatformModel::whereIn('platform_code', $platformCodes)
                        ->pluck('platform_code')
                        ->toArray();
                    
                    // 分离新增和更新的数据
                    $insertData = [];
                    foreach ($batchData as $data) {
                        if (in_array($data['platform_code'], $existingCodes)) {
                            // 更新现有记录
                            TecBanhuaEcommercePlatformModel::where('platform_code', $data['platform_code'])
                                ->update([
                                    'platform_name' => $data['platform_name'],
                                    'short_name' => $data['short_name'],
                                    'shop_name' => $data['shop_name'],
                                    'shop_url' => $data['shop_url'],
                                    'manager_name' => $data['manager_name'],
                                    'contact_phone' => $data['contact_phone'],
                                    'contact_email' => $data['contact_email'],
                                    'api_key' => $data['api_key'],
                                    'app_id' => $data['app_id'],
                                    'remarks' => $data['remarks'],
                                    'status' => $data['status'],
                                    'updated_at' => now(),
                                ]);
                                
                            // 如果提供了密码字段，则更新密码
                            if (!empty($data['api_secret'])) {
                                TecBanhuaEcommercePlatformModel::where('platform_code', $data['platform_code'])
                                    ->update(['api_secret' => $data['api_secret']]);
                            }
                            
                            if (!empty($data['app_secret'])) {
                                TecBanhuaEcommercePlatformModel::where('platform_code', $data['platform_code'])
                                    ->update(['app_secret' => $data['app_secret']]);
                            }
                                
                            Log::info('更新电商平台记录', [
                                'platform_code' => $data['platform_code'],
                                'platform_name' => $data['platform_name'],
                            ]);
                            $success++;
                        } else {
                            // 添加到新增数据数组
                            $insertData[] = $data;
                        }
                    }
                    
                    // 批量插入新记录
                    if (!empty($insertData)) {
                        // 分批插入，每批最多100条
                        foreach (array_chunk($insertData, 100) as $chunk) {
                            TecBanhuaEcommercePlatformModel::insert($chunk);
                            $success += count($chunk);
                            Log::info('批量插入电商平台记录', [
                                'count' => count($chunk),
                            ]);
                        }
                    }
                }
                
                // 提交事务
                DB::commit();
                
                // 返回成功信息
                $message = "导入完成：总计 {$total} 条记录，成功导入 {$success} 条，跳过 {$skipped} 条。";
                
                if (!empty($errors)) {
                    $message .= "<br>错误信息：<br>" . implode("<br>", array_slice($errors, 0, 5));
                    if (count($errors) > 5) {
                        $message .= "<br>... 等共 " . count($errors) . " 条错误";
                    }
                    return response()->json([
                        'status' => true,
                        'message' => $message,
                        'type' => 'warning'
                    ]);
                }
                
                return response()->json([
                    'status' => true,
                    'message' => $message,
                    'type' => 'success'
                ]);
                
            } catch (\Exception $e) {
                // 回滚事务
                DB::rollBack();
                
                Log::error('导入电商平台数据失败', [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                return response()->json([
                    'status' => false,
                    'message' => '导入失败：' . $e->getMessage()
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('处理电商平台导入文件失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '处理导入文件失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 自定义HTML，使用与图库导入相同的格式
     *
     * @return string
     */
    public function html()
    {
        $token = csrf_token();
        
        Admin::script(<<<JS
$(function () {
    // 导入表单提交处理
    $('#import-ecommerce-form').on('submit', function(e) {
        e.preventDefault();
        
        var fileInput = $('#ecommerce-import-file');
        if (fileInput.val() === '') {
            Dcat.error('请选择要导入的文件');
            return false;
        }
        
        // 显示加载中
        Dcat.loading();
        
        // 提交表单数据
        var formData = new FormData(this);
        
        $.ajax({
            url: '{$this->getHandleRoute()}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                Dcat.loading(false);
                
                var type = 'success';
                if (response.status === false) {
                    type = 'error';
                } else if (response.type) {
                    type = response.type;
                }
                
                Dcat.notify(response.message, type);
                
                // 如果导入成功，关闭模态框并刷新页面
                if (response.status === true) {
                    setTimeout(function() {
                        $('#ecommerce-import-modal').modal('hide');
                        Dcat.reload();
                    }, 2000);
                }
            },
            error: function(xhr) {
                Dcat.loading(false);
                var message = '导入失败';
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                
                Dcat.error(message);
            }
        });
        
        return false;
    });
    
    // 模板下载按钮处理
    $('#download-ecommerce-template').on('click', function(e) {
        e.preventDefault();
        
        var templateUrl = '{$this->getTemplateUrl()}';
        
        // 直接打开新窗口下载，避免使用iframe
        window.open(templateUrl, '_blank');
        
        // 显示提示
        Dcat.success('模板下载中...');
        
        return false;
    });
});
JS
        );
        
        return <<<HTML
<a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline" style="margin-right:3px" data-toggle="modal" data-target="#ecommerce-import-modal">
    <i class="fa fa-upload"></i><span class="d-none d-sm-inline">&nbsp; 导入</span>
</a>

<a href="javascript:void(0);" id="download-ecommerce-template" class="btn btn-primary btn-mini btn-outline" style="margin-right:3px">
    <i class="fa fa-download"></i><span class="d-none d-sm-inline">&nbsp; 下载模板</span>
</a>

<div class="modal fade" id="ecommerce-import-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导入电商平台数据</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="import-ecommerce-form" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <p>导入说明:</p>
                        <ul>
                            <li>支持 .xlsx, .xls 格式的Excel文件</li>
                            <li>必填字段：平台名称、平台代码、平台简称、店铺名称、店铺网址、负责人</li>
                            <li>状态字段可选值：1(启用)、0(禁用)</li>
                            <li>如果平台代码已存在，将会更新对应的记录</li>
                            <li>请先下载模板，按照模板格式填写数据</li>
                        </ul>
                    </div>
                    <div class="form-group">
                        <label for="ecommerce-import-file">选择Excel文件</label>
                        <input type="file" id="ecommerce-import-file" name="file" class="form-control" accept=".xlsx,.xls" required>
                    </div>
                    <input type="hidden" name="_token" value="{$token}">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">上传</button>
                </div>
            </form>
        </div>
    </div>
</div>
HTML;
    }
    
    /**
     * 获取处理路由
     *
     * @return string
     */
    protected function getHandleRoute()
    {
        return admin_url('banhua/ecommerce-import');
    }
    
    /**
     * 获取模板下载URL
     *
     * @return string
     */
    public function getTemplateUrl()
    {
        return admin_url('banhua/ecommerce-import-template');
    }
} 