<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaLocationModel;

class TecBanhuaLocationBulkSoftDeleteAction extends BatchAction
{
    protected $title = '批量软删除';

    public function handle(Request $request)
    {
        // 获取选中的ID
        $ids = $request->input('_key', []);

        if (empty($ids)) {
            return $this->response()->error('请选择要删除的记录');
        }

        // 执行软删除，仅对未删除的记录进行操作
        $count = TecBanhuaLocationModel::whereIn('id', $ids)
            ->whereNull('deleted_at')  // 仅对未删除的记录进行软删除
            ->delete();

        if ($count > 0) {
            return $this->response()->success("成功删除 {$count} 条记录")->refresh();
        } else {
            return $this->response()->error('没有可删除的记录，可能已被删除')->refresh();
        }
    }

    public function confirm()
    {
        return ['确认批量软删除', '您确定要删除选中的记录吗？'];
    }
} 