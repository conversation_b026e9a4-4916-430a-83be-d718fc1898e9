<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Actions\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use Illuminate\Support\Facades\Log;

class TecBanhuaProductSkuBatchDeleteAction extends BatchAction
{
    /**
     * 批量操作按钮名称
     *
     * @return string
     */
    protected $title = '批量删除';

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        // 获取选中的行ID
        $keys = $this->getKey();
        
        if (empty($keys)) {
            return $this->response()
                ->error('未选择任何记录');
        }
        
        // 记录操作信息
        Log::info('执行批量删除SKU操作', [
            'user_id' => auth('admin')->id(),
            'user_name' => auth('admin')->user()->name ?? '未知用户',
            'ids' => $keys
        ]);
        
        try {
            // 使用事务保证数据一致性
            DB::beginTransaction();
            
            // 删除所选的SKU记录
            $deleted = TecBanhuaProductSkuModel::whereIn('id', $keys)->delete();
            
            DB::commit();
            
            // 记录成功信息
            Log::info('批量删除SKU成功', [
                'deleted_count' => $deleted,
                'ids' => $keys
            ]);
            
            return $this->response()
                ->success("成功删除 {$deleted} 条记录")
                ->refresh();
        } catch (\Exception $e) {
            DB::rollBack();
            
            // 记录错误信息
            Log::error('批量删除SKU失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ids' => $keys
            ]);
            
            return $this->response()
                ->error('删除失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 确认信息
     *
     * @return string
     */
    public function confirm()
    {
        return '确定要删除选中的SKU记录吗？此操作不可撤销！';
    }
    
    /**
     * 设置请求参数
     * 
     * @return array
     */
    public function parameters()
    {
        return [];
    }
} 