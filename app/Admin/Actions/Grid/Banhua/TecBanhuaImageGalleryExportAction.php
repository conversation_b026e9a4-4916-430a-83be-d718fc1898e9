<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaImageGalleryModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

/**
 * 阪画图库数据导出操作
 */
class TecBanhuaImageGalleryExportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-download"></i> 导出';

    /**
     * 处理请求
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function handle(Request $request)
    {
        try {
            // 获取筛选条件
            $imageId = $request->get('image_id');
            $name = $request->get('name');
            
            // 记录导出条件
            Log::info('导出图库数据', [
                'image_id' => $imageId,
                'name' => $name
            ]);
            
            // 构建查询
            $query = TecBanhuaImageGalleryModel::query();
            
            // 应用筛选条件
            if (!empty($imageId)) {
                $query->where('image_id', 'like', "%{$imageId}%");
            }
            
            if (!empty($name)) {
                $query->where('name', 'like', "%{$name}%");
            }
            
            // 获取数据
            $galleries = $query->get();
            
            // 如果没有数据，返回错误信息
            if ($galleries->isEmpty()) {
                return $this->response()->error('没有符合条件的数据可导出');
            }
            
            // 准备CSV数据
            $headers = [
                'ID', '图库ID', '图片名称', '图片路径', '备注', '创建时间', '更新时间'
            ];
            
            $rows = [];
            $rows[] = $headers;
            
            foreach ($galleries as $gallery) {
                $rows[] = [
                    $gallery->id,
                    $gallery->image_id,
                    $gallery->name,
                    $gallery->path,
                    $gallery->remark,
                    $gallery->created_at,
                    $gallery->updated_at
                ];
            }
            
            // 生成CSV文件
            $filename = 'banhua_gallery_export_' . date('YmdHis') . '.csv';
            $tempFile = storage_path('app/public/' . $filename);
            
            // 创建CSV文件
            $fp = fopen($tempFile, 'w');
            
            // 添加BOM头，解决中文乱码问题
            fwrite($fp, "\xEF\xBB\xBF");
            
            // 写入数据
            foreach ($rows as $row) {
                fputcsv($fp, $row);
            }
            
            fclose($fp);
            
            // 记录导出成功
            Log::info('图库导出成功', [
                'filename' => $filename,
                'count' => count($rows) - 1
            ]);
            
            // 返回文件下载响应
            return response()->download($tempFile, $filename, [
                'Content-Type' => 'text/csv; charset=UTF-8',
            ])->deleteFileAfterSend(true);
            
        } catch (\Exception $e) {
            Log::error('图库导出失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->response()->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 渲染按钮
     *
     * @return string
     */
    public function render()
    {
        // 自定义按钮样式
        return <<<HTML
<a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline export-gallery" style="margin-right:3px">
    <i class="fa fa-download"></i><span class="d-none d-sm-inline">&nbsp; 导出</span>
</a>
<script>
$(function () {
    $('.export-gallery').on('click', function () {
        // 获取当前页面的所有筛选参数
        var params = {};
        var queryString = window.location.search.substring(1);
        var pairs = queryString.split('&');
        
        for (var i = 0; i < pairs.length; i++) {
            var pair = pairs[i].split('=');
            if (pair[0] && pair[0] !== '_pjax') {
                params[pair[0]] = decodeURIComponent(pair[1] || '');
            }
        }
        
        // 构建导出URL
        var exportUrl = '{$this->getHandleRoute()}';
        var queryParams = [];
        
        for (var key in params) {
            if (params.hasOwnProperty(key)) {
                queryParams.push(key + '=' + encodeURIComponent(params[key]));
            }
        }
        
        if (queryParams.length > 0) {
            exportUrl += '?' + queryParams.join('&');
        }
        
        // 显示加载中提示
        Dcat.loading();
        
        // 下载文件
        window.location.href = exportUrl;
        
        // 延迟关闭加载提示
        setTimeout(function() {
            Dcat.loading(false);
        }, 2000);
    });
});
</script>
HTML;
    }
    
    /**
     * 获取处理URL
     *
     * @return string
     */
    public function getHandleRoute()
    {
        return admin_url('banhua/gallery-export');
    }
} 