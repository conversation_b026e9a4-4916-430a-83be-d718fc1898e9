<?php

namespace App\Admin\Actions\Grid\Banhua;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use App\Models\Banhua\TecBanhuaSkuStockModel;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\TecWarehouseModel;
use App\Models\TecWarehouseLocationModel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;

class TecBanhuaSkuStockExportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '<i class="fa fa-download"></i> 导出Excel';

    /**
     * 存储导出任务进度的缓存键前缀
     */
    protected $progressCachePrefix = 'sku_stock_export_progress_';
    
    /**
     * 启动导出任务
     */
    public function start(Request $request)
    {
        try {
            // 生成任务ID
            $taskId = uniqid('export_');
            
            // 保存任务初始状态
            $this->updateProgress($taskId, [
                'progress' => 0,
                'status' => '初始化导出任务',
                'detail' => '准备数据中...',
                'completed' => false,
                'include_images' => $request->get('include_images', '0') === '1',
                'params' => $request->all()
            ]);
            
            // 在后台处理导出任务
            $this->dispatchExportJob($taskId, $request->all());
            
            return response()->json([
                'status' => true,
                'message' => '导出任务已启动',
                'data' => [
                    'task_id' => $taskId
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('启动导出任务失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '启动导出任务失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取导出进度
     */
    public function progress(Request $request)
    {
        $taskId = $request->get('task_id');
        
        if (!$taskId) {
            return response()->json([
                'status' => false,
                'message' => '缺少任务ID'
            ]);
        }
        
        $progress = $this->getProgress($taskId);
        
        if (!$progress) {
            return response()->json([
                'status' => false,
                'message' => '找不到任务进度信息'
            ]);
        }
        
        return response()->json([
            'status' => true,
            'data' => $progress
        ]);
    }
    
    /**
     * 在后台处理导出任务
     */
    protected function dispatchExportJob($taskId, $params)
    {
        // 直接在当前请求中处理，避免额外的复杂性
        // 在实际生产环境中，应该使用队列处理
        $this->updateProgress($taskId, [
            'progress' => 5,
            'status' => '正在处理导出请求',
            'detail' => '加载数据中...',
            'completed' => false
        ]);
        
        try {
            // 调用处理方法
            $result = $this->handleExport($taskId, $params);
            
            // 更新进度为完成
            $this->updateProgress($taskId, [
                'progress' => 100,
                'status' => '导出完成',
                'detail' => '文件已准备好，即将下载',
                'completed' => true,
                'download_url' => $result['download_url'] ?? null
            ]);
            
            return true;
        } catch (\Exception $e) {
            // 记录错误
            Log::error('导出处理失败', [
                'task_id' => $taskId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 更新进度为失败
            $this->updateProgress($taskId, [
                'progress' => 100,
                'status' => '导出失败',
                'detail' => '错误: ' . $e->getMessage(),
                'completed' => true,
                'error' => true
            ]);
            
            return false;
        }
    }
    
    /**
     * 更新导出进度
     */
    protected function updateProgress($taskId, $data)
    {
        $cacheKey = $this->progressCachePrefix . $taskId;
        $expireTime = 3600; // 1小时过期
        
        // 使用文件缓存，确保在多进程间可以共享
        \Cache::put($cacheKey, $data, $expireTime);
    }
    
    /**
     * 获取导出进度
     */
    protected function getProgress($taskId)
    {
        $cacheKey = $this->progressCachePrefix . $taskId;
        return \Cache::get($cacheKey);
    }
    
    /**
     * 处理导出请求
     *
     * @param string $taskId 任务ID
     * @param array $params 请求参数
     * @return array 处理结果
     */
    protected function handleExport($taskId, $params)
    {
        // 设置更长的执行时间限制和内存限制
        set_time_limit(300); // 5分钟
        ini_set('memory_limit', '1G');
        
        // 获取筛选条件
        $warehouseId = $params['warehouse_id'] ?? null;
        $locationId = $params['location_id'] ?? null;
        $skuFilter = $params['sku_filter'] ?? null;
        $stockFilter = $params['stock_filter'] ?? null;
        $includeImages = ($params['include_images'] ?? '0') === '1'; // 是否包含图片
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 10,
            'status' => '正在查询数据',
            'detail' => '应用筛选条件...',
            'completed' => false
        ]);
        
        // 构建查询
        $query = TecBanhuaSkuStockModel::query();
        
        // 应用筛选条件
        if (!empty($warehouseId)) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        if (!empty($locationId)) {
            $query->where('location_id', $locationId);
        }
        
        if (!empty($skuFilter)) {
            $query->whereHas('sku', function ($skuQuery) use ($skuFilter) {
                $skuQuery->where('sku', 'like', "%{$skuFilter}%");
            });
        }
        
        if (!empty($stockFilter)) {
            if ($stockFilter == 'zero') {
                $query->where('stock', 0);
            } elseif ($stockFilter == 'positive') {
                $query->where('stock', '>', 0);
            }
        }
        
        // 优化：先获取SKU IDs，然后分批处理
        $stockIds = $query->pluck('id')->toArray();
        $totalCount = count($stockIds);
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 20,
            'status' => '找到 ' . $totalCount . ' 条记录',
            'detail' => '正在加载详细数据...',
            'completed' => false
        ]);
        
        // 如果没有数据，抛出异常
        if ($totalCount === 0) {
            throw new \Exception('没有符合条件的数据可导出');
        }
        
        // 处理请求的主要逻辑
        return $this->processExport($taskId, $stockIds, $includeImages);
    }
    
    /**
     * 处理导出请求的主要逻辑
     *
     * @param string $taskId 任务ID
     * @param array $stockIds 库存ID列表
     * @param bool $includeImages 是否包含图片
     * @return array 处理结果
     */
    protected function processExport($taskId, $stockIds, $includeImages)
    {
        // 如果数据量过大，分批处理
        $batchSize = 500;
        $stocks = collect();
        $totalBatches = ceil(count($stockIds) / $batchSize);
        $currentBatch = 0;
        
        // 分批查询数据
        foreach (array_chunk($stockIds, $batchSize) as $batchIds) {
            $currentBatch++;
            
            // 更新进度
            $progress = 20 + ($currentBatch / $totalBatches) * 30; // 20%-50%的进度
            $this->updateProgress($taskId, [
                'progress' => round($progress),
                'status' => '加载数据 (' . $currentBatch . '/' . $totalBatches . ')',
                'detail' => '已加载 ' . ($currentBatch * $batchSize) . '/' . count($stockIds) . ' 条记录',
                'completed' => false
            ]);
            
            $batchStocks = TecBanhuaSkuStockModel::whereIn('id', $batchIds)
                ->with([
                    'sku' => function($query) {
                        $query->select('id', 'sku', 'name', 'attr_value_ids', 'pid', 'image');
                    },
                    'warehouse' => function($query) {
                        $query->select('id', 'name');
                    },
                    'location' => function($query) {
                        $query->select('id', 'area_number', 'shelf_number', 'level_number');
                    }
                ])
                ->get();
            
            $stocks = $stocks->concat($batchStocks);
        }
        
        // 调用原始的处理方法，但传入进度更新回调
        return $this->generateExcel($taskId, $stocks, $includeImages);
    }
    
    /**
     * 生成Excel文件
     *
     * @param string $taskId 任务ID
     * @param \Illuminate\Support\Collection $stocks 库存数据
     * @param bool $includeImages 是否包含图片
     * @return array 处理结果
     */
    protected function generateExcel($taskId, $stocks, $includeImages)
    {
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 50,
            'status' => '正在创建Excel文件',
            'detail' => '准备Excel表格...',
            'completed' => false
        ]);
        
        // 创建Excel文件
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('SKU库存列表');
        
        // 设置表头
        $headers = [
            'ID', '图片', 'SKU编码', 'SKU名称', '属性组合', '仓库', '库位', 
            '库存数量', '成本价', '销售价', '批次号', '有效期', '创建时间', '更新时间'
        ];
        
        // 写入表头
        foreach ($headers as $colIndex => $header) {
            $sheet->setCellValueByColumnAndRow($colIndex + 1, 1, $header);
        }
        
        // 设置表头样式
        $headerRange = 'A1:' . Coordinate::stringFromColumnIndex(count($headers)) . '1';
        $sheet->getStyle($headerRange)->getFont()->setBold(true);
        $sheet->getStyle($headerRange)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('CCCCCC');
        $sheet->getStyle($headerRange)->getBorders()->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN);
        $sheet->getStyle($headerRange)->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);
        
        // 预加载属性和属性值，用于显示属性组合
        $attrs = \App\Models\Banhua\TecBanhuaAttrModel::with('values')->get();
        $attrValuesMap = [];
        
        // 构建属性值映射
        foreach ($attrs as $attr) {
            foreach ($attr->values as $value) {
                $attrValuesMap[$value->id] = [
                    'attr_name' => $attr->name,
                    'value_name' => $value->name
                ];
            }
        }
        
        // 设置行高和列宽
        $sheet->getRowDimension(1)->setRowHeight(30);
        $sheet->getColumnDimension('A')->setWidth(8);  // ID
        $sheet->getColumnDimension('B')->setWidth(15); // 图片
        $sheet->getColumnDimension('C')->setWidth(15); // SKU编码
        $sheet->getColumnDimension('D')->setWidth(20); // SKU名称
        $sheet->getColumnDimension('E')->setWidth(25); // 属性组合
        $sheet->getColumnDimension('F')->setWidth(15); // 仓库
        $sheet->getColumnDimension('G')->setWidth(15); // 库位
        $sheet->getColumnDimension('H')->setWidth(10); // 库存数量
        
        // 创建图片缓存
        $imageCache = [];
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 60,
            'status' => '正在处理图片',
            'detail' => '准备图片数据...',
            'completed' => false
        ]);
        
        // 只有在用户选择包含图片时才处理图片
        if ($includeImages) {
            // 创建临时目录用于存储图片
            $tempImageDir = storage_path('app/temp/export_images');
            if (!file_exists($tempImageDir)) {
                mkdir($tempImageDir, 0755, true);
            }
            
            // 处理图片下载
            $imageCache = $this->processImages($taskId, $stocks, $tempImageDir);
        }
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 80,
            'status' => '正在填充数据',
            'detail' => '写入Excel数据...',
            'completed' => false
        ]);
        
        // 写入数据
        $rowIndex = 2;
        foreach ($stocks as $stock) {
            $colIndex = 1;
            
            // 写入数据
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->id);
            
            // 图片列 - 留空，稍后添加图片
            $imageCell = $colIndex++;
            
            // 继续写入其他数据
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->sku->sku ?? '');
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->sku->name ?? '');
            
            // 属性组合
            $attrCombination = '';
            if ($stock->sku && !empty($stock->sku->attr_value_ids)) {
                $attrValueIds = is_array($stock->sku->attr_value_ids) ? 
                    $stock->sku->attr_value_ids : 
                    json_decode($stock->sku->attr_value_ids, true);
                
                if (is_array($attrValueIds)) {
                    $attrParts = [];
                    foreach ($attrValueIds as $valueId) {
                        if (isset($attrValuesMap[$valueId])) {
                            $attrParts[] = $attrValuesMap[$valueId]['attr_name'] . ':' . $attrValuesMap[$valueId]['value_name'];
                        }
                    }
                    $attrCombination = implode(', ', $attrParts);
                }
            }
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $attrCombination);
            
            // 仓库、库位、库存数量
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->warehouse->name ?? '');
            
            $locationName = '';
            if ($stock->location) {
                $locationName = $stock->location->area_number . '-' . 
                                $stock->location->shelf_number . '-' . 
                                $stock->location->level_number;
            }
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $locationName);
            
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->stock);
            
            // 成本价、销售价、批次号、有效期
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->cost_price ?? '');
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->selling_price ?? '');
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->batch_number ?? '');
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->expiry_date ?? '');
            
            // 创建时间、更新时间
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->created_at ? $stock->created_at->format('Y-m-d H:i:s') : '');
            $sheet->setCellValueByColumnAndRow($colIndex++, $rowIndex, $stock->updated_at ? $stock->updated_at->format('Y-m-d H:i:s') : '');
            
            // 如果有图片，添加到Excel
            if ($includeImages && $stock->sku && !empty($stock->sku->id) && isset($imageCache[$stock->sku->id])) {
                try {
                    $imagePath = $imageCache[$stock->sku->id];
                    if (file_exists($imagePath)) {
                        $drawing = new Drawing();
                        $drawing->setName('SKU_' . $stock->sku->id);
                        $drawing->setDescription('SKU Image');
                        $drawing->setPath($imagePath);
                        $drawing->setCoordinates(Coordinate::stringFromColumnIndex($imageCell) . $rowIndex);
                        $drawing->setOffsetX(5);
                        $drawing->setOffsetY(5);
                        $drawing->setWidth(50);
                        $drawing->setHeight(50);
                        $drawing->setWorksheet($sheet);
                        
                        // 设置行高以适应图片
                        $sheet->getRowDimension($rowIndex)->setRowHeight(60);
                    }
                } catch (\Exception $e) {
                    Log::warning('添加图片到Excel失败', [
                        'sku_id' => $stock->sku->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            $rowIndex++;
            
            // 每处理100行更新一次进度
            if ($rowIndex % 100 === 0) {
                $progress = 80 + ($rowIndex / count($stocks)) * 15; // 80%-95%的进度
                $this->updateProgress($taskId, [
                    'progress' => min(95, round($progress)),
                    'status' => '正在填充数据',
                    'detail' => '已处理 ' . ($rowIndex - 2) . '/' . count($stocks) . ' 条记录',
                    'completed' => false
                ]);
            }
        }
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 95,
            'status' => '正在保存Excel文件',
            'detail' => '即将完成...',
            'completed' => false
        ]);
        
        // 保存Excel文件
        $exportDir = storage_path('app/exports');
        if (!file_exists($exportDir)) {
            mkdir($exportDir, 0755, true);
        }
        
        $filename = 'sku_stock_export_' . date('YmdHis') . '.xlsx';
        $filePath = $exportDir . '/' . $filename;
        
        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);
        
        // 清理临时图片
        if ($includeImages) {
            foreach ($imageCache as $imagePath) {
                if (file_exists($imagePath)) {
                    @unlink($imagePath);
                }
            }
        }
        
        // 返回下载URL
        $downloadUrl = admin_url('banhua/sku-stock-export/download?file=' . urlencode($filename));
        
        return [
            'download_url' => $downloadUrl
        ];
    }
    
    /**
     * 处理图片下载
     *
     * @param string $taskId 任务ID
     * @param \Illuminate\Support\Collection $stocks 库存数据
     * @param string $tempImageDir 临时图片目录
     * @return array 图片缓存
     */
    protected function processImages($taskId, $stocks, $tempImageDir)
    {
        // 创建图片缓存
        $imageCache = [];
        
        // 直接从SKU图片获取，更简单可靠
        $skuImages = [];
        $uniqueUrls = []; // 用于去重URL
        $urlToSkuMap = []; // 记录URL对应的SKU IDs
        
        // 收集图片URL并去重
        foreach ($stocks as $stock) {
            if (!empty($stock->sku) && !empty($stock->sku->image)) {
                $skuId = $stock->sku->id;
                $imageUrl = $stock->sku->image;
                $skuImages[$skuId] = $imageUrl;
                
                // 记录唯一URL
                if (!in_array($imageUrl, $uniqueUrls)) {
                    $uniqueUrls[] = $imageUrl;
                    $urlToSkuMap[$imageUrl] = [];
                }
                
                // 记录使用相同URL的SKU IDs
                $urlToSkuMap[$imageUrl][] = $skuId;
            }
        }
        
        // 更新进度
        $this->updateProgress($taskId, [
            'progress' => 65,
            'status' => '处理图片',
            'detail' => '共 ' . count($uniqueUrls) . ' 个唯一图片，总计 ' . count($skuImages) . ' 个SKU图片',
            'completed' => false
        ]);
        
        // 批量下载唯一图片
        if (!empty($uniqueUrls)) {
            // 分批处理图片下载，避免一次性处理太多
            $batchSize = 50;
            $processed = 0;
            $urlImageCache = []; // 临时存储URL到图片路径的映射
            
            foreach (array_chunk($uniqueUrls, $batchSize) as $batchUrls) {
                foreach ($batchUrls as $imageUrl) {
                    // 为URL创建唯一文件名
                    $urlHash = md5($imageUrl);
                    $tempImagePath = $tempImageDir . '/img_' . $urlHash . '.jpg';
                    
                    try {
                        // 下载图片到临时文件
                        $imageContent = @file_get_contents($imageUrl);
                        if ($imageContent !== false) {
                            file_put_contents($tempImagePath, $imageContent);
                            $urlImageCache[$imageUrl] = $tempImagePath;
                            
                            // 将图片路径应用到所有使用此URL的SKU
                            if (isset($urlToSkuMap[$imageUrl])) {
                                foreach ($urlToSkuMap[$imageUrl] as $skuId) {
                                    $imageCache[$skuId] = $tempImagePath;
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        Log::warning('预处理图片下载失败', [
                            'image_url' => $imageUrl,
                            'error' => $e->getMessage()
                        ]);
                    }
                    
                    $processed++;
                    if ($processed % 10 === 0) {
                        $percentage = 65 + ($processed / count($uniqueUrls)) * 10; // 65%-75%的进度
                        $this->updateProgress($taskId, [
                            'progress' => round($percentage),
                            'status' => '下载图片中',
                            'detail' => '已处理 ' . $processed . '/' . count($uniqueUrls) . ' 个图片',
                            'completed' => false
                        ]);
                    }
                }
            }
        }
        
        return $imageCache;
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function handle(Request $request)
    {
        try {
            // 设置更长的执行时间限制和内存限制
            set_time_limit(300); // 5分钟
            ini_set('memory_limit', '1G');
            
            // 获取筛选条件
            $warehouseId = $request->get('warehouse_id');
            $locationId = $request->get('location_id');
            $skuFilter = $request->get('sku_filter');
            $stockFilter = $request->get('stock_filter');
            $includeImages = $request->get('include_images', '0') === '1'; // 是否包含图片
            
            // 记录导出条件
            Log::info('导出SKU库存数据', [
                'warehouse_id' => $warehouseId,
                'location_id' => $locationId,
                'sku_filter' => $skuFilter,
                'stock_filter' => $stockFilter,
                'include_images' => $includeImages
            ]);
            
            // 构建查询
            $query = TecBanhuaSkuStockModel::query();
            
            // 应用筛选条件
            if (!empty($warehouseId)) {
                $query->where('warehouse_id', $warehouseId);
            }
            
            if (!empty($locationId)) {
                $query->where('location_id', $locationId);
            }
            
            if (!empty($skuFilter)) {
                $query->whereHas('sku', function ($skuQuery) use ($skuFilter) {
                    $skuQuery->where('sku', 'like', "%{$skuFilter}%");
                });
            }
            
            if (!empty($stockFilter)) {
                if ($stockFilter == 'zero') {
                    $query->where('stock', 0);
                } elseif ($stockFilter == 'positive') {
                    $query->where('stock', '>', 0);
                }
            }
            
            // 优化：先获取SKU IDs，然后分批处理
            $stockIds = $query->pluck('id')->toArray();
            $totalCount = count($stockIds);
            
            // 记录总数
            Log::info('准备导出SKU库存数据', [
                'total_count' => $totalCount
            ]);
            
            // 如果数据量过大，分批处理
            $batchSize = 500;
            $stocks = collect();
            
            // 分批查询数据
            foreach (array_chunk($stockIds, $batchSize) as $batchIds) {
                $batchStocks = TecBanhuaSkuStockModel::whereIn('id', $batchIds)
                    ->with([
                        'sku' => function($query) {
                            $query->select('id', 'sku', 'name', 'attr_value_ids', 'pid', 'image');
                        },
                        'sku.gallery' => function($query) {
                            $query->select('id', 'path');
                        },
                        'warehouse' => function($query) {
                            $query->select('id', 'name');
                        },
                        'location' => function($query) {
                            $query->select('id', 'area_number', 'shelf_number', 'level_number');
                        }
                    ])
                    ->get();
                
                $stocks = $stocks->concat($batchStocks);
            }
            
            // 如果没有数据，返回错误信息
            if ($stocks->isEmpty()) {
                return $this->response()->error('没有符合条件的数据可导出');
            }
            
            // 创建Excel文件
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('SKU库存列表');
            
            // 设置表头
            $headers = [
                'ID', '图片', 'SKU编码', 'SKU名称', '属性组合', '仓库', '库位', 
                '库存数量', '成本价', '销售价', '批次号', '有效期', '创建时间', '更新时间'
            ];
            
            // 写入表头
            foreach ($headers as $colIndex => $header) {
                $sheet->setCellValueByColumnAndRow($colIndex + 1, 1, $header);
            }
            
            // 设置表头样式
            $headerRange = 'A1:' . Coordinate::stringFromColumnIndex(count($headers)) . '1';
            $sheet->getStyle($headerRange)->getFont()->setBold(true);
            $sheet->getStyle($headerRange)->getFill()
                ->setFillType(Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            $sheet->getStyle($headerRange)->getBorders()->getAllBorders()
                ->setBorderStyle(Border::BORDER_THIN);
            $sheet->getStyle($headerRange)->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_CENTER)
                ->setVertical(Alignment::VERTICAL_CENTER);
            
            // 预加载属性和属性值，用于显示属性组合
            $attrs = \App\Models\Banhua\TecBanhuaAttrModel::with('values')->get();
            $attrValuesMap = [];
            
            // 构建属性值映射
            foreach ($attrs as $attr) {
                foreach ($attr->values as $value) {
                    $attrValuesMap[$value->id] = [
                        'attr_name' => $attr->name,
                        'value_name' => $value->name
                    ];
                }
            }
            
            // 设置行高和列宽
            $sheet->getRowDimension(1)->setRowHeight(30);
            $sheet->getColumnDimension('A')->setWidth(8);  // ID
            $sheet->getColumnDimension('B')->setWidth(15); // 图片
            $sheet->getColumnDimension('C')->setWidth(15); // SKU编码
            $sheet->getColumnDimension('D')->setWidth(20); // SKU名称
            $sheet->getColumnDimension('E')->setWidth(25); // 属性组合
            $sheet->getColumnDimension('F')->setWidth(15); // 仓库
            $sheet->getColumnDimension('G')->setWidth(15); // 库位
            $sheet->getColumnDimension('H')->setWidth(10); // 库存数量
            
            // 创建图片缓存
            $imageCache = [];
            
            // 只有在用户选择包含图片时才处理图片
            if ($includeImages) {
                // 创建临时目录用于存储图片
                $tempImageDir = storage_path('app/temp/export_images');
                if (!file_exists($tempImageDir)) {
                    mkdir($tempImageDir, 0755, true);
                }
                
                                // 直接从SKU图片获取，更简单可靠
                $skuImages = [];
                $uniqueUrls = []; // 用于去重URL
                $urlToSkuMap = []; // 记录URL对应的SKU IDs
                
                // 收集图片URL并去重
                foreach ($stocks as $stock) {
                    if (!empty($stock->sku) && !empty($stock->sku->image)) {
                        $skuId = $stock->sku->id;
                        $imageUrl = $stock->sku->image;
                        $skuImages[$skuId] = $imageUrl;
                        
                        // 记录唯一URL
                        if (!in_array($imageUrl, $uniqueUrls)) {
                            $uniqueUrls[] = $imageUrl;
                            $urlToSkuMap[$imageUrl] = [];
                        }
                        
                        // 记录使用相同URL的SKU IDs
                        $urlToSkuMap[$imageUrl][] = $skuId;
                    }
                }
                
                Log::info('预处理SKU图片', [
                    'sku_images_count' => count($skuImages),
                    'unique_urls' => count($uniqueUrls),
                    'duplicate_rate' => count($skuImages) > 0 ? 
                        round((1 - count($uniqueUrls) / count($skuImages)) * 100, 2) . '%' : '0%'
                ]);
                
                // 批量下载唯一图片
                if (!empty($uniqueUrls)) {
                    // 分批处理图片下载，避免一次性处理太多
                    $batchSize = 50;
                    $processed = 0;
                    $urlImageCache = []; // 临时存储URL到图片路径的映射
                    
                    foreach (array_chunk($uniqueUrls, $batchSize) as $batchUrls) {
                        foreach ($batchUrls as $imageUrl) {
                            // 为URL创建唯一文件名
                            $urlHash = md5($imageUrl);
                            $tempImagePath = $tempImageDir . '/img_' . $urlHash . '.jpg';
                            
                            try {
                                // 下载图片到临时文件
                                $imageContent = @file_get_contents($imageUrl);
                                if ($imageContent !== false) {
                                    file_put_contents($tempImagePath, $imageContent);
                                    $urlImageCache[$imageUrl] = $tempImagePath;
                                    
                                    // 将图片路径应用到所有使用此URL的SKU
                                    if (isset($urlToSkuMap[$imageUrl])) {
                                        foreach ($urlToSkuMap[$imageUrl] as $skuId) {
                                            $imageCache[$skuId] = $tempImagePath;
                                        }
                                    }
                                }
                            } catch (\Exception $e) {
                                Log::warning('预处理图片下载失败', [
                                    'image_url' => $imageUrl,
                                    'error' => $e->getMessage()
                                ]);
                            }
                            
                            $processed++;
                            if ($processed % 10 === 0) {
                                $percentage = round(($processed / count($uniqueUrls)) * 100, 2);
                                Log::info('图片下载进度', [
                                    'processed' => $processed,
                                    'total' => count($uniqueUrls),
                                    'percentage' => $percentage . '%'
                                ]);
                            }
                        }
                    }
                    
                    Log::info('图片下载完成', [
                        'total_images' => count($skuImages),
                        'unique_images_downloaded' => count($urlImageCache),
                        'cached_sku_images' => count($imageCache)
                    ]);
                }
                
                Log::info('图片预处理完成', ['cached_images' => count($imageCache)]);
            } else {
                Log::info('用户选择不包含图片，跳过图片处理');
                $tempImageDir = null;
            }
            
            // 写入数据
            $rowIndex = 2;
            $processedCount = 0;
            $totalCount = $stocks->count();
            
            foreach ($stocks as $stock) {
                // 每处理100条记录记录一次进度
                $processedCount++;
                if ($processedCount % 100 === 0) {
                    Log::info('导出进度', [
                        'processed' => $processedCount,
                        'total' => $totalCount,
                        'percentage' => round(($processedCount / $totalCount) * 100, 2) . '%'
                    ]);
                }
                
                // 获取属性值文本
                $attrValuesText = '';
                if (!empty($stock->sku) && !empty($stock->sku->attr_value_ids)) {
                    $attrValues = [];
                    foreach ($stock->sku->attr_value_ids as $valueId) {
                        if (isset($attrValuesMap[$valueId])) {
                            $attrValues[] = $attrValuesMap[$valueId]['value_name'];
                        }
                    }
                    $attrValuesText = implode(', ', $attrValues);
                }
                
                // 写入数据
                $sheet->setCellValueByColumnAndRow(1, $rowIndex, $stock->id);
                // 图片列留空，稍后添加
                $sheet->setCellValueByColumnAndRow(3, $rowIndex, $stock->sku ? $stock->sku->sku : '');
                $sheet->setCellValueByColumnAndRow(4, $rowIndex, $stock->sku ? $stock->sku->name : '');
                $sheet->setCellValueByColumnAndRow(5, $rowIndex, $attrValuesText);
                $sheet->setCellValueByColumnAndRow(6, $rowIndex, $stock->warehouse ? $stock->warehouse->name : '');
                $sheet->setCellValueByColumnAndRow(7, $rowIndex, $stock->location ? $stock->location->location_name : '');
                $sheet->setCellValueByColumnAndRow(8, $rowIndex, $stock->stock);
                $sheet->setCellValueByColumnAndRow(9, $rowIndex, $stock->cost_price);
                $sheet->setCellValueByColumnAndRow(10, $rowIndex, $stock->sale_price);
                $sheet->setCellValueByColumnAndRow(11, $rowIndex, $stock->batch_no);
                $sheet->setCellValueByColumnAndRow(12, $rowIndex, $stock->expire_date);
                $sheet->setCellValueByColumnAndRow(13, $rowIndex, $stock->created_at);
                $sheet->setCellValueByColumnAndRow(14, $rowIndex, $stock->updated_at);
                
                // 设置行高，为图片留出空间
                $sheet->getRowDimension($rowIndex)->setRowHeight(60);
                
                // 只有在用户选择包含图片时才添加图片
                if ($includeImages && !empty($stock->sku)) {
                    $skuId = $stock->sku->id;
                    
                    // 检查是否有缓存的图片
                    if (isset($imageCache[$skuId])) {
                        $tempImagePath = $imageCache[$skuId];
                        
                        // 添加图片到Excel
                        $drawing = new Drawing();
                        $drawing->setName('SKU图片');
                        $drawing->setDescription('SKU图片');
                        $drawing->setPath($tempImagePath);
                        $drawing->setCoordinates('B' . $rowIndex);
                        $drawing->setOffsetX(5);
                        $drawing->setOffsetY(5);
                        $drawing->setWidth(50);
                        $drawing->setHeight(50);
                        $drawing->setWorksheet($sheet);
                    }
                    // 如果没有缓存但有图片URL，直接下载
                    else if (!empty($stock->sku->image) && $tempImageDir) {
                        $imageUrl = $stock->sku->image;
                        $tempImagePath = $tempImageDir . '/sku_' . $skuId . '_row_' . $rowIndex . '.jpg';
                        
                        try {
                            // 下载图片到临时文件
                            $imageContent = @file_get_contents($imageUrl);
                            if ($imageContent !== false) {
                                file_put_contents($tempImagePath, $imageContent);
                                
                                // 添加图片到Excel
                                $drawing = new Drawing();
                                $drawing->setName('SKU图片');
                                $drawing->setDescription('SKU图片');
                                $drawing->setPath($tempImagePath);
                                $drawing->setCoordinates('B' . $rowIndex);
                                $drawing->setOffsetX(5);
                                $drawing->setOffsetY(5);
                                $drawing->setWidth(50);
                                $drawing->setHeight(50);
                                $drawing->setWorksheet($sheet);
                            }
                        } catch (\Exception $e) {
                            // 图片处理失败，记录日志但继续处理
                            Log::warning('导出图片处理失败', [
                                'sku_id' => $skuId,
                                'row_index' => $rowIndex,
                                'image_url' => $imageUrl,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }
                
                $rowIndex++;
            }
            
            Log::info('数据写入完成，开始设置样式', [
                'rows' => $rowIndex - 2
            ]);
            
            // 设置所有单元格边框 - 优化：分批设置样式，避免一次性处理太多
            $batchSize = 1000; // 每批处理的行数
            $lastRow = $rowIndex - 1;
            $colCount = count($headers);
            $lastCol = Coordinate::stringFromColumnIndex($colCount);
            
            // 分批设置边框样式
            for ($startRow = 1; $startRow <= $lastRow; $startRow += $batchSize) {
                $endRow = min($startRow + $batchSize - 1, $lastRow);
                $rangeToStyle = 'A' . $startRow . ':' . $lastCol . $endRow;
                $sheet->getStyle($rangeToStyle)->getBorders()->getAllBorders()
                    ->setBorderStyle(Border::BORDER_THIN);
                
                Log::info('设置样式进度', [
                    'range' => $rangeToStyle,
                    'progress' => round(($endRow / $lastRow) * 100, 2) . '%'
                ]);
            }
            
            // 设置数字列的对齐方式 - 同样分批处理
            for ($startRow = 2; $startRow <= $lastRow; $startRow += $batchSize) {
                $endRow = min($startRow + $batchSize - 1, $lastRow);
                $sheet->getStyle('H' . $startRow . ':J' . $endRow)->getAlignment()
                    ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            }
            
            Log::info('样式设置完成，开始保存文件');
            
            // 生成Excel文件
            $filename = 'banhua_sku_stock_export_' . date('YmdHis') . '.xlsx';
            $tempFile = storage_path('app/public/' . $filename);
            
            // 使用低内存模式写入文件
            $writer = new Xlsx($spreadsheet);
            $writer->setPreCalculateFormulas(false); // 禁用公式预计算
            
            // 设置写入选项，优化内存使用
            $writer->setOffice2003Compatibility(false);
            $writer->save($tempFile);
            
            // 释放内存
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet);
            
            Log::info('Excel文件保存完成，开始清理临时文件');
            
            // 清理临时图片
            if ($includeImages && $tempImageDir && file_exists($tempImageDir)) {
                $files = glob($tempImageDir . '/*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        @unlink($file);
                    }
                }
                @rmdir($tempImageDir);
            }
            
            // 记录导出成功
            Log::info('SKU库存导出成功', [
                'filename' => $filename,
                'count' => $rowIndex - 2,
                'memory_peak' => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB'
            ]);
            
            // 返回文件下载响应
            return response()->download($tempFile, $filename, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ])->deleteFileAfterSend(true);
            
        } catch (\Exception $e) {
            Log::error('SKU库存导出失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->response()->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 渲染按钮
     *
     * @return string
     */
    public function render()
    {
        // 自定义按钮样式
        return <<<HTML
<div class="btn-group" role="group">
    <a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline export-sku-stock" data-include-images="0" style="margin-right:3px">
        <i class="fa fa-download"></i><span class="d-none d-sm-inline">&nbsp; 导出Excel</span>
    </a>
    <a href="javascript:void(0);" class="btn btn-primary btn-mini btn-outline export-sku-stock" data-include-images="1" style="margin-right:3px">
        <i class="fa fa-download"></i><span class="d-none d-sm-inline">&nbsp; 导出Excel(含图片)</span>
    </a>
</div>
<!-- 导出进度对话框 -->
<div class="modal fade" id="exportProgressModal" tabindex="-1" role="dialog" aria-labelledby="exportProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportProgressModalLabel">导出进度</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3" id="exportStatusText">正在准备导出数据...</div>
                <div class="progress">
                    <div id="exportProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                </div>
                <div class="mt-3 small text-muted" id="exportDetailText"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>
<script>
$(function () {
    // 创建导出进度检查函数
    var checkExportProgress = function(taskId, includeImages) {
        var progressCheckCount = 0;
        var progressInterval = setInterval(function() {
            $.ajax({
                url: '{$this->getHandleRoute()}/progress',
                data: { task_id: taskId },
                success: function(response) {
                    if (response.status) {
                        // 更新进度条
                        var progress = response.data.progress || 0;
                        var status = response.data.status || '处理中';
                        var detail = response.data.detail || '';
                        
                        $('#exportProgressBar').css('width', progress + '%').attr('aria-valuenow', progress);
                        $('#exportStatusText').text(status);
                        $('#exportDetailText').text(detail);
                        
                        // 如果完成，停止检查并下载文件
                        if (response.data.completed) {
                            clearInterval(progressInterval);
                            
                            // 关闭进度条
                            setTimeout(function() {
                                $('#exportProgressModal').modal('hide');
                                
                                // 如果有下载URL，触发下载
                                if (response.data.download_url) {
                                    window.location.href = response.data.download_url;
                                }
                            }, 1000);
                        }
                    } else {
                        // 出错时显示错误信息
                        $('#exportStatusText').text('导出出错: ' + (response.message || '未知错误'));
                        $('#exportDetailText').text('请稍后重试或联系管理员');
                        clearInterval(progressInterval);
                    }
                },
                error: function() {
                    progressCheckCount++;
                    
                    // 如果连续5次检查失败，显示错误信息
                    if (progressCheckCount >= 5) {
                        $('#exportStatusText').text('无法获取导出进度');
                        $('#exportDetailText').text('服务器可能正忙，但导出仍在后台进行，请稍后查看导出结果');
                        clearInterval(progressInterval);
                    }
                }
            });
        }, 2000); // 每2秒检查一次进度
    };
    
    // 导出按钮点击事件
    $('.export-sku-stock').on('click', function () {
        // 获取是否包含图片选项
        var includeImages = $(this).data('include-images');
        
        // 获取当前页面的所有筛选参数
        var params = {};
        var queryString = window.location.search.substring(1);
        var pairs = queryString.split('&');
        
        for (var i = 0; i < pairs.length; i++) {
            var pair = pairs[i].split('=');
            if (pair[0] && pair[0] !== '_pjax') {
                params[pair[0]] = decodeURIComponent(pair[1] || '');
            }
        }
        
        // 添加是否包含图片参数
        params['include_images'] = includeImages;
        
        // 显示进度条对话框
        $('#exportProgressModal').modal('show');
        $('#exportProgressBar').css('width', '0%').attr('aria-valuenow', 0);
        $('#exportStatusText').text(includeImages === "1" ? '正在准备导出带图片的Excel...' : '正在准备导出Excel...');
        $('#exportDetailText').text('初始化导出任务...');
        
        // 发起导出请求
        $.ajax({
            url: '{$this->getHandleRoute()}/start',
            type: 'POST',
            data: params,
            success: function(response) {
                if (response.status && response.data.task_id) {
                    // 开始检查进度
                    checkExportProgress(response.data.task_id, includeImages);
                } else {
                    // 显示错误信息
                    $('#exportStatusText').text('启动导出任务失败: ' + (response.message || '未知错误'));
                    $('#exportDetailText').text('请稍后重试');
                }
            },
            error: function(xhr) {
                var errorMsg = '启动导出任务失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg += ': ' + xhr.responseJSON.message;
                }
                $('#exportStatusText').text(errorMsg);
                $('#exportDetailText').text('请稍后重试');
            }
        });
    });
});
</script>
HTML;
    }
    
    /**
     * 获取处理URL
     *
     * @return string
     */
    public function getHandleRoute()
    {
        return admin_url('banhua/sku-stock-export');
    }
    
    /**
     * 下载导出的文件
     */
    public function download(Request $request)
    {
        $filename = $request->get('file');
        
        if (!$filename) {
            return response()->json(['status' => false, 'message' => '缺少文件名参数']);
        }
        
        // 安全检查，确保文件名不包含路径
        $filename = basename($filename);
        $filePath = storage_path('app/exports/' . $filename);
        
        if (!file_exists($filePath)) {
            return response()->json(['status' => false, 'message' => '文件不存在']);
        }
        
        // 下载完成后删除文件
        $response = response()->download($filePath, $filename, [], 'inline');
        $response->deleteFileAfterSend(true);
        
        return $response;
    }
} 