<?php

/*
 * // +----------------------------------------------------------------------
 * // | erp
 * // +----------------------------------------------------------------------
 * // | Copyright (c) 2006~2020 erp All rights reserved.
 * // +----------------------------------------------------------------------
 * // | Licensed ( LICENSE-1.0.0 )
 * // +----------------------------------------------------------------------
 * // | Author: yxx <<EMAIL>>
 * // +----------------------------------------------------------------------
 */

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BatchCreatePro extends AbstractTool
{
    /**
     * @return string
     */
    protected $title = '批量新增';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */

    public function html()
    {
        return <<<HTML
<span {$this->formatHtmlAttributes()} id="my-more-create-select-resourc" href="javascript:void(0)"><button class="btn btn-primary dialog-create  btn-mini"><i
class="feather icon-plus"></i> {$this->title()}</button></span>
HTML;
    }

    public function script()
    {
        // if (!defined('Dcat\Admin\Grid::IFRAME_QUERY_NAME')) {
        //     define('Dcat\Admin\Grid::IFRAME_QUERY_NAME', 1);
        // }
        $url = route('dcat.admin.products.index', [
            // Grid::IFRAME_QUERY_NAME => 1,
            true,
            'order_model' => $this->getModel(),
            'order_id' => $this->getOrderId(),
        ]);

        return <<<JS
        $("#my-more-create-select-resourc").on("click",function(){
            var url = "{$url}";
            layer.open({
                type: 2,
                area: ['70%', '90%'], //宽高
                content:[url,'no'],
                end: function(){
                    Dcat.reload();
                }
            })
        });

JS;
    }

    public function getModel(): string
    {
        return admin_controller_name() . 'Model';
    }

    public function getOrderId(): int
    {
        // 首先尝试从路由参数获取
        $orderId = request()->route()->parameter($this->getTable());
        
        // 如果从路由参数获取失败，尝试从 Grid 实例获取
        if ($orderId === null && $this->parent instanceof Grid) {
            $model = $this->parent->model()->getModel();
            $orderId = $model->id ?? null;
        }
        
        // 记录日志以便调试
        \Log::info('BatchCreatePro getOrderId Debug', [
            'route_param' => $orderId,
            'table' => $this->getTable(),
            'parent_exists' => $this->parent instanceof Grid,
            'parent_model_id' => $this->parent ? ($this->parent->model()->getModel()->id ?? 'N/A') : 'No Parent'
        ]);
        
        // 如果仍然为 null，返回 0
        return $orderId ? (int)$orderId : 0;
    }

    /**
     * @return string
     */
    public function getTable(): string
    {
        return Str::snake(admin_controller_name());
    }
}
