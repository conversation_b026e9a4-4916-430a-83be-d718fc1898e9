<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cCustomerPurchaseRequest;

use App\Models\TecH3cCustomerPurchaseOrderModel;
use App\Models\TecH3cCustomerPurchaseRequestModel;
use App\Models\TecH3cPurchaseBaseModel;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Support\Facades\DB;

class TecH3cCustomerPurchaseRequestPaymentAction extends AbstractTool
{
    /**
     * 按钮标题
     * @var string
     */
    protected $title = '确认收款';

    /**
     * 获取按钮标题（带图标）
     * @return string
     */
    public function title()
    {
        return '<i class="fa fa-money"></i> ' . $this->title;
    }

    /**
     * 生成按钮HTML
     * @return string
     */
    protected function html()
    {
        // 生成唯一ID
        $id = 'h3c-customer-purchase-request-payment-btn';

        // 设置HTML属性
        $this->setHtmlAttribute([
            'class' => 'btn btn-primary',
            'data-action' => 'payment',
            'id' => $id,
            'title' => '批量确认收款'
        ]);
        $attributes = $this->formatHtmlAttributes();

        return <<<HTML
<button {$attributes}>{$this->title()}</button>
HTML;
    }

    /**
     * 渲染按钮及JS
     * @return string
     */
    public function render()
    {
        return $this->html() . $this->script() . $this->paymentDateModal();
    }

    /**
     * 付款日期选择模态框
     * @return string
     */
    protected function paymentDateModal()
    {
        return <<<HTML
<div class="modal fade" id="payment-date-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择收款日期</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>收款日期</label>
                    <input type="date" id="payment-date" class="form-control" value="{$this->getTodayDate()}">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-payment-date">确认</button>
            </div>
        </div>
    </div>
</div>
HTML;
    }

    /**
     * 获取今天的日期
     * @return string
     */
    protected function getTodayDate(): string
    {
        return date('Y-m-d');
    }

    /**
     * 生成JS脚本
     * @return string
     */
    protected function script()
    {
        return <<<'JS'
<script>
$(document).ready(function() {
    // 点击付款处理按钮
    $('[data-action="payment"]').on('click', function() {
        // 显示模态框
        $('#payment-date-modal').modal('show');
    });

    // 确认付款日期
    $('#confirm-payment-date').on('click', function() {
        var selectedDate = $('#payment-date').val();
        var selectedRows = Dcat.grid.selected();

        if (selectedRows.length === 0) {
            Dcat.error('请选择要确认收款的记录');
            return;
        }

        Dcat.loading();
        
        $.ajax({
            method: 'POST',
            url: '/admin/h3c_customer_purchase_requests/batch_payment',
            data: {
                ids: selectedRows,
                payment_date: selectedDate
            },
            success: function(response) {
                Dcat.loading(false);
                if (response.status) {
                    Dcat.success(response.message);
                    Dcat.reload();
                } else {
                    Dcat.error(response.message);
                }
            },
            error: function(xhr) {
                Dcat.loading(false);
                Dcat.error('确认收款失败');
            }
        });

        $('#payment-date-modal').modal('hide');
    });
});
</script>
JS;
    }
}
