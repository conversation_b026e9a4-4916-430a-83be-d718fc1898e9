<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cCustomerPurchaseRequest;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * H3C客户請求书一览导出Action
 * 用法与TecH3cPurchaseOrderExportAction一致
 */
class TecH3cCustomerPurchaseRequestExportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '导出請求书';

    public function title()
    {
        return '<i class="fa fa-download"></i> ' . $this->title;
    }

    protected function html()
    {
        $id = 'h3c-customer-request-export-btn';
        $this->setHtmlAttribute([
            'class' => 'btn btn-primary',
            'data-action' => 'export',
            'id' => $id,
            'title' => '导出請求书及明细数据'
        ]);
        $attributes = $this->formatHtmlAttributes();
        Log::info('生成客户請求书导出按钮HTML', ['id' => $id, 'attributes' => $attributes]);
        return <<<HTML
<button {$attributes}>{$this->title()}</button>
HTML;
    }

    /**
     * 构建导出URL
     *
     * @return string
     */
    protected function buildExportUrl()
    {
        // 修正路由前缀，保持与后台实际路由一致
        $url = admin_url('r_h3c_customer_orders/h3c_customer_purchase_requests_export');
        if ($queries = request()->all()) {
            $url .= '?' . http_build_query($queries);
        }
        Log::info('构建客户請求书导出URL', ['url' => $url, 'queries' => $queries ?? []]);
        return $url;
    }

    /**
     * 渲染方法
     */
    public function render()
    {
        Log::info('开始渲染客户請求书导出按钮');
        return $this->html() . $this->script();
    }

    /**
     * 初始化导出功能的JavaScript
     *
     * @return string
     */
    protected function script()
    {
        // 直接打开导出链接，无需 AJAX
        $url = admin_url('r_h3c_customer_orders/h3c_customer_purchase_requests_export');
        return <<<JS
<script>
$(function () {
    $('#h3c-customer-request-export-btn').off('click').on('click', function() {
        Dcat.NP.start();
        var query = $('.grid-filter-form').serialize();
        var link = '{$url}' + (query ? ('?' + query) : '');
        window.open(link, '_blank');
        setTimeout(function() { Dcat.NP.done(); }, 500);
    });
});
</script>
JS;
    }

    /**
     * 处理导出请求（通常不会被直接调用）
     *
     * @param Request $request
     * @return mixed
     */
    public function handle(Request $request)
    {
        return $this->response()->success('正在导出...')->refresh();
    }
}
