<?php

namespace App\Admin\Actions\Grid\TecH3cCustomerOrder;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\TecSndContactModel;
use App\Models\TecH3cProductListModel;
use App\Admin\Actions\Grid\ImportAction;

class TecH3cCustomerOrderImportAction extends AbstractTool
{
    protected $title = '导入订单';

    public function html()
    {
        $modal = Modal::make()
            ->lg()
            ->title($this->title)
            ->body($this->form())
            ->button('<button class="btn btn-primary"><i class="feather icon-upload"></i> ' . $this->title . '</button>');

        return $modal->render();
    }

    protected function form()
    {
        return <<<HTML
        <form method="post" action="{$this->getHandleRoute()}" enctype="multipart/form-data" pjax-container>
            <div class="form-group">
                <input type="file" name="import_file" class="form-control" accept="application/json" required/>
                <small class="form-text text-muted">请选择要导入的JSON文件</small>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">确定</button>
            </div>
        </form>
        HTML;
    }

    protected function getHandleRoute()
    {
        return admin_url('r_h3c_customer_orders/import');
    }

    public function handle(Request $request)
    {
        try {
            Log::info('开始处理文件上传');

            if (!$request->hasFile('import_file')) {
                Log::error('未找到上传文件');
                return $this->response()->error('未找到上传文件');
            }

            $file = $request->file('import_file');
            Log::info('上传的文件名: ' . $file->getClientOriginalName());

            if ($file->getClientOriginalExtension() !== 'json') {
                Log::error('上传的文件不是JSON文件');
                return $this->response()->error('请上传JSON文件');
            }

            $jsonFilePath = $file->getPathname();
            $jsonContent = file_get_contents($jsonFilePath);

            // 记录上传的 JSON 内容
            Log::info('上传的 JSON 内容:', ['content' => $jsonContent]);

            $jsonData = json_decode($jsonContent, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON 解析错误: ' . json_last_error_msg());
                return $this->response()->error('JSON 解析失败: ' . json_last_error_msg());
            }

            // 提取注文番号和担当信息
            $orderNumber = null;
            $representative = null;
            $representativeName = null;

            foreach ($jsonData as $item) {
                if (isset($item['data'])) {
                    foreach ($item['data'] as $row) {
                        // 提取注文番号
                        if (!empty($row[0]['text']) && strpos($row[0]['text'], '注文番号') !== false) {
                            $orderNumber = trim($row[1]['text']);
                        }
                        // 提取担当信息
                        if (!empty($row[0]['text']) && strpos($row[0]['text'], '担当') !== false) {
                            $representative = trim($row[0]['text']);
                        }
                        // 提取担当者姓名
                        if (!empty($row[0]['text']) && mb_strlen($row[0]['text']) <= 10 && strpos($row[0]['text'], ' ') !== false) {
                            $representativeName = trim($row[0]['text']);
                        }
                    }
                }
            }

            if (!isset($orderNumber)) {
                Log::error('无法从文本中提取注文番号');
                return $this->response()->error('无法提取注文番号');
            }

            // 提取表格明细逻辑
            $tableDetails = [];
            $isTableHeader = false;
            $headerIndexes = [];
            $currentProduct = null;

            foreach ($jsonData as $item) {
                if (isset($item['data'])) {
                    foreach ($item['data'] as $row) {
                        // 检查是否为表头行
                        if (!empty($row) && count($row) >= 5) {
                            $headerTexts = array_column($row, 'text');
                            if (in_array('No', $headerTexts) && in_array('製  品  名', $headerTexts)) {
                                $isTableHeader = true;
                                // 记录各列的索引
                                foreach ($row as $index => $cell) {
                                    switch ($cell['text']) {
                                        case '数量':
                                            $headerIndexes['quantity'] = $index;
                                            break;
                                        case '単 価':
                                            $headerIndexes['unit_price'] = $index;
                                            break;
                                        case '金 額':
                                            $headerIndexes['amount'] = $index;
                                            break;
                                    }
                                }
                                continue;
                            }
                        }

                        // 如果已找到表头，开始提取数据
                        if ($isTableHeader && !empty($row)) {
                            // 首先检查是否是产品名称行
                            $foundProductName = false;
                            foreach ($row as $cell) {
                                if (!empty($cell['text']) && preg_match('/^[A-Z0-9-]+$/', $cell['text'])) {
                                    $currentProduct = $cell['text'];
                                    $foundProductName = true;
                                    break;
                                }
                            }

                            // 如果不是产品名称行，检查是否包含数量和价格信息
                            if (!$foundProductName && $currentProduct !== null && 
                                isset($headerIndexes['quantity']) && 
                                isset($row[$headerIndexes['quantity']]) && 
                                !empty($row[$headerIndexes['quantity']]['text'])) {
                                
                                $detail = [
                                    'product_name' => $currentProduct,
                                    'quantity' => trim($row[$headerIndexes['quantity']]['text']),
                                    'unit_price' => isset($headerIndexes['unit_price']) && isset($row[$headerIndexes['unit_price']]) ? 
                                        str_replace(['¥', ','], '', trim($row[$headerIndexes['unit_price']]['text'])) : '',
                                    'amount' => isset($headerIndexes['amount']) && isset($row[$headerIndexes['amount']]) ? 
                                        str_replace(['¥', ','], '', trim($row[$headerIndexes['amount']]['text'])) : ''
                                ];
                                
                                $tableDetails[] = $detail;
                                $currentProduct = null;
                            }
                        }
                    }
                }
            }

            // 遍历 t_snd_contacts 表
            $contacts = TecSndContactModel::all();
            $contactId = 3;
            $companyId = 2;
            foreach ($contacts as $contact) {
                // 获取联系人全名
                $fullName = $contact->getFullNameAttribute();
                // 计算相似度
                $similarity = 0;
                similar_text($fullName, $representativeName, $similarity);
                // 输出相似度
                Log::info("联系人: $fullName, 相似度: $similarity%");
                // 比较全名
                if ($similarity > 80) {
                    $contactId = $contact->id;
                    $companyId = $contact->company_id;
                    break;
                }
            }

            // 遍历 t_product_lists_h3c 表
            $products = TecH3cProductListModel::all();
            foreach ($tableDetails as &$detail) {
                $importedProductName = $detail['product_name'];
                $maxSimilarity = 0;
                $matchedProduct = null;
                
                // 遍历产品列表进行匹配
                foreach ($products as $product) {
                    $productName = $product->product;
                    $similarity = 0;
                    similar_text($productName, $importedProductName, $similarity);
                    Log::info("正向比较: 数据库中的产品名称: $productName, 导入的产品名称: $importedProductName, 相似度: $similarity%");
                    
                    if ($similarity > $maxSimilarity) {
                        $maxSimilarity = $similarity;
                        $matchedProduct = $product;
                    }
                }
                
                // 如果导入与数据库的相似度低于60%则进行反向比较
                if ($maxSimilarity < 60) {
                    $reverseMaxSimilarity = 0;
                    $reverseMatchedProduct = null;
                    
                    foreach ($products as $product) {
                        $productName = $product->product;
                        $similarity = 0;
                        similar_text($importedProductName, $productName, $similarity);
                        Log::info("反向比较: 数据库中的产品名称: $productName, 导入的产品名称: $importedProductName, 相似度: $similarity%");
                        
                        if ($similarity > $reverseMaxSimilarity) {
                            $reverseMaxSimilarity = $similarity;
                            $reverseMatchedProduct = $product;
                        }
                    }
                    
                    // 如果反向比较的相似度更高，则使用反向比较的结果
                    if ($reverseMaxSimilarity > $maxSimilarity) {
                        $maxSimilarity = $reverseMaxSimilarity;
                        $matchedProduct = $reverseMatchedProduct;
                    }
                }
                
                Log::info("=====================最大相似度: $maxSimilarity=====================");
                if ($matchedProduct !== null && $maxSimilarity >= 60) {
                    Log::info("=====================最终选择的产品: 产品ID: {$matchedProduct->id}, 产品名称: {$matchedProduct->product}=====================");
                }
                
                // 如果找到匹配度高的产品，更新产品信息
                if ($matchedProduct !== null && $maxSimilarity >= 60) {
                    $detail['product_id'] = $matchedProduct->id;
                    $detail['product_name'] = $matchedProduct->product;
                    $detail['product_category_id'] = $matchedProduct->category_id;
                    $detail['major_category_id'] = 1;
                    $detail['product_bom'] = $matchedProduct->product_bom;
                    $detail['product_zokusei'] = $matchedProduct->product_zokusei;
                    $detail['standard_selling_price'] = $matchedProduct->standard_selling_price;
                    $detail['amount'] = $detail['quantity'] * $matchedProduct->standard_selling_price;
                }
            }

            // 添加第二种提取逻辑
            $orderItems = [];
            $isProductFound = false;
            
            // 第一种提取逻辑
            foreach ($jsonData as $item) {
                if ($item['spec_index'] == 2) {
                    foreach ($item['data'] as $row) {
                        foreach ($row as $cell) {
                            if (!empty($cell['text'])) {
                                // 如果找到了产品名称，记录到明细中
                                if (isset($orderItems[count($orderItems) - 1]) && empty($orderItems[count($orderItems) - 1]['product_name'])) {
                                    $orderItems[count($orderItems) - 1]['product_name'] = $cell['text'];
                                    $isProductFound = true;
                                }
                                
                                // 如果是数字并且前一个单元格是"No"，则创建新的明细记录
                                if (is_numeric($cell['text']) && isset($prevText) && $prevText === 'No') {
                                    $orderItems[] = [
                                        'product_name' => '',
                                        'quantity' => '',
                                        'unit_price' => '',
                                        'amount' => ''
                                    ];
                                }
                                
                                // 记录当前单元格的文本，用于下一次判断
                                $prevText = $cell['text'];
                            }
                        }
                    }
                }
            }
            
            
            // 如果仍然没有找到产品，返回错误
            if (empty($orderItems)) {
                $error = new MessageBag([
                    'title'   => '错误',
                    'message' => '无法提取产品信息',
                ]);
                return back()->with(compact('error'));
            }

            // 处理提取的信息
            $orderDetails = [
                '注文番号' => $orderNumber,
                '担当' => $representativeName,
                '联系人ID' => $contactId,
                '公司ID' => $companyId,
                '明细' => $orderItems
            ];

            // 记录提取的信息
            Log::info('提取的信息: ', $orderDetails);

            // 插入到 t_customer_purchase_order_h3c 表
            $orderId = DB::table('t_customer_purchase_order_h3c')->insertGetId([
                'major_category_id' => 1,
                'sndCompany_id' => $companyId,
                'contact_person' => $contactId,
                'case_name' => $orderNumber,
                'order_no' => $orderNumber,
                'business_date' => date('Y-m-d'),
            ]);

            // 插入到 t_customer_purchase_order_items_h3c 表
            foreach ($orderItems as $item) {
                DB::table('t_customer_purchase_order_items_h3c')->insert([
                    'order_id' => $orderId,
                    'category_id' => $item['product_category_id'],
                    'product' => $item['product_id'],
                    'product_bom' => $item['product_bom'],
                    'product_zokusei' => $item['product_zokusei'],
                    'quantity' => $item['quantity'],
                    'standard_selling_price' => $item['unit_price'],
                ]);
            }

            return $this->response()->success('文件上传成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('上传失败：' . $e->getMessage());
        }
    }
}