<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cCustomerOrder;

use App\Models\TecH3cCustomerPurchaseOrderModel;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Yxx\LaravelQuick\Exceptions\Api\ApiRequestException;
use Dcat\Admin\Admin;

class TecH3cCustomerOrderPrintAction extends AbstractTool
{
    /**
     * @return string
     */
    protected $title = '納品書';

    public function __construct($title = null)
    {
        parent::__construct();
        $this->title = '<i class="fa fa-print"></i> ' . $this->title;
        Admin::script($this->script());
    }

    public function getHandleRoute()
    {
        $key = request()->route('r_h3c_customer_order');
        return admin_url("r_customer_orders_h3c/{$key}/print");
    }

    protected function script()
    {
        return <<<JS
        $(function () {
            $('.grid-print-btn').on('click', function() {
                var url = $(this).data('url');
                var id = $(this).data('key');

                if (!id) {
                    Dcat.error('未找到订单ID');
                    return;
                }

                $.ajax({
                    url: url,
                    type: 'GET',
                    success: function(response) {
                        if (response.status === false) {
                            Dcat.error(response.message);
                        } else {
                            var printWindow = window.open('', '_blank');
                            if (printWindow) {
                                printWindow.document.write(response);
                                printWindow.document.close();
                            } else {
                                Dcat.error('请允许浏览器打开新窗口');
                            }
                        }
                    },
                    error: function(xhr) {
                        Dcat.error('打印失败：' + (xhr.responseJSON?.message || '未知错误'));
                    }
                });
            });
        });
        JS;
    }

    public function handle(Request $request)
    {
        try {
            // 从请求中获取 ID
            $id = $request->get('_key');
            
            Log::info('开始获取打印订单数据', [
                'order_id' => $id,
                'request_path' => $request->path(),
                'full_url' => $request->fullUrl(),
                'request_all' => $request->all()
            ]);
            
            if (empty($id)) {
                throw new ApiRequestException('订单ID不能为空！');
            }
            
            // 获取订单数据
            $order = TecH3cCustomerPurchaseOrderModel::with([
                'items.productlist',
                'items.unitInfo',
                'customer'
            ])->findOrFail($id);
            
            Log::info('打印订单数据获取成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'items_count' => $order->items->count()
            ]);
            
            // 返回确认视图
            return view('admin.h3c.tec_h3c_customer_order_confirm', compact('order'));
            
        } catch (\Exception $e) {
            Log::error('获取打印订单数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_all' => $request->all()
            ]);
            return $this->response()->error('获取打印订单数据失败：' . $e->getMessage());
        }
    }

    protected function html()
    {
        // 添加按钮的 class 和 data 属性
        $this->setHtmlAttribute([
            'data-url' => $this->getHandleRoute(),
            'data-action' => 'print',
            'class' => 'btn btn-primary btn-mini grid-print-btn',
        ]);

        return <<<HTML
<div class="btn-group" style="margin-right: 5px">
    <button {$this->formatHtmlAttributes()}>
        {$this->title}
    </button>
</div>
HTML;
    }

    protected function parameters(): array
    {
        return [
            '_key' => request()->route('r_h3c_customer_order'),
        ];
    }
}
