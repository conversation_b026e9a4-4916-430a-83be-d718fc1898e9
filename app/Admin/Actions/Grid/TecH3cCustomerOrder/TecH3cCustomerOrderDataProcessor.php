<?php

namespace App\Admin\Actions\Grid\TecH3cCustomerOrder;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * 订单数据处理器
 * 处理从PDF解析结果中提取的数据，转换为系统可用的订单信息
 */
class TecH3cCustomerOrderDataProcessor
{
    /**
     * 保存从PDF解析结果中提取的订单数据
     * 
     * @param array $combinedData 提取的订单数据
     * @return bool 保存结果
     */
    public function saveTableResults($combinedData)
    {
        try {
            // 检查是否有有效数据
            if (!isset($combinedData['details']) || empty($combinedData['details'])) {
                Log::warning("没有解析到任何订单明细数据");
                return false;
            }
            
            // 记录要保存的数据
            $headerData = $combinedData['header'] ?? [];
            $detailsData = $combinedData['details'] ?? [];
            
            Log::info("===== 开始保存订单数据 =====");
            Log::info("订单头部数据: " . json_encode($headerData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            Log::info("订单明细数据: " . json_encode($detailsData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            // 开始事务
            DB::beginTransaction();
            
            try {
                // TODO: 根据具体业务需求实现订单数据保存逻辑
                // 1. 可能需要检查订单是否已存在
                // 2. 创建订单头部记录
                // 3. 创建订单明细记录
                // 4. 关联其他相关数据
                
                // 示例代码 - 实际实现需要根据数据库结构和业务逻辑调整
                /*
                // 创建订单头部
                $orderData = [
                    'order_number' => $headerData['注文番号'] ?? null,
                    'order_date' => $headerData['発行日'] ?? date('Y-m-d'),
                    'status' => 'pending',
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                
                $orderId = DB::table('t_h3c_customer_orders')->insertGetId($orderData);
                
                // 创建订单明细
                foreach ($detailsData as $detail) {
                    $orderItemData = [
                        'order_id' => $orderId,
                        'product_name' => $detail['製品名'] ?? null,
                        'quantity' => $detail['数量'] ?? 0,
                        'unit_price' => $detail['単価'] ?? 0,
                        'amount' => $detail['金額'] ?? 0,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                    
                    DB::table('t_h3c_customer_order_items')->insert($orderItemData);
                }
                */
                
                // 提交事务
                DB::commit();
                Log::info("===== 订单数据保存成功 =====");
                return true;
                
            } catch (\Exception $e) {
                // 回滚事务
                DB::rollBack();
                Log::error("保存订单数据失败: " . $e->getMessage());
                Log::error($e->getTraceAsString());
                return false;
            }
            
        } catch (\Exception $e) {
            Log::error("处理订单数据时出错: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * 处理订单数据，尝试识别订单信息
     * 
     * @param array $headers 表头数据
     * @param array $rows 行数据
     * @param int $tableIndex 表格索引
     * @return array|null 处理结果
     */
    public function identifyOrderInformation($headers, $rows, $tableIndex)
    {
        Log::info("===== 开始识别表格 #" . ($tableIndex + 1) . " 的订单信息 =====");
        
        // 合并所有表头行来识别列
        $mergedHeaders = [];
        foreach ($headers as $headerRow) {
            $mergedHeaders = array_merge($mergedHeaders, $headerRow);
        }
        
        // 尝试识别关键列
        $columnMapping = $this->mapColumns($mergedHeaders);
        Log::info("列映射结果: " . json_encode($columnMapping, JSON_UNESCAPED_UNICODE));
        
        // 如果有足够的列被识别，则尝试提取订单信息
        if (count(array_filter($columnMapping)) >= 3) {
            $orderData = $this->extractOrderData($rows, $columnMapping);
            Log::info("提取的订单数据: " . json_encode($orderData, JSON_UNESCAPED_UNICODE));
            
            Log::info("===== 结束识别表格 #" . ($tableIndex + 1) . " 的订单信息 =====");
            return $orderData;
        } else {
            Log::warning("无法识别足够的列信息，跳过处理");
            Log::info("===== 结束识别表格 #" . ($tableIndex + 1) . " 的订单信息 =====");
            return null;
        }
    }
    
    /**
     * 映射表头列到系统字段
     * 
     * @param array $headers 表头数组
     * @return array 列映射关系
     */
    private function mapColumns($headers)
    {
        $columnMapping = [
            'product_name' => null,  // 产品名称
            'model' => null,         // 产品型号
            'quantity' => null,      // 数量
            'price' => null,         // 单价
            'total' => null          // 总价
        ];
        
        // 可能的列名映射（根据实际情况调整）
        $possibleMappings = [
            'product_name' => ['产品名称', '产品', '商品名称', '商品', '名称', '货物', '服务内容', '品名', '製品名'],
            'model' => ['型号', '规格型号', '规格', '型号规格', '货号', '商品型号', '配置', '货物编号', '製品型号'],
            'quantity' => ['数量', '购买数量', '订购数量', '件数', 'qty', '数目', '购买量', '個数'],
            'price' => ['单价', '价格', '单位价格', '单位价', '价钱', '元/个', '元/件', '元/台', '単価'],
            'total' => ['金额', '总金额', '总价', '总价格', '合计', '小计', '价税合计', '金額']
        ];
        
        // 遍历所有表头尝试匹配
        foreach ($headers as $index => $header) {
            $header = trim(strtolower(str_replace(' ', '', $header)));
            
            foreach ($possibleMappings as $field => $possibleNames) {
                foreach ($possibleNames as $name) {
                    if (strpos($header, strtolower($name)) !== false) {
                        $columnMapping[$field] = $index;
                        break 2;
                    }
                }
            }
        }
        
        return $columnMapping;
    }
    
    /**
     * 从行数据中提取订单信息
     * 
     * @param array $rows 行数据
     * @param array $columnMapping 列映射
     * @return array 提取的订单数据
     */
    private function extractOrderData($rows, $columnMapping)
    {
        $orderItems = [];
        
        foreach ($rows as $row) {
            // 跳过空行或无效行
            if (count(array_filter($row)) < 3) {
                continue;
            }
            
            $item = [];
            
            // 提取各字段值
            foreach ($columnMapping as $field => $index) {
                if ($index !== null && isset($row[$index])) {
                    $item[$field] = trim($row[$index]);
                }
            }
            
            // 如果至少有产品名称和数量，则认为是有效的订单项
            if (isset($item['product_name']) && isset($item['quantity'])) {
                $orderItems[] = $item;
            }
        }
        
        // 记录提取的订单项数量，便于调试
        Log::info("共提取了 " . count($orderItems) . " 个有效订单项");
        
        return $orderItems;
    }
} 