<?php

namespace App\Admin\Actions\Grid\TecH3cCustomerOrder;

use Illuminate\Support\Facades\Log;

/**
 * PDF表格分析器
 * 负责解析阿里云API返回的表格数据,提取结构化信息
 */
class TecH3cCustomerOrderPDFAnalyzerParsing
{
  /**
   * 解析阿里云API返回的PDF数据
   * 
   * @param array $pdfData API返回的原始数据
   * @return array 解析后的结构化数据
   */
  public function parsePDFData($pdfData)
  {
    try {
      // 统一数据格式，兼容不同的API响应结构
      $dataSource = $this->normalizeDataStructure($pdfData);
      
      // 检查数据有效性
      if (empty($dataSource) || !isset($dataSource['layouts'])) {
        Log::error('PDF数据解析失败: 无效的数据格式', ['data' => $pdfData]);
        return [
          'success' => false,
          'message' => '无效的PDF数据格式',
          'data' => []
        ];
      }

      $layouts = $dataSource['layouts'];
      $result = [
        'success' => true,
        'message' => '解析成功',
        'data' => [
          'title' => '',
          'subtitle' => '',
          'contents' => [],
          'tables' => [],
          'texts' => [],
          'figures' => []
        ]
      ];

      // 处理各种版面信息
      foreach ($layouts as $layout) {
        $type = $layout['type'] ?? '';
        $subType = $layout['subType'] ?? '';
        $text = $layout['text'] ?? '';
        $markdownContent = $layout['markdownContent'] ?? '';
        $pageNum = $layout['pageNum'] ?? 0;
        $uniqueId = $layout['uniqueId'] ?? '';

        // 根据类型处理不同的版面数据
        switch ($type) {
          case 'title':
            if ($subType === 'doc_title') {
              $result['data']['title'] = $text;
            } elseif ($subType === 'doc_subtitle') {
              $result['data']['subtitle'] = $text;
            } elseif ($subType === 'para_title') {
              $result['data']['contents'][] = [
                'type' => 'title',
                'text' => $text,
                'page' => $pageNum,
                'id' => $uniqueId
              ];
            }
            break;

          case 'table':
            // 表格数据处理
            $result['data']['tables'][] = [
              'text' => $text,
              'markdown' => $markdownContent,
              'page' => $pageNum,
              'id' => $uniqueId,
              'cells' => $layout['cells'] ?? [],
              'numRow' => $layout['numRow'] ?? 0,
              'numCol' => $layout['numCol'] ?? 0
            ];
            break;

          case 'text':
            $result['data']['texts'][] = [
              'text' => $text,
              'markdown' => $markdownContent,
              'page' => $pageNum,
              'id' => $uniqueId,
              'subType' => $subType
            ];
            break;

          case 'figure':
            // 图表数据处理
            $result['data']['figures'][] = [
              'text' => $text,
              'type' => $subType,
              'page' => $pageNum,
              'id' => $uniqueId
            ];
            break;
        }
      }

      // 处理llmResult，如果存在的话
      if (isset($dataSource['llmResult'])) {
        $result['data']['llmResult'] = $dataSource['llmResult'];
      }

      return $result;
    } catch (\Exception $e) {
      Log::error('PDF数据解析异常: ' . $e->getMessage(), [
        'trace' => $e->getTraceAsString(),
        'data' => $pdfData
      ]);
      
      return [
        'success' => false,
        'message' => '解析过程发生异常: ' . $e->getMessage(),
        'data' => []
      ];
    }
  }
  
  /**
   * 将不同格式的API响应数据转换为统一的结构
   * 
   * @param array $data 原始API响应数据
   * @return array 统一格式的数据
   */
  private function normalizeDataStructure($data)
  {
    if (empty($data)) {
      return [];
    }
    
    // 尝试多种可能的数据结构
    $possiblePaths = [
      // 直接Data.layouts结构
      ['Data', 'layouts'],
      // body中的Data.layouts结构
      ['body', 'Data', 'layouts'],
      // rawResponse中的body.Data.layouts结构
      ['rawResponse', 'body', 'Data', 'layouts']
    ];
    
    foreach ($possiblePaths as $path) {
      $current = $data;
      $valid = true;
      
      // 遍历路径
      foreach ($path as $key) {
        if (isset($current[$key])) {
          $current = $current[$key];
        } else {
          $valid = false;
          break;
        }
      }
      
      // 如果找到了layouts数据
      if ($valid && is_array($current)) {
        $normalized = [
          'layouts' => $current
        ];
        
        // 如果存在llmResult，也添加进来
        if (isset($data['Data']['llmResult'])) {
          $normalized['llmResult'] = $data['Data']['llmResult'];
        } elseif (isset($data['body']['Data']['llmResult'])) {
          $normalized['llmResult'] = $data['body']['Data']['llmResult'];
        } elseif (isset($data['rawResponse']['body']['Data']['llmResult'])) {
          $normalized['llmResult'] = $data['rawResponse']['body']['Data']['llmResult'];
        }
        
        return $normalized;
      }
    }
    
    // 如果所有路径都失败，返回原始数据
    return $data;
  }
  
  /**
   * 提取并记录表格数据，兼容旧的API调用
   * 
   * @param array $responseData API响应数据
   * @return array 包含header和details的处理结果
   */
  public function extractAndLogTableData($responseData)
  {
    Log::info('TecH3cCustomerOrderPDFAnalyzerParsing->extractAndLogTableData(): 开始处理API响应数据');
    
    // 检查是否为空数据
    if (empty($responseData)) {
      Log::warning('TecH3cCustomerOrderPDFAnalyzerParsing->extractAndLogTableData(): 响应数据为空');
      return ['header' => [], 'details' => []];
    }
    
    // 使用新的parsePDFData方法解析数据
    $parsedData = $this->parsePDFData($responseData);
    
    // 检查解析结果
    if (!$parsedData['success']) {
      Log::warning('TecH3cCustomerOrderPDFAnalyzerParsing->extractAndLogTableData(): 解析失败 - ' . $parsedData['message']);
      return ['header' => [], 'details' => []];
    }
    
    // 从解析结果中提取表格数据
    $extractedData = $this->extractOrderData($parsedData['data']);
    
    // 记录提取结果
    Log::info('TecH3cCustomerOrderPDFAnalyzerParsing->extractAndLogTableData(): 解析完成，' . 
            '头部字段数: ' . count($extractedData['header']) . 
            ', 明细行数: ' . count($extractedData['details']));
    
    return $extractedData;
  }
  
  /**
   * 从解析结果中提取订单数据
   * 
   * @param array $parsedData 解析后的数据
   * @return array 提取的订单数据，包含header和details
   */
  private function extractOrderData($parsedData)
  {
    $result = [
      'header' => [],
      'details' => []
    ];
    
    // 1. 处理表格数据，提取订单基本信息和产品明细
    if (!empty($parsedData['tables'])) {
      foreach ($parsedData['tables'] as $table) {
        // 检查是否为订单基本信息表（通常行数少，列数少）
        if (isset($table['numRow']) && $table['numRow'] <= 5 && isset($table['numCol']) && $table['numCol'] <= 3) {
          $this->extractOrderBasicInfo($table, $result['header']);
        }
        // 检查是否为产品明细表（通常行数多，列数多）
        else if (isset($table['numRow']) && $table['numRow'] > 2 && isset($table['numCol']) && $table['numCol'] >= 4) {
          $this->extractProductDetails($table, $result['details']);
        }
        
        // 解析任何表格中的Markdown内容
        if (!empty($table['markdown'])) {
          $this->extractDataFromMarkdownTable($table['markdown'], $result);
        }
      }
    }
    
    // 2. 处理文本数据，提取金额等信息
    if (!empty($parsedData['texts'])) {
      foreach ($parsedData['texts'] as $text) {
        $content = $text['text'] ?? '';
        
        // 提取金额信息
        if (preg_match('/注文金額\s*¥([\d,]+)/u', $content, $matches) || 
            preg_match('/ご注文金額\s*¥([\d,]+)/u', $content, $matches)) {
          $result['header']['total_amount'] = $matches[1];
        }
        
        // 提取日期信息
        if (preg_match('/発行日[:\s]*([\S]+)/u', $content, $matches)) {
          $result['header']['issue_date'] = $matches[1];
        }
        
        // 提取订单号
        if (preg_match('/注文番号[:\s]*([\S]+)/u', $content, $matches)) {
          $result['header']['order_number'] = $matches[1];
        }
      }
    }
    
    return $result;
  }
  
  /**
   * 从订单基本信息表中提取数据
   * 
   * @param array $table 表格数据
   * @param array &$header 引用传递的头部数据数组
   */
  private function extractOrderBasicInfo($table, &$header)
  {
    // 从markdown表格中提取
    if (!empty($table['markdown'])) {
      $lines = explode("\n", $table['markdown']);
      
      foreach ($lines as $line) {
        // 跳过分隔行和空行
        if (empty($line) || preg_match('/^\|[\s\-\|]+\|$/', $line)) {
          continue;
        }
        
        // 查找订单号行
        if (strpos($line, '注文番号') !== false && preg_match('/\|\s*注文番号\s*\|\s*([\S]+)\s*\|/u', $line, $matches)) {
          $header['order_number'] = $matches[1];
        }
        
        // 查找发行日行
        if (strpos($line, '発行日') !== false && preg_match('/\|\s*発行日\s*\|\s*([\S]+)\s*\|/u', $line, $matches)) {
          $header['issue_date'] = $matches[1];
        }
      }
    }
    
    // 从表格单元格中提取
    if (!empty($table['cells'])) {
      foreach ($table['cells'] as $cell) {
        $cellLayouts = $cell['layouts'] ?? [];
        
        if (is_array($cellLayouts) && !empty($cellLayouts)) {
          $cellText = $cellLayouts[0]['text'] ?? '';
          
          // 订单号单元格
          if ($cellText == '注文番号') {
            // 获取相邻单元格的值
            $rightCellIndex = ($cell['xsc'] + 1) . '_' . $cell['ysc'];
            foreach ($table['cells'] as $adjacentCell) {
              if ($adjacentCell['xsc'] == $cell['xsc'] + 1 && $adjacentCell['ysc'] == $cell['ysc']) {
                $cellLayouts = $adjacentCell['layouts'] ?? [];
                if (is_array($cellLayouts) && !empty($cellLayouts)) {
                  $header['order_number'] = $cellLayouts[0]['text'] ?? '';
                }
                break;
              }
            }
          }
          
          // 发行日单元格
          if ($cellText == '発行日') {
            // 获取相邻单元格的值
            foreach ($table['cells'] as $adjacentCell) {
              if ($adjacentCell['xsc'] == $cell['xsc'] + 1 && $adjacentCell['ysc'] == $cell['ysc']) {
                $cellLayouts = $adjacentCell['layouts'] ?? [];
                if (is_array($cellLayouts) && !empty($cellLayouts)) {
                  $header['issue_date'] = $cellLayouts[0]['text'] ?? '';
                }
                break;
              }
            }
          }
        }
      }
    }
  }
  
  /**
   * 从产品明细表中提取数据
   * 
   * @param array $table 表格数据
   * @param array &$details 引用传递的明细数据数组
   */
  private function extractProductDetails($table, &$details)
  {
    // 从markdown表格中提取
    if (!empty($table['markdown'])) {
      $rows = $this->parseMarkdownTable($table['markdown']);
      
      if (count($rows) > 1) {
        // 第一行是表头
        $headers = $rows[0];
        
        // 从第二行开始是数据行
        for ($i = 1; $i < count($rows); $i++) {
          $row = $rows[$i];
          
          // 跳过空行
          if (empty(array_filter($row))) {
            continue;
          }
          
          $item = [];
          foreach ($headers as $j => $header) {
            if (isset($row[$j])) {
              $normalizedKey = $this->normalizeKey($header);
              $item[$normalizedKey] = $row[$j];
            }
          }
          
          // 确保行有实际数据
          if (!empty($item) && isset($item['no']) && isset($item['数量'])) {
            $details[] = $item;
          }
        }
      }
    }
  }
  
  /**
   * 从Markdown表格内容中提取数据
   * 
   * @param string $markdown 表格的Markdown文本
   * @param array &$result 引用传递的结果数组
   */
  private function extractDataFromMarkdownTable($markdown, &$result)
  {
    $rows = $this->parseMarkdownTable($markdown);
    
    // 检查是否为订单基本信息表
    $isOrderInfoTable = false;
    foreach ($rows as $row) {
      if (count($row) >= 2) {
        if (in_array(trim($row[0]), ['発行日', '注文番号', '明細番号', '見積書番号'])) {
          $isOrderInfoTable = true;
          // 添加到头部信息
          $key = $this->normalizeKey($row[0]);
          $result['header'][$key] = trim($row[1]);
        }
      }
    }
    
    // 如果不是订单信息表，检查是否为产品明细表
    if (!$isOrderInfoTable && count($rows) > 1) {
      $headers = $rows[0];
      
      // 检查是否包含典型的产品明细表头（Ｎｏ、製品名、数量、単価等）
      if (count(array_intersect(array_map('trim', $headers), ['Ｎｏ', 'No', '製品名', '製 品 名', '数量', '単価', '金額'])) >= 3) {
        // 从第二行开始是数据行
        for ($i = 1; $i < count($rows); $i++) {
          $row = $rows[$i];
          
          // 跳过空行
          if (empty(array_filter($row))) {
            continue;
          }
          
          $item = [];
          foreach ($headers as $j => $header) {
            if (isset($row[$j])) {
              $normalizedKey = $this->normalizeKey($header);
              $item[$normalizedKey] = trim($row[$j]);
            }
          }
          
          // 确保行有实际数据
          if (!empty($item) && !empty(array_filter($item, function($value) { return !empty($value); }))) {
            $result['details'][] = $item;
          }
        }
      }
    }
  }
  
  /**
   * 解析Markdown表格内容为二维数组
   * 
   * @param string $markdown 表格的Markdown文本
   * @return array 解析后的表格数据
   */
  private function parseMarkdownTable($markdown)
  {
    $rows = [];
    $lines = explode("\n", $markdown);
    
    foreach ($lines as $line) {
      $line = trim($line);
      
      // 跳过表头分隔行和空行
      if (empty($line) || preg_match('/^\|[\s\-\|]+\|$/', $line)) {
        continue;
      }
      
      // 处理表格行
      if (strpos($line, '|') !== false) {
        $cells = array_map('trim', explode('|', $line));
        
        // 移除首尾空单元格
        if (empty($cells[0])) {
          array_shift($cells);
        }
        
        if (empty($cells[count($cells) - 1])) {
          array_pop($cells);
        }
        
        if (!empty($cells)) {
          $rows[] = $cells;
        }
      }
    }
    
    return $rows;
  }
  
  /**
   * 规范化键名，移除特殊字符并转换为小写
   * 
   * @param string $key 原始键名
   * @return string 规范化后的键名
   */
  private function normalizeKey($key)
  {
    $key = trim($key);
    $key = strtolower($key);
    
    // 常见字段映射
    $mappings = [
      'ｎｏ' => 'no',
      '製 品 名' => 'product_name',
      '製品名' => 'product_name',
      '数量' => 'quantity',
      '単位' => 'unit',
      '単価' => 'price',
      '金額' => 'amount',
      '備考' => 'remarks',
      '発行日' => 'issue_date',
      '注文番号' => 'order_number',
      '明細番号' => 'detail_number',
      '見積書番号' => 'quote_number',
      'ご注文金額' => 'total_amount',
      '注文金額' => 'total_amount',
      '合　計' => 'subtotal',
      '消費税' => 'tax'
    ];
    
    if (isset($mappings[$key])) {
      return $mappings[$key];
    }
    
    // 如果没有特定映射，则进行一般规范化
    $key = preg_replace('/[^a-z0-9_]+/', '_', $key);
    $key = preg_replace('/_+/', '_', $key);
    $key = trim($key, '_');
    
    return $key;
  }
}