<?php

namespace App\Admin\Actions\Grid\TecH3cCustomerOrder;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;

class TecH3cCustomerOrderImportPDFAction extends AbstractTool
{
    protected $title = '导入PDF订单';
    
    // 相关服务类实例
    protected $apiClient;
    protected $pdfAnalyzer;
    protected $dataProcessor;
    
    /**
     * 构造函数,初始化所需的服务
     */
    public function __construct()
    {
        parent::__construct();
        $this->apiClient = new TecH3cCustomerOrderAlibabaApiClient();
        $this->pdfAnalyzer = new TecH3cCustomerOrderPDFAnalyzerParsing();
        $this->dataProcessor = new TecH3cCustomerOrderDataProcessor();
        
        // 执行初始化操作
        $this->initModalFormListener();
    }
    
    /**
     * 初始化Modal表单监听器
     * 在父窗口添加代码,准备接收PDF导入后的数据
     */
    public function initModalFormListener()
    {
        // 添加简单的监听器，不再包含复杂的JavaScript代码
        $script = <<<'SCRIPT'
// 页面加载完成后检查localStorage中的PDF导入数据
document.addEventListener('DOMContentLoaded', function() {
    console.log('PDF导入监听器初始化');
});
SCRIPT;

        // 将脚本添加到全局资源队列
        Admin::script($script);
        
        return $this;
    }

    public function html()
    {
        $url = $this->getImportUrl();
        
        // 使用字符串拼接而不是HEREDOC，防止变量解析问题
        $html = '<div class="btn-group">';
        $html .= '<button class="btn btn-primary btn-import-pdf" type="button" data-import-url="'.$url.'"><i class="fa fa-print"></i> PDF导入</button>';
        $html .= '</div>';
        
        $html .= '<!-- 模态窗口容器 -->';
        $html .= '<div class="modal fade" id="pdf-import-modal" tabindex="-1" role="dialog" aria-labelledby="pdf-import-modal-label" aria-hidden="true">';
        $html .= '    <div class="modal-dialog modal-xl" style="max-width: 90%;">';
        $html .= '        <div class="modal-content">';
        $html .= '            <div class="modal-header">';
        $html .= '                <h5 class="modal-title" id="pdf-import-modal-label">PDF订单导入</h5>';
        $html .= '                <button type="button" class="close" data-dismiss="modal" aria-label="Close">';
        $html .= '                    <span aria-hidden="true">&times;</span>';
        $html .= '                </button>';
        $html .= '            </div>';
        $html .= '            <div class="modal-body p-0">';
        $html .= '                <iframe id="pdf-import-iframe" style="width: 100%; height: 80vh; border: none;"></iframe>';
        $html .= '            </div>';
        $html .= '        </div>';
        $html .= '    </div>';
        $html .= '</div>';
        
        // 简化的JavaScript代码，只保留必要的初始化逻辑
        $html .= <<<'JAVASCRIPT'
<script>
$(function () {
    // 点击按钮时，设置iframe的src并打开模态窗口
    $('.btn-import-pdf').on('click', function() {
        var url = $(this).data('import-url');
        
        // 设置iframe的src
        $('#pdf-import-iframe').attr('src', url);
        
        // 使用标准Bootstrap方法打开模态窗口
        $('#pdf-import-modal').modal('show');
        
        // 记录操作到日志
        console.log('PDF导入模态窗口已打开，URL: ' + url);
        
        return false;
    });
});
</script>
JAVASCRIPT;

        return $html;
    }

    protected function form()
    {
        // 使用Blade视图代替硬编码的HTML
        return view('admin.h3c.tec_h3c_customer_order_import_pdf', [
            'handleRoute' => $this->getHandleRoute(),
        ])->render();
    }

    protected function getHandleRoute()
    {
        return admin_url('r_h3c_customer_orders/import');
    }

    /**
     * 获取PDF导入页面URL
     * 
     * @return string
     */
    protected function getImportUrl()
    {
        return admin_url('r_h3c_customer_orders/import-pdf');
    }
    
    /**
     * 获取处理导入数据的URL
     * 
     * @return string
     */
    protected function getProcessDataUrl()
    {
        return admin_url('api/process-imported-data');
    }

    /**
     * 获取新增页面的URL
     * 
     * @return string
     */
    protected function getCreateUrl()
    {
        // 根据用户反馈，新增页面不是标准的create路径
        // 使用定义在路由中的正确路径
        return admin_url('r_h3c_customer_orders');
    }

    public function handle(Request $request)
    {
        try {
            // 获取文件
            $file = $request->file('import_file');
            $jobId = $request->input('job_id', '');

            // 检查文件是否有效
            if (!$file || !$file->isValid()) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的文件上传'
                ]);
            }

            // 获取文件信息
            $fileName = $file->getClientOriginalName();
            $filePath = $file->getRealPath();
            
            // 保存文件名到会话中,以便后续使用
            session(['uploaded_pdf_filename' => $fileName]);
            Log::info('TecH3cCustomerOrderImportPDFAction->handle(): 保存上传的文件名到会话中: ' . $fileName);

            // 调用阿里云API处理PDF文件
            $apiClient = new TecH3cCustomerOrderAlibabaApiClient();
            
            if (!empty($jobId)) {
                Log::info('TecH3cCustomerOrderImportPDFAction->handle(): 使用现有任务ID: ' . $jobId);
                
                // 查询现有任务结果
                $result = $apiClient->getDocParserResult($jobId, true);
                
                Log::info('TecH3cCustomerOrderImportPDFAction->handle(): 获取到任务结果，将开始提取数据');
                
                // 使用PDF分析器处理返回的结果
                $analyzer = new TecH3cCustomerOrderPDFAnalyzerParsing();
                $extractedData = $analyzer->extractAndLogTableData($result);
                
                // 记录提取的数据量
                Log::info('TecH3cCustomerOrderImportPDFAction->handle(): 提取到的数据: 头部字段数=' . 
                         count($extractedData['header']) . ', 明细行数=' . count($extractedData['details']));
                
                // 获取原始文件名作为案件名称(不含扩展名)
                $originalFileName = pathinfo($fileName, PATHINFO_FILENAME);
                Log::info('TecH3cCustomerOrderImportPDFAction->handle(): 将使用PDF文件名作为案件名: ' . $originalFileName);

                // 添加文件名到提取的数据中
                $extractedData['file_name'] = $originalFileName;
                $extractedData['full_file_name'] = $fileName;
                
                // 准备响应
                return response()->json([
                    'status' => true,
                    'message' => '已成功分析PDF文件',
                    'data' => [
                        'extracted' => $extractedData,
                        'file_name' => $originalFileName,
                        'full_file_name' => $fileName
                    ]
                ]);
            }
            
            Log::info('TecH3cCustomerOrderImportPDFAction->handle(): 开始上传文件: ' . $fileName);
            
            // 上传文件并启动解析任务
            $response = $apiClient->submitDocParserJob($file);
            
            Log::info('API响应数据: ' . json_encode($response));
            
            // 检查响应格式，获取任务ID
            if (!isset($response['success']) || $response['success'] !== true) {
                Log::error('TecH3cCustomerOrderImportPDFAction->handle(): 文件上传失败，API返回: ' . json_encode($response));
                return response()->json([
                    'status' => false,
                    'message' => isset($response['message']) ? $response['message'] : '文件上传失败，无法获取任务ID'
                ]);
            }
            
            // 尝试从data.jobId或data.id获取任务ID
            if (isset($response['data']['jobId'])) {
                $jobId = $response['data']['jobId'];
            } elseif (isset($response['data']['id'])) {
                $jobId = $response['data']['id'];
            } else {
                Log::error('TecH3cCustomerOrderImportPDFAction->handle(): 无法从响应中获取任务ID: ' . json_encode($response));
                return response()->json([
                    'status' => false,
                    'message' => '文件上传成功，但无法获取任务ID'
                ]);
            }
            
            Log::info('TecH3cCustomerOrderImportPDFAction->handle(): 文件上传成功，获取到任务ID: ' . $jobId);
            
            // 准备状态检查URL
            $statusCheckUrl = admin_url('h3c-customer-order/check-status');
            
            // 保存原始文件名到状态检查数据中，便于后续处理
            $originalFileName = pathinfo($fileName, PATHINFO_FILENAME);
            Log::info('TecH3cCustomerOrderImportPDFAction->handle(): 保存文件名供后续使用: ' . $originalFileName);
            
            // 成功响应
            return response()->json([
                'status' => true,
                'message' => '文件上传成功，正在处理中',
                'data' => [
                    'job_id' => $jobId,
                    'status_url' => $statusCheckUrl,
                    'file_name' => $originalFileName,
                    'full_file_name' => $fileName
                ],
                'content' => [
                    'job_id' => $jobId,
                    'status_url' => $statusCheckUrl,
                    'file_name' => $originalFileName,
                    'full_file_name' => $fileName
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('TecH3cCustomerOrderImportPDFAction->handle(): 处理PDF文件时出错: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            return response()->json([
                'status' => false,
                'message' => '处理PDF文件时出错: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 检查文档抽取任务状态
     */
    public function checkStatus()
    {
        // 获取任务ID，同时兼容job_id和jobId两种参数名
        $jobId = request('job_id') ?: request('jobId');
        
        // 记录获取状态请求
        Log::info("开始checkStatus检查任务状态: jobId = {$jobId}");
        
        // 检查任务ID
        if (empty($jobId)) {
            return $this->response()->error('任务ID不能为空');
        }
        
        try {
            // 使用API客户端获取任务状态
            $statusData = $this->apiClient->queryDocParserStatus($jobId);
            
            Log::info("API状态查询响应: " . json_encode($statusData['data'] ?? [], JSON_PRETTY_PRINT));
            
            // 确保data键存在且为数组
            if (!isset($statusData['data']) || !is_array($statusData['data'])) {
                $statusData['data'] = [
                    'completed' => false,
                    'failed' => false,
                    'shouldStopPolling' => false,
                    'status' => 'processing'
                ];
                Log::warning("状态数据中缺少data字段，已添加默认值");
            }
            
            // 确保所有必要字段都存在
            if (!isset($statusData['data']['completed'])) {
                $statusData['data']['completed'] = false;
                Log::warning("状态数据中缺少completed字段，已设置为false");
            }
            
            if (!isset($statusData['data']['failed'])) {
                $statusData['data']['failed'] = false;
                Log::warning("状态数据中缺少failed字段，已设置为false");
            }
            
            if (!isset($statusData['data']['shouldStopPolling'])) {
                $statusData['data']['shouldStopPolling'] = false;
                Log::warning("状态数据中缺少shouldStopPolling字段，已设置为false");
            }
            
            // 检查任务是否完成
            $isCompleted = $statusData['data']['completed'];
            $isFailed = $statusData['data']['failed'];
            
            // 如果任务失败，返回错误
            if ($isFailed) {
                Log::error("文档解析任务失败，jobId: {$jobId}");
                return response()->json([
                    'status' => false,
                    'message' => '文档解析任务失败',
                    'data' => $statusData['data']
                ]);
            }
            
            // 准备响应数据
            $responseData = $statusData;
            
            // 如果任务已完成且成功，获取解析结果
            if ($isCompleted) {
                Log::info("文档解析任务已完成，获取结果数据");
                
                // 获取文档解析的详细结果
                $responseData = $this->apiClient->getDocParserResult($jobId);
                
                // 添加日志，记录收到的响应结构
                Log::info("收到的文档解析结果结构：" . json_encode(array_keys($responseData)));
                
                // 检查API调用是否成功
                if (!isset($responseData['success']) || $responseData['success'] !== true) {
                    Log::error("API调用失败: " . ($responseData['message'] ?? '未知错误'));
                    return response()->json([
                        'status' => false,
                        'message' => '获取文档解析结果失败: ' . ($responseData['message'] ?? '未知错误'),
                        'data' => [
                            'completed' => true,
                            'failed' => true,
                            'shouldStopPolling' => true
                        ]
                    ]);
                }
                
                // 确保responseData中有body字段
                if (!isset($responseData['body']) && isset($responseData['rawResponse']) && isset($responseData['rawResponse']['body'])) {
                    $responseData['body'] = $responseData['rawResponse']['body'];
                }
                
                // 使用PDF分析器处理API结果
                $analyzer = $this->pdfAnalyzer;
                $extractedData = $analyzer->extractAndLogTableData($responseData);
                
                // 获取原始文件名
                $originalFileName = request('file_name', ''); // 尝试从请求中获取文件名
                if (empty($originalFileName)) {
                    // 使用更有描述性的文件名
                    $uploadedFileName = session('uploaded_pdf_filename');
                    if (!empty($uploadedFileName)) {
                        $originalFileName = pathinfo($uploadedFileName, PATHINFO_FILENAME);
                        Log::info("从会话中获取到文件名: {$uploadedFileName}, 使用: {$originalFileName}");
                    } else {
                        $originalFileName = 'pdf_imported_order'; // 如果没有找到，则使用默认值
                        Log::warning("未能从请求中获取文件名，使用默认值: {$originalFileName}");
                    }
                } else {
                    Log::info("从请求中获取到文件名: {$originalFileName}");
                }
                
                // 添加文件名到提取的数据中
                $extractedData['file_name'] = $originalFileName;
                
                // 记录处理完成的数据
                Log::info("文档解析完成，准备返回结果");
                
                // 构建返回数据
                return response()->json([
                    'status' => true,
                    'message' => '文档解析完成',
                    'data' => [
                        'completed' => true,
                        'failed' => false,
                        'shouldStopPolling' => true,
                        'extracted' => $extractedData,
                        'file_name' => $originalFileName
                    ]
                ]);
            }
            
            // 记录状态
            $completed = $isCompleted ? "yes" : "no";
            Log::info("checkStatus最终返回状态: completed={$completed}");
            
            // 确保响应有正确的结构
            if (!isset($responseData['data'])) {
                $responseData['data'] = [];
            }
            
            // 确保响应中包含完成状态
            $responseData['data']['completed'] = $isCompleted;
            $responseData['data']['shouldStopPolling'] = $isCompleted;
            
            // 返回JSON响应
            return response()->json([
                'status' => true,
                'message' => $isCompleted ? '文档解析已完成' : '文档解析中...',
                'data' => $responseData['data']
            ]);
            
        } catch (\Exception $e) {
            Log::error("检查任务状态异常: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            // 返回错误响应
            return response()->json([
                'status' => false,
                'message' => '检查任务状态出错: ' . $e->getMessage(),
                'data' => [
                    'completed' => false,
                    'shouldStopPolling' => true, // 出错时停止轮询
                    'error' => $e->getMessage()
                ]
            ]);
        }
    }
}