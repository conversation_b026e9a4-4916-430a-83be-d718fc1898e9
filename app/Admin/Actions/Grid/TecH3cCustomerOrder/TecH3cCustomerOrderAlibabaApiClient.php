<?php

namespace App\Admin\Actions\Grid\TecH3cCustomerOrder;

// ==== 重要修复：首先确保全局变量已定义 ====
if (!isset($GLOBALS['useAccelerate'])) {
    $GLOBALS['useAccelerate'] = false;
}
global $useAccelerate;
$useAccelerate = false;

// 为Utils::getEndpoint方法定义一个可在全局使用的安全替代函数
if (!function_exists('safe_get_endpoint')) {
    function safe_get_endpoint($endpoint, $serverUse, $endpointType) {
        if ('internal' == $endpointType) {
            $tmp = explode('.', $endpoint);
            $tmp[0] .= '-internal';
            $endpoint = implode('.', $tmp);
        }
        
        if ('accelerate' == $endpointType) {
            return 'oss-accelerate.aliyuncs.com';
        }
        
        return $endpoint;
    }
}

use Illuminate\Support\Facades\Log;
use AlibabaCloud\SDK\Docmindapi\V20220711\Docmindapi;
use AlibabaCloud\SDK\Docmindapi\V20220711\Models\SubmitDocParserJobAdvanceRequest;
use AlibabaCloud\SDK\Docmindapi\V20220711\Models\QueryDocParserStatusRequest;
use AlibabaCloud\SDK\Docmindapi\V20220711\Models\GetDocParserResultRequest;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\Tea\Exception\TeaUnableRetryError;
use GuzzleHttp\Psr7\Stream;
use AlibabaCloud\Tea\Exception\ClientException;
use AlibabaCloud\Tea\Exception\ServerException;

/**
 * 阿里云文档智能API客户端
 * 处理PDF解析的API调用功能
 */
class TecH3cCustomerOrderAlibabaApiClient
{
    /**
     * 构造函数 - 确保在实例化时初始化全局变量
     */
    public function __construct()
    {
        // 每次实例化时都重新确保全局变量已定义
        if (!isset($GLOBALS['useAccelerate'])) {
            $GLOBALS['useAccelerate'] = false;
        }
        global $useAccelerate;
        $useAccelerate = false;
        
        Log::info('阿里云PDF分析客户端已初始化');
    }
    
    /**
     * 获取阿里云API客户端配置
     *
     * @return Config|null 阿里云API配置对象
     */
    public function getClientConfig()
    {
        Log::info('获取阿里云配置信息');
        
        // 确保全局变量已定义
        if (!isset($GLOBALS['useAccelerate'])) {
            $GLOBALS['useAccelerate'] = false;
        }
        global $useAccelerate;
        $useAccelerate = false;
        
        // 尝试从环境变量获取
        $accessKeyId = env('ALIBABA_CLOUD_ACCESS_KEY_ID', '');
        $accessKeySecret = env('ALIBABA_CLOUD_ACCESS_KEY_SECRET', '');
        
        // 如果环境变量为空，尝试从config获取
        if (!$accessKeyId || !$accessKeySecret) {
            Log::info('环境变量为空，尝试从config获取');
            $accessKeyId = config('services.alibaba.access_key_id', '');
            $accessKeySecret = config('services.alibaba.access_key_secret', '');
        }
        
        // 如果配置仍为空，使用硬编码备用值（仅用于紧急修复，之后应移除）
        if (!$accessKeyId || !$accessKeySecret) {
            Log::info('配置为空，使用硬编码备用值');
            $accessKeyId = 'LTAI5tS7Lrd8MtYCXtmeUnX9';
            $accessKeySecret = '******************************';
        }

        if (!$accessKeyId || !$accessKeySecret) {
            Log::error('阿里云配置信息缺失，请检查凭证配置');
            return null;
        }

        // 创建配置对象
        $config = new Config([
            // 设置域名
            'endpoint' => 'docmind-api.cn-hangzhou.aliyuncs.com',
            // 设置凭证
            'accessKeyId' => $accessKeyId,
            'accessKeySecret' => $accessKeySecret,
            // 设置认证方式
            'type' => 'access_key',
            // 设置区域
            'regionId' => 'cn-hangzhou'
        ]);
        
        return $config;
    }
    
    /**
     * 提交文档抽取任务
     *
     * @param \Illuminate\Http\UploadedFile $file 上传的PDF文件
     * @return array 提交结果
     */
    public function submitDocParserJob($file)
    {
        // 额外保险：再次确保全局变量已定义
        if (!isset($GLOBALS['useAccelerate'])) {
            $GLOBALS['useAccelerate'] = false;
        }
        global $useAccelerate;
        $useAccelerate = false;
        
        try {
            // 初始化客户端
            $config = $this->getClientConfig();
            if (!$config) {
                return ['success' => false, 'message' => '阿里云配置信息缺失，请联系管理员'];
            }
            
            $client = new Docmindapi($config);
            
            // 记录开始提交任务
            Log::info("开始提交文档抽取任务，文件名：" . $file->getClientOriginalName());
            
            // 确保文件是一个有效的文件对象
            if (!$file->isValid()) {
                Log::error("文件无效: " . $file->getErrorMessage());
                return ['success' => false, 'message' => '文件无效: ' . $file->getErrorMessage()];
            }
            
            // 直接使用文件路径
            $filePath = $file->getRealPath();
            Log::info("文件路径: " . $filePath);
            
            // 使用GuzzleHttp的Stream类来创建符合PSR-7的流对象
            $fileResource = fopen($filePath, 'r');
            if (!$fileResource) {
                Log::error("无法打开文件: " . $filePath);
                return ['success' => false, 'message' => '无法打开文件'];
            }
            
            // 使用GuzzleHttp的Stream类包装文件资源
            $stream = new Stream($fileResource);
            
            // 创建请求对象
            $request = new SubmitDocParserJobAdvanceRequest();
            $request->fileName = $file->getClientOriginalName();
            $request->fileNameExtension = $file->getClientOriginalExtension();
            $request->fileUrlObject = $stream;
            
            // 设置运行时选项
            $runtime = new RuntimeOptions();
            $runtime->maxIdleConns = 3;
            $runtime->connectTimeout = 30000; // 30秒
            $runtime->readTimeout = 120000; // 120秒
            $runtime->autoretry = true; // 启用自动重试
            $runtime->maxAttempts = 3; // 最多尝试3次
            
            // 发送请求
            Log::info("开始发送API请求");
            $response = $client->submitDocParserJobAdvance($request, $runtime);
            
            // 处理响应结果
            $responseData = $response->toMap();
            
            // 详细记录整个响应内容，便于后续处理
            Log::info("===== PDF任务提交响应详情开始 =====");
            Log::info("响应数据: " . json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            Log::info("===== PDF任务提交响应详情结束 =====");
            
            // 根据阿里云API实际返回格式解析ID
            // 检查body.Data.Id路径 (新版API返回格式)
            if (isset($responseData['body']['Data']['Id'])) {
                $jobId = $responseData['body']['Data']['Id'];
                Log::info("成功从body.Data.Id路径获取任务ID: " . $jobId);
                
                return [
                    'success' => true, 
                    'message' => '文档抽取任务已提交', 
                    'data' => [
                        'id' => $jobId, 
                        'jobId' => $jobId  // 同时返回两种格式确保前端兼容
                    ]
                ];
            } 
            // 检查data.id路径(兼容旧版API)
            else if (isset($responseData['data']['id'])) {
                $jobId = $responseData['data']['id'];
                Log::info("成功从data.id路径获取任务ID: " . $jobId);
                
                return [
                    'success' => true, 
                    'message' => '文档抽取任务已提交', 
                    'data' => [
                        'id' => $jobId,
                        'jobId' => $jobId
                    ]
                ];
            }
            // 尝试其他可能的路径
            else {
                // 检查所有可能的路径
                $possiblePaths = [
                    'Data.Id',
                    'body.Data.Id',
                    'data.Id',
                    'id',
                    'Id'
                ];
                
                foreach ($possiblePaths as $path) {
                    $parts = explode('.', $path);
                    $value = $responseData;
                    $valid = true;
                    
                    foreach ($parts as $part) {
                        if (isset($value[$part])) {
                            $value = $value[$part];
                        } else {
                            $valid = false;
                            break;
                        }
                    }
                    
                    if ($valid) {
                        $jobId = $value;
                        Log::info("成功从 {$path} 路径获取任务ID: " . $jobId);
                        
                        return [
                            'success' => true, 
                            'message' => '文档抽取任务已提交', 
                            'data' => [
                                'id' => $jobId,
                                'jobId' => $jobId
                            ]
                        ];
                    }
                }
                
                // 记录完整响应，以便排查问题
                Log::error("未能从响应中找到任务ID，完整响应: " . json_encode($responseData));
                return [
                    'success' => false, 
                    'message' => '提交文档抽取任务时未获取到任务ID，请联系管理员检查API响应格式'
                ];
            }
            
        } catch (TeaUnableRetryError $e) {
            $errorMsg = "调用SubmitDocParserJobAdvance API失败: " . $e->getMessage();
            Log::error($errorMsg);
            if (strpos($e->getMessage(), 'InvalidAccessKeyId') !== false) {
                $errorMsg = "阿里云访问密钥无效，请检查配置";
            } elseif (strpos($e->getMessage(), 'SignatureDoesNotMatch') !== false) {
                $errorMsg = "阿里云签名不匹配，请检查AccessKey配置";
            }
            return ['success' => false, 'message' => $errorMsg];
        } catch (\Error $e) {
            Log::error("PHP错误: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ['success' => false, 'message' => 'PHP错误: ' . $e->getMessage()];
        } catch (\Exception $e) {
            Log::error("提交文档抽取任务异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ['success' => false, 'message' => '系统异常: ' . $e->getMessage()];
        }
    }
    
    /**
     * 查询文档解析任务状态
     *
     * @param string $jobId 任务ID
     * @return array 任务状态
     */
    public function queryDocParserStatus($jobId)
    {
        try {
            // 确保全局变量已初始化
            if (!isset($GLOBALS['useAccelerate'])) {
                $GLOBALS['useAccelerate'] = false;
            }
            global $useAccelerate;
            $useAccelerate = false;
            
            // 获取客户端配置
            $config = $this->getClientConfig();
            if (!$config) {
                return [
                    'success' => false, 
                    'message' => '阿里云配置信息缺失，请联系管理员'
                ];
            }
            
            $client = new Docmindapi($config);
            
            // 记录请求开始
            Log::info("开始查询文档解析任务状态: jobId = {$jobId}");
            
            $request = new QueryDocParserStatusRequest();
            $request->id = $jobId;
            
            $runtime = new RuntimeOptions();
            $runtime->maxIdleConns = 3;
            $runtime->connectTimeout = 10000;
            $runtime->readTimeout = 10000;
            
            // 发送请求并获取响应
            $response = $client->queryDocParserStatus($request, $runtime);
            
            // 记录响应
            $responseData = $response->toMap();
            Log::info("任务状态查询结果:");
            Log::info(json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            // 检查任务状态 - 按照阿里云API文档，Status字段的值为Success、Fail或处理中
            $status = null;
            
            // 兼容不同的返回结构
            if (isset($responseData['body']['Data']['Status'])) {
                $status = $responseData['body']['Data']['Status'];
            } elseif (isset($responseData['Data']['Status'])) {
                $status = $responseData['Data']['Status'];
            } elseif (isset($responseData['body']['data']['Status'])) {
                $status = $responseData['body']['data']['Status'];
            } elseif (isset($responseData['data']['Status'])) {
                $status = $responseData['data']['Status'];
            }
            
            // 标准化状态值（阿里云API可能返回大小写不一致的状态值）
            $status = strtolower($status ?? 'processing');
            
            // 根据状态确定处理结果
            $isCompleted = ($status === 'success');
            $isFailed = ($status === 'fail');
            
            Log::info("任务状态: " . ($status ?? 'Unknown') . ", 是否完成: " . ($isCompleted ? 'Yes' : 'No'));
            
            // 返回状态信息 - 保持与原有代码兼容的格式
            return [
                'success' => true,
                'message' => $isCompleted ? '文档解析任务已完成' : ($isFailed ? '文档解析任务失败' : '文档解析任务进行中'),
                'data' => [
                    'status' => $isCompleted ? 'completed' : ($isFailed ? 'failed' : 'processing'),
                    'completed' => $isCompleted,  // 为确保与现有代码兼容，保留completed字段
                    'failed' => $isFailed,
                    'shouldStopPolling' => $isCompleted || $isFailed,
                    'isComplete' => $isCompleted,
                    'rawStatus' => $status,
                    'numberOfSuccessfulParsing' => isset($responseData['body']['Data']['NumberOfSuccessfulParsing']) ? 
                        $responseData['body']['Data']['NumberOfSuccessfulParsing'] : 0,
                    'tokens' => isset($responseData['body']['Data']['Tokens']) ? 
                        $responseData['body']['Data']['Tokens'] : 0,
                    'paragraphCount' => isset($responseData['body']['Data']['ParagraphCount']) ? 
                        $responseData['body']['Data']['ParagraphCount'] : 0,
                    'tableCount' => isset($responseData['body']['Data']['TableCount']) ? 
                        $responseData['body']['Data']['TableCount'] : 0,
                    'imageCount' => isset($responseData['body']['Data']['ImageCount']) ? 
                        $responseData['body']['Data']['ImageCount'] : 0
                ],
                'rawResponse' => $responseData // 传递原始响应，以便提取更多信息
            ];
            
        } catch (TeaUnableRetryError $e) {
            Log::error("调用 QueryDocParserStatus API 时出现错误: ". $e->getMessage());
            return [
                'success' => false,
                'message' => '调用 QueryDocParserStatus API 时出现错误: '. $e->getMessage(),
                'data' => [
                    'completed' => false,
                    'failed' => true,
                    'shouldStopPolling' => true
                ]
            ];
        } catch (\Exception $e) {
            Log::error("查询文档解析任务状态出错: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            return [
                'success' => false,
                'message' => '系统异常: ' . $e->getMessage(),
                'data' => [
                    'completed' => false,
                    'failed' => true,
                    'shouldStopPolling' => true
                ]
            ];
        }
    }
    
    /**
     * 获取文档解析结果
     *
     * @param string $jobId 文档解析任务ID
     * @param bool $withLayout 是否包含布局信息
     * @return array 解析结果
     */
    public function getDocParserResult($jobId, $withLayout = true)
    {
        Log::info("开始获取文档解析结果，任务ID: {$jobId}, 包含布局: " . ($withLayout ? 'true' : 'false'));
        
        try {
            // 确保全局变量已初始化
            if (!isset($GLOBALS['useAccelerate'])) {
                $GLOBALS['useAccelerate'] = false;
            }
            global $useAccelerate;
            $useAccelerate = false;
            
            // 初始化客户端
            $config = $this->getClientConfig();
            if (!$config) {
                Log::error("未配置API客户端，无法获取文档解析结果");
                return [
                    'success' => false,
                    'message' => '未配置API客户端'
                ];
            }
            
            $client = new Docmindapi($config);
            
            // 创建请求对象
            $request = new GetDocParserResultRequest();
            $request->id = $jobId;
            $request->layoutNum = 0;
            $request->layoutStepSize = $withLayout ? 100 : 0;
            
            // 设置请求运行时配置
            $runtime = new RuntimeOptions();
            $runtime->maxIdleConns = 3;
            $runtime->connectTimeout = 10000;
            $runtime->readTimeout = 10000;
            
            // 发送请求并获取响应
            $response = $client->getDocParserResult($request, $runtime);
            
            // 记录原始响应
            $responseData = $response->toMap();
            Log::info("文档解析结果API响应 (开始)");
            Log::info("RequestId: " . ($responseData['body']['RequestId'] ?? '未知'));
            
            // 记录顶层字段
            Log::info("响应包含以下顶层字段: " . json_encode(array_keys($responseData)));
            if (isset($responseData['body'])) {
                Log::info("body包含以下字段: " . json_encode(array_keys($responseData['body'])));
            }
            
            // 检查响应状态
            if (!isset($responseData['body'])) {
                Log::error("获取文档解析结果失败: 响应缺少body字段");
                return [
                    'success' => false,
                    'message' => "获取文档解析结果失败: 响应格式错误"
                ];
            }
            
            // 记录响应结构,帮助调试
            Log::info("响应body字段包含: " . json_encode(array_keys($responseData['body'])));
            
            // 检查是否有错误码 - 兼容多种格式
            $hasError = false;
            $errorMessage = '未知错误';
            
            if (isset($responseData['body']['Code'])) {
                // 有Code字段的情况
                if ($responseData['body']['Code'] !== 'Success' && $responseData['body']['Code'] !== '200') {
                    $hasError = true;
                    $errorMessage = isset($responseData['body']['Message']) 
                        ? $responseData['body']['Message'] 
                        : '错误码: ' . $responseData['body']['Code'];
                }
            }
            
            if ($hasError) {
                Log::error("获取文档解析结果失败: {$errorMessage}");
                return [
                    'success' => false,
                    'message' => "获取文档解析结果失败: {$errorMessage}"
                ];
            }
            
            // 直接检查是否有Data字段 - 即使没有Code字段也可以处理
            if (!isset($responseData['body']['Data']) && !isset($responseData['body']['data'])) {
                Log::warning("API返回成功但无Data字段: " . json_encode(array_keys($responseData['body'])));
                // 尝试返回整个body作为Data
                return [
                    'success' => true,
                    'message' => '获取文档解析结果成功',
                    'rawResponse' => $responseData,
                    'body' => [
                        'Data' => $responseData['body'] // 将整个body作为Data返回
                    ]
                ];
            }
            
            // 增强调试输出
            $this->logDataNodes($responseData);
            
            // 判断是否无响应数据
            if (empty($responseData['body']['Data'])) {
                Log::warning("API返回成功但无数据 (Data节点为空)");
                return [
                    'success' => true,
                    'message' => '获取文档解析结果成功，但无数据',
                    'data' => []
                ];
            }
            
            // 返回成功结果
            Log::info("成功获取文档解析结果");
            Log::info("文档解析结果API响应 (结束)");
            
            // 为后续处理准备数据，添加原始响应到返回结构中
            return [
                'success' => true,
                'message' => '获取文档解析结果成功',
                'rawResponse' => $responseData,
                'body' => $responseData['body']
            ];
            
        } catch (TeaUnableRetryError $e) {
            // 处理客户端异常
            Log::error("获取文档解析结果时客户端异常: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            return [
                'success' => false,
                'message' => "客户端错误: " . $e->getMessage()
            ];
        } catch (\Exception $e) {
            // 处理其他异常
            Log::error("获取文档解析结果时发生异常: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            return [
                'success' => false,
                'message' => "系统错误: " . $e->getMessage()
            ];
        }
    }
    
    /**
     * 对响应数据中的关键节点进行日志记录，帮助调试
     *
     * @param array $responseData API响应数据
     */
    private function logDataNodes($responseData)
    {
        // 检查各种可能的数据节点路径
        $dataNode = null;
        $dataNodePath = '';
        
        if (isset($responseData['body']['Data'])) {
            $dataNode = $responseData['body']['Data'];
            $dataNodePath = 'body.Data';
        } elseif (isset($responseData['Data'])) {
            $dataNode = $responseData['Data'];
            $dataNodePath = 'Data';
        } elseif (isset($responseData['data'])) {
            $dataNode = $responseData['data'];
            $dataNodePath = 'data';
        } elseif (isset($responseData['body']['data'])) {
            $dataNode = $responseData['body']['data'];
            $dataNodePath = 'body.data';
        }
        
        if ($dataNode) {
            Log::info("找到数据节点: {$dataNodePath}");
            
            // 记录数据节点的键
            Log::info("数据节点键: " . json_encode(array_keys($dataNode)));
            
            // 检查是否有layouts
            if (isset($dataNode['layouts'])) {
                Log::info("找到layouts数据, 包含 " . count($dataNode['layouts']) . " 个布局元素");
            }
            
            // 检查是否有kvInfo
            if (isset($dataNode['kvInfo'])) {
                $kvCount = is_array($dataNode['kvInfo']) ? count($dataNode['kvInfo']) : 'non-array';
                Log::info("找到kvInfo数据, 包含 {$kvCount} 个KV项");
            }
            
            // 检查是否有kvListInfo
            if (isset($dataNode['kvListInfo'])) {
                $kvListCount = is_array($dataNode['kvListInfo']) ? count($dataNode['kvListInfo']) : 'non-array';
                Log::info("找到kvListInfo数据, 包含 {$kvListCount} 个KVList项");
            }
            
            // 检查是否有tables
            if (isset($dataNode['tables'])) {
                $tablesCount = is_array($dataNode['tables']) ? count($dataNode['tables']) : 'non-array';
                Log::info("找到tables数据, 包含 {$tablesCount} 个表格");
            }
        } else {
            Log::warning("未找到数据节点 (Data/data)");
            Log::info("响应数据顶层键: " . json_encode(array_keys($responseData)));
            
            if (isset($responseData['body'])) {
                Log::info("body节点键: " . json_encode(array_keys($responseData['body'])));
            }
        }
    }
    
    /**
     * 递归查找表格数据
     * 
     * @param array $data 数据源
     * @param int $depth 当前递归深度
     * @return array|null 找到的表格数据
     */
    private function findTablesRecursive($data, $depth = 0)
    {
        if ($depth > 10) return null; // 防止无限递归
        
        // 检查当前节点是否是表格数组的特征
        if (is_array($data) && !empty($data) && isset($data[0]) && is_array($data[0])) {
            // 检查是否有表格特征（有sheetName或至少有header/body字段）
            $hasTableFeatures = false;
            if (isset($data[0]['sheetName']) || isset($data[0]['SheetName']) || 
                isset($data[0]['name']) || isset($data[0]['Name'])) {
                $hasTableFeatures = true;
            } elseif (isset($data[0]['header']) || isset($data[0]['body']) || 
                     isset($data[0]['Header']) || isset($data[0]['Body'])) {
                $hasTableFeatures = true;
            } elseif (isset($data[0]['tableKVs']) || isset($data[0]['TableKVs']) || 
                     isset($data[0]['kvInfo']) || isset($data[0]['kvListInfo'])) {
                $hasTableFeatures = true;
            }
            
            if ($hasTableFeatures) {
                return $data;
            }
        }
        
        // 递归检查子元素
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (is_array($value)) {
                    $result = $this->findTablesRecursive($value, $depth + 1);
                    if ($result !== null) {
                        return $result;
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 分析响应结构
     * 
     * @param array $data 响应数据
     * @return array 结构信息
     */
    public function analyzeResponseStructure($data)
    {
        $structure = [];
        
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                if ($key === 'headers') {
                    $structure[$key] = '[headers数组]';
                } else {
                    $structure[$key] = $this->getArrayInfo($value);
                }
            } else {
                $structure[$key] = gettype($value);
            }
        }
        
        return $structure;
    }
    
    /**
     * 获取数组信息
     * 
     * @param array $array 数组
     * @return string 数组信息
     */
    private function getArrayInfo($array)
    {
        if (empty($array)) {
            return '空数组';
        }
        
        $keys = array_keys($array);
        $firstKey = reset($keys);
        
        if (is_numeric($firstKey)) {
            return '索引数组 [' . count($array) . '项]';
        } else {
            $subKeys = [];
            foreach ($keys as $key) {
                if (isset($array[$key]) && is_array($array[$key])) {
                    $subKeys[] = $key . ':[' . $this->getArrayInfo($array[$key]) . ']';
                } else {
                    $subKeys[] = $key;
                }
            }
            return '关联数组 {' . implode(', ', $subKeys) . '}';
        }
    }
} 