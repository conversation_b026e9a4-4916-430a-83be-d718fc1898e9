<?php

namespace App\Admin\Actions\Grid\TecH3cCustomerOrder;

use Illuminate\Support\Facades\Log;

/**
 * PDF表格分析器
 * 负责解析阿里云API返回的表格数据,提取结构化信息
 */
class TecH3cCustomerOrderPDFAnalyzer
{
    /**
     * 提取并记录表格数据
     * 
     * @param array $responseData API响应数据
     * @return array|null 整合后的数据
     */
    // public function extractAndLogTableData($responseData)
    // {
    //     Log::info('TecH3cCustomerOrderPDFAnalyzer->extractAndLogTableData(): 开始处理API响应数据');
        
    //     // 记录API响应信息
    //     if (empty($responseData)) {
    //         Log::warning('TecH3cCustomerOrderPDFAnalyzer->extractAndLogTableData(): 响应数据为空');
    //         return ['header' => [], 'details' => []];
    //     }
        
    //     // 记录响应结构以进行调试
    //     $keys = is_array($responseData) ? array_keys($responseData) : ['非数组响应'];
    //     Log::info('收到API响应，顶层结构: ' . json_encode($keys));
    //     $this->logJsonStructure($responseData, '响应根节点');
        
    //     // 尝试多种可能的路径定位数据节点
    //     $dataNodePaths = [
    //         ['body', 'Data'],
    //         ['body', 'data'],
    //         ['body'], // 尝试整个body作为数据源
    //         ['Data'],
    //         ['data'],
    //         ['rawResponse', 'body', 'Data'],
    //         ['rawResponse', 'body'],
    //         ['RequestId'], // 如果存在,说明这可能是直接的API响应
    //     ];
        
    //     foreach ($dataNodePaths as $path) {
    //         $node = $responseData;
    //         $validPath = true;
    //         $pathStr = '';
            
    //         foreach ($path as $key) {
    //             $pathStr .= ($pathStr ? '.' : '') . $key;
    //             if (isset($node[$key])) {
    //                 $node = $node[$key];
    //             } else {
    //                 $validPath = false;
    //                 break;
    //             }
    //         }
            
    //         if ($validPath) {
    //             Log::info("尝试使用路径 '{$pathStr}' 处理数据");
    //             // 处理找到的数据节点
    //             $result = $this->processDataNode($node);
    //             if (!empty($result['header']) || !empty($result['details'])) {
    //                 Log::info("成功从路径 '{$pathStr}' 提取数据");
    //                 return $result;
    //             }
    //         }
    //     }
        
    //     // 如果所有路径都失败,尝试深度提取
    //     Log::info("预定义路径均未找到有效数据,尝试深度数据提取");
    //     $allSourceData = $this->extractFromAllPossibleSources($responseData);
        
    //     // 检查是否已提取到有效数据
    //     if (!empty($allSourceData['header']) || !empty($allSourceData['details'])) {
    //         Log::info('深度数据提取成功，返回结果。头部字段数: ' . count($allSourceData['header']) . 
    //                  ', 明细行数: ' . count($allSourceData['details']));
    //         return $allSourceData;
    //     }
        
    //     // 所有尝试都失败,尝试递归搜索表格结构
    //     Log::info("尝试递归搜索表格结构");
    //     $tables = $this->findTablesDeepSearch($responseData);
        
    //     if (!empty($tables)) {
    //         Log::info('找到 ' . count($tables) . ' 个可能的表格结构');
            
    //         $result = [
    //             'header' => [],
    //             'details' => []
    //         ];
            
    //         foreach ($tables as $index => $table) {
    //             Log::info("处理第 {$index} 个表格结构");
    //             $this->processTableData($table, $result);
    //         }
            
    //         if (!empty($result['header']) || !empty($result['details'])) {
    //             Log::info('表格处理成功,返回结果');
    //             return $result;
    //         }
    //     }
        
    //     // 下面是原有的处理逻辑,作为最后的尝试
    //     // 寻找Data节点 - 支持多种可能的响应格式
    //     $dataNode = null;
    //     if (isset($responseData['body']) && isset($responseData['body']['Data'])) {
    //         $dataNode = $responseData['body']['Data'];
    //     } elseif (isset($responseData['Data'])) {
    //         $dataNode = $responseData['Data'];
    //     } elseif (isset($responseData['data'])) {
    //         $dataNode = $responseData['data'];
    //     } elseif (isset($responseData['body']) && isset($responseData['body']['data'])) {
    //         $dataNode = $responseData['body']['data'];
    //     }

    //     if (!$dataNode) {
    //         Log::error('无法找到数据节点(Data/data)，响应结构: ' . json_encode(array_keys($responseData)));
    //         return ['header' => [], 'details' => []];
    //     }

    //     $result = [
    //         'header' => [],
    //         'details' => []
    //     ];

    //     // 检查是否有layouts字段
    //     if (isset($dataNode['layouts']) && !empty($dataNode['layouts'])) {
    //         Log::info('找到layouts字段，处理表格布局');
    //         foreach ($dataNode['layouts'] as $layout) {
    //             $this->processTableLayout($layout, $result);
    //         }
    //     } else {
    //         Log::warning('未找到layouts字段，尝试处理其他数据格式');
    //         $this->processAlternativeFormat($dataNode, $result);
    //     }

    //     Log::info('表格数据提取完成，头部字段数: ' . count($result['header']) . ', 明细行数: ' . count($result['details']));
    //     return $result;
    // }
    
    // /**
    //  * 处理不同类型的数据节点
    //  * 
    //  * @param array $node 数据节点
    //  * @return array 处理结果
    //  */
    // private function processDataNode($node)
    // {
    //     $result = [
    //         'header' => [],
    //         'details' => []
    //     ];
        
    //     // 检查是否有layouts字段
    //     if (isset($node['layouts']) && !empty($node['layouts'])) {
    //         Log::info('找到layouts字段，处理表格布局');
    //         foreach ($node['layouts'] as $layout) {
    //             $this->processTableLayout($layout, $result);
    //         }
    //     } 
    //     // 检查是否有tables字段
    //     else if (isset($node['tables']) && !empty($node['tables'])) {
    //         Log::info('找到tables字段，共有 ' . count($node['tables']) . ' 个表格');
    //         foreach ($node['tables'] as $table) {
    //             $this->processTableData($table, $result);
    //         }
    //     }
    //     // 尝试其他可能的结构
    //     else {
    //         Log::info('尝试处理非标准数据格式');
    //         $this->processAlternativeFormat($node, $result);
    //     }
        
    //     return $result;
    // }
    
    // /**
    //  * 处理标准响应格式
    //  * 
    //  * @param array $data Data节点数据
    //  * @return array 处理后的表格数据
    //  */
    // private function processStandardResponse($data)
    // {
    //     $result = [
    //         'header' => [],
    //         'details' => []
    //     ];
        
    //     // 检查是否有layouts字段
    //     if (isset($data['layouts']) && !empty($data['layouts'])) {
    //         Log::info('找到layouts字段，共有 ' . count($data['layouts']) . ' 个布局元素');
            
    //         // 查找表格类型的布局
    //         $tableLayouts = array_filter($data['layouts'], function($layout) {
    //             return isset($layout['type']) && $layout['type'] === 'table';
    //         });
            
    //         if (!empty($tableLayouts)) {
    //             Log::info('找到 ' . count($tableLayouts) . ' 个表格布局');
                
    //             foreach ($tableLayouts as $layout) {
    //                 $this->processTableLayout($layout, $result);
    //             }
    //         } else {
    //             Log::warning('layouts中未找到表格类型的布局');
    //         }
    //     } else {
    //         Log::warning('Data节点中未找到layouts字段');
    //     }
        
    //     return $result;
    // }
    
    // /**
    //  * 处理替代数据格式，当layouts字段不存在时使用
    //  *
    //  * @param array $dataNode 数据节点
    //  * @param array &$result 引用传递的结果数组，用于存储提取的数据
    //  */
    // private function processAlternativeFormat($dataNode, &$result)
    // {
    //     Log::info('尝试从替代数据格式中提取数据');
        
    //     // 1. 尝试寻找表格数据
    //     $tableData = $this->findTablesRecursive($dataNode);
    //     if ($tableData) {
    //         Log::info('在替代格式中找到表格数据');
    //         $tableResult = $this->processAlternativeDataFormat($tableData);
    //         $result['header'] = array_merge($result['header'], $tableResult['header']);
    //         $result['details'] = array_merge($result['details'], $tableResult['details']);
    //         return;
    //     }
        
    //     // 2. 尝试寻找KV信息(通常是头部信息)
    //     $kvInfo = $this->findKVInfo($dataNode);
    //     if ($kvInfo && is_array($kvInfo) && !empty($kvInfo)) {
    //         Log::info('找到KV信息，包含 ' . count($kvInfo) . ' 项');
            
    //         foreach ($kvInfo as $kv) {
    //             $key = null;
    //             $value = null;
                
    //             // 提取键
    //             if (isset($kv['key'])) {
    //                 $key = is_array($kv['key']) ? implode(' ', $kv['key']) : $kv['key'];
    //             } elseif (isset($kv['Key'])) {
    //                 $key = is_array($kv['Key']) ? implode(' ', $kv['Key']) : $kv['Key'];
    //             }
                
    //             // 提取值
    //             if (isset($kv['value'])) {
    //                 $value = is_array($kv['value']) ? implode(' ', $kv['value']) : $kv['value'];
    //             } elseif (isset($kv['Value'])) {
    //                 $value = is_array($kv['Value']) ? implode(' ', $kv['Value']) : $kv['Value'];
    //             }
                
    //             if ($key && $value) {
    //                 $normalizedKey = $this->normalizeKey($key);
    //                 $result['header'][$normalizedKey] = $value;
    //             }
    //         }
    //     }
        
    //     // 3. 尝试寻找KVList信息(通常是明细信息)
    //     $kvListInfo = $this->findKVListInfo($dataNode);
    //     if ($kvListInfo && is_array($kvListInfo) && !empty($kvListInfo)) {
    //         Log::info('找到KVList信息，包含 ' . count($kvListInfo) . ' 行');
            
    //         foreach ($kvListInfo as $row) {
    //             $item = [];
                
    //             // 确保行是数组
    //             if (!is_array($row)) {
    //                 continue;
    //             }
                
    //             // 处理行中的每个KV项
    //             foreach ($row as $kv) {
    //                 if (!is_array($kv)) {
    //                     continue;
    //                 }
                    
    //                 $key = null;
    //                 $value = null;
                    
    //                 // 提取键
    //                 if (isset($kv['key'])) {
    //                     $key = is_array($kv['key']) ? implode(' ', $kv['key']) : $kv['key'];
    //                 } elseif (isset($kv['Key'])) {
    //                     $key = is_array($kv['Key']) ? implode(' ', $kv['Key']) : $kv['Key'];
    //                 }
                    
    //                 // 提取值
    //                 if (isset($kv['value'])) {
    //                     $value = is_array($kv['value']) ? implode(' ', $kv['value']) : $kv['value'];
    //                 } elseif (isset($kv['Value'])) {
    //                     $value = is_array($kv['Value']) ? implode(' ', $kv['Value']) : $kv['Value'];
    //                 }
                    
    //                 if ($key && $value) {
    //                     $normalizedKey = $this->normalizeKey($key);
    //                     $item[$normalizedKey] = $value;
                        
    //                     // 处理常见字段映射
    //                     $this->mapToStandardField($key, $value, $item);
    //                 }
    //             }
                
    //             if (!empty($item)) {
    //                 $result['details'][] = $item;
    //             }
    //         }
    //     }
        
    //     Log::info('替代格式处理完成，提取的头部字段数: ' . count($result['header']) . 
    //              ', 明细行数: ' . count($result['details']));
    // }
    
    // /**
    //  * 处理替代数据格式的表格数据
    //  *
    //  * @param array $tableData 表格数据
    //  * @return array 包含header和details的关联数组
    //  */
    // private function processAlternativeDataFormat($tableData)
    // {
    //     $result = [
    //         'header' => [],
    //         'details' => []
    //     ];
        
    //     Log::info('处理替代数据格式的表格数据');
        
    //     // 检查是否有行数据
    //     $rowsKey = null;
    //     if (isset($tableData['rows'])) {
    //         $rowsKey = 'rows';
    //     } elseif (isset($tableData['Rows'])) {
    //         $rowsKey = 'Rows';
    //     }
        
    //     if (!$rowsKey || empty($tableData[$rowsKey])) {
    //         Log::warning('表格数据中没有行信息');
    //         return $result;
    //     }
        
    //     // 提取标题行（首行）
    //     $headers = [];
    //     $headerRow = $tableData[$rowsKey][0];
        
    //     $cellsKey = null;
    //     if (isset($headerRow['cells'])) {
    //         $cellsKey = 'cells';
    //     } elseif (isset($headerRow['Cells'])) {
    //         $cellsKey = 'Cells';
    //     }
        
    //     if (!$cellsKey || empty($headerRow[$cellsKey])) {
    //         Log::warning('表格数据中没有单元格信息');
    //         return $result;
    //     }
        
    //     // 提取表头信息
    //     foreach ($headerRow[$cellsKey] as $index => $cell) {
    //         $content = null;
    //         if (isset($cell['content'])) {
    //             $content = $cell['content'];
    //         } elseif (isset($cell['Content'])) {
    //             $content = $cell['Content'];
    //         } elseif (isset($cell['text'])) {
    //             $content = $cell['text'];
    //         } elseif (isset($cell['Text'])) {
    //             $content = $cell['Text'];
    //         }
            
    //         if ($content) {
    //             $headers[$index] = $this->normalizeKey($content);
    //         } else {
    //             $headers[$index] = "column_" . $index;
    //         }
    //     }
        
    //     // 提取数据行
    //     for ($i = 1; $i < count($tableData[$rowsKey]); $i++) {
    //         $row = $tableData[$rowsKey][$i];
            
    //         if (!isset($row[$cellsKey]) || empty($row[$cellsKey])) {
    //             continue;
    //         }
            
    //         $item = [];
    //         foreach ($row[$cellsKey] as $index => $cell) {
    //             $content = null;
    //             if (isset($cell['content'])) {
    //                 $content = $cell['content'];
    //             } elseif (isset($cell['Content'])) {
    //                 $content = $cell['Content'];
    //             } elseif (isset($cell['text'])) {
    //                 $content = $cell['text'];
    //             } elseif (isset($cell['Text'])) {
    //                 $content = $cell['Text'];
    //             }
                
    //             if ($content && isset($headers[$index])) {
    //                 $item[$headers[$index]] = $content;
                    
    //                 // 处理常见字段映射
    //                 $this->mapToStandardField($headers[$index], $content, $item);
    //             }
    //         }
            
    //         if (!empty($item)) {
    //             $result['details'][] = $item;
    //         }
    //     }
        
    //     Log::info('替代数据格式表格处理完成，提取的详情行数: ' . count($result['details']));
    //     return $result;
    // }
    
    // /**
    //  * 将常见字段名映射到标准字段
    //  * 
    //  * @param string $key 原始字段名
    //  * @param string $value 字段值
    //  * @param array &$item 要添加映射的数据项
    //  */
    // private function mapToStandardField($key, $value, &$item)
    // {
    //     // 转换为小写便于比较
    //     $lowerKey = mb_strtolower(trim($key));
        
    //     // 订单号/批号相关
    //     if (mb_strpos($lowerKey, '订单号') !== false || 
    //         mb_strpos($lowerKey, 'po') !== false || 
    //         mb_strpos($lowerKey, '注文番号') !== false ||
    //         mb_strpos($lowerKey, 'order') !== false) {
    //         $item['order_number'] = $value;
    //     }
        
    //     // 日期相关
    //     elseif (mb_strpos($lowerKey, '日期') !== false || 
    //            mb_strpos($lowerKey, 'date') !== false || 
    //            mb_strpos($lowerKey, '発行日') !== false) {
    //         $item['order_date'] = $value;
    //     }
        
    //     // 产品名称相关
    //     elseif (mb_strpos($lowerKey, '产品名称') !== false || 
    //            mb_strpos($lowerKey, '产品') !== false || 
    //            mb_strpos($lowerKey, '名称') !== false || 
    //            mb_strpos($lowerKey, 'product') !== false ||
    //            mb_strpos($lowerKey, 'item') !== false ||
    //            mb_strpos($lowerKey, 'name') !== false) {
    //         $item['product_name'] = $value;
    //     }
        
    //     // 型号相关
    //     elseif (mb_strpos($lowerKey, '型号') !== false || 
    //            mb_strpos($lowerKey, 'model') !== false || 
    //            mb_strpos($lowerKey, 'sku') !== false || 
    //            mb_strpos($lowerKey, 'part') !== false) {
    //         $item['model'] = $value;
    //     }
        
    //     // 数量相关
    //     elseif (mb_strpos($lowerKey, '数量') !== false || 
    //            mb_strpos($lowerKey, 'qty') !== false || 
    //            mb_strpos($lowerKey, 'quantity') !== false || 
    //            mb_strpos($lowerKey, '個数') !== false) {
    //         $item['quantity'] = $this->extractNumber($value);
    //     }
        
    //     // 单价相关
    //     elseif (mb_strpos($lowerKey, '单价') !== false || 
    //            mb_strpos($lowerKey, 'price') !== false || 
    //            mb_strpos($lowerKey, 'unit price') !== false || 
    //            mb_strpos($lowerKey, '単価') !== false) {
    //         $item['unit_price'] = $this->extractNumber($value);
    //     }
        
    //     // 金额相关
    //     elseif (mb_strpos($lowerKey, '金额') !== false || 
    //            mb_strpos($lowerKey, 'amount') !== false || 
    //            mb_strpos($lowerKey, 'total') !== false || 
    //            mb_strpos($lowerKey, '合計') !== false) {
    //         $item['amount'] = $this->extractNumber($value);
    //     }
        
    //     // 预计到货日期
    //     elseif (mb_strpos($lowerKey, '到货日期') !== false || 
    //            mb_strpos($lowerKey, 'arrival') !== false || 
    //            mb_strpos($lowerKey, 'delivery') !== false || 
    //            mb_strpos($lowerKey, 'eta') !== false) {
    //         $item['arrival_date'] = $value;
    //     }
    // }
    
    // /**
    //  * 从文本中提取数字
    //  * 
    //  * @param string $text 包含数字的文本
    //  * @return float|null 提取的数字，如果没有找到则返回null
    //  */
    // private function extractNumber($text)
    // {
    //     // 移除所有空格
    //     $text = str_replace(' ', '', $text);
        
    //     // 匹配数字（包括小数点和逗号）
    //     if (preg_match('/([0-9,\.]+)/', $text, $matches)) {
    //         // 移除逗号，保留小数点
    //         $number = str_replace(',', '', $matches[1]);
    //         return (float)$number;
    //     }
        
    //     return null;
    // }
    
    // /**
    //  * 处理表格类型的布局
    //  * 
    //  * @param array $layout 表格布局数据
    //  * @param array &$combinedData 引用传递的结果数据
    //  */
    // private function processTableLayout($layout, &$combinedData)
    // {
    //     // 检查表格是否有内容
    //     if (!isset($layout['cells']) || empty($layout['cells'])) {
    //         Log::info("表格没有有效的cells数据");
    //         return;
    //     }
        
    //     // 提取表头和行数据
    //     $headers = [];
    //     $rows = [];
    //     $rowData = [];
    //     $currentRow = -1;
        
    //     foreach ($layout['cells'] as $cell) {
    //         // 检查单元格是否有行和列信息
    //         if (!isset($cell['rowIndex']) || !isset($cell['columnIndex']) || !isset($cell['content'])) {
    //             continue;
    //         }
            
    //         $rowIndex = $cell['rowIndex'];
    //         $columnIndex = $cell['columnIndex'];
    //         $content = $cell['content'];
            
    //         // 处理表头（第一行）
    //         if ($rowIndex === 0) {
    //             $headers[$columnIndex] = $content;
    //         } else {
    //             // 处理数据行
    //             if ($currentRow !== $rowIndex) {
    //                 // 存储前一行数据
    //                 if (!empty($rowData) && $currentRow > 0) {
    //                     $rows[] = $rowData;
    //                 }
    //                 // 开始新行
    //                 $currentRow = $rowIndex;
    //                 $rowData = [];
    //             }
                
    //             $rowData[$columnIndex] = $content;
    //         }
    //     }
        
    //     // 添加最后一行
    //     if (!empty($rowData)) {
    //         $rows[] = $rowData;
    //     }
        
    //     // 通过表格内容判断表格类型（订单头部信息还是明细）
    //     if ($this->isDetailTable($headers, $rows)) {
    //         // 处理订单明细表格
    //         $this->processDetailTable($headers, $rows, $combinedData);
    //     } else {
    //         // 处理订单头部信息表格
    //         $this->processHeaderTable($headers, $rows, $combinedData);
    //     }
    // }
    
    // /**
    //  * 处理段落类型的布局
    //  * 
    //  * @param array $layout 段落布局数据
    //  * @param array &$combinedData 引用传递的结果数据
    //  */
    // private function processParagraphLayout($layout, &$combinedData)
    // {
    //     // 检查段落是否有内容
    //     if (!isset($layout['content']) || empty($layout['content'])) {
    //         return;
    //     }
        
    //     $content = $layout['content'];
        
    //     // 处理可能包含的键值对信息 - 增加日文支持
    //     if (preg_match('/(?:订单号|注文番号|注文書番号|発注番号)[:：]?\s*([A-Za-z0-9-]+)/u', $content, $matches)) {
    //         $combinedData['header']['order_number'] = trim($matches[1]);
    //     }
        
    //     // 匹配日期格式包括日文格式 (令和X年X月X日)
    //     if (preg_match('/(?:日期|発行日|納期)[:：]?\s*(\d{4}[\/\-\.]\d{1,2}[\/\-\.]\d{1,2})/u', $content, $matches)) {
    //         $combinedData['header']['order_date'] = trim($matches[1]);
    //     } elseif (preg_match('/(?:令和|平成)\s*(\d+)\s*年\s*(\d+)\s*月\s*(\d+)\s*日/u', $content, $matches)) {
    //         // 处理日本年号 (简单处理，令和1年=2019年)
    //         $year = 0;
    //         if (mb_strpos($content, '令和') !== false) {
    //             $year = 2018 + (int)$matches[1]; // 令和1年 = 2019年
    //         } elseif (mb_strpos($content, '平成') !== false) {
    //             $year = 1988 + (int)$matches[1]; // 平成1年 = 1989年
    //         }
            
    //         if ($year > 0) {
    //             $month = (int)$matches[2];
    //             $day = (int)$matches[3];
    //             $combinedData['header']['order_date'] = sprintf("%04d-%02d-%02d", $year, $month, $day);
    //         }
    //     }
        
    //     // 匹配公司名称 (日文)
    //     if (preg_match('/(?:株式会社|有限会社)\s*([^\n\r]+)(?:\r|\n|$)/u', $content, $matches)) {
    //         $combinedData['header']['customer'] = trim($matches[0]); // 包含"株式会社"等前缀
    //     }
        
    //     // 匹配总金额
    //     if (preg_match('/(?:合計金額|総額|合計|総計)[:：]?\s*.*?([0-9,\.]+)(?:\s*円)?/u', $content, $matches)) {
    //         $amount = str_replace(',', '', $matches[1]);
    //         $combinedData['header']['total_amount'] = (float)$amount;
    //     }
        
    //     // 其他可能的字段...
    // }
    
    // /**
    //  * 判断是否为明细表格
    //  * 
    //  * @param array $headers 表头
    //  * @param array $rows 行数据
    //  * @return bool 是否为明细表格
    //  */
    // private function isDetailTable($headers, $rows)
    // {
    //     // 典型的明细表格包含商品名称、数量、单价等字段
    //     $detailKeywords = [
    //         '商品', '产品', '货物', '品名', '製品', '数量', '单价', '金额', 
    //         '価格', '単価', '金額', '明細', '品目', '摘要', '備考',
    //         '納期', '仕様', '规格', '構成', '型番'
    //     ];
        
    //     if (empty($headers)) {
    //         return false;
    //     }
        
    //     // 检查表头是否包含明细表格特征
    //     $matchCount = 0;
    //     foreach ($headers as $header) {
    //         foreach ($detailKeywords as $keyword) {
    //             if (mb_strpos($header, $keyword) !== false) {
    //                 $matchCount++;
    //                 break;
    //             }
    //         }
    //     }
        
    //     // 如果匹配了足够多的关键词，则视为明细表格
    //     return $matchCount >= 2;
    // }
    
    // /**
    //  * 处理订单头部信息表格
    //  * 
    //  * @param array $headers 表头
    //  * @param array $rows 行数据
    //  * @param array &$combinedData 引用传递的结果数据
    //  */
    // private function processHeaderTable($headers, $rows, &$combinedData)
    // {
    //     // 处理可能是键值对形式的表格
    //     foreach ($rows as $row) {
    //         foreach ($row as $columnIndex => $value) {
    //             if (isset($headers[$columnIndex])) {
    //                 $key = $headers[$columnIndex];
                    
    //                 // 匹配常见的订单头部字段 - 增加日文支持
    //                 if (mb_strpos($key, '订单号') !== false || mb_strpos($key, '注文番号') !== false || mb_strpos($key, '注文書番号') !== false) {
    //                     $combinedData['header']['order_number'] = $value;
    //                 } else if (mb_strpos($key, '日期') !== false || mb_strpos($key, '発行日') !== false || mb_strpos($key, '納期') !== false) {
    //                     $combinedData['header']['order_date'] = $value;
    //                 } else if (mb_strpos($key, '客户') !== false || mb_strpos($key, '客様') !== false || mb_strpos($key, '会社名') !== false) {
    //                     $combinedData['header']['customer'] = $value;
    //                 } else if (mb_strpos($key, '联系人') !== false || mb_strpos($key, '担当') !== false || mb_strpos($key, '担当者') !== false) {
    //                     $combinedData['header']['contact'] = $value;
    //                 } else if (mb_strpos($key, '电话') !== false || mb_strpos($key, '電話') !== false || mb_strpos($key, 'TEL') !== false) {
    //                     $combinedData['header']['telephone'] = $value;
    //                 } else if (mb_strpos($key, '总金额') !== false || mb_strpos($key, '合計') !== false || mb_strpos($key, '総計') !== false) {
    //                     // 提取数字部分
    //                     if (preg_match('/[0-9,\.]+/', $value, $matches)) {
    //                         $amount = str_replace(',', '', $matches[0]);
    //                         $combinedData['header']['total_amount'] = (float)$amount;
    //                     } else {
    //                         $combinedData['header']['total_amount'] = $value;
    //                     }
    //                 } else {
    //                     // 其他字段，使用表头作为键名
    //                     $combinedData['header'][$this->normalizeKey($key)] = $value;
    //                 }
    //             }
    //         }
    //     }
    // }
    
    // /**
    //  * 处理订单明细表格
    //  * 
    //  * @param array $headers 表头
    //  * @param array $rows 行数据
    //  * @param array &$combinedData 引用传递的结果数据
    //  */
    // private function processDetailTable($headers, $rows, &$combinedData)
    // {
    //     // 映射表头到标准字段
    //     $columnMap = [];
        
    //     // 常用字段映射 - 增加日文支持
    //     $fieldMappings = [
    //         'product_name' => ['产品名称', '品名', '商品名称', '製品名', '品名', '服务内容', '摘要', '項目', '品目', '明細'],
    //         'model' => ['型号', '规格', '規格', '型番', '货号', '商品型号', 'モデル', '仕様'],
    //         'quantity' => ['数量', '購入数量', '数目', '個数', 'qty', '数'],
    //         'unit_price' => ['单价', '价格', '単価', '价钱', '元/个', '円/個', '単価(税抜)'],
    //         'amount' => ['金额', '总金额', '合计', '総金額', '小计', '小計', '金額', '金額(税抜)'],
    //         'tax' => ['税額', '消費税', '税金', '税', 'TAX'],
    //         'delivery_date' => ['纳期', '纳期', '出荷日', '納期', '出荷予定日', '予定納期']
    //     ];
        
    //     // 建立列映射
    //     foreach ($headers as $columnIndex => $header) {
    //         foreach ($fieldMappings as $field => $keywords) {
    //             foreach ($keywords as $keyword) {
    //                 if (mb_strpos($header, $keyword) !== false) {
    //                     $columnMap[$columnIndex] = $field;
    //                     break 2;
    //                 }
    //             }
    //         }
    //     }
        
    //     // 处理每一行数据
    //     foreach ($rows as $row) {
    //         $item = [];
            
    //         // 填充标准字段
    //         foreach ($columnMap as $columnIndex => $field) {
    //             if (isset($row[$columnIndex])) {
    //                 $item[$field] = $row[$columnIndex];
    //             }
    //         }
            
    //         // 填充未映射的字段
    //         foreach ($row as $columnIndex => $value) {
    //             if (!isset($columnMap[$columnIndex]) && isset($headers[$columnIndex])) {
    //                 $item[$this->normalizeKey($headers[$columnIndex])] = $value;
    //             }
    //         }
            
    //         // 添加到明细列表
    //         if (!empty($item)) {
    //             $combinedData['details'][] = $item;
    //         }
    //     }
    // }
    
    // /**
    //  * 规范化键名
    //  * 
    //  * @param string $key 原始键名
    //  * @return string 规范化后的键名
    //  */
    // private function normalizeKey($key)
    // {
    //     // 去除空格
    //     $key = trim($key);
        
    //     // 转为小写
    //     $key = mb_strtolower($key);
        
    //     // 替换特殊字符为下划线
    //     $key = preg_replace('/[^a-z0-9_\x{4e00}-\x{9fa5}]/u', '_', $key);
        
    //     // 确保唯一性（如果需要）
    //     return $key;
    // }
    
    // /**
    //  * 递归查找表格数据
    //  * 
    //  * @param array $data 数据节点
    //  * @return array|null 找到的表格数据
    //  */
    // private function findTablesRecursive($data)
    // {
    //     // 检查是否有明确的表格数据
    //     $tableKeys = ['tables', 'Tables', 'tableData', 'TableData', 'tableDetection', 'TableDetection'];
        
    //     foreach ($tableKeys as $tableKey) {
    //         if (isset($data[$tableKey]) && !empty($data[$tableKey])) {
    //             Log::info("找到表格数据: {$tableKey}");
    //             return $data[$tableKey];
    //         }
    //     }
        
    //     // 检查是否有KV信息 (键值对信息，通常是订单头部)
    //     $kvKeys = ['kvInfo', 'KVInfo', 'keyValuePairs', 'KeyValuePairs'];
        
    //     $hasKvInfo = false;
    //     foreach ($kvKeys as $kvKey) {
    //         if (isset($data[$kvKey]) && !empty($data[$kvKey])) {
    //             Log::info("找到KV信息: {$kvKey}");
    //             $hasKvInfo = true;
    //         }
    //     }
        
    //     // 检查是否有KV列表信息 (通常用于明细)
    //     $kvListKeys = ['kvListInfo', 'KVListInfo', 'keyValueListPairs', 'KeyValueListPairs'];
        
    //     $hasKvListInfo = false;
    //     foreach ($kvListKeys as $kvListKey) {
    //         if (isset($data[$kvListKey]) && !empty($data[$kvListKey])) {
    //             Log::info("找到KV列表信息: {$kvListKey}");
    //             $hasKvListInfo = true;
    //         }
    //     }
        
    //     // 如果同时具有KV信息和KV列表信息，这是一个完整的数据结构
    //     if ($hasKvInfo || $hasKvListInfo) {
    //         Log::info("找到包含KV信息或KV列表信息的数据结构");
    //         return $data;
    //     }
        
    //     // 递归检查子属性
    //     foreach ($data as $key => $value) {
    //         if (is_array($value)) {
    //             $result = $this->findTablesRecursive($value);
    //             if ($result) {
    //                 return $result;
    //             }
    //         }
    //     }
        
    //     // 如果找不到任何相关结构，返回null
    //     return null;
    // }
    
    // /**
    //  * 找出KV信息
    //  * 
    //  * @param array $data 数据结构
    //  * @return array|null KV信息数组
    //  */
    // private function findKVInfo($data)
    // {
    //     // 检查常见的KV信息字段名
    //     $kvKeys = ['kvInfo', 'KVInfo', 'keyValuePairs', 'KeyValuePairs'];
        
    //     foreach ($kvKeys as $kvKey) {
    //         if (isset($data[$kvKey]) && !empty($data[$kvKey])) {
    //             return $data[$kvKey];
    //         }
    //     }
        
    //     // 直接检查父节点
    //     if (isset($data['key']) || isset($data['Key'])) {
    //         // 这可能是一个单一的KV信息
    //         return [$data];
    //     }
        
    //     // 如果$data本身就是一个KV信息数组
    //     $firstItem = reset($data);
    //     if (is_array($firstItem) && (isset($firstItem['key']) || isset($firstItem['Key']))) {
    //         return $data;
    //     }
        
    //     // 递归检查子属性
    //     foreach ($data as $key => $value) {
    //         if (is_array($value)) {
    //             $result = $this->findKVInfo($value);
    //             if ($result) {
    //                 return $result;
    //             }
    //         }
    //     }
        
    //     return null;
    // }
    
    // /**
    //  * 找出KV列表信息
    //  * 
    //  * @param array $data 数据结构
    //  * @return array|null KV列表信息数组
    //  */
    // private function findKVListInfo($data)
    // {
    //     // 检查常见的KV列表信息字段名
    //     $kvListKeys = ['kvListInfo', 'KVListInfo', 'keyValueListPairs', 'KeyValueListPairs'];
        
    //     foreach ($kvListKeys as $kvListKey) {
    //         if (isset($data[$kvListKey]) && !empty($data[$kvListKey])) {
    //             return $data[$kvListKey];
    //         }
    //     }
        
    //     // 检查是否有rows属性，这通常表示表格行
    //     if (isset($data['rows']) && is_array($data['rows'])) {
    //         Log::info("找到rows数据，可能包含表格行");
    //         return $data['rows'];
    //     }
        
    //     if (isset($data['Rows']) && is_array($data['Rows'])) {
    //         Log::info("找到Rows数据，可能包含表格行");
    //         return $data['Rows'];
    //     }
        
    //     // 如果$data是一个嵌套数组，并且内部数组包含键值对
    //     $firstItem = reset($data);
    //     if (is_array($firstItem) && is_array(reset($firstItem)) && 
    //         (isset(reset($firstItem)['key']) || isset(reset($firstItem)['Key'])) {
    //         return $data;
    //     }
        
    //     // 递归检查子属性
    //     foreach ($data as $key => $value) {
    //         if (is_array($value)) {
    //             $result = $this->findKVListInfo($value);
    //             if ($result) {
    //                 return $result;
    //             }
    //         }
    //     }
        
    //     return null;
    // }
    
    // /**
    //  * 判断表格是否为头部表格（包含订单基本信息）
    //  * 
    //  * @param array $table 表格数据
    //  * @param string $sheetName 表格名称
    //  * @return bool
    //  */
    // private function isHeaderTable($table, $sheetName)
    // {
    //     // 1. 检查表格名称是否包含特定关键词
    //     $headerKeywords = ['表头', '订单', '头部', '基本信息', '订单信息', 'header', 'order'];
    //     foreach ($headerKeywords as $keyword) {
    //         if (stripos($sheetName, $keyword) !== false) {
    //             Log::info("基于表格名称 '{$sheetName}' 包含关键词 '{$keyword}' 判定为头部表格");
    //             return true;
    //         }
    //     }
        
    //     // 2. 检查表格数据结构中是否有KV信息（Key-Value形式的信息通常是头部表格的特征）
    //     $kvInfo = $this->findKVInfo($table);
    //     if ($kvInfo && count($kvInfo) > 0) {
    //         // 检查KV信息中是否包含典型的订单头部字段
    //         $headerFields = ['発行日', '注文番号', '订单号', '日期', '客户', '公司', '電話番号', '联系人', '交期'];
    //         foreach ($kvInfo as $kv) {
    //             $keyName = null;
    //             // 获取键名
    //             if (isset($kv['key'])) {
    //                 $keyName = is_array($kv['key']) ? $kv['key'][0] : $kv['key'];
    //             } elseif (isset($kv['Key'])) {
    //                 $keyName = is_array($kv['Key']) ? $kv['Key'][0] : $kv['Key'];
    //             }
                
    //             if ($keyName) {
    //                 foreach ($headerFields as $field) {
    //                     if (stripos($keyName, $field) !== false) {
    //                         Log::info("基于KV信息中包含字段 '{$keyName}' 匹配 '{$field}' 判定为头部表格");
    //                         return true;
    //                     }
    //                 }
    //             }
    //         }
    //     }
        
    //     // 3. 如果是第一个表格，且没有特别特征表明它是明细表格，则默认为头部表格
    //     if ($sheetName === '表格1' || $sheetName === 'Table1' || strpos($sheetName, '1') !== false) {
    //         Log::info("基于表格名称 '{$sheetName}' 为第一个表格默认判定为头部表格");
    //         return true;
    //     }
        
    //     return false;
    // }
    
    // /**
    //  * 判断表格是否为明细表格（包含产品列表信息）
    //  * 
    //  * @param array $table 表格数据
    //  * @param string $sheetName 表格名称
    //  * @return bool
    //  */
    // private function isSheetDetailTable($table, $sheetName)
    // {
    //     // 1. 检查表格名称是否包含特定关键词
    //     $detailKeywords = ['明细', '产品', '货物', '列表', '清单', 'items', 'products', 'details'];
    //     foreach ($detailKeywords as $keyword) {
    //         if (stripos($sheetName, $keyword) !== false) {
    //             Log::info("基于表格名称 '{$sheetName}' 包含关键词 '{$keyword}' 判定为明细表格");
    //             return true;
    //         }
    //     }
        
    //     // 2. 检查表格数据结构中是否有KVList信息（多行结构通常是明细表格的特征）
    //     $kvListInfo = $this->findKVListInfo($table);
    //     if ($kvListInfo && count($kvListInfo) > 0) {
    //         // 检查第一行数据中是否包含典型的产品明细字段
    //         if (isset($kvListInfo[0]) && is_array($kvListInfo[0])) {
    //             $detailFields = ['製品名', '产品名称', '数量', '単価', '单价', '金額', '金额', 'quantity', 'price'];
                
    //             foreach ($kvListInfo[0] as $kv) {
    //                 $keyName = null;
    //                 // 获取键名
    //                 if (isset($kv['key'])) {
    //                     $keyName = is_array($kv['key']) ? $kv['key'][0] : $kv['key'];
    //                 } elseif (isset($kv['Key'])) {
    //                     $keyName = is_array($kv['Key']) ? $kv['Key'][0] : $kv['Key'];
    //                 }
                    
    //                 if ($keyName) {
    //                     foreach ($detailFields as $field) {
    //                         if (stripos($keyName, $field) !== false) {
    //                             Log::info("基于KVList信息中包含字段 '{$keyName}' 匹配 '{$field}' 判定为明细表格");
    //                             return true;
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     }
        
    //     // 3. 如果是第二个表格，且没有特别特征表明它是头部表格，则默认为明细表格
    //     if ($sheetName === '表格2' || $sheetName === 'Table2' || strpos($sheetName, '2') !== false) {
    //         Log::info("基于表格名称 '{$sheetName}' 为第二个表格默认判定为明细表格");
    //         return true;
    //     }
        
    //     // 4. 对表格中的cells进行分析，如果有多行结构化数据，可能是明细表格
    //     if (isset($table['cells']) && is_array($table['cells']) && count($table['cells']) > 5) {
    //         Log::info("基于表格 '{$sheetName}' 包含大量单元格(" . count($table['cells']) . ")判定为明细表格");
    //         return true;
    //     }
        
    //     return false;
    // }
    
    // /**
    //  * 记录JSON数据结构，帮助调试
    //  *
    //  * @param mixed $data 要记录的数据
    //  * @param string $prefix 日志前缀
    //  * @param int $maxDepth 最大递归深度
    //  * @param int $currentDepth 当前递归深度
    //  */
    // public function logJsonStructure($data, $prefix = '数据结构', $maxDepth = 5, $currentDepth = 0)
    // {
    //     if ($currentDepth >= $maxDepth) {
    //         Log::info("{$prefix} (已达到最大递归深度{$maxDepth})");
    //         return;
    //     }

    //     if (!is_array($data)) {
    //         Log::info("{$prefix}: 非数组数据，类型: " . gettype($data));
    //         return;
    //     }

    //     // 记录当前层级的键
    //     $keys = array_keys($data);
    //     Log::info("{$prefix} 包含键: " . json_encode($keys));

    //     // 对于较小的数组（少于5个键），递归记录每个键的结构
    //     if (count($keys) < 5 && $currentDepth < $maxDepth - 1) {
    //         foreach ($keys as $key) {
    //             if (is_array($data[$key])) {
    //                 // 防止无限递归
    //                 if ($data[$key] === $data) {
    //                     Log::info("{$prefix}.{$key}: [循环引用]");
    //                     continue;
    //                 }
                    
    //                 // 如果是数组且不是空数组，递归记录结构
    //                 if (!empty($data[$key])) {
    //                     $this->logJsonStructure($data[$key], "{$prefix}.{$key}", $maxDepth, $currentDepth + 1);
    //                 } else {
    //                     Log::info("{$prefix}.{$key}: [空数组]");
    //                 }
    //             } else if ($key !== 'headers' && $key !== 'RequestId') { // 跳过不重要的字段
    //                 $valueStr = is_string($data[$key]) ? 
    //                             (strlen($data[$key]) > 100 ? substr($data[$key], 0, 97) . '...' : $data[$key]) : 
    //                             gettype($data[$key]);
    //                 Log::info("{$prefix}.{$key}: {$valueStr}");
    //             }
    //         }
    //     }
        
    //     // 特殊处理Data节点
    //     if (isset($data['Data'])) {
    //         $this->logJsonStructure($data['Data'], "{$prefix}.Data", $maxDepth, $currentDepth + 1);
    //     } else if (isset($data['data'])) {
    //         $this->logJsonStructure($data['data'], "{$prefix}.data", $maxDepth, $currentDepth + 1);
    //     } else if (isset($data['body']) && isset($data['body']['Data'])) {
    //         $this->logJsonStructure($data['body']['Data'], "{$prefix}.body.Data", $maxDepth, $currentDepth + 1);
    //     }
        
    //     // 特别关注表格和布局数据
    //     if (isset($data['layouts'])) {
    //         Log::info("{$prefix}.layouts 包含 " . count($data['layouts']) . " 个布局项");
    //         if (!empty($data['layouts']) && $currentDepth < $maxDepth - 1) {
    //             $layout = $data['layouts'][0];
    //             Log::info("{$prefix}.layouts[0] 类型: " . ($layout['type'] ?? '未知'));
    //             if (isset($layout['type']) && $layout['type'] === 'table') {
    //                 Log::info("{$prefix}.layouts[0] 是表格，包含 " . ($layout['numRow'] ?? '未知') . " 行, " . 
    //                          ($layout['numCol'] ?? '未知') . " 列");
    //             }
    //         }
    //     }
        
    //     if (isset($data['tables'])) {
    //         Log::info("{$prefix}.tables 包含 " . count($data['tables']) . " 个表格");
    //     }
    // }
    
    // /**
    //  * 深度递归搜索任何可能包含有用数据的节点
    //  * 
    //  * @param array $data 要搜索的数据
    //  * @param int $maxDepth 最大搜索深度
    //  * @param int $currentDepth 当前深度
    //  * @return array 找到的所有可能的数据节点
    //  */
    // public function deepSearchForData($data, $maxDepth = 6, $currentDepth = 0)
    // {
    //     if (!is_array($data) || $currentDepth >= $maxDepth) {
    //         return [];
    //     }
        
    //     $results = [];
        
    //     // 检查当前节点是否可能包含数据
    //     $dataIndicators = [
    //         'table', 'Table', 'tables', 'Tables', 
    //         'kv', 'Kv', 'kvInfo', 'KvInfo', 
    //         'kvList', 'KvList', 'kvListInfo', 'KvListInfo',
    //         'layout', 'Layout', 'layouts', 'Layouts',
    //         'cell', 'Cell', 'cells', 'Cells',
    //         'row', 'Row', 'rows', 'Rows',
    //         'column', 'Column', 'columns', 'Columns',
    //         'data', 'Data', 'content', 'Content',
    //         'tableResult', 'TableResult', 'tableResults', 'TableResults',
    //         'structResult', 'StructResult', 'prism_wordsInfo', 'blocks',
    //         'paragraphs', 'Paragraphs', 'lines', 'Lines'
    //     ];
        
    //     // 检查当前节点的键名是否与数据指示器匹配
    //     $currentNodeHasData = false;
    //     foreach (array_keys($data) as $key) {
    //         foreach ($dataIndicators as $indicator) {
    //             if (stripos($key, $indicator) !== false) {
    //                 $currentNodeHasData = true;
    //                 break 2;
    //             }
    //         }
    //     }
        
    //     // 检查是否是二维数组结构（可能是表格数据）
    //     if (!$currentNodeHasData && is_array($data) && count($data) > 0) {
    //         $isTable = true;
    //         $rowStructure = null;
            
    //         // 检查前5行以确定是否为二维数组表格
    //         $rowsToCheck = min(5, count($data));
    //         for ($i = 0; $i < $rowsToCheck; $i++) {
    //             if (!isset($data[$i]) || !is_array($data[$i])) {
    //                 $isTable = false;
    //                 break;
    //             }
                
    //             // 检查每一行是否有类似的结构
    //             $currentRowStructure = array_keys($data[$i]);
    //             if ($rowStructure === null) {
    //                 $rowStructure = $currentRowStructure;
    //             } else {
    //                 // 如果键完全不同，可能不是表格
    //                 $diff1 = array_diff($rowStructure, $currentRowStructure);
    //                 $diff2 = array_diff($currentRowStructure, $rowStructure);
    //                 if (count($diff1) > count($rowStructure) / 2 || count($diff2) > count($currentRowStructure) / 2) {
    //                     $isTable = false;
    //                     break;
    //                 }
    //             }
    //         }
            
    //         if ($isTable && count($rowStructure) > 1) {
    //             $currentNodeHasData = true;
    //             Log::info("发现可能的表格数据结构，行数: " . count($data) . ", 列数: " . count($rowStructure));
    //         }
    //     }
        
    //     // 如果当前节点可能包含数据，添加到结果中
    //     if ($currentNodeHasData) {
    //         $results[] = $data;
    //     }
        
    //     // 递归检查所有数组子节点
    //     foreach ($data as $key => $value) {
    //         if (is_array($value)) {
    //             $subResults = $this->deepSearchForData($value, $maxDepth, $currentDepth + 1);
    //             $results = array_merge($results, $subResults);
    //         }
    //     }
        
    //     return $results;
    // }
    
    // /**
    //  * 判断数据结构是否像表格
    //  *
    //  * @param array $data 要检查的数据
    //  * @return bool 是否可能是表格
    //  */
    // private function looksLikeTable($data)
    // {
    //     // 检查是否是数组
    //     if (!is_array($data)) {
    //         return false;
    //     }
        
    //     // 检查layouts类型是否为table
    //     if (isset($data['type']) && (
    //         $data['type'] === 'table' || 
    //         $data['type'] === 'Table' || 
    //         stripos($data['type'], 'table') !== false
    //     )) {
    //         return true;
    //     }
        
    //     // 检查是否有日文表格字段名
    //     $jaTableIndicators = ['表', 'リスト', '明細', '一覧', '表格', 'テーブル'];
    //     foreach ($data as $key => $value) {
    //         if (is_string($key)) {
    //             foreach ($jaTableIndicators as $indicator) {
    //                 if (mb_strpos($key, $indicator) !== false) {
    //                     return true;
    //                 }
    //             }
    //         }
    //     }
        
    //     // 检查是否有rows或cells等表格关键字段
    //     $tableKeywords = [
    //         'rows', 'cells', 'header', 'body', 'columns', 'data', 
    //         'Rows', 'Cells', 'Header', 'Body', 'Columns', 'Data',
    //         'tableData', 'TableData', 'tableInfo', 'TableInfo',
    //         'tableContent', 'TableContent', 'content', 'Content'
    //     ];
        
    //     foreach ($tableKeywords as $keyword) {
    //         if (isset($data[$keyword]) && is_array($data[$keyword]) && !empty($data[$keyword])) {
    //             return true;
    //         }
    //     }
        
    //     // 检查是否是规则的二维数组(可能是表格数据)
    //     if (!empty($data) && is_array($data) && isset($data[0]) && is_array($data[0])) {
    //         // 检查第一行的所有元素是否具有相同的键
    //         $firstRowKeys = array_keys($data[0]);
    //         if (count($firstRowKeys) <= 1) {
    //             return false; // 只有一列不太可能是表格
    //         }
            
    //         $allRowsHaveSameStructure = true;
            
    //         // 检查前5行(或全部如果少于5行)
    //         $rowsToCheck = min(5, count($data));
    //         for ($i = 1; $i < $rowsToCheck; $i++) {
    //             if (!isset($data[$i]) || !is_array($data[$i])) {
    //                 $allRowsHaveSameStructure = false;
    //                 break;
    //             }
                
    //             $currentRowKeys = array_keys($data[$i]);
    //             // 允许一定的结构差异
    //             $diff1 = array_diff($firstRowKeys, $currentRowKeys);
    //             $diff2 = array_diff($currentRowKeys, $firstRowKeys);
    //             if (count($diff1) > count($firstRowKeys) / 3 || count($diff2) > count($currentRowKeys) / 3) {
    //                 $allRowsHaveSameStructure = false;
    //                 break;
    //             }
    //         }
            
    //         if ($allRowsHaveSameStructure) {
    //             return true;
    //         }
    //     }
        
    //     // 检查是否包含KV信息结构
    //     if (isset($data['key']) && isset($data['value'])) {
    //         return false; // 单个KV对不是表格
    //     } else if (!empty($data) && is_array($data)) {
    //         // 检查是否是KV对数组
    //         $kvPairCount = 0;
    //         $itemsToCheck = min(count($data), 10);
            
    //         for ($i = 0; $i < $itemsToCheck; $i++) {
    //             if (isset($data[$i]) && is_array($data[$i]) && 
    //                 (isset($data[$i]['key']) || isset($data[$i]['Key'])) && 
    //                 (isset($data[$i]['value']) || isset($data[$i]['Value']))) {
    //                 $kvPairCount++;
    //             }
    //         }
            
    //         // 如果大部分是KV对，可能是表格
    //         if ($kvPairCount >= $itemsToCheck / 2) {
    //             return true;
    //         }
    //     }
        
    //     return false;
    // }

    // /**
    //  * 寻找纯文本形式的日文明细内容和总金额 
    //  * 
    //  * @param string $content 文本内容
    //  * @param array &$combinedData 结果数据
    //  */
    // private function extractTextDetails($content, &$combinedData)
    // {
    //     // 尝试匹配以下格式的行项目:
    //     // 1. 項目名/数量/単価/金額
    //     // 2. 明細名/数量X単価＝金額

    //     // 匹配项目行内容
    //     preg_match_all('/^(.+?)\s+(\d+)\s*(?:個|点|台)?\s+?(\d[\d,\.]*)\s*(?:円|元)?\s+(\d[\d,\.]*)\s*(?:円|元)?$/m', $content, $matches, PREG_SET_ORDER);

    //     if (!empty($matches)) {
    //         Log::info('找到文本格式的明细行: ' . count($matches) . ' 行');
            
    //         foreach ($matches as $match) {
    //             $item = [
    //                 'product_name' => trim($match[1]),
    //                 'quantity' => (int)$match[2],
    //                 'unit_price' => (float)str_replace(',', '', $match[3]),
    //                 'amount' => (float)str_replace(',', '', $match[4])
    //             ];
                
    //             $combinedData['details'][] = $item;
    //         }
    //     } else {
    //         // 尝试另一种格式 "名称 X 数量 = 金额"
    //         preg_match_all('/^(.+?)\s+(\d+)\s*(?:個|点|台)?\s*[Xx×]\s*(\d[\d,\.]*)\s*(?:円|元)?\s*[=＝]\s*(\d[\d,\.]*)\s*(?:円|元)?$/m', $content, $matches, PREG_SET_ORDER);
            
    //         if (!empty($matches)) {
    //             Log::info('找到乘法格式的明细行: ' . count($matches) . ' 行');
                
    //             foreach ($matches as $match) {
    //                 $item = [
    //                     'product_name' => trim($match[1]),
    //                     'quantity' => (int)$match[2],
    //                     'unit_price' => (float)str_replace(',', '', $match[3]),
    //                     'amount' => (float)str_replace(',', '', $match[4])
    //                 ];
                    
    //                 $combinedData['details'][] = $item;
    //             }
    //         }
    //     }
        
    //     // 匹配总金额 (支持多种格式)
    //     if (preg_match('/(?:合計|総額|総計|消費税込合計)[:：]?\s*[\\\¥￥]?\s*(\d[\d,\.]*)\s*(?:円|元)?/u', $content, $matches)) {
    //         $amount = (float)str_replace(',', '', $matches[1]);
    //         $combinedData['header']['total_amount'] = $amount;
    //     }
    // }
    
    // /**
    //  * 从任何可能的数据源提取并整合数据
    //  * 
    //  * @param array $responseData API响应数据
    //  * @return array 整合后的数据
    //  */
    // public function extractFromAllPossibleSources($responseData)
    // {
    //     // 初始化结果结构
    //     $combinedData = [
    //         'header' => [],
    //         'details' => []
    //     ];
        
    //     Log::info("开始从所有可能的数据源提取数据");
        
    //     // 1. 记录完整响应结构以便调试
    //     $this->logJsonStructure($responseData, "API响应");
        
    //     // 2. 深度搜索所有可能包含数据的节点
    //     $possibleDataNodes = $this->deepSearchForData($responseData);
    //     Log::info("找到 " . count($possibleDataNodes) . " 个可能包含数据的节点");
        
    //     // 3. 从每个节点尝试提取数据
    //     $headerData = [];
    //     $detailsData = [];
        
    //     foreach ($possibleDataNodes as $index => $node) {
    //         Log::info("处理潜在数据节点 #{$index}");
            
    //         // 尝试提取KV信息(通常是头部信息)
    //         $kvInfo = $this->findKVInfo($node);
    //         if ($kvInfo && is_array($kvInfo) && !empty($kvInfo)) {
    //             Log::info("从节点 #{$index} 找到KV信息，包含 " . count($kvInfo) . " 项");
                
    //             foreach ($kvInfo as $kv) {
    //                 $key = null;
    //                 $value = null;
                    
    //                 // 提取键
    //                 if (isset($kv['key'])) {
    //                     $key = is_array($kv['key']) ? implode(' ', $kv['key']) : $kv['key'];
    //                 } elseif (isset($kv['Key'])) {
    //                     $key = is_array($kv['Key']) ? implode(' ', $kv['Key']) : $kv['Key'];
    //                 }
                    
    //                 // 提取值
    //                 if (isset($kv['value'])) {
    //                     $value = is_array($kv['value']) ? implode(' ', $kv['value']) : $kv['value'];
    //                 } elseif (isset($kv['Value'])) {
    //                     $value = is_array($kv['Value']) ? implode(' ', $kv['Value']) : $kv['Value'];
    //                 }
                    
    //                 if ($key && $value) {
    //                     $normalizedKey = $this->normalizeKey($key);
    //                     $headerData[$normalizedKey] = $value;
                        
    //                     // 特殊处理常见字段
    //                     if (mb_strpos($key, '订单号') !== false || mb_strpos($key, '注文番号') !== false) {
    //                         $headerData['order_number'] = $value;
    //                     } elseif (mb_strpos($key, '日期') !== false || mb_strpos($key, '発行日') !== false) {
    //                         $headerData['order_date'] = $value;
    //                     }
    //                 }
    //             }
    //         }
            
    //         // 尝试提取KVList信息(通常是明细信息)
    //         $kvListInfo = $this->findKVListInfo($node);
    //         if ($kvListInfo && is_array($kvListInfo) && !empty($kvListInfo)) {
    //             Log::info("从节点 #{$index} 找到KVList信息，包含 " . count($kvListInfo) . " 行");
                
    //             foreach ($kvListInfo as $row) {
    //                 $item = [];
                    
    //                 // 确保行是数组
    //                 if (!is_array($row)) {
    //                     Log::warning("KVList行不是数组，跳过");
    //                     continue;
    //                 }
                    
    //                 // 处理行中的每个KV项
    //                 foreach ($row as $kv) {
    //                     if (!is_array($kv)) {
    //                         Log::warning("KV项不是数组，跳过");
    //                         continue;
    //                     }
                        
    //                     $key = null;
    //                     $value = null;
                        
    //                     // 提取键
    //                     if (isset($kv['key'])) {
    //                         $key = is_array($kv['key']) ? implode(' ', $kv['key']) : $kv['key'];
    //                     } elseif (isset($kv['Key'])) {
    //                         $key = is_array($kv['Key']) ? implode(' ', $kv['Key']) : $kv['Key'];
    //                     }
                        
    //                     // 提取值
    //                     if (isset($kv['value'])) {
    //                         $value = is_array($kv['value']) ? implode(' ', $kv['value']) : $kv['value'];
    //                     } elseif (isset($kv['Value'])) {
    //                         $value = is_array($kv['Value']) ? implode(' ', $kv['Value']) : $kv['Value'];
    //                     }
                        
    //                     if ($key && $value) {
    //                         $normalizedKey = $this->normalizeKey($key);
    //                         $item[$normalizedKey] = $value;
                            
    //                         // 处理常见字段映射
    //                         $this->mapToStandardField($key, $value, $item);
    //                     }
    //                 }
                    
    //                 if (!empty($item)) {
    //                     $detailsData[] = $item;
    //                 }
    //             }
    //         }
            
    //         // 如果节点有单元格，尝试直接提取表格结构
    //         if (isset($node['cells']) && is_array($node['cells']) && !empty($node['cells'])) {
    //             Log::info("从节点 #{$index} 找到cells，包含 " . count($node['cells']) . " 个单元格");
                
    //             // 这里可以调用我们现有的processTableLayout方法的逻辑
    //             $cellData = [
    //                 'header' => [],
    //                 'details' => []
    //             ];
    //             $this->processTableLayout($node, $cellData);
                
    //             // 合并提取的数据
    //             $headerData = array_merge($headerData, $cellData['header']);
    //             $detailsData = array_merge($detailsData, $cellData['details']);
    //         }
    //     }
        
    //     // 4. 合并所有提取的数据
    //     $combinedData['header'] = $headerData;
    //     $combinedData['details'] = $detailsData;
        
    //     Log::info("从所有可能的数据源提取完成，头部字段数: " . count($headerData) . 
    //              ", 明细行数: " . count($detailsData));
        
    //     return $combinedData;
    // }
    
    // /**
    //  * 深度搜索表格结构
    //  *
    //  * @param mixed $data 要搜索的数据
    //  * @param int $depth 当前深度
    //  * @return array 找到的表格结构
    //  */
    // private function findTablesDeepSearch($data, $depth = 0)
    // {
    //     if ($depth > 10) return []; // 防止无限递归
        
    //     $tables = [];
        
    //     // 检查是否是数组
    //     if (!is_array($data)) {
    //         return [];
    //     }
        
    //     // 检查当前节点是否像表格
    //     if ($this->looksLikeTable($data)) {
    //         $tables[] = $data;
    //     }
        
    //     // 递归搜索所有子节点
    //     foreach ($data as $key => $value) {
    //         if (is_array($value)) {
    //             // 检查子节点是否像表格
    //             if ($this->looksLikeTable($value)) {
    //                 $tables[] = $value;
    //             }
                
    //             // 递归搜索更深层次
    //             $subTables = $this->findTablesDeepSearch($value, $depth + 1);
    //             if (!empty($subTables)) {
    //                 $tables = array_merge($tables, $subTables);
    //             }
    //         }
    //     }
        
    //     return $tables;
    // }
    
    // /**
    //  * 处理表格数据
    //  *
    //  * @param array $table 表格数据
    //  * @param array &$result 结果数组
    //  */
    // private function processTableData($table, &$result)
    // {
    //     // 处理标准表格布局
    //     if (isset($table['type']) && $table['type'] === 'table') {
    //         $this->processTableLayout($table, $result);
    //         return;
    //     }
        
    //     // 处理行列数据
    //     if (isset($table['rows']) && is_array($table['rows'])) {
    //         $headers = [];
    //         $details = [];
            
    //         // 提取表头(假设第一行是表头)
    //         if (!empty($table['rows'])) {
    //             $headers = is_array($table['rows'][0]) ? $table['rows'][0] : [];
                
    //             // 提取数据行
    //             for ($i = 1; $i < count($table['rows']); $i++) {
    //                 if (is_array($table['rows'][$i])) {
    //                     $details[] = $table['rows'][$i];
    //                 }
    //             }
    //         }
            
    //         // 如果找到了表头和数据,填充结果
    //         if (!empty($headers) && !empty($details)) {
    //             // 处理表头
    //             foreach ($headers as $key => $value) {
    //                 if (!isset($result['header'][$key])) {
    //                     $result['header'][$key] = $value;
    //                 }
    //             }
                
    //             // 添加明细行
    //             foreach ($details as $row) {
    //                 $result['details'][] = $row;
    //             }
    //         }
    //     }
    //     // 处理二维数组(可能是表格数据)
    //     else if (!empty($table) && is_array($table) && isset($table[0]) && is_array($table[0])) {
    //         // 提取表头(第一行)
    //         $headers = $table[0];
    //         $details = [];
            
    //         // 提取数据行(第二行开始)
    //         for ($i = 1; $i < count($table); $i++) {
    //             if (is_array($table[$i])) {
    //                 $details[] = $table[$i];
    //             }
    //         }
            
    //         // 填充结果
    //         if (!empty($headers) && !empty($details)) {
    //             // 处理表头
    //             foreach ($headers as $key => $value) {
    //                 if (!isset($result['header'][$key])) {
    //                     $result['header'][$key] = $value;
    //                 }
    //             }
                
    //             // 添加明细行
    //             foreach ($details as $row) {
    //                 $result['details'][] = $row;
    //             }
    //         }
    //     }
    // }
} 