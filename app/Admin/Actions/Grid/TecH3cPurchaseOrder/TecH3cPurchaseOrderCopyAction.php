<?php

namespace App\Admin\Actions\Grid;

namespace App\Admin\Actions\Grid\TecH3cPurchaseOrder;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use App\Models\TecH3cPurchaseOrderModel;
use App\Models\TecH3cPurchaseOrderItemModel;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
class TecH3cPurchaseOrderCopyAction extends BatchAction
{
    protected $title = '复制订单';

    public function match($key)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return false;
        }

        // 只有选择单条数据时才显示
        return $this->getKey() ? count($this->getKey()) === 1 : false;
    }

    public function handle(Request $request)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return $this->response()->error('回收站中不允许复制订单');
        }

        // 获取选中的记录 ID
        $keys = $this->getKey() ?? [];
        
        if (count($keys) !== 1) {
            return $this->response()
                ->error('请选择单条数据进行复制')
                ->refresh();
        }

        $id = reset($keys);  // 获取数组的第一个元素

        try {
            // 开启数据库事务
            return DB::transaction(function () use ($id) {
                // 获取原始订单
                $originalOrder = TecH3cPurchaseOrderModel::findOrFail($id);

                // 创建新订单，复制原始订单的所有属性
                $newOrder = $originalOrder->replicate();
                
                // 修改 case_name，添加后缀
                $newOrder->case_name = $originalOrder->case_name . '-copy';
                
                // 生成新的订单号
                $newOrder->order_no = $this->generateNewOrderNo($originalOrder);
                
                // 重置一些不需要复制的字段
                $newOrder->business_date = now();
                $newOrder->order_status = 0; // 默认状态
                $newOrder->review_status = 0; // 默认审核状态
                $newOrder->created_at = now();
                $newOrder->updated_at = now();

                // 保存新订单
                $newOrder->save();

                // 复制订单明细
                $originalItems = TecH3cPurchaseOrderItemModel::where('order_id', $id)->get();
                
                foreach ($originalItems as $originalItem) {
                    $newItem = $originalItem->replicate();
                    $newItem->order_id = $newOrder->id;
                    $newItem->created_at = now();
                    $newItem->updated_at = now();
                    $newItem->save();
                }

                return $this->response()
                    ->success('订单复制成功')
                    ->refresh();
            });
        } catch (Exception $e) {
            return $this->response()
                ->error('订单复制失败：' . $e->getMessage())
                ->refresh();
        }
    }

    public function confirm()
    {
        return ['确认要复制选中的订单吗？', '将创建一个新的订单副本'];
    }

    private function generateNewOrderNo($originalOrder)
    {
        Log::info('generateNewOrderNo start', [
            'originalOrder' => $originalOrder
        ]);
        
        // 确保供应商关联已加载
        $originalOrder->load('sndCompany');
        
        Log::info('supplier info', [
            'sndCompany' => $originalOrder->sndCompany,
            'sndCompany_id' => $originalOrder->sndCompany_id
        ]);
        
        // 如果无法获取供应商代码，使用原订单号中的代码
        if (!$originalOrder->sndCompany || !$originalOrder->sndCompany->code) {
            // 从原订单号中提取供应商代码
            preg_match('/PO-(.*?)-/', $originalOrder->order_no, $matches);
            $supplierCode = $matches[1] ?? 'H3CHK';
            Log::info('using extracted code', ['supplierCode' => $supplierCode]);
        } else {
            $supplierCode = $originalOrder->sndCompany->code;
            Log::info('using supplier code', ['supplierCode' => $supplierCode]);
        }
        
        $prefix = 'PO-' . $supplierCode . '-' . $originalOrder->incoterms . '-';
        Log::info('generated prefix', ['prefix' => $prefix]);
        
        return build_h3_corder_no($prefix).'-copy';
    }
}
