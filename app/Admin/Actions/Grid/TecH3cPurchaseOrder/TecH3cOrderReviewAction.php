<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cPurchaseOrder;

use App\Models\BaseModel;
use App\Models\PurchaseBaseModel;
use App\Models\TecH3cPurchaseBaseModel;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Yxx\LaravelQuick\Exceptions\Api\ApiRequestException;
use Yxx\LaravelQuick\Exceptions\Api\ApiSystemException;
use App\Admin\Actions\Grid\OrderReview;

class TecH3cOrderReviewAction extends OrderReview
{
    /**
     * @var int
     */
    protected $review_status;

    /**
     * @var int|null
     */
    protected $delivery_status;


    public function __construct($review_status = null)
    {
        parent::__construct($review_status);
        
        Log::info('H3cOrderReview construct', [
            'review_status' => $review_status,
            'title' => $this->title,
            'request_all' => request()->all()
        ]);
        
        // 获取订单当前状态
        $orderId = request()->route('r_h3c_purchase_order');
        $currentStatus = null;  
        if ($orderId) {
            $modelClass = "\\App\\Models\\{$this->getModel()}Model";
            $order = $modelClass::find($orderId);
            if ($order) {
                $currentStatus = $order->review_status;
                // 根据订单当前状态设置按钮文字
                $this->title = match((int)$currentStatus) {
                    TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_WAIT => '审核',  // 未审核状态显示"审核"按钮
                    TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK => '退回',    // 已审核状态显示"退回"按钮
                    TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_NG => '审核',    // 已退回状态显示"审核"按钮
                    default => '审核'
                };
            }
        }
        
        $this->review_status = $review_status;
        
        Log::info('H3cOrderReview button title', [
            'review_status' => $review_status,
            'order_id' => $orderId ?? null,
            'current_status' => $currentStatus ?? null,
            'title' => $this->title
        ]);
    }

    public function handle(Request $request)
    {
        try {
            // 从请求中获取 ID
            $id = $request->get('_key');
            if (empty($id)) {
                $id = $request->get('id');
            }
            if (empty($id)) {
                $id = request()->route('r_h3c_purchase_order');
            }
            
            // 记录请求信息
            Log::info('H3cOrderReview handle', [
                'request_all' => $request->all(),
                'id' => $id,
                'route_params' => request()->route()->parameters()
            ]);

            if (empty($id)) {
                throw new ApiRequestException("订单ID不能为空！");
            }

            // 获取订单
            $modelClass = "\\App\\Models\\{$this->getModel()}Model";
            $order = $modelClass::find($id);
            
            if (!$order) {
                throw new ApiRequestException("找不到订单！订单ID: {$id}, 模型: {$modelClass}");
            }

            // 获取订单当前状态
            $currentStatus = (int)$order->review_status;
            
            // 更新审核状态
            $newStatus = match($currentStatus) {
                TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_WAIT => TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK,   // 未审核 -> 已审核
                TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK => TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_NG,     // 已审核 -> 已退回
                TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_NG => TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK,     // 已退回 -> 已审核
                default => throw new ApiRequestException("无效的订单状态：{$currentStatus}")
            };            
            
            // 退回时，设置发货状态为待下单
            if ($newStatus === TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_NG) {
                $order->order_status = TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING; // 退回状态
            }            
            // 更新审核状态
            $order->review_status = $newStatus;      
            $order->save();
            
            $statusText = Arr::get(self::REVIEW_STATUS, $newStatus, '未知状态');
            
            // 根据当前状态选择跳转路由
            $redirectRoute = match($newStatus) {
                TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_WAIT => 'r_h3c_purchase_orders/type/pending',
                TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK => 'r_h3c_purchase_orders/type/approved',
                TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_NG => 'r_h3c_purchase_orders/type/rejected',
                default => 'r_h3c_purchase_orders/type/all'
            };
            log::info('H3cOrderReview redirectRoute', [
                'redirectRoute' => $redirectRoute,
                'currentStatus' => $currentStatus,
                'newStatus' => $newStatus,
                'statusText' => $statusText,
                'request_all' => $request->all(),
                'id' => $id,
                'route_params' => request()->route()->parameters()
            ]);
            // 使用 Dcat Admin 的方法同步菜单状态和路由
            return $this->response()
                ->success("单据{$statusText}成功！")
                ->script("
                    // 使用 Dcat Admin 的方法跳转和同步菜单
                    if (parent.Admin && parent.Admin.reload) {
                        parent.Admin.reload('/admin/{$redirectRoute}');
                    } else {
                        parent.location.href = '/admin/{$redirectRoute}';
                    }
                ");
        } catch (\Exception $exception) {
            Log::error('H3cOrderReview error', [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
                'request_all' => $request->all(),
                'id' => $id ?? null,
                'current_status' => $currentStatus ?? null,
                'route_params' => request()->route()->parameters()
            ]);
            return $this->response()->error($exception->getMessage());
        }
    }

    /**
     * @return string
     */
    public function html(): string
    {
        $buttonClass = match($this->review_status) {
            TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_WAIT => 'btn-warning',
            TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK => 'btn-success',
            TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_NG => 'btn-danger',
            // BaseModel::H3C_REVIEW_STATUS_PENDING => 'btn-success',
            default => 'btn-primary'
        };
        
        $icon = match($this->review_status) {
            TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_WAIT => 'feather icon-clock',
            TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK => 'feather icon-check-circle',
            TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_NG => 'feather icon-x-circle',
            // BaseModel::H3C_REVIEW_STATUS_PENDING => 'feather icon-shopping-cart',
            default => 'feather icon-user-check'
        };
        
        return <<<HTML
<div class="btn-group pull-right" style="margin-right: 5px">
    <a {$this->formatHtmlAttributes()}>
        <button class="btn {$buttonClass} btn-mini">
            <i class="{$icon}"></i> {$this->title()}
        </button>
    </a>
</div>
HTML;
    }

    /**
     * @return string
     */
    public function getModel(): string
    {
        return 'TecH3cPurchaseOrder';
    }

    /**
     * @return array
     */
    protected function parameters(): array
    {
        $key = request()->route('r_h3c_purchase_order');
        
        Log::info('H3cOrderReview parameters', [
            'model' => $this->getModel(),
            'review_status' => $this->review_status,
            'key' => $key,
            'request_all' => request()->all()
        ]);

        return [
            'table'         => 'r_h3c_purchase_orders',
            'model'         => $this->getModel(),
            'review_status' => $this->review_status,
            '_key'          => $key,
            'id'            => $key
        ];
    }

    /**
     * 检查订单状态和明细
     */
    protected function check(Request $request): void
    {
        $model = $this->getModel();
        $modelClass = "\\App\\Models\\{$model}Model";

        // 从请求中获取 ID
        $id = $request->get('_key');
        if (empty($id)) {
            $id = $request->get('id');
        }
        if (empty($id)) {
            $id = request()->route('r_h3c_purchase_order');
        }

        Log::info('H3cOrderReview check', [
            'model' => $model,
            'modelClass' => $modelClass,
            'request_all' => $request->all(),
            'id' => $id,
            'route_params' => request()->route()->parameters()
        ]);

        if (!class_exists($modelClass)) {
            throw new ApiRequestException("找不到模型类：{$modelClass}");
        }

        // 获取订单
        $this->model = $modelClass::find($id);
        
        if (!$this->model) {
            throw new ApiRequestException("找不到订单！订单ID: {$id}, 模型: {$modelClass}");
        }
    }

    /**
     * @return array
     */
    public function confirm(): array
    {
        return ["你确定要{$this->title}单据?"];
    }
} 