<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cPurchaseOrder;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TecH3cPurchaseOrderExportAction extends AbstractTool
{
    /**
     * 按钮标题
     *
     * @return string
     */
    protected $title = '导出订单';

    public function title()
    {
        return '<i class="fa fa-download"></i> ' . $this->title;
    }

    protected function html()
    {
        // 生成唯一ID
        $id = 'h3c-order-export-btn';
        
        // 设置HTML属性
        $this->setHtmlAttribute([
            'class' => 'btn btn-primary', 
            'data-action' => 'export',
            'id' => $id,
            'title' => '导出订单及明细数据'
        ]);
        
        $attributes = $this->formatHtmlAttributes();
        
        // 记录日志
        Log::info('生成导出按钮HTML', [
            'id' => $id,
            'attributes' => $attributes,
        ]);
        
        return <<<HTML
<button {$attributes}>{$this->title()}</button>
HTML;
    }

    /**
     * 构建导出URL
     *
     * @return string
     */
    protected function buildExportUrl()
    {
        $url = admin_url('h3c_purchase_orders_export');
        
        // 附加当前查询参数
        if ($queries = request()->all()) {
            $url .= '?' . http_build_query($queries);
        }
        
        Log::info('构建导出URL', [
            'url' => $url,
            'queries' => $queries ?? [],
        ]);
        
        return $url;
    }

    /**
     * 渲染方法
     */
    public function render()
    {
        Log::info('开始渲染导出按钮');
        
        return $this->html() . $this->script();
    }

    /**
     * 初始化导出功能的JavaScript
     *
     * @return string
     */
    protected function script()
    {
        $exportUrl = $this->buildExportUrl();
        
        return <<<JS
<script>
$(function () {
    console.log('初始化导出按钮 #h3c-order-export-btn');
    
    $('#h3c-order-export-btn').off('click').on('click', function() {
        console.log('导出按钮被点击');
        
        // 显示加载提示
        Dcat.NP.start();
        
        // 获取当前过滤条件（手动序列化，确保日期等格式正确）
        var filterParams = {};
        $('.grid-filter-form').find('input, select').each(function() {
            var input = $(this);
            var name = input.attr('name');
            var value = input.val();
            
            // 检查是否为日期范围
            if (name === 'business_date' && Array.isArray(value)) {
                // 将日期数组转为逗号分隔的字符串
                filterParams[name] = value.join(',');
                console.log('转换日期范围:', name, filterParams[name]);
            } else if (name && value !== null && value !== undefined) {
                filterParams[name] = value;
            }
        });
        
        console.log('过滤条件:', filterParams);
        
        // 构建URL参数
        var queryParams = $.param(filterParams);
        
        // 构建完整URL
        var url = '{$exportUrl}';
        if (queryParams) {
            url += (url.indexOf('?') >= 0 ? '&' : '?') + queryParams;
        }
        console.log('导出URL:', url);
        
        // 先测试导出接口是否可用
        $.ajax({
            url: url + (url.indexOf('?') >= 0 ? '&' : '?') + 'test=1',
            type: 'GET',
            success: function(res) {
                Dcat.NP.done();
                if (res.status) {
                    console.log('导出接口测试成功，开始下载');
                    
                    // 创建一个隐藏的iframe来处理下载，这样可以捕获错误响应
                    var iframeName = 'export-frame-' + Date.now();
                    var iframe = $('<iframe name="' + iframeName + '" style="display:none;"></iframe>');
                    
                    $('body').append(iframe);
                    
                    // 创建表单并提交
                    var form = $('<form target="' + iframeName + '" method="get" action="' + url + '"></form>');
                    $('body').append(form);
                    
                    // 提交表单
                    form.submit();
                    
                    // 监听iframe加载事件
                    iframe.on('load', function() {
                        try {
                            // 尝试获取iframe内容
                            var content = iframe.contents().find('body').text();
                            
                            // 检查是否为JSON (表示错误)
                            if (content && content.indexOf('{') === 0) {
                                try {
                                    var jsonResponse = JSON.parse(content);
                                    if (!jsonResponse.status && jsonResponse.message) {
                                        console.error('导出失败:', jsonResponse.message);
                                        Dcat.error(jsonResponse.message);
                                    }
                                } catch (e) {
                                    console.log('iframe内容非JSON格式，可能是文件下载');
                                }
                            }
                        } catch (e) {
                            console.log('无法访问iframe内容，可能是跨域限制或文件下载');
                        }
                        
                        // 稍后移除iframe和表单
                        setTimeout(function() {
                            iframe.remove();
                            form.remove();
                        }, 5000);
                    });
                } else {
                    console.error('导出接口返回错误:', res.message);
                    Dcat.error(res.message || '导出失败');
                }
            },
            error: function(xhr) {
                Dcat.NP.done();
                console.error('导出请求失败:', xhr);
                Dcat.error('导出请求失败: ' + (xhr.responseJSON?.message || '未知错误'));
            }
        });
    });
});
</script>
JS;
    }

    /**
     * 处理导出请求
     *
     * @param Request $request
     * @return mixed
     */
    public function handle(Request $request)
    {
        // 请注意：此方法通常不会被调用，因为我们是通过URL直接访问控制器的导出方法
        return $this->response()->success('正在导出...')->refresh();
    }
}