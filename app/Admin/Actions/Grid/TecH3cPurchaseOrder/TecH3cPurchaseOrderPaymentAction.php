<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cPurchaseOrder;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Admin;

/**
 * 批量付款处理按钮
 * 可在一览画面对选择的数据批量将 payment_status 字段更新为2
 */
use Dcat\Admin\Grid\Tools\AbstractTool;

class TecH3cPurchaseOrderPaymentAction extends AbstractTool
{
    /**
     * 按钮标题
     * @var string
     */
    protected $title = '付款处理';

    /**
     * 获取按钮标题（带图标）
     * @return string
     */
    public function title()
    {
        return '<i class="fa fa-money"></i> ' . $this->title;
    }

    /**
     * 生成按钮HTML
     * @return string
     */
    protected function html()
    {
        // 生成唯一ID
        $id = 'h3c-order-payment-btn';

        // 设置HTML属性
        $this->setHtmlAttribute([
            'class' => 'btn btn-primary',
            'data-action' => 'payment',
            'id' => $id,
            'title' => '批量付款处理'
        ]);
        $attributes = $this->formatHtmlAttributes();

        return <<<HTML
<button {$attributes}>{$this->title()}</button>
HTML;
    }

    /**
     * 渲染按钮及JS
     * @return string
     */
    public function render()
    {
        return $this->html() . $this->script() . $this->paymentDateModal();
    }

    /**
     * 付款日期选择模态框
     * @return string
     */
    protected function paymentDateModal()
    {
        return <<<HTML
<div class="modal fade" id="payment-date-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择付款日期</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>付款日期</label>
                    <input type="date" id="payment-date" class="form-control" value="{$this->getTodayDate()}">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-payment-date">确认</button>
            </div>
        </div>
    </div>
</div>
HTML;
    }

    /**
     * 获取今天的日期
     * @return string
     */
    protected function getTodayDate()
    {
        return date('Y-m-d');
    }

    /**
     * 按钮点击JS逻辑，完全仿照导出按钮风格
     * @return string
     */
    protected function script()
    {
        // 后端接口URL（可根据实际需要调整）
        $url = admin_url('h3c_purchase_orders/batch-payment');
        return <<<JS
<script>
$(function () {
    $('#h3c-order-payment-btn').off('click').on('click', function() {
        // 获取选中的数据ID
        var selected = Dcat.grid.selected();
        if (!selected || selected.length === 0) {
            Dcat.error('未选择任何数据');
            return;
        }
        
        // 弹出日期选择模态框
        $('#payment-date-modal').modal('show');
        
        // 确认付款日期
        $('#confirm-payment-date').off('click').on('click', function() {
            var paymentDate = $('#payment-date').val();
            
            Dcat.NP.start();
            // 发起批量付款处理请求
            $.ajax({
                url: '{$url}',
                type: 'POST',
                data: {
                    _token: Dcat.token,
                    ids: selected,
                    payment_date: paymentDate
                },
                success: function(res) {
                    Dcat.NP.done();
                    if (res.status) {
                        Dcat.success(res.message || '操作成功');
                        $('#payment-date-modal').modal('hide');
                        Dcat.reload();
                    } else {
                        Dcat.error(res.message || '操作失败');
                    }
                },
                error: function(xhr) {
                    Dcat.NP.done();
                    Dcat.error(xhr.responseJSON?.message || '请求失败');
                }
            });
        });
    });
});
</script>
JS;
    }
}
