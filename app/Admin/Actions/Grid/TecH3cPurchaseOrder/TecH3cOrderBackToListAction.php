<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cPurchaseOrder;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Admin;

class TecH3cOrderBackToListAction extends AbstractTool
{
    protected $title = '返回一览';
    
    /**
     * @var string
     */
    protected $resource;

    public function __construct($title = null)
    {
        parent::__construct();
        $this->title = '<i class="fa fa-list"></i> ' . $this->title;
        
        // 设置资源路径
        $this->resource = 'r_h3c_purchase_orders/type/all';

        // 添加 JavaScript 代码
        Admin::script($this->script());
    }

    /**
     * 获取返回列表的URL
     */
    public function getBackToListUrl()
    {
        return admin_url($this->resource);
    }

    protected function script()
    {
        return <<<JS
        $(function () {
            $('.grid-back-to-list-btn').on('click', function() {
                var url = $(this).data('url');
                
                console.log('Back to list button clicked:', {
                    url: url
                });

                // 直接跳转到列表页面
                window.location.href = url;
            });
        });
        JS;
    }

    protected function html()
    {
        // 添加按钮的 class 和 data 属性
        $this->setHtmlAttribute([
            'data-url' => $this->getBackToListUrl(),
            'class' => 'btn btn-primary btn-mini grid-back-to-list-btn',
        ]);

        return <<<HTML
<div class="btn-group pull-right" style="margin-right: 5px">
    <button {$this->formatHtmlAttributes()}>
        {$this->title}
    </button>
</div>
HTML;
    }
} 