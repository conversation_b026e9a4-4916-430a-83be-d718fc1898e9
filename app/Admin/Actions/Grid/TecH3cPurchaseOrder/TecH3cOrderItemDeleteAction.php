<?php

namespace App\Admin\Actions\Grid\TecH3cPurchaseOrder;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use App\Models\TecH3cPurchaseOrderItemModel;
use Exception;
use Illuminate\Support\Facades\Log;

class TecH3cOrderItemDeleteAction extends BatchAction
{
    protected $title = '删除';

    /**
     * 确认弹窗信息
     */
    public function confirm()
    {
        return '确定要删除选中的明细吗？';
    }

    /**
     * 处理请求
     */
    public function handle(Request $request)
    {
        // 输入验证
        $keys = $this->getKey();
        if (empty($keys) || !is_array($keys)) {
            Log::warning('Invalid Delete Request', [
                'keys' => $keys,
                'user_id' => auth()->id()
            ]);
            return $this->response()->error('无效的删除请求');
        }

        try {
            // 记录初始日志
            Log::info('Order Items Delete Attempt', [
                'keys' => $keys,
                'user_id' => auth()->id(),
                'timestamp' => now()->toDateTimeString()
            ]);
            
            // 检查订单明细数量
            $orderId = TecH3cPurchaseOrderItemModel::withTrashed()->whereIn('id', $keys)->value('order_id');
            
            if (!$orderId) {
                Log::warning('Delete Order Items Failed: No Order ID Found', [
                    'keys' => $keys
                ]);
                
                return $this->response()->error('未找到订单明细');
            }

            // 记录检查前的数量信息
            $totalItems = TecH3cPurchaseOrderItemModel::withTrashed()->where('order_id', $orderId)->count();
            Log::info('Order Items Count Check', [
                'order_id' => $orderId,
                'total_items' => $totalItems,
                'items_to_delete' => count($keys)
            ]);

            // 防止删除所有订单明细
            if ($totalItems <= count($keys)) {
                Log::warning('Delete Order Items Failed: Insufficient Remaining Items', [
                    'order_id' => $orderId,
                    'total_items' => $totalItems,
                    'items_to_delete' => count($keys)
                ]);
                
                return $this->response()->error('至少要保留一条订单明细');
            }

            // 执行删除操作
            $result = TecH3cPurchaseOrderItemModel::withTrashed()->whereIn('id', $keys)->forceDelete();

            // 记录删除结果
            Log::info('Order Items Deletion Result', [
                'order_id' => $orderId,
                'deleted_count' => $result,
                'total_items_before' => $totalItems,
                'total_items_after' => $totalItems - $result
            ]);

            // 返回操作结果
            return $result 
                ? $this->response()->success('删除成功')->refresh()
                : $this->response()->error('删除失败');

        } catch (Exception $e) {
            // 记录详细的异常信息
            Log::error('Order Items Deletion Error', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'keys' => $keys,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->response()->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 自定义行为按钮的HTML
     */
    protected function html()
    {
        return <<<HTML
<a {$this->formatHtmlAttributes()}>
    <button class="btn btn-primary btn-mini">
        <i class="feather icon-trash-2"></i> {$this->title()}
    </button>
</a>
HTML;
    }

    /**
     * 设置行为按钮的JS脚本
     */
    public function actionScript()
    {
        $warning = "请选择要删除的明细！";

        return <<<JS
function (data, target, action) {
    var key = {$this->getSelectedKeysScript()}

    if (key.length === 0) {
        Dcat.warning('{$warning}');
        return false;
    }

    // 设置主键为复选框选中的行ID数组
    action.options.key = key;
}
JS;
    }
}
