<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cPurchaseOrder;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use App\Models\TecH3cPurchaseOrderModel;
use App\Models\TecH3cPurchaseOrderItemModel;

class TecH3cPurchaseOrderPermanentDeleteAction extends BatchAction
{
    protected $title = '删除';

    public function match($key)
    {
        // 仅在回收站页面显示
        return request('_scope_') === 'trashed';
    }

    public function handle(Request $request)
    {
        // 获取选中的记录 ID
        $keys = $this->getKey();
        
        if (empty($keys)) {
            return $this->response()->error('请选择要删除的记录');
        }

        try {
            // 直接使用模型进行操作，而不是通过控制器
            $records = TecH3cPurchaseOrderModel::withTrashed()->whereIn('id', $keys);
            $count = $records->count();

            // 在删除主表记录之前，先删除关联的子表记录
            TecH3cPurchaseOrderItemModel::whereIn('order_id', $keys)->forceDelete();

            // 执行永久删除
            $records->forceDelete();

            return $this->response()
                ->success("成功永久删除 {$count} 条记录")
                ->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('删除失败：' . $e->getMessage());
        }
    }

    public function confirm()
    {
        return [
            '确认要彻底删除选中的记录吗？', 
            '此操作不可撤销',
            [
                'showLoaderOnConfirm' => true,
                'preConfirm' => true
            ]
        ];
    }

    /**
     * @return string
     */
    protected function html(): string
    {
        return <<<HTML
<a style="cursor: pointer;" class="act-zBYbBSUIrYh8rCMs"><button class="btn btn-primary btn-mini btn-outline"><i class="feather icon-trash-2"></i> {$this->title()}</button></a>
HTML;
    }
}
