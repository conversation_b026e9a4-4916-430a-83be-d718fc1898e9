<?php

/*
 * // +----------------------------------------------------------------------
 * // | erp
 * // +----------------------------------------------------------------------
 * // | Copyright (c) 2006~2020 erp All rights reserved.
 * // +----------------------------------------------------------------------
 * // | Licensed ( LICENSE-1.0.0 )
 * // +----------------------------------------------------------------------
 * // | Author: yxx <<EMAIL>>
 * // +----------------------------------------------------------------------
 */

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cPurchaseOrder;

use App\Models\TecH3cPurchaseOrderModel;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Yxx\LaravelQuick\Exceptions\Api\ApiRequestException;
use Dcat\Admin\Admin;

class TecH3cOrderPrintAction extends AbstractTool
{
    protected $title = '生成PO';
    
    /**
     * @var string
     */
    protected $resource;

    public function __construct($title = null)
    {
        parent::__construct();
        $this->title = '<i class="fa fa-print"></i> ' . $this->title;
        
        // 设置资源路径
        $this->resource = 'r_h3c_purchase_orders';

        // 添加 JavaScript 代码
        Admin::script($this->script());
    }

    /**
     * 获取请求地址
     */
    public function getHandleRoute()
    {
        $key = request()->route('r_h3c_purchase_order');
        return admin_url("r_h3c_purchase_orders/{$key}/print");
    }

    protected function script()
    {
        $parameters = $this->parameters();
        $key = $parameters['_key'] ?? '';
        
        return <<<JS
        $(function () {
            $('.grid-print-btn').on('click', function() {
                var url = $(this).data('url');
                var id = '{$key}';

                console.log('Print button clicked:', {
                    url: url,
                    id: id
                });

                if (!id) {
                    Dcat.error('未找到订单ID');
                    return;
                }

                // 直接在新窗口中打开打印页面（类似客户报价单的方式）
                var printWindow = window.open(url, '_blank');
                if (!printWindow) {
                    Dcat.error('请允许浏览器打开新窗口');
                }
            });
        });
        JS;
    }

    public function handle(Request $request)
    {
        try {
            // 从请求中获取 ID
            $id = $request->get('_key');
            
            Log::info('开始获取打印订单数据', [
                'order_id' => $id,
                'request_path' => $request->path(),
                'full_url' => $request->fullUrl(),
                'request_all' => $request->all()
            ]);
            
            if (empty($id)) {
                throw new ApiRequestException('订单ID不能为空！');
            }
            
            // 获取订单数据
            $order = TecH3cPurchaseOrderModel::with(['items.productlist', 'sndCompany'])
                ->findOrFail($id);
            
            Log::info('打印订单数据获取成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'items_count' => $order->items->count()
            ]);
            
            // 返回确认视图
            return view('admin.h3c.tec_h3c_purchase_order_confirm', compact('order'));
            
        } catch (\Exception $e) {
            Log::error('获取打印订单数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_all' => $request->all()
            ]);
            return $this->response()->error('获取打印订单数据失败：' . $e->getMessage());
        }
    }

    protected function html()
    {
        // 添加按钮的 class 和 data 属性
        $this->setHtmlAttribute([
            'data-url' => $this->getHandleRoute(),
            'data-action' => 'print',
            'class' => 'btn btn-primary btn-mini grid-print-btn',
        ]);

        return <<<HTML
<div class="btn-group pull-right" style="margin-right: 5px">
    <button {$this->formatHtmlAttributes()}>
        {$this->title}
    </button>
</div>
HTML;
    }

    protected function parameters(): array
    {
        $key = request()->route('r_h3c_purchase_order');
        
        Log::info('打印订单参数', [
            'key' => $key,
            'request_all' => request()->all(),
            'resource' => $this->resource
        ]);

        return [
            'table' => $this->resource,
            '_key'  => $key,
            'id'    => $key
        ];
    }

    public function confirm()
    {
        return ['确定要打印此订单吗？'];
    }
}
