<?php

declare(strict_types=1);

namespace App\Admin\Actions\Grid\TecH3cPurchaseOrder;

use App\Models\BaseModel;
use App\Models\PurchaseBaseModel;
use App\Models\TecH3cPurchaseBaseModel;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Yxx\LaravelQuick\Exceptions\Api\ApiRequestException;

class TecH3cPurchaseOrderAction extends AbstractTool
{
    /**
     * @var int|null
     */
    protected $order_status;


    public function __construct($order_status = null)
    {
        parent::__construct();
        
        Log::info('TecH3cPurchaseOrderAction construct', [
            'order_status' => $order_status,
            'title' => $this->title,
            'request_all' => request()->all()
        ]);
        
        // 获取订单当前状态
        $orderId = request()->route('r_h3c_purchase_order');
        if ($orderId) {
            $modelClass = "\\App\\Models\\{$this->getModel()}Model";
            $order = $modelClass::find($orderId);
            if ($order) {
                $currentOrderStatus = $order->order_status;
                
                // 根据发货状态设置按钮文字
                $this->title = TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_BUTTON_TEXT[$currentOrderStatus] 
                    ?? TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_BUTTON_TEXT['_default'];
            }
        }
        
        $this->order_status = $order_status;
        
        Log::info('TecH3cPurchaseOrderAction button title', [
            'order_status' => $order_status,
            'order_id' => $orderId ?? null,
            'current_order_status' => $currentOrderStatus ?? null,
            'title' => $this->title
        ]);
    }

    public function handle(Request $request)
    {
        try {
            // 从请求中获取 ID
            $id = $request->get('_key');
            if (empty($id)) {
                $id = $request->get('id');
            }
            if (empty($id)) {
                $id = request()->route('r_h3c_purchase_order');
            }
            
            // 记录请求信息
            Log::info('TecH3cOrderDelivery handle', [
                'request_all' => $request->all(),
                'id' => $id,
                'route_params' => request()->route()->parameters()
            ]);

            if (empty($id)) {
                throw new ApiRequestException("订单ID不能为空！");
            }

            // 获取订单
            $modelClass = "\\App\\Models\\{$this->getModel()}Model";
            $order = $modelClass::find($id);
            
            if (!$order) {
                throw new ApiRequestException("找不到订单！订单ID: {$id}, 模型: {$modelClass}");
            }

            // 获取订单当前发货状态
            $currentStatus = (int)$order->order_status;
            
            // 更新发货状态
            $newStatus = match($currentStatus) {
                TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING => TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_ORDERED,           // 待下单 -> 已下单
                // PurchaseBaseModel::H3C_DELIVERY_ORDERED => PurchaseBaseModel::H3C_WAREHOUSING_STATUS_PENDING,     // 已下单 -> 部分入库
                // TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS_PENDING => PurchaseBaseModel::H3C_DELIVERY_COMPLETED,   // 部分入库 -> 完全入库
                default => throw new ApiRequestException("无效的发货状态：{$currentStatus}")
            };            
            
            // 更新发货状态
            $order->order_status = $newStatus;      
            $order->save();
            
            $statusText = Arr::get(TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_BUTTON_TEXT, $newStatus, '未知状态');
            
            // 根据当前状态选择跳转路由
            $redirectRoute = match($currentStatus) {
                TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING => 'r_h3c_purchase_orders/type/ordered',
                // PurchaseBaseModel::H3C_DELIVERY_ORDERED => 'r_h3c_purchase_orders/type/partial_delivery',
                // TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS_PENDING => 'r_h3c_purchase_orders/type/full_delivery',
                default => 'r_h3c_purchase_orders/type/all'
            };
            
            log::info('H3cOrderDelivery redirectRoute', [
                'redirectRoute' => $redirectRoute,
                'currentStatus' => $currentStatus,
                'newStatus' => $newStatus,
                'statusText' => $statusText,
                'request_all' => $request->all(),
                'id' => $id,
                'route_params' => request()->route()->parameters()
            ]);
            
            // 使用 Dcat Admin 的方法同步菜单状态和路由
            return $this->response()
                ->success("单据{$statusText}成功！")
                ->script("
                    // 使用 Dcat Admin 的方法跳转和同步菜单
                    if (parent.Admin && parent.Admin.reload) {
                        parent.Admin.reload('/admin/{$redirectRoute}');
                    } else {
                        parent.location.href = '/admin/{$redirectRoute}';
                    }
                ");
        } catch (\Exception $exception) {
            Log::error('TecH3cOrderDelivery error', [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
                'request_all' => $request->all(),
                'id' => $id ?? null,
                'current_status' => $currentStatus ?? null,
                'route_params' => request()->route()->parameters()
            ]);
            return $this->response()->error($exception->getMessage());
        }
    }

    /**
     * @return string
     */
    public function html(): string
    {
        $buttonClass = match($this->order_status) {
            TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING => 'btn-success',
            default => 'btn-success'
        };
        
        $icon = match($this->order_status) {
            TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING => 'feather icon-shopping-cart',
            default => 'feather icon-shopping-cart'
        };
        
        return <<<HTML
<div class="btn-group pull-right" style="margin-right: 5px">
    <a {$this->formatHtmlAttributes()}>
        <button class="btn {$buttonClass} btn-mini">
            <i class="{$icon}"></i> {$this->title()}
        </button>
    </a>
</div>
HTML;
    }

    /**
     * @return string
     */
    public function getModel(): string
    {
        return 'TecH3cPurchaseOrder';
    }

    /**
     * @return array
     */
    protected function parameters(): array
    {
        $key = request()->route('r_h3c_purchase_order');
        
        Log::info('TecH3cOrderDelivery parameters', [
            'model' => $this->getModel(),
            'order_status' => $this->order_status,
            'key' => $key,
            'request_all' => request()->all()
        ]);

        return [
            'table'         => 'r_h3c_purchase_orders',
            'model'         => $this->getModel(),
            'order_status' => $this->order_status,
            '_key'          => $key,
            'id'            => $key
        ];
    }

    /**
     * @return array
     */
    public function confirm(): array
    {
        return ["你确定要{$this->title}此订单?"];
    }
} 