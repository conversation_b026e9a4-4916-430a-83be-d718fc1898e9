<?php

namespace App\Admin\Actions\Grid\TecH3cProductList;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use App\Models\TecH3cProductListModel;

class TecH3cProductListCopyAction extends BatchAction
{
    protected $title = '复制产品';

    public function match($key)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return false;
        }

        // 只有选择单条数据时才显示
        return $this->grid->getSelectedKeys()->count() === 1;
    }

    public function handle(Request $request)
    {
        // 检查是否在回收站页面
        $scope = request('_scope_', '');
        if ($scope === 'trashed') {
            return $this->response()->error('回收站中不允许复制产品');
        }

        // 获取选中的记录 ID
        $keys = $this->getKey() ?? [];
        
        if (count($keys) !== 1) {
            return $this->response()
                ->error('请选择单条数据进行复制')
                ->refresh();
        }

        $id = reset($keys);  // 获取数组的第一个元素

        try {
            // 获取原始产品
            $originalProduct = TecH3cProductListModel::findOrFail($id);

            // 创建新产品，复制原始产品的所有属性
            $newProduct = $originalProduct->replicate();

            // 修改商品名称，添加 -copy 后缀
            $newProduct->product = $originalProduct->product . '-copy';

            // 保存新产品
            $newProduct->save();

            return $this->response()
                ->success('成功复制产品')
                ->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('复制产品失败：' . $e->getMessage());
        }
    }

    public function confirm()
    {
        return ['确认要复制选中的产品吗？', '将创建一个新的产品副本'];
    }
}
