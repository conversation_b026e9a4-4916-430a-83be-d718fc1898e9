<?php

/*
 * // +----------------------------------------------------------------------
 * // | erp
 * // +----------------------------------------------------------------------
 * // | Copyright (c) 2006~2020 erp All rights reserved.
 * // +----------------------------------------------------------------------
 * // | Licensed ( LICENSE-1.0.0 )
 * // +----------------------------------------------------------------------
 * // | Author: yxx <<EMAIL>>
 * // +----------------------------------------------------------------------
 */

namespace App\Admin\Controllers;

use Dcat\Admin\Http\Controllers\AuthController as BaseAuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Mews\Captcha\Facades\Captcha;
use Dcat\Admin\Layout\Content;

class AuthController extends BaseAuthController
{
    /**
     * 显示登录页面
     *
     * @param Content $content
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|Content
     */
    public function getLogin(Content $content)
    {
        if ($this->guard()->check()) {
            return redirect($this->getRedirectPath());
        }

        return $content
            ->full()
            ->body(view('admin.auth.login'));
    }
    
    /**
     * 处理登录请求
     *
     * @param Request $request
     *
     * @return mixed
     */
    public function postLogin(Request $request)
    {
        // 检查IP是否被锁定
        if ($this->hasTooManyLoginAttempts($request)) {
            return $this->sendLockoutResponse($request);
        }

        $credentials = $request->only([$this->username(), 'password', 'captcha']);
        $validator = Validator::make($credentials, [
            $this->username()   => 'required',
            'password'          => 'required',
            'captcha'           => 'required|captcha'
        ], [
            'captcha.required' => '请输入验证码',
            'captcha.captcha' => '验证码不正确'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withInput($request->only($this->username(), 'remember'))
                ->withErrors($validator);
        }

        $credentials = $request->only([$this->username(), 'password']);
        if ($this->guard()->attempt($credentials, $request->has('remember'))) {
            // 登录成功，清除失败计数
            $this->clearLoginAttempts($request);
            return $this->sendLoginResponse($request);
        }

        // 增加失败次数
        $this->incrementLoginAttempts($request);
        
        return $this->sendFailedLoginResponse($request);
    }
    
    /**
     * 检查登录尝试是否过多
     *
     * @param Request $request
     * @return bool
     */
    protected function hasTooManyLoginAttempts(Request $request)
    {
        $key = 'login_attempts:'.$request->ip();
        $maxAttempts = config('security.auth.max_attempts', 5);
        
        $attempts = Cache::get($key, 0);
        
        return $attempts >= $maxAttempts;
    }
    
    /**
     * 增加登录尝试次数
     *
     * @param Request $request
     */
    protected function incrementLoginAttempts(Request $request)
    {
        $key = 'login_attempts:'.$request->ip();
        $decayMinutes = config('security.auth.lockout_minutes', 60);
        
        $attempts = Cache::get($key, 0);
        Cache::put($key, $attempts + 1, now()->addMinutes($decayMinutes));
    }
    
    /**
     * 清除登录尝试记录
     *
     * @param Request $request
     */
    protected function clearLoginAttempts(Request $request)
    {
        $key = 'login_attempts:'.$request->ip();
        Cache::forget($key);
    }
    
    /**
     * 发送被锁定的响应
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    protected function sendLockoutResponse(Request $request)
    {
        $message = trans('admin.auth.throttle', [
            'seconds' => config('security.auth.lockout_minutes', 60) * 60,
        ]);
        
        return redirect()->back()
            ->withInput($request->only($this->username(), 'remember'))
            ->withErrors([
                $this->username() => $message,
            ]);
    }
    
    /**
     * 发送登录失败响应
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    protected function sendFailedLoginResponse(Request $request)
    {
        return redirect()->back()
            ->withInput($request->only($this->username(), 'remember'))
            ->withErrors([
                $this->username() => $this->getFailedLoginMessage(),
            ]);
    }
    
    /**
     * 获取登录失败消息
     *
     * @return string
     */
    protected function getFailedLoginMessage()
    {
        return trans('admin.auth.failed');
    }
}
