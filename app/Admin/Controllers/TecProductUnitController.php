<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\TecProductUnitRepo;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use App\Models\TecProductUnitModel;
use App\Admin\Actions\Post\Restore;


class TecProductUnitController extends AdminController
{
    public function index(Content $content)
    {
        return $content
            ->header('Product Categories')
            ->description('Manage your product categories')
            ->body(function (Row $row) {
                // 左侧占据一半宽度
                $row->column(4, function (Column $column) {
                    $column->append($this->grid());
                });

                // 右侧留空
                $row->column(8, function (Column $column) {
                    // 留空，不添加内容
                });
            });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecProductUnitRepo(), function (Grid $grid) {
            $grid->column('id')->style('width: 100px; font-weight: bold; text-align: left;');
            $grid->column('unit_name')->style('width: 200px; font-weight: bold; text-align: left;');

            // 禁止多选
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed();
        
            });

            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecProductUnitModel::class));
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecProductUnitRepo(), function (Show $show) {
            $show->field('id');
            $show->field('unit_name');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecProductUnitRepo(), function (Form $form) {
            $form->display('id');
            $form->text('unit_name')->required();

            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮
            // 隐藏查看按钮
            $form->tools(function (Form\Tools $tools) {
                $tools->disableView();
            });
        });
    }
}
