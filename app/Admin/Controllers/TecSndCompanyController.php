<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Content;
use App\Models\TecSndCompanyModel;
use App\Models\TecPaymentTermModel;
use App\Models\TecProductMajorCategoryModel;
use App\Admin\Repositories\TecSndCompanyRepo;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\Post\Restore;
use App\Admin\Actions\Grid\TecSndCompany\TecSndCompanyCopyAction;
use App\Support\GridStyleHelper;

class TecSndCompanyController extends AdminController
{
    // 配置 Grid
    protected function grid()
    {
        return Grid::make(new TecSndCompanyRepo(new TecSndCompanyModel()), function (Grid $grid) {
            $grid->scrollbarX();
            
            // 预加载关系，避免N+1查询
            $grid->model()->with(['paymentTerm']);
            
            // 公司名称
            GridStyleHelper::companyNameColumnConfig()($grid);

            // 貿易条件列
            GridStyleHelper::incotermsColumnConfig()($grid);

            // 公司业务角色
            GridStyleHelper::companyRoleColumnConfig()($grid);

            // 业务类别
            GridStyleHelper::businessCategoriesColumnConfig()($grid);

            // 付款条款
            GridStyleHelper::paymentTermsColumnConfig()($grid);

            // 交易币种
            GridStyleHelper::currencyColumnConfig()($grid);

            // 是否需要计算インセンティブ
            $grid->column('need_incentive_calc', 'ｲﾝｾﾝﾃｨﾌﾞ')
                ->switch()
                ->sortable();

            // 银行 1 名称
            $grid->column('bank1_name');

            // 合并地址字段
            $grid->column('full_address', '地址')->display(function () {
                return trim(
                    (optional($this)->postal_code ?? '') . ' ' .
                    (optional($this)->country ?? '') . ' ' .
                    (optional($this)->state ?? '') . ' ' .
                    (optional($this)->city ?? '') . ' ' .
                    (optional($this)->address1 ?? '') . ' ' .
                    (optional($this)->address2 ?? '') . ' ' .
                    (optional($this)->address3 ?? '')
                );
            });

            $grid->filter(function (Grid\Filter $filter) {
                // 过滤器中的角色筛选器
                $filter->like('snd_role', '公司业务角色')->select(function () {
                    // 从配置文件中读取业务角色选项
                    $roles = config('purchase_config.roles');
                    // 将角色的键值对互换，键变成值，值变成键
                    return $roles;  // 直接使用原配置文件中的值
                })->width(2);

                $filter->like('business_categories', '业务类别')->select(function () {
                    // 从数据库中获取业务类别选项
                    return TecProductMajorCategoryModel::all()->pluck('major_category', 'id')->toArray();
                })->width(2);
                $filter->scope('trashed', '回收站')->onlyTrashed();

            });

            $grid->quickSearch(['name']);

            // 判断是否为回收站作用域
            $isTrashedScope = request('_scope_') === 'trashed';

            // 添加复制功能
            if (!$isTrashedScope) {
                $grid->batchActions([
                    new TecSndCompanyCopyAction()
                ]);
            }

            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecSndCompanyModel::class));
                }
            });
        });
    }

    // 配置 Index 页面
    public function index(Content $content)
    {
        return $content
            ->header('顧客管理')
            ->description('顧客管理')
            ->body($this->grid());
    }

    // 配置 Form 页面
    protected function form()
    {
        return Form::make(new TecSndCompanyRepo(new TecSndCompanyModel()), function (Form $form) {
            // 确保使用正确的 `BlockForm` 实例
            $form->tab('基本信息', function (Form $form) {

                $form->column(6, function (Form $form) {

                    $form->text('name', '供应商名称')->required();
                    $form->text('abbreviation', 'PO缩写')->required();
                    
                    // 添加公司颜色选择器
                    $form->color('company_color', '公司代表颜色')
                        ->default('#3498db')
                        ->help('选择公司的代表颜色，用于在系统中区分不同公司');

                    $form->radio('incoterms', '取引条件')->options(function () {
                        // 从配置文件中读取业务角色
                        return config('purchase_config.incoterms');
                    })->required();

                    $form->radio('PO_template', 'PO模板')->options(function () {
                        // 从配置文件中读取业务角色
                        return config('purchase_config.incoterms');
                    })->required();

                    $form->radio('snd_role', '公司业务角色')->options(function () {
                        // 从配置文件中读取业务角色
                        return config('purchase_config.roles');
                    })->required();

                    // 添加 checkbox 选择框来选择业务类别
                    $form->checkbox('business_categories', '业务类别')->options(function () {
                        return TecProductMajorCategoryModel::all()->mapWithKeys(function ($item) {
                            $color = $item->background_color; // 获取颜色
                            $colorStyles = [
                                'success' => 'var(--success)',
                                'warning' => 'var(--orange)',
                                'primary' => 'var(--primary)',
                                'danger' => 'var(--red)',
                                'info' => 'var(--cyan)'
                            ];

                            // 确保颜色存在
                            $bgColor = $colorStyles[$color] ?? '#ffffff';

                            return [
                                $item->id => '<span style="background-color: ' . $bgColor . '; color: white; padding: 5px; display: inline-block;">' . $item->major_category . '</span>'
                            ];
                        });
                    })->required();

                    // 选择付款条款时显示付款条款名称
                    $form->select('payment_terms_id', '付款条款')->options(function () {
                        return TecPaymentTermModel::all()->pluck('term_name', 'id');
                    })->required();

                    $form->decimal('profit_margin', '利润率')
                        ->default(10)  // 设置默认值为 10，即 10%
                        ->help('请输入利润率，单位为百分比（例如：10 表示 10%）');

                    $form->currency('credit_limit_usd', '信用额度 (美元)')
                        ->symbol('USD')
                        ->default(0);
                    $form->currency('credit_limit_jpy', '信用额度 (日元)')
                        ->symbol('JPY')
                        ->default(0);

                    $form->radio('transaction_currency', '交易币种')->options(function () {
                        // 从配置文件中读取业务角色
                        return config('purchase_config.currencies');
                    })->required();

                    $form->decimal('exchange_rate', '商定汇率')->help('所选交易币种对日元汇率')->default(160);
                    $form->switch('need_incentive_calc', 'ｲﾝｾﾝﾃｨﾌﾞ计算')
                        ->default(false);
                    $form->text('notes', '备注');
                });

                $form->column(6, function (Form $form) {
                    $form->text('postal_code', '邮政编码');
                    $form->text('country', '国家');
                    $form->text('province', '省');
                    $form->text('city', '城市');
                    $form->text('address1', '地址 1');
                    $form->text('address2', '地址 2');
                    $form->text('address3', '地址 3');
                    $form->url('website', '网站');
                });
            });

            // 银行信息 1 选项卡
            $form->tab('银行信息 1', function (Form $form) {
                $form->text('bank1_name', '银行 1 名称');
                $form->text('bank1_swift_code', '银行 1 SWIFT 代码');
                $form->text('bank1_address', '银行 1 地址');
                $form->text('bank1_code', '银行 1 代码');
                $form->text('bank1_branch_name', '银行 1 分行名称');
                $form->text('bank1_account_number', '银行 1 账号');
                $form->text('bank1_account_name', '银行 1 账户名');
                $form->select('bank1_currency', '银行 1 货币')->options(config('purchase_config.currencies'));
            });

            // 银行信息 2 选项卡
            $form->tab('银行信息 2', function (Form $form) {
                $form->text('bank2_name', '银行 2 名称');
                $form->text('bank2_swift_code', '银行 2 SWIFT 代码');
                $form->text('bank2_address', '银行 2 地址');
                $form->text('bank2_code', '银行 2 代码');
                $form->text('bank2_branch_name', '银行 2 分行名称');
                $form->text('bank2_account_number', '银行 2 账号');
                $form->text('bank2_account_name', '银行 2 账户名');
                $form->select('bank2_currency', '银行 2 货币')->options(config('purchase_config.currencies'));
            });

            $form->saving(function (Form $form) {
                // 表单保存前的逻辑
            });
        });
    }
}
