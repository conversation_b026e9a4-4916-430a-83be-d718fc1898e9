<?php

/*
 * // +----------------------------------------------------------------------
 * // | erp
 * // +----------------------------------------------------------------------
 * // | Copyright (c) 2006~2020 erp All rights reserved.
 * // +----------------------------------------------------------------------
 * // | Licensed ( LICENSE-1.0.0 )
 * // +----------------------------------------------------------------------
 * // | Author: yxx <<EMAIL>>
 * // +----------------------------------------------------------------------
 */

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Services\H3cOrderExcelService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;

class TecH3cPrintController extends Controller
{
    protected $excelService;

    public function __construct(H3cOrderExcelService $excelService)
    {
        $this->excelService = $excelService;
    }

    public function print(Request $request)
    {
        $orderId = $request->input('ids');
        
        // 如果没有传递有效的订单ID，返回错误
        if (!$orderId || $orderId === '0') {
            return response()->json([
                'status' => 'error',
                'message' => '请选择要打印的订单'
            ], 400);
        }

        $modelName = $request->input('model');
        /** @var Model $modelClass */
        $modelClass = "\\App\Models\\" . $modelName;
        
        try {
            $order = $modelClass::query()->findOrFail($orderId);

            // 生成Excel文件
            $excelPath = $this->excelService->fillTemplate($order);

            // 返回文件下载
            return response()->download($excelPath, '采购订单_' . $order->order_no . '.xlsx')->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            // 记录详细错误日志
            Log::error('打印订单失败', [
                'order_id' => $orderId,
                'model_name' => $modelName,
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '打印订单时发生错误: ' . $e->getMessage()
            ], 500);
        }
    }
}
