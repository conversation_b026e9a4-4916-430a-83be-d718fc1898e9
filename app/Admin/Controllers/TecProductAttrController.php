<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\TecProductAttrRepo;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class TecProductAttrController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecProductAttrRepo(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('product_id');
            $grid->column('attr_id');
            $grid->column('attr_value_ids');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecProductAttrRepo(), function (Show $show) {
            $show->field('id');
            $show->field('product_id');
            $show->field('attr_id');
            $show->field('attr_value_ids');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecProductAttrRepo(), function (Form $form) {
            $form->display('id');
            $form->text('product_id');
            $form->text('attr_id');
            $form->text('attr_value_ids');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
