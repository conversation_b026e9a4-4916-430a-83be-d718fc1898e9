<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use App\Models\TecTagModel;
use Illuminate\Http\Request;
use Dcat\Admin\Layout\Content;
use App\Models\TecSkuAttributeModel;
use App\Models\TecProductCategoryModel;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;

class TecSkuAttributeController extends AdminController
{
    private $attrType = [
        'checkbox' => '复选框',
        'radio' => '单选框',
    ];

    /**
     * 获取SKU属性接口
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSkuAttributes(Request $request)
    {
        // 获取前端传递的分类ID
        $categoryId = $request->input('category_id');

        // 根据分类ID获取相关的SKU属性
        $skuAttributes = TecSkuAttributeModel::where('product_category_id', $categoryId)->get();

        // 如果没有找到相关的SKU属性，则返回空数组
        if ($skuAttributes->isEmpty()) {
            return response()->json([]);
        }

        // 将SKU属性模型数据转换为数组格式
        $skuAttributesArray = $skuAttributes->toArray();

        // 收集所有的标签ID
        $tagIds = [];
        foreach ($skuAttributesArray as $attribute) {
            if (is_array($attribute['attr_value'])) {
                foreach ($attribute['attr_value'] as $valueObj) {
                    $tagIds[] = $valueObj['value'];
                }
            }
        }

        // 根据标签ID查询标签名称和背景颜色，并按ID索引
        $tags = TecTagModel::whereIn('id', $tagIds)->get()->keyBy('id')->toArray();

        // 替换attr_value中的value字段为标签名称，并包含background_color
        foreach ($skuAttributesArray as &$attribute) {
            if (is_array($attribute['attr_value'])) {
                foreach ($attribute['attr_value'] as &$valueObj) {
                    $tagId = $valueObj['value'];
                    if (isset($tags[$tagId])) {
                        $valueObj['value'] = $tags[$tagId]['tag'];
                        $valueObj['background_color'] = $tags[$tagId]['background_color'];
                    }
                }
            }
        }

        // 返回处理后的SKU属性数据
        return response()->json($skuAttributesArray);
    }

    /**
     * Index interface.
     *
     * @param  Content  $content
     *
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->title('属性列表')
            ->body(function (Row $row) {
                // 左侧占据一半宽度
                $row->column(6, function (Column $column) {
                    $column->append($this->grid());
                });

                // 右侧留空
                $row->column(6, function (Column $column) {
                    // 留空，不添加内容
                });
            });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecSkuAttributeModel(), function (Grid $grid) {
            $grid->model()->orderBy('id');
            $grid->id->sortable()->style('width: 100px; text-align: left;');

            // 显示产品分类并设置背景色
            $grid->column('product_category_id', '产品分类')->display(function ($productCategoryId) {
                $category = TecProductCategoryModel::find($productCategoryId);
                $title = $category->title ?? '-';
                $backgroundColor = $category->background_color ?? '';
                if ($backgroundColor) {
                    return "<span style='background-color: {$backgroundColor}; padding: 2px 4px; border-radius: 3px;'>{$title}</span>";
                }
                return $title;
            })->style('width: 200px; text-align: left;');

            $grid->column('attr_name', '属性名称');

            // 设置属性类型的标签样式，单选框为绿色背景，复选框为蓝色背景
            $grid->column('attr_type', '属性类型')
                ->using($this->attrType)
                ->label([
                    'checkbox' => 'primary', // 复选框为蓝色背景
                    'radio' => 'success'     // 单选框为绿色背景
                ]);

            $grid->column('sort', '排序')->help('排序越大越靠前');

            // 自定义显示属性值
            $grid->column('attr_value', '属性值')->display(function ($attrValue) {
                if (is_string($attrValue)) {
                    $attrValue = json_decode($attrValue, true);
                }

                if (is_array($attrValue)) {
                    $tags = TecTagModel::whereIn('id', array_column($attrValue, 'value'))
                        ->get()
                        ->keyBy('id')
                        ->toArray();

                    $displayValues = array_map(function ($value) use ($tags) {
                        $tagId = $value['value'];
                        $tagName = $tags[$tagId]['tag'] ?? $tagId;
                        $backgroundColor = $tags[$tagId]['background_color'] ?? '';
                        if ($backgroundColor) {
                            return "<span style='background-color: {$backgroundColor}; padding: 2px 4px; border-radius: 3px;'>{$tagName}</span>";
                        }
                        return $tagName;
                    }, $attrValue);

                    return implode(' ', $displayValues);
                }
                return $attrValue;
            });

            $grid->disableViewButton();
            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('product_category_id', '分类')->select(TecProductCategoryModel::all()->pluck('full_path', 'id'))->width(4);
            });
        });
    }

    /**
     * Edit interface.
     *
     * @param  mixed  $id
     * @param  Content  $content
     *
     * @return Content
     */
    public function edit($id, Content $content)
    {
        return $content
            ->title('编辑属性')
            ->body($this->form()->edit($id));
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecSkuAttributeModel(), function (Form $form) {
            $form->display('id');

            // 分类选择
            $form->select('product_category_id', '分类')->options(
                TecProductCategoryModel::all()->pluck('full_path', 'id')
            )->required();

            // 属性名称
            $form->text('attr_name', '属性名称')->required();

            // 属性类型
            $form->radio('attr_type', '属性类型')->options($this->attrType)->required();

            // 获取所有 t_tags 表中的标签数据
            $tagOptions = TecTagModel::all();

            // 使用 table 字段实现属性值的动态添加和选择
            // $form->table('attr_value', '属性值', function ($table) use ($tagOptions) {
            //     $options = [];
            //     foreach ($tagOptions as $tag) {
            //         $options[$tag->id] = $tag->tag;
            //     }
            //     $table->select('value', '选择值')->options($options)->default('');
            // });

            $form->table('attr_value', '属性值', function ($table) use ($tagOptions) {
                $options = [];
                foreach ($tagOptions as $tag) {
                    $options[$tag->id] = $tag->tag;
                }

                $table->select('value', '选择值')->options($options)->default('');
            });

            // 在下拉列表后面添加一个链接，点击时打开新的页面
            $form->html(function () {
                return <<<HTML
                    <div style="margin-top: 5px;">
                        <a href="javascript:void(0);" id="custom-link">属性追加画面</a>
                    </div>
                    <script>
                        document.getElementById('custom-link').addEventListener('click', function () {
                            window.open('/admin/r_tag', '_blank');
                        });
                    </script>
            HTML;
            });

            // 排序
            $form->number('sort', '排序')->default(0)->min(0)->max(100);

            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮
            // 隐藏查看按钮
            $form->tools(function (Form\Tools $tools) {
                $tools->disableView();
            });

            // 保存前处理数据格式
            $form->saving(function (Form $form) {
                if (is_string($form->attr_value)) {
                    $form->attr_value = json_decode($form->attr_value, true);
                }

                if (is_array($form->attr_value)) {
                    $form->attr_value = array_filter($form->attr_value, function ($value) {
                        return isset($value['value']);
                    });
                    $form->attr_value = array_values($form->attr_value);

                    $form->model()->attr_value = json_encode($form->attr_value);
                }
            });

            // 编辑时处理数据格式
            $form->editing(function (Form $form) {
                if (is_string($form->model()->attr_value)) {
                    $form->model()->attr_value = json_decode($form->model()->attr_value, true);
                }
            });
        });
    }



    /**
     * Create interface.
     *
     * @param  Content  $content
     *
     * @return Content
     */
    public function create(Content $content)
    {
        return $content
            ->title('添加属性')
            ->body($this->form());
    }
}
