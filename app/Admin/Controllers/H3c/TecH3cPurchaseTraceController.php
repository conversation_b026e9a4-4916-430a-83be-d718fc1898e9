<?php

namespace App\Admin\Controllers\H3c;

use App\Admin\Config\PurchaseConfig;
use App\Models\TecH3cPurchaseOrderItemModel;
use App\Models\TecH3cPurchaseOrderModel;
use App\Models\TecH3cProductListModel;
use App\Models\TecWarehouseLocationModel;
use App\Models\TecWarehouseModel;
use App\Models\TecH3cWarehouseEntryItemModel;
use App\Models\TecH3cWarehouseEntryModel;
use App\Models\TecH3cWarehouseInventoryModel;
use App\Models\TecH3cWarehouseTransactionModel;
use App\Models\TecH3cWarehouseExitItemModel;
use App\Models\TecH3cWarehouseExitSourceModel;
use App\Models\TecH3cWarehouseExitModel;
use App\Models\TecSndCompanyModel;
use App\Models\TecSndContactModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\DB;
use App\Models\TecProductUnitModel;

/**
 * H3C采购追溯控制器
 * 
 * 基于采购订单的追溯视图,可以显示采购订单-入库单-库存-出库单的完整链路
 */
class TecH3cPurchaseTraceController extends AdminController
{
    /**
     * 标题
     * 
     * @var string
     */
    protected $title = '采购订单追溯';

    /**
     * 获取产品属性对应的颜色
     *
     * @param string $zokusei 属性值
     * @return string 颜色名称
     */
    protected function getZokuseiColor($zokusei)
    {
        $colors = [
            'normal' => 'primary',
            'rr' => 'warning',
            'inspection' => 'info',
            'N' => 'success',
            'R' => 'danger',
            'I' => 'info'
        ];
        
        return $colors[$zokusei] ?? 'default';
    }

    /**
     * 首页
     * 
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header($this->title)
            ->description('从采购订单追溯库存和入出库记录')
            ->body($this->grid());
    }

    /**
     * 详情页
     * 
     * @param int $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content)
    {
        return $content
            ->header($this->title)
            ->description('详情')
            ->body($this->detail($id));
    }

    /**
     * 批量预加载数据以优化性能
     */
    private function preloadBulkData()
    {
        // 使用更长的缓存时间，减少数据库查询
        $warehouses = \Illuminate\Support\Facades\Cache::remember('bulk_warehouses_v2', 3600, function() {
            return TecWarehouseModel::select('id', 'name')->get()->keyBy('id');
        });
        
        $locations = \Illuminate\Support\Facades\Cache::remember('bulk_locations_v2', 3600, function() {
            return TecWarehouseLocationModel::select('id', 'area_number', 'shelf_number', 'level_number', 'warehouse_id')->get()->keyBy('id');
        });
        
        $units = \Illuminate\Support\Facades\Cache::remember('bulk_units_v2', 3600, function() {
            return TecProductUnitModel::select('id', 'unit_name')->get()->keyBy('id');
        });
        
        // 预加载产品信息
        $products = \Illuminate\Support\Facades\Cache::remember('bulk_products_v2', 3600, function() {
            return TecH3cProductListModel::select('id', 'product', 'product_bom', 'product_zokusei')->get()->keyBy('id');
        });
        
        // 预加载公司信息
        $companies = \Illuminate\Support\Facades\Cache::remember('bulk_companies_v2', 3600, function() {
            return TecSndCompanyModel::select('id', 'name', 'company_color', 'notes')->get()->keyBy('id');
        });
        
        // 将数据存储在静态变量中供后续使用
        app()->instance('bulk_warehouses', $warehouses);
        app()->instance('bulk_locations', $locations);
        app()->instance('bulk_units', $units);
        app()->instance('bulk_products', $products);
        app()->instance('bulk_companies', $companies);
        
        // 简化库存数据预加载，只在真正需要时加载
        $this->preloadInventoryData();
    }
    
    /**
     * 预加载库存数据
     */
    private function preloadInventoryData()
    {
        // 使用更高效的查询方式获取产品ID和货位ID
        $productLocationPairs = \Illuminate\Support\Facades\DB::table('t_warehouse_entry_items_h3c as ei')
            ->join('t_purchase_order_items_h3c as poi', 'ei.order_item_id', '=', 'poi.id')
            ->select('poi.product as product_id', 'ei.location_id')
            ->whereNotNull('poi.product')
            ->whereNotNull('ei.location_id')
            ->distinct()
            ->get()
            ->toArray();
        
        // 批量查询所有相关的库存信息
        if (!empty($productLocationPairs)) {
            $productIds = collect($productLocationPairs)->pluck('product_id')->unique();
            $locationIds = collect($productLocationPairs)->pluck('location_id')->unique();
            
            // 一次性查询所有可能的库存记录
            $allInventories = TecH3cWarehouseInventoryModel::select('id', 'product_id', 'location_id', 'warehouse_id', 'quantity', 'available_quantity', 'locked_quantity', 'status')
                ->whereIn('product_id', $productIds)
                ->whereIn('location_id', $locationIds)
                ->get();
            
            // 按产品ID和货位ID组合进行索引
            $inventoryIndex = [];
            foreach ($allInventories as $inventory) {
                $key = $inventory->product_id . '_' . $inventory->location_id;
                if (!isset($inventoryIndex[$key])) {
                    $inventoryIndex[$key] = collect();
                }
                $inventoryIndex[$key]->push($inventory);
            }
            
            app()->instance('bulk_inventories', $inventoryIndex);
        } else {
            app()->instance('bulk_inventories', []);
        }
    }

    /**
     * 优化查询，减少 N+1 问题
     */
    private function optimizeQueries()
    {
        // 预加载所有必要的数据到内存中
        $this->preloadAllNecessaryData();
        
        // 启用查询日志以便调试
        if (config('app.debug')) {
            \DB::enableQueryLog();
        }
    }
    
    /**
     * 预加载所有必要的数据
     */
    private function preloadAllNecessaryData()
    {
        // 获取当前分页的数据，避免加载所有数据
        $currentPage = request()->get('page', 1);
        $perPage = 20;
        
        // 预加载当前页面需要的产品信息
        $productIds = TecH3cPurchaseOrderItemModel::select('product')
            ->distinct()
            ->skip(($currentPage - 1) * $perPage)
            ->take($perPage)
            ->pluck('product')
            ->toArray();
            
        if (!empty($productIds)) {
            $products = \Illuminate\Support\Facades\Cache::remember('products_trace_' . md5(implode(',', $productIds)), 1800, function() use ($productIds) {
                return TecH3cProductListModel::select('id', 'product', 'product_bom', 'product_zokusei')
                    ->whereIn('id', $productIds)
                    ->get()
                    ->keyBy('id');
            });
            app()->instance('cached_products', $products);
        }
        
        // 预加载所有单位信息（数据量小，可以全部缓存）
        $units = \Illuminate\Support\Facades\Cache::remember('all_units_trace_v2', 3600, function() {
            return TecProductUnitModel::select('id', 'unit_name')->get()->keyBy('id');
        });
        
        // 预加载所有公司信息（数据量小，可以全部缓存）
        $companies = \Illuminate\Support\Facades\Cache::remember('all_companies_trace_v2', 3600, function() {
            return TecSndCompanyModel::select('id', 'name', 'company_color', 'notes')->get()->keyBy('id');
        });
        
        // 预加载所有联系人信息（数据量小，可以全部缓存）
        $contacts = \Illuminate\Support\Facades\Cache::remember('all_contacts_trace_v2', 3600, function() {
            return TecSndContactModel::select('id', 'first_name', 'last_name', 'phone', 'phone_landline', 'email')->get()->keyBy('id');
        });
        
        // 将数据存储到应用实例中
        app()->instance('cached_units', $units);
        app()->instance('cached_companies', $companies);
        app()->instance('cached_contacts', $contacts);
    }

    /**
     * 表格
     * 
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecH3cPurchaseOrderItemModel(), function (Grid $grid) {
            // 性能监控开始
            $startTime = microtime(true);
            
            // 添加查询优化
            $this->optimizeQueries();
            
            // 只在需要时预加载批量数据
            $this->preloadBulkData();
            
            // 使用强力的 N+1 查询解决方案
            $this->eliminateNPlusOneQueries($grid);
            
            $grid->scrollbarX();
            
            // 优化关联加载，预加载所有需要的关系以避免N+1查询
            $grid->model()->with([
                'order:id,order_no,case_name,business_date,incoterms,sndCompany_id,contact_person',
                'order.supplier:id,name,company_color,notes',
                'order.contact:id,first_name,last_name,phone,phone_landline,email',
                'order.items:id,order_id,product,quantity,unit',
                'order.items.productlist:id,product,product_bom,product_zokusei',
                'order.items.unitInfo:id,unit_name',
                'productlist:id,product,product_bom,product_zokusei',
                'unitInfo:id,unit_name',
                'entryItems:id,order_item_id,actual_quantity,location_id,entry_id',
                'entryItems.entry:id,entry_no,entry_date,warehouse_id',
                'entryItems.entry.warehouse:id,name',
                'entryItems.location:id,area_number,shelf_number,level_number,warehouse_id',
                'exitSources:id,purchase_order_item_id,quantity,exit_item_id',
                'exitSources.exitItem:id,exit_id',
                'exitSources.exitItem.exit:id,exit_no,exit_date,exit_type'
            ])->orderBy('order_id', 'asc');
            
            // 设置分页，避免一次性加载太多数据
            $grid->paginate(20);
            
            // 采购单信息
            $grid->column('order.order_no', '采购单号')->display(function($orderNo) {
                $order = $this->order;
                
                if (!$order) return '-';
                
                $html = sprintf(
                    "<a href='/admin/r_h3c_purchase_orders/type/all?order_no=%s' target='_blank'>%s</a><br>项目: %s<br>日期: %s<br>贸易条件: %s",
                    $order->order_no ?? '-',
                    $order->order_no ?? '-',
                    $order->case_name ?? '-',
                    $order->business_date ? date('Y-m-d', strtotime($order->business_date)) : '-',
                    $order->incoterms ?? '-'
                );
                
                return $html;
            });
            
            // 供应商信息 - 优化版本，使用预加载的数据和公司颜色
            $grid->column('order.supplier.name', '供应商')->display(function($name) {
                $order = $this->order;
                
                if (!$order) {
                    return '-';
                }
                
                try {
                    $supplierCompany = $order->supplier;
                    $contact = $order->contact;
                    
                    if ($supplierCompany) {
                        $companyName = $supplierCompany->name ?? '-';
                        
                        // 添加备注信息到公司名称
                        if (!empty($supplierCompany->notes)) {
                            $companyName .= " ({$supplierCompany->notes})";
                        }
                        
                        $companyColor = $supplierCompany->company_color ?? '#3498db';
                        
                        $contactName = '';
                        $contactPhone = '';
                        $contactEmail = '';
                        
                        if ($contact) {
                            $contactName = $contact->last_name && $contact->first_name ? 
                                $contact->last_name . ' ' . $contact->first_name : 
                                ($contact->last_name ?: $contact->first_name ?: $contact->contact_name ?: '-');
                            
                            $contactPhone = $contact->phone ?: $contact->phone_landline ?: '-';
                            $contactEmail = $contact->email ?: '-';
                        }
                        
                        return sprintf(
                            "<strong style='color: %s; border-left: 4px solid %s; padding-left: 8px;'>%s</strong>%s%s%s",
                            $companyColor,
                            $companyColor,
                            $companyName,
                            !empty($contactName) ? "<br>联系人: $contactName" : "",
                            !empty($contactPhone) ? "<br>电话: $contactPhone" : "",
                            !empty($contactEmail) ? "<br>邮箱: $contactEmail" : ""
                        );
                    } else if (!empty($order->snd_company)) {
                        // 如果没有找到供应商记录，直接使用订单上的供应商字段
                        return "<strong>" . $order->snd_company . "</strong>";
                    }
                    
                    return '-';
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('供应商信息显示错误: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                    return '供应商信息加载失败';
                }
            });
            
            // 产品信息 - 优化版本，使用预加载的数据
            $grid->column('product_info', '产品信息')->display(function() {
                try {
                    $product = $this->productlist;
                    
                    if (!$product) {
                        return '<span class="badge badge-secondary">无产品信息</span>';
                    }
                    
                    $html = '';
                    $productName = $product->product ?? '';
                    $productBom = $product->product_bom ?? '';
                    
                    if (!empty($productName)) {
                        $html .= $productName;
                    }
                    
                    if (!empty($productBom)) {
                        $html .= '<strong><br><br>BOM: ' . $productBom . '</strong>';
                    }
                    
                    return $html;
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('产品信息显示错误: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                    return '产品信息加载失败: ' . $e->getMessage();
                }
            });
            
            // 产品属性 - 优化版本，使用预加载的数据
            $grid->column('product_attribute', '产品属性')->display(function() {
                try {
                    $product = $this->productlist;
                    
                    if (!$product) {
                        return '-';
                    }
                    
                    $zokusei = $product->product_zokusei ?? '';
                    if (empty($zokusei)) {
                        return '-';
                    }
                    
                    // 使用产品属性映射
                    $zokuseiMap = \App\Admin\Config\PurchaseConfig::getProductZokusei();
                    $zokuseiText = $zokuseiMap[$zokusei] ?? $zokusei;
                    
                    // 获取颜色类
                    $colorMap = [
                        'normal' => 'secondary',
                        'rr' => 'info',
                        'inspection' => 'warning',
                        'N' => 'success',
                        'R' => 'warning',
                        'I' => 'danger'
                    ];
                    $colorClass = isset($colorMap[$zokusei]) ? $colorMap[$zokusei] : 'secondary';
                    
                    return "<span class='badge badge-{$colorClass}'>{$zokuseiText}</span>";
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('产品属性显示错误: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                    return '-';
                }
            });
            
            // 订单数量
            $grid->column('quantity', '订单数量');
            
            // 入库信息 - 优化版本，使用预加载的数据
            $grid->column('entry_info', '入库单号')->display(function() {
                $entryItems = $this->entryItems;
                
                if ($entryItems->isEmpty()) {
                    return '<span class="badge badge-warning">未入库</span>';
                }
                
                $data = [];
                
                foreach ($entryItems as $item) {
                    $entry = $item->entry;
                    if (!$entry) continue;
                    
                    $data[] = sprintf(
                        "<a href='/admin/r_warehouse_entries_h3c?entry_no=%s' target='_blank'>%s</a><br>日期: %s<br>数量: %s<br>",
                        $entry->entry_no ?? '-',
                        $entry->entry_no ?? '-',
                        $entry->entry_date ? date('Y-m-d', strtotime($entry->entry_date)) : '-',
                        $item->actual_quantity ?? 0,
                        $entry->entry_type_name ?? $entry->entry_type ?? '-'
                    );
                }
                
                if (empty($data)) {
                    return '<span class="badge badge-warning">未入库</span>';
                }
                
                $totalEntryQuantity = $entryItems->sum('actual_quantity');
                $orderQuantity = $this->quantity ?? 0;
                
                $status = $totalEntryQuantity >= $orderQuantity 
                    ? '<span class="badge badge-success">已全部入库</span>' 
                    : sprintf('<span class="badge badge-warning">已入库 %s/%s</span>', $totalEntryQuantity, $orderQuantity);
                
                return $status . '<br><br>' . implode('<hr>', $data);
            });
            
            // 库存状态 - 终极优化版本，使用预加载的批量库存数据
            $grid->column('inventory_status', '库存状态')->display(function() {
                try {
                    $productId = $this->product;
                    $entryItems = $this->entryItems;
                    
                    if (empty($productId)) {
                        return '<span class="badge badge-secondary">无产品ID</span>';
                    }
                    
                    if ($entryItems->isEmpty()) {
                        return '<span class="badge badge-secondary">无入库记录</span>';
                    }
                    
                    // 获取所有相关的货位ID
                    $locationIds = $entryItems->pluck('location_id')->filter()->unique();
                    
                    if ($locationIds->isEmpty()) {
                        return '<span class="badge badge-warning">无货位信息</span>';
                    }
                    
                    // 从预加载的批量库存数据中获取库存信息
                    $bulkInventories = app('bulk_inventories');
                    $inventories = collect();
                    
                    foreach ($locationIds as $locationId) {
                        $key = $productId . '_' . $locationId;
                        if (isset($bulkInventories[$key])) {
                            $inventories = $inventories->merge($bulkInventories[$key]);
                        }
                    }
                    
                    if ($inventories->isEmpty()) {
                        return '<span class="badge badge-warning">有入库记录但无关联库存</span>';
                    }
                    
                    $totalQuantity = $inventories->sum('quantity');
                    $totalAvailable = $inventories->sum('available_quantity');
                    $totalLocked = $inventories->sum('locked_quantity');
                    
                    // 生成汇总显示
                    $summary = [
                        sprintf('<span class="badge badge-primary">总库存: %s</span>', $totalQuantity),
                        sprintf('<span class="badge badge-success">可用: %s</span>', $totalAvailable),
                        sprintf('<span class="badge badge-warning">锁定: %s</span>', $totalLocked),
                    ];
                    
                    // 获取预加载的批量数据
                    $warehouses = app('bulk_warehouses');
                    $locations = app('bulk_locations');
                    
                    // 生成详细信息
                    $details = [];
                    foreach ($inventories as $inventory) {
                        // 从预加载的批量数据获取仓库名称
                        $warehouse = $warehouses->get($inventory->warehouse_id);
                        $warehouseName = $warehouse ? ($warehouse->name ?? '-') : '-';
                        
                        // 从预加载的批量数据获取货位名称
                        $location = $locations->get($inventory->location_id);
                        $locationName = $location ? sprintf('%s区%s架%s层', $location->area_number, $location->shelf_number, $location->level_number) : '-';
                        
                        // 获取库存状态
                        $status = $inventory->status ?? 'unknown';
                        $statusClass = 'primary';
                        
                        switch($status) {
                            case 'normal':
                                $statusLabel = '正常';
                                $statusClass = 'success';
                                break;
                            case 'locked':
                                $statusLabel = '锁定';
                                $statusClass = 'warning';
                                break;
                            case 'reserved':
                                $statusLabel = '预留';
                                $statusClass = 'info';
                                break;
                            case 'pending':
                                $statusLabel = '待处理';
                                $statusClass = 'secondary';
                                break;
                            default:
                                $statusLabel = $status;
                                $statusClass = 'default';
                        }
                        
                        $details[] = sprintf(
                            "仓库: %s<br>货位: %s<br>状态: <span class='badge badge-%s'>%s</span>",
                            $warehouseName,
                            $locationName,
                            $statusClass,
                            $statusLabel
                        );
                    }
                    
                    return implode(' ', $summary) . '<br><hr>' . implode('<hr>', $details);
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('库存状态显示错误: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                    return '库存状态加载失败';
                }
            });
            
            // 出库信息 - 优化版本，使用预加载的数据
            $grid->column('exit_info', '出库信息')->display(function() {
                $exitSources = $this->exitSources;
                
                if ($exitSources->isEmpty()) {
                    return '<span class="badge badge-info">未出库</span>';
                }
                
                $totalExitQuantity = $exitSources->sum('quantity');
                $data = [];
                
                foreach ($exitSources as $source) {
                    $exitItem = $source->exitItem;
                    if (!$exitItem || !$exitItem->exit) continue;
                    
                    $exit = $exitItem->exit;
                    
                    $data[] = sprintf(
                        "<a href='/admin/r_h3c_warehouse_exit?exit_no=%s' target='_blank'>%s</a><br>出库日期: %s<br>出库数量: %d<br>出库类型: %s",
                        $exit->exit_no ?? '-',
                        $exit->exit_no ?? '-',
                        $exit->exit_date ? date('Y-m-d', strtotime($exit->exit_date)) : '-',
                        (int)$source->quantity,
                        $exit->exit_type_name ?? $exit->exit_type ?? '-'
                    );
                }
                
                if (empty($data)) {
                    return '<span class="badge badge-info">未出库</span>';
                }
                
                $status = sprintf('<span class="badge badge-success">已出库 %d</span>', (int)$totalExitQuantity);
                
                return $status . '<br><br>' . implode('<hr>', $data);
            });
            
            // 筛选条件
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->expand();
                
                // 采购单信息筛选
                $filter->where('采购单号', function ($query, $value) {
                    $query->whereHas('order', function ($q) use ($value) {
                        $q->where('order_no', 'like', "%{$value}%");
                    });
                }, '采购单号')->width(2);
                
                $filter->where('项目名称', function ($query, $value) {
                    $query->whereHas('order', function ($q) use ($value) {
                        $q->where('case_name', 'like', "%{$value}%");
                    });
                }, '项目名称')->width(2);
                
                // 供应商筛选 - 改为下拉列表，使用缓存避免重复查询
                $filter->equal('order.sndCompany_id', '供应商')->select(
                    \Illuminate\Support\Facades\Cache::remember('supplier_companies_list', 3600, function() {
                        return \App\Models\TecSndCompanyModel::where('snd_role', 'supplier')
                            ->select('id', 'name', 'notes')
                            ->get()
                            ->mapWithKeys(function($company) {
                                $displayName = $company->name;
                                if (!empty($company->notes)) {
                                    $displayName .= " ({$company->notes})";
                                }
                                return [$company->id => $displayName];
                            })
                            ->toArray();
                    })
                )->width(4);
                
                // 产品信息筛选
                $filter->where('产品名称', function ($query, $value) {
                    $query->whereHas('productlist', function ($q) use ($value) {
                        $q->where('product', 'like', "%{$value}%");
                    });
                }, '产品名称')->width(2);
                
                $filter->where('产品BOM', function ($query, $value) {
                    $query->whereHas('productlist', function ($q) use ($value) {
                        $q->where('product_bom', 'like', "%{$value}%");
                    });
                }, '产品BOM')->width(2);
                
                // 入库状态筛选
                $filter->where('入库状态', function ($query, $value) {
                    if ($value == 'not_stored') {
                        // 未入库
                        $query->whereDoesntHave('entryItems');
                    } elseif ($value == 'partial_stored') {
                        // 部分入库
                        $query->whereHas('entryItems')
                            ->whereRaw('(SELECT SUM(actual_quantity) FROM t_warehouse_entry_items_h3c WHERE t_warehouse_entry_items_h3c.order_item_id = t_purchase_order_items_h3c.id) < t_purchase_order_items_h3c.quantity');
                    } elseif ($value == 'fully_stored') {
                        // 完全入库
                        $query->whereHas('entryItems')
                            ->whereRaw('(SELECT SUM(actual_quantity) FROM t_warehouse_entry_items_h3c WHERE t_warehouse_entry_items_h3c.order_item_id = t_purchase_order_items_h3c.id) >= t_purchase_order_items_h3c.quantity');
                    }
                }, '入库状态')->select([
                    'not_stored' => '未入库',
                    'partial_stored' => '部分入库',
                    'fully_stored' => '完全入库'
                ])->width(2);
                
                // 出库状态筛选
                $filter->where('出库状态', function ($query, $value) {
                    if ($value == 'not_exited') {
                        // 未出库
                        $query->whereDoesntHave('exitSources');
                    } elseif ($value == 'exited') {
                        // 已出库
                        $query->whereHas('exitSources');
                    }
                }, '出库状态')->select([
                    'not_exited' => '未出库',
                    'exited' => '已出库'
                ])->width(2);
            });
            
            // 表格设置
            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->disableBatchActions();
            $grid->paginate(20);
            $grid->withBorder();
            
            // 性能监控结束
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime) * 1000; // 转换为毫秒
            \Illuminate\Support\Facades\Log::info('采购跟踪页面性能监控', [
                'execution_time_ms' => round($executionTime, 2),
                'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
                'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2)
            ]);
        });
    }
    
    /**
     * 彻底消除 N+1 查询问题
     */
    private function eliminateNPlusOneQueries($grid)
    {
        // 获取当前页面的所有数据ID
        $currentPage = request()->get('page', 1);
        $perPage = 20;
        
        // 预先获取当前页面所有相关的ID
        $orderItemIds = TecH3cPurchaseOrderItemModel::select('id', 'order_id', 'product', 'unit')
            ->skip(($currentPage - 1) * $perPage)
            ->take($perPage)
            ->get();
            
        if ($orderItemIds->isEmpty()) {
            return;
        }
        
        $orderIds = $orderItemIds->pluck('order_id')->unique()->toArray();
        $productIds = $orderItemIds->pluck('product')->unique()->toArray();
        $unitIds = $orderItemIds->pluck('unit')->unique()->toArray();
        
        // 一次性预加载所有订单数据
        $orders = \Illuminate\Support\Facades\Cache::remember('orders_batch_' . md5(implode(',', $orderIds)), 600, function() use ($orderIds) {
            return TecH3cPurchaseOrderModel::select('id', 'order_no', 'case_name', 'business_date', 'incoterms', 'sndCompany_id', 'contact_person')
                ->with([
                    'supplier:id,name,company_color,notes',
                    'contact:id,first_name,last_name,phone,phone_landline,email'
                ])
                ->whereIn('id', $orderIds)
                ->get()
                ->keyBy('id');
        });
        
        // 一次性预加载所有产品数据
        $products = \Illuminate\Support\Facades\Cache::remember('products_batch_' . md5(implode(',', $productIds)), 600, function() use ($productIds) {
            return TecH3cProductListModel::select('id', 'product', 'product_bom', 'product_zokusei')
                ->whereIn('id', $productIds)
                ->get()
                ->keyBy('id');
        });
        
        // 一次性预加载所有单位数据
        $units = \Illuminate\Support\Facades\Cache::remember('units_batch_' . md5(implode(',', $unitIds)), 600, function() use ($unitIds) {
            return TecProductUnitModel::select('id', 'unit_name')
                ->whereIn('id', $unitIds)
                ->get()
                ->keyBy('id');
        });
        
        // 将预加载的数据存储到应用实例中
        app()->instance('preloaded_orders', $orders);
        app()->instance('preloaded_products', $products);
        app()->instance('preloaded_units', $units);
    }

    /**
     * 详情
     * 
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecH3cPurchaseOrderItemModel(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('order.purchase_order_no', '采购单号');
            $show->field('order.case_name', '项目名称');
            $show->field('order.snd_company', '供应商');
            $show->field('order.business_date', '业务日期');
            $show->field('productlist.product', '产品名称');
            $show->field('productlist.product_bom', '产品BOM');
            $show->field('quantity', '订单数量');
            
            $show->panel()->tools(function (Show\Tools $tools) {
                $tools->disableEdit();
                $tools->disableDelete();
            });
        });
    }
} 
