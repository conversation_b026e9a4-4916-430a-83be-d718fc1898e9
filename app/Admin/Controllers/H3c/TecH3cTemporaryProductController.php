<?php

declare(strict_types=1);

namespace App\Admin\Controllers\H3c;

use App\Models\TecH3cCustomerTemporaryProductModel;
use App\Models\TecH3cProductCategoryModel;
use App\Models\TecH3cProductListModel;
use App\Models\TecProductUnitModel;
use App\Admin\Actions\H3c\TecH3cConvertToFormalProductAction;
use App\Admin\Actions\H3c\TecH3cPromoteTempProductBatchAction;
use App\Admin\Actions\H3c\TecH3cCleanupTempProductBatchAction;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Admin;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * 临时商品管理控制器
 */
class TecH3cTemporaryProductController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '临时商品管理';

    /**
     * 获取表格
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecH3cCustomerTemporaryProductModel(), function (Grid $grid) {
            $grid->model()->orderBy('last_used_at', 'desc');


            
            $grid->column('id', 'ID')->sortable();
            
            $grid->column('product_name', '商品名称')
                ->editable();
                
            $grid->column('product_model', '型号')
                ->editable();
                
            $grid->column('product_spec', '规格')
                ->editable()
                ->width('120px'); // 缩小规格列宽度

            $grid->column('product_unit', '单位')
                ->display(function ($value) {
                    if (!$value) return '-';

                    // 查找单位名称
                    $unit = \App\Models\TecProductUnitModel::find($value);
                    return $unit ? $unit->unit_name : $value;
                })
                ->width('80px'); // 设置单位列宽度

            $grid->column('unit_price', '单价')
                ->display(function ($value) {
                    return $value ? '¥' . number_format((int)$value, 0) : '-';
                })
                ->editable()
                ->width('100px');

            $grid->column('remark', '备注')
                ->editable()
                ->width('150px');

            $grid->column('category_id', '产品分类')
                ->display(function ($value) {
                    if (!$value) return '<span class="text-muted">未分类</span>';

                    $category = TecH3cProductCategoryModel::find($value);
                    return $category ? $category->title : '分类不存在';
                })
                ->width('120px');

            $grid->column('source_type', '来源类型')
                ->display(function ($value) {
                    $labels = [
                        'quote' => '报价单',
                        'order' => '订单',
                        'manual' => '手动创建'
                    ];
                    return $labels[$value] ?? $value;
                });
                
            $grid->column('source_id', '来源案件')
                ->display(function ($value) {
                    if (!$value || $this->source_type === 'manual') {
                        return '-';
                    }

                    $caseName = '';
                    $sourceUrl = '';

                    if ($this->source_type === 'quote') {
                        $quote = \App\Models\TecH3cCustomerQuoteModel::find($value);
                        $caseName = $quote ? $quote->case_name : "报价单 #{$value}";
                        // 使用搜索URL而不是详情URL
                        $sourceUrl = admin_url('r_h3c_customer_quotes?case_name=' . urlencode($caseName));
                    } elseif ($this->source_type === 'order') {
                        $order = \App\Models\TecH3cCustomerOrderModel::find($value);
                        $caseName = $order ? $order->case_name : "订单 #{$value}";
                        // 使用搜索URL而不是详情URL
                        $sourceUrl = admin_url('r_h3c_customer_orders?case_name=' . urlencode($caseName));
                    }

                    return $sourceUrl ? "<a href='{$sourceUrl}' target='_blank'>{$caseName}</a>" : $caseName;
                });
                
            $grid->column('usage_count', '使用次数')
                ->sortable();
                
            $grid->column('is_reused', '已复用')
                ->switch();
                
            $grid->column('formal_product_id', '关联正式产品')
                ->display(function ($value) {
                    if (!$value) return '-';

                    $product = TecH3cProductListModel::find($value);
                    if (!$product) return $value;

                    // 使用搜索URL而不是详情URL
                    $searchUrl = admin_url('r_h3c_product_lists?product=' . urlencode($product->product));

                    return "<a href='{$searchUrl}' target='_blank'>{$product->product}</a>";
                });
                
            $grid->column('last_used_at', '最后使用时间')
                ->display(function ($value) {
                    return $value ? $value->format('Y-m-d') : '-';
                })
                ->sortable();

            $grid->column('created_at', '创建时间')
                ->display(function ($value) {
                    return $value ? $value->format('Y-m-d') : '-';
                })
                ->sortable();
                
            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                // 只保留清理未使用的批量操作
                $batch->add(new TecH3cCleanupTempProductBatchAction());
            });
            
            // 行操作
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $tempProduct = $actions->row;

                // 确保删除按钮始终显示
                $actions->disableDelete(false); // 明确启用删除按钮

                // 只有未被重用且没有关联正式产品的才能转换
                // 使用更严格的条件判断
                if (!$tempProduct->is_reused &&
                    (is_null($tempProduct->formal_product_id) || $tempProduct->formal_product_id == 0 || $tempProduct->formal_product_id === '')) {
                    $actions->append(new TecH3cConvertToFormalProductAction());
                }
            });
            
            // 过滤器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->expand();
                
                $filter->like('product_name', '商品名称')->width(2);
                $filter->like('product_model', '型号')->width(2);
                $filter->equal('source_type', '来源类型')->select([
                    'quote' => '报价单',
                    'order' => '订单',
                    'manual' => '手动创建'
                ])->width(2);
                $filter->equal('is_reused', '是否已复用')->select([
                    0 => '否',
                    1 => '是'
                ])->width(2);
                $filter->gt('usage_count', '最小使用次数')->width(2);
                $filter->between('last_used_at', '最后使用时间')->date()->width(3);
                $filter->between('created_at', '创建时间')->date()->width(3);
            });
            
            // 工具栏
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<a href="' . admin_url('r_h3c_temporary_products/cleanup') . '" class="btn btn-danger">清理长期未使用商品</a>');
            });
        });
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @return Content
     */
    public function detail($id)
    {
        return Content::make()
            ->title('临时商品详情')
            ->description('查看临时商品详细信息')
            ->body($this->form()->edit($id));
    }

    /**
     * 编辑表单
     *
     * @param mixed $id
     * @return Form
     */
    protected function form()
    {
        // 预先获取主分类选项，避免N+1查询
        $majorCategories = \App\Models\TecProductMajorCategoryModel::pluck('major_category', 'id');
        $defaultMajorCategoryId = $majorCategories->keys()->first() ?? 1;

        // 预先获取产品分类选项（参照采购订单的实现）
        $productCategories = TecH3cProductListModel::with('category')
            ->select('category_id')
            ->distinct()
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->category_id,
                    'title' => $item->category->title ?? ''
                ];
            })
            ->pluck('title', 'id')
            ->filter()
            ->toArray();

        return Form::make(new TecH3cCustomerTemporaryProductModel(), function (Form $form) use ($majorCategories, $defaultMajorCategoryId, $productCategories) {
            $form->display('id', 'ID');

            // 基本信息
            $form->divider('基本信息');

            // 第一行：商品名称和BOM
            $form->row(function (Form\Row $row) {
                $row->width(6)->text('product_name', '商品名称')->required();
                $row->width(6)->text('product_model', 'BOM');
            });

            // 第二行：产品描述和单位
            $form->row(function (Form\Row $row) {
                $row->width(6)->text('product_spec', '产品描述');
                $row->width(6)->select('product_unit', '单位')
                    ->options(TecProductUnitModel::getUnitOptions())
                    ->default(1); // 默认选择第一个单位（通常是"台"）
            });

            // 第三行：单价和备注
            $form->row(function (Form\Row $row) {
                $row->width(6)->currency('unit_price', '单价')
                    ->symbol('¥')
                    ->digits(0);
                $row->width(6)->text('remark', '备注');
            });

            // 隐藏字段设置默认值
            $form->hidden('is_reused')->default(0);
            $form->hidden('usage_count')->default(0);  // 手动创建时使用次数为0
            $form->hidden('source_type')->default('manual');
            $form->hidden('source_id')->default(0);

            // 表单保存前处理
            $form->saving(function (Form $form) {
                if (is_null($form->is_reused)) {
                    $form->is_reused = 0;
                }
                if (is_null($form->usage_count)) {
                    $form->usage_count = 0;  // 手动创建时使用次数为0
                }
                if (empty($form->source_type)) {
                    $form->source_type = 'manual';
                }
                if (is_null($form->source_id)) {
                    $form->source_id = 0;
                }
            });

            // 第三行：单价
            $form->currency('unit_price', '单价')->symbol('¥');

            // 第四行：分类信息（转正式商品用）
            $form->divider('分类信息（转正式商品时使用）');

            $form->row(function (Form\Row $row) use ($majorCategories, $defaultMajorCategoryId, $productCategories) {
                $row->width(6)->select('major_category_id', '主分类')
                    ->options($majorCategories)
                    ->default($defaultMajorCategoryId)
                    ->script("
                        $(document).ready(function() {
                            var majorCategorySelect = $('select[name=\"major_category_id\"]');
                            if (!majorCategorySelect.val() || majorCategorySelect.val() === '') {
                                majorCategorySelect.val('{$defaultMajorCategoryId}').trigger('change');
                            }
                        });
                    ")
                    ->help('选择主分类便于后续转为正式商品');

                $row->width(6)->select('category_id', '产品分类')
                    ->options($productCategories)
                    ->help('选择分类便于后续转为正式商品');
            });

            $form->textarea('description', '详细描述')->rows(4);

            // 根据创建/编辑模式显示不同字段
            if ($form->isCreating()) {
                // 新增模式：自动设置为手动创建
                $form->hidden('source_type')->default('manual');
                $form->hidden('source_id')->default(0);
                $form->hidden('usage_count')->default(0);  // 手动创建时使用次数为0
                $form->hidden('is_reused')->default(false);

            } else {
                // 编辑模式：显示只读的来源信息
                $form->divider('来源信息');

                // 来源类型
                $form->display('source_type', '来源类型')
                    ->with(function ($value) {
                        $labels = [
                            'quote' => '<span class="badge badge-primary">报价单</span>',
                            'order' => '<span class="badge badge-success">订单</span>',
                            'manual' => '<span class="badge badge-secondary">手动创建</span>'
                        ];
                        return $labels[$value] ?? $value;
                    });

                // 来源ID
                $form->display('source_id', '来源ID')
                    ->with(function ($value, $model) {
                        if (!$value) return '<span class="text-muted">无</span>';

                        $url = '';
                        if ($model && $model->source_type === 'quote') {
                            $url = admin_url('r_h3c_customer_quotes/' . $value);
                        } elseif ($model && $model->source_type === 'order') {
                            $url = admin_url('r_h3c_customer_orders/' . $value);
                        }

                        return $url ? "<a href='{$url}' target='_blank' class='badge badge-info'>#{$value}</a>" :
                                      "<span class='badge badge-info'>#{$value}</span>";
                    });

                // 使用次数
                $form->display('usage_count', '使用次数')
                    ->with(function ($value) {
                        $color = $value > 1 ? 'success' : 'secondary';
                        return "<span class='badge badge-{$color}'>{$value} 次</span>";
                    });
            }

            // 状态和时间信息（仅编辑模式显示）
            if ($form->isEditing()) {
                $form->divider('状态信息');

                $form->row(function (Form\Row $row) {
                    $row->width(6)->switch('is_reused', '已复用')->disable();

                    $row->width(6)->display('formal_product_id', '关联正式产品')
                        ->with(function ($value) {
                            if (!$value) return '<span class="text-muted">未关联</span>';

                            $product = TecH3cProductListModel::find($value);
                            $productUrl = admin_url('r_h3c_product_lists/' . $value);

                            return $product ?
                                "<a href='{$productUrl}' target='_blank' class='badge badge-success'>{$product->product}</a>" :
                                "<span class='badge badge-warning'>产品#{$value}</span>";
                        });
                });

                // 时间信息
                $form->divider('时间信息');

                $form->row(function (Form\Row $row) {
                    $row->width(4)->display('last_used_at', '最后使用时间');
                    $row->width(4)->display('created_at', '创建时间');
                    $row->width(4)->display('updated_at', '更新时间');
                });
            }

            // 添加说明信息
            if ($form->isEditing()) {
                $model = $form->model();
                if ($model->is_reused) {
                    $form->html('<div class="alert alert-info">
                        <h5><i class="icon fas fa-info-circle"></i> 只读模式</h5>
                        该临时商品已被升级为正式产品，为保证数据完整性，不允许修改。
                        <br>如需修改，请直接编辑关联的正式产品。
                    </div>');
                } else {
                    $form->html('<div class="alert alert-warning">
                        <h5><i class="icon fas fa-exclamation-triangle"></i> 注意事项</h5>
                        • 临时商品用于存储客户报价单中的自定义商品信息<br>
                        • 可以通过"升级为正式产品"功能将其添加到产品库<br>
                        • 升级后的临时商品将变为只读状态
                    </div>');
                }
            } else {
                $form->html('<div class="alert alert-success">
                    <h5><i class="icon fas fa-plus-circle"></i> 创建临时商品</h5>
                    临时商品通常由报价单自动创建，也可以手动添加。
                    <br>创建后可以在需要时升级为正式产品。
                </div>');
            }

            // 允许删除临时商品（报价单项目表已存储完整信息）

            // 如果已经复用，则禁用编辑功能但保留删除功能
            if ($form->isEditing() && $form->model()->is_reused) {
                $form->disableSubmitButton();
                $form->disableResetButton();
                // 移除 readonly() 以保留删除按钮
                $form->disable(['product_name', 'product_model', 'product_spec', 'product_unit', 'unit_price', 'remark', 'major_category_id', 'category_id', 'description']);
            }
        });
    }

    /**
     * 升级为正式产品表单
     *
     * @param Request $request
     * @param Content $content
     * @return Content
     */
    public function promote($id, Content $content)
    {
        $tempProduct = TecH3cCustomerTemporaryProductModel::findOrFail($id);
        
        return $content
            ->title('升级临时商品')
            ->description('将临时商品升级为正式产品')
            ->body(Form::make(new TecH3cCustomerTemporaryProductModel(), function (Form $form) use ($tempProduct) {
                $form->hidden('id')->value($tempProduct->id);
                
                $form->display('product_name', '商品名称')
                    ->default($tempProduct->product_name);
                    
                $form->display('product_model', '型号')
                    ->default($tempProduct->product_model);
                    
                $form->display('product_spec', '规格')
                    ->default($tempProduct->product_spec);
                    
                $form->display('product_unit', '单位')
                    ->default($tempProduct->product_unit);
                    
                                 $form->select('category_id', '产品分类')
                    ->options(TecH3cProductCategoryModel::pluck('title', 'id'))
                    ->required();
                    
                // 禁用默认操作按钮
                $form->disableDeleteButton();
                $form->disableViewButton();
                $form->disableEditButton();
                
                // 处理表单提交
                $form->handle(function (Form $form) {
                    $id = $form->input('id');
                    $categoryId = $form->input('category_id');
                    
                    DB::beginTransaction();
                    
                    try {
                        $tempProduct = TecH3cCustomerTemporaryProductModel::findOrFail($id);
                        
                        // 创建正式产品
                        $product = new TecH3cProductListModel();
                        $product->product = $tempProduct->product_name;
                        $product->product_bom = $tempProduct->product_model ?? '-';
                        $product->spec = $tempProduct->product_spec;
                        $product->category_id = $categoryId;
                        $product->major_category_id = 1; // 默认主分类ID，需要根据实际情况调整
                        $product->product_bom = '-';
                        $product->product_zokusei = '常规';
                        $product->incoterms = 'CIP'; // 默认贸易条件
                        $product->save();
                        
                        // 更新临时商品状态
                        $tempProduct->is_reused = true;
                        $tempProduct->formal_product_id = $product->id;
                        $tempProduct->save();
                        
                        DB::commit();
                        
                        return $form->response()
                            ->success('临时商品已成功升级为正式产品')
                            ->redirect('r_h3c_temporary_products');
                    } catch (\Exception $e) {
                        DB::rollback();
                        return $form->response()->error('升级失败: ' . $e->getMessage());
                    }
                });
            }));
    }

    /**
     * 批量升级为正式产品表单
     *
     * @param Request $request
     * @param Content $content
     * @return Content
     */
    public function promoteBatch(Request $request, Content $content)
    {
        $ids = explode(',', $request->get('ids', ''));
        
        if (empty($ids)) {
            admin_toastr('未选择临时商品', 'error');
            return redirect(admin_url('r_h3c_temporary_products'));
        }
        
        $tempProducts = TecH3cCustomerTemporaryProductModel::whereIn('id', $ids)
            ->where('is_reused', false)
            ->get();
            
        if ($tempProducts->isEmpty()) {
            admin_toastr('没有可升级的临时商品', 'error');
            return redirect(admin_url('r_h3c_temporary_products'));
        }
        
        return $content
            ->title('批量升级临时商品')
            ->description('将多个临时商品升级为正式产品')
            ->body(view('admin.h3c.tec_h3c_temporary_product_batch_promote', [
                'tempProducts' => $tempProducts,
                'categories' => TecH3cProductCategoryModel::pluck('name', 'id')
            ]));
    }

    /**
     * 处理批量升级
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function doBatchPromote(Request $request): JsonResponse
    {
        $data = $request->validate([
            'products' => 'required|array',
            'products.*.id' => 'required|integer',
            'products.*.category_id' => 'required|integer',
        ]);
        
        DB::beginTransaction();
        
        try {
            $successCount = 0;
            
            foreach ($data['products'] as $item) {
                $tempProduct = TecH3cCustomerTemporaryProductModel::find($item['id']);
                
                if (!$tempProduct || $tempProduct->is_reused) {
                    continue;
                }
                
                // 创建正式产品
                $product = new TecH3cProductListModel();
                $product->product = $tempProduct->product_name;
                $product->product_bom = $tempProduct->product_model ?? '-';
                $product->spec = $tempProduct->product_spec;
                $product->category_id = $item['category_id'];
                $product->major_category_id = 1; // 默认主分类ID，需要根据实际情况调整
                $product->product_bom = '-';
                $product->product_zokusei = '常规';
                $product->incoterms = 'CIP'; // 默认贸易条件
                $product->save();
                
                // 更新临时商品状态
                $tempProduct->is_reused = true;
                $tempProduct->formal_product_id = $product->id;
                $tempProduct->save();
                
                $successCount++;
            }
            
            DB::commit();
            
            return response()->json([
                'status' => true,
                'message' => "成功升级 {$successCount} 个临时商品",
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'status' => false,
                'message' => '批量升级失败: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 清理长期未使用的临时商品
     *
     * @param Request $request
     * @return Content
     */
    public function cleanup(Request $request)
    {
        // 如果是POST请求，执行清理操作
        if ($request->isMethod('post')) {
            return $this->doCleanup($request);
        }

        // 显示清理表单
        return Content::make()
            ->title('清理临时商品')
            ->description('清理长期未使用的临时商品')
            ->body($this->cleanupForm());
    }

    /**
     * 执行清理操作
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function doCleanup(Request $request)
    {
        $request->validate([
            'days' => 'required|integer|min:30',
            'max_usage' => 'required|integer|min:0',
        ]);

        $days = $request->input('days', 365);
        $maxUsage = $request->input('max_usage', 0);

        $threshold = now()->subDays($days);

        // 查找符合清理条件的临时商品
        $candidateIds = TecH3cCustomerTemporaryProductModel::where('last_used_at', '<', $threshold)
            ->where('usage_count', '<=', $maxUsage)
            ->where('is_reused', false)
            ->pluck('id')
            ->toArray();

        if (empty($candidateIds)) {
            admin_toastr('没有符合条件的临时商品可清理', 'info');
            return redirect(admin_url('r_h3c_temporary_products'));
        }

        // 检查是否有报价单引用这些临时商品
        $referencedIds = DB::table('t_customer_quote_items_h3c')
            ->whereIn('temp_product_id', $candidateIds)
            ->whereNotNull('temp_product_id')
            ->pluck('temp_product_id')
            ->unique()
            ->toArray();

        // 过滤掉有引用的临时商品
        $safeToDeleteIds = array_diff($candidateIds, $referencedIds);

        if (empty($safeToDeleteIds)) {
            admin_toastr('符合条件的临时商品都有报价单引用，无法清理', 'warning');
            return redirect(admin_url('r_h3c_temporary_products'));
        }

        // 执行安全删除
        $count = TecH3cCustomerTemporaryProductModel::whereIn('id', $safeToDeleteIds)->delete();

        $skippedCount = count($referencedIds);
        $message = "成功清理 {$count} 个临时商品";
        if ($skippedCount > 0) {
            $message .= "，跳过 {$skippedCount} 个有引用的商品";
        }

        admin_toastr($message, 'success');

        return redirect(admin_url('r_h3c_temporary_products'));
    }

    /**
     * 清理表单
     *
     * @return string
     */
    protected function cleanupForm()
    {
        return '<div class="card">
            <div class="card-body">
                <form method="POST" action="' . admin_url('r_h3c_temporary_products/cleanup') . '">
                    ' . csrf_field() . '
                    <div class="form-group">
                        <label for="days">未使用天数</label>
                        <input type="number" class="form-control" id="days" name="days" value="365" min="30" required>
                        <small class="form-text text-muted">清理超过指定天数未使用的临时商品</small>
                    </div>
                    <div class="form-group">
                        <label for="max_usage">最大使用次数</label>
                        <input type="number" class="form-control" id="max_usage" name="max_usage" value="0" min="0" required>
                        <small class="form-text text-muted">只清理使用次数小于等于此值的临时商品（0=从未使用，1=使用过1次）</small>
                    </div>
                    <button type="submit" class="btn btn-danger">执行清理</button>
                    <a href="' . admin_url('r_h3c_temporary_products') . '" class="btn btn-secondary">取消</a>
                </form>
            </div>
        </div>';
    }
}