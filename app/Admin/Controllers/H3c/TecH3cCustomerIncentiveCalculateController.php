<?php

namespace App\Admin\Controllers\H3c;

use App\Models\TecH3cCustomerPurchaseIncentiveModel;
use App\Models\TecH3cCustomerPurchaseIncentiveItemModel;
use Illuminate\Http\Request;
use Dcat\Admin\Http\Controllers\AdminController;

class TecH3cCustomerIncentiveCalculateController extends AdminController
{
    /**
     * 计算インセンティブ相关值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculate(Request $request)
    {
        try {
            $itemData = $request->input('item');
            
            // 创建一个临时的Item模型实例
            $item = new TecH3cCustomerPurchaseIncentiveItemModel();
            $item->standard_price = $itemData['standard_price'];
            $item->exchange_rate = $itemData['exchange_rate'];
            $item->cip_cost = $itemData['cip_cost'];
            $item->standard_profit_rate = $itemData['standard_profit_rate'];
            $item->standard_selling_price = $itemData['standard_selling_price'];
            $item->qty_requested = $itemData['qty_requested'];
            $item->need_incentive_calc = $itemData['need_incentive_calc'];
            
            // 使用Model的计算方法
            $values = TecH3cCustomerPurchaseIncentiveModel::calculateItemValues($item);
            
            return response()->json([
                'status' => true,
                'data' => $values
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
