<?php

namespace App\Admin\Controllers\H3c;

use App\Models\TecH3cCustomerIncentiveInModel;
use App\Models\TecSndCompanyModel;
use App\Models\TecSndContactModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Illuminate\Support\Facades\Log;

class TecH3cCustomerIncentiveInController extends AdminController
{
    protected $title = 'インセンティブ収入管理';

    protected function grid()
    {
        return Grid::make(new TecH3cCustomerIncentiveInModel(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('title', '标题');
            $grid->column('date', '发生日期')->display(function($value) {
                return $value ? date('Y-m-d', strtotime($value)) : '';
            })->sortable();
            $grid->column('reason', '事由/用途');
            $grid->column('total_amount', '总额')->display(function ($value) {
                return number_format($value, 0);
            })->sortable()->style('color: #21b978;font-weight: bold;');
            $grid->column('receipt', '是否有收据')->using([0 => '无', 1 => '有'])->dot([0 => 'danger', 1 => 'success']);
            $grid->column('remark1', '备注1');
            $grid->column('remark2', '备注2');
            $grid->column('remark3', '备注3');
            $grid->column('remark4', '备注4');

            // 过滤器
            $grid->filter(function(Grid\Filter $filter) {
                $filter->between('date', '发生日期')->date()->width(3);
                $filter->like('title', '标题')->width(2);
                // 事由/用途下拉列表，自动从数据库去重提取
                $reasonList = 
                    TecH3cCustomerIncentiveInModel::query()
                    ->distinct('reason')
                    ->pluck('reason', 'reason')
                    ->filter(function($item) { return !empty($item); })
                    ->toArray();
                $filter->equal('reason', '事由/用途')->select($reasonList)->width(2);
                $filter->equal('receipt', '是否有收据')->select([0 => '无', 1 => '有'])->width(2);
            });

            // 工具栏
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(new \App\Admin\Actions\Grid\TecH3cCustomerIncentiveOut\TecH3cCustomerIncentiveInExportAction());
            });

            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                $batch->add(new Grid\Tools\BatchDelete('批量删除'));
            });

            // 操作列
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->disableDelete(false); // 启用删除按钮
            });
        });
    }

    protected function form()
    {
        return Form::make(new TecH3cCustomerIncentiveInModel(), function (Form $form) {
            $form->text('title', '标题')->required();
            $form->date('date', '发生日期')->required();
            $form->text('reason', '事由/用途')->required();
            $form->currency('total_amount', '总额')->symbol('￥')->digits(0)->required();
            $form->switch('receipt', '是否有收据')->default(1);
            $form->text('remark1', '备注1');
            $form->text('remark2', '备注2');
            $form->text('remark3', '备注3');
            $form->text('remark4', '备注4');
        });
    }

    /**
     * 导出数据
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function export(Request $request)
    {
        try {
            // 创建一个新的Spreadsheet对象
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置标题行
            $sheet->setCellValue('A1', 'ID');
            $sheet->setCellValue('B1', '标题');
            $sheet->setCellValue('C1', '发生日期');
            $sheet->setCellValue('D1', '事由/用途');
            $sheet->setCellValue('E1', '总额');
            $sheet->setCellValue('F1', '是否有收据');
            $sheet->setCellValue('G1', '备注1');
            $sheet->setCellValue('H1', '备注2');
            $sheet->setCellValue('I1', '备注3');
            $sheet->setCellValue('J1', '备注4');
            $sheet->setCellValue('K1', '创建时间');
            $sheet->setCellValue('L1', '更新时间');

            // 获取数据
            $data = TecH3cCustomerIncentiveInModel::get();

            // 填充数据
            $row = 2;
            foreach ($data as $item) {
                $sheet->setCellValue('A' . $row, $item->id);
                $sheet->setCellValue('B' . $row, $item->title);
                $sheet->setCellValue('C' . $row, $item->date);
                $sheet->setCellValue('D' . $row, $item->reason);
                $sheet->setCellValue('E' . $row, $item->total_amount);
                $sheet->setCellValue('F' . $row, $item->receipt ? '有' : '无');
                $sheet->setCellValue('G' . $row, $item->remark1);
                $sheet->setCellValue('H' . $row, $item->remark2);
                $sheet->setCellValue('I' . $row, $item->remark3);
                $sheet->setCellValue('J' . $row, $item->remark4);
                $sheet->setCellValue('K' . $row, $item->created_at);
                $sheet->setCellValue('L' . $row, $item->updated_at);
                $row++;
            }

            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(10);
            $sheet->getColumnDimension('B')->setWidth(20);
            $sheet->getColumnDimension('C')->setWidth(15);
            $sheet->getColumnDimension('D')->setWidth(15);
            $sheet->getColumnDimension('E')->setWidth(15);
            $sheet->getColumnDimension('F')->setWidth(15);
            $sheet->getColumnDimension('G')->setWidth(20);
            $sheet->getColumnDimension('H')->setWidth(20);
            $sheet->getColumnDimension('I')->setWidth(20);
            $sheet->getColumnDimension('J')->setWidth(20);
            $sheet->getColumnDimension('K')->setWidth(20);
            $sheet->getColumnDimension('L')->setWidth(20);

            // 设置标题行样式
            $sheet->getStyle('A1:L1')->applyFromArray([
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => [
                        'rgb' => 'CCCCCC',
                    ],
                ],
            ]);

            // 设置文件名
            $filename = 'インセンティブ手动调整一览_' . date('Y-m-d_His') . '.xlsx';

            // 创建writer对象并输出文件
            $writer = new Xlsx($spreadsheet);
            $path = storage_path('app/public/exports/' . $filename);
            
            // 确保目录存在
            if (!file_exists(dirname($path))) {
                mkdir(dirname($path), 0755, true);
            }
            
            $writer->save($path);

            return response()->download($path, $filename)->deleteFileAfterSend();

        } catch (\Exception $e) {
            Log::error('导出インセンティブ収入失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status'  => false,
                'message' => '导出失败：' . $e->getMessage()
            ]);
        }

    }
}
