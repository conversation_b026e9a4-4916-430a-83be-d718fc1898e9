<?php

declare(strict_types=1);

namespace App\Admin\Controllers\H3c;

use App\Imports\TecH3cWarehouseEntrySNBatchImport;
use App\Models\TecH3cWarehouseEntryModel;
use App\Models\TecH3cWarehouseEntrySNModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\TecProductModel;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use Illuminate\Support\Facades\Storage;
use App\Admin\Config\PurchaseConfig;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Http\Controllers\Controller;
use App\Models\Common\PurchaseConfig as CommonPurchaseConfig;
use App\Models\TecH3cPurchaseOrderItemModel;

class TecH3cWarehouseEntrySNImportController extends AdminController
{
    /**
     * 页面标题
     */
    protected $title = 'SN批量导入';
    
    /**
     * 首页
     */
    public function index(Content $content)
    {
        // 直接展示入库单导入表单
        return $content
            ->title($this->title)
            ->description('批量生成和导入SN编号')
            ->body($this->importByEntryForm());
    }
    
    /**
     * 入库单的导入表单
     */
    protected function importByEntryForm()
    {
        // 生成自定义导入表单 HTML
        $html = <<<HTML
<input type="hidden" id="csrf_token" value="{$this->getCsrfToken()}">
<div class="form-group row">
    <label for="entry_id" class="col-sm-2 control-label">入库单</label>
    <div class="col-sm-8">
        <select class="form-control" name="entry_id" required>
            <option value="">请选择入库单</option>
HTML;

        // 添加入库单选项
        $entries = TecH3cWarehouseEntryModel::with(['order', 'items.product'])->get();
        foreach ($entries as $entry) {
            // 只显示未完成 SN 导入的入库单
            // 计算总计划数量
            $totalPlanned = $entry->items->sum('actual_quantity');
            // 计算已导入 SN 数量
            $importedCounts = TecH3cWarehouseEntrySNModel::where('entry_id', $entry->id)
                ->count();
            if ($importedCounts >= $totalPlanned || $totalPlanned <= 0) {
                continue;
            }
            
            if ($entry->order) {
                // 获取每个产品的SN数量统计
                $snCounts = TecH3cWarehouseEntrySNModel::where('entry_id', $entry->id)
                    ->groupBy('product_id')
                    ->select('product_id', DB::raw('count(*) as sn_count'))
                    ->get()
                    ->pluck('sn_count', 'product_id');
                
                // 汇总产品BOM和数量信息
                $productInfo = '';
                if ($entry->items && $entry->items->count() > 0) {
                    $productDetails = [];
                    foreach ($entry->items as $item) {
                        // 优先使用明细项的product_bom字段
                        $bom = $item->product_bom;
                        
                        // 如果明细项的BOM为空或为"-"，则从关联产品获取
                        if (empty($bom) || $bom == '-') {
                            $bom = $item->product ? ($item->product->product_bom ?? 'Unknown') : 'Unknown';
                        }
                        
                        $quantity = $item->actual_quantity ?? 0;
                        $snCount = $snCounts[$item->product_id] ?? 0;
                        if ($quantity > 0) {
                            $productDetails[] = "BOM:{$bom} 数量:{$quantity}, SN记录:{$snCount}";
                        }
                    }
                    $productInfo = !empty($productDetails) ? ' (' . implode(', ', $productDetails) . ')' : '';
                }
                
                $label = e("{$entry->entry_no} - {$entry->order->order_no}{$productInfo}");
                $html .= "<option value=\"{$entry->id}\">{$label}</option>";
            } else {
                $label = e("{$entry->entry_no} - 无关联订单");
                $html .= "<option value=\"{$entry->id}\">{$label}</option>";
            }
        }

        // 添加文件上传字段和维保信息
        $html .= <<<HTML
            </select>
        </div>
    </div>
    <div class="form-group row">
        <label for="sn_file" class="col-sm-2 control-label">SN文件</label>
        <div class="col-sm-8">
            <input type="file" name="sn_file" class="form-control" accept=".xlsx,.xls,.csv" required>
            <span class="help-block">
                <i class="feather icon-info"></i>&nbsp;请上传Excel文件，系统会自动查找SN_LIST工作表
            </span>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-12">
            <hr>
            <h4>维保信息</h4>
        </div>
    </div>
    <div class="form-group row">
        <label for="warranty_length" class="col-sm-2 control-label">维保长度</label>
        <div class="col-sm-8">
            <select class="form-control" name="warranty_length">
HTML;

        // 添加维保选项
        $maintenanceOptions = config('purchase_config.maintenance');
        foreach ($maintenanceOptions as $value => $label) {
            $selected = ($value == PurchaseConfig::MAINTENANCE_1YEAR) ? 'selected' : '';
            $html .= "<option value=\"{$value}\" {$selected}>{$label}</option>";
        }

        $today = now()->format('Y-m-d');
        $nextYear = now()->addYears(1)->format('Y-m-d');

        $html .= <<<HTML
            </select>
        </div>
    </div>
    <div class="form-group row">
        <label for="warranty_start_date" class="col-sm-2 control-label">维保开始日</label>
        <div class="col-sm-8">
            <input type="date" class="form-control" name="warranty_start_date" value="{$today}">
        </div>
    </div>
    <div class="form-group row">
        <label for="warranty_end_date" class="col-sm-2 control-label">维保结束日</label>
        <div class="col-sm-8">
            <input type="date" class="form-control" name="warranty_end_date" value="{$nextYear}">
        </div>
    </div>
    <div class="form-group row">
        <label for="warranty_notes" class="col-sm-2 control-label">保修备注</label>
        <div class="col-sm-8">
            <textarea class="form-control" name="warranty_notes" rows="3"></textarea>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-8 offset-sm-2">
            <button type="button" id="sn-import-btn" class="btn btn-primary">导入</button>
        </div>
    </div>
<!-- end sn-import-container -->
HTML;

        // 添加维保长度变更时的JS脚本
        $script = <<<JS
        $(function() {
            // 设置CSRF令牌到所有Ajax请求头
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                }
            });
            
            // 选择器
            var warrantyLengthSelect = $('select[name="warranty_length"]');
            var startDateInput = $('input[name="warranty_start_date"]');
            var endDateInput = $('input[name="warranty_end_date"]');
            
            // 维保长度变更事件处理函数
            function updateEndDate() {
                var selectedLength = warrantyLengthSelect.val();
                
                // 根据字符串判断维保时长
                var months = 12; // 默认1年
                if (selectedLength) {
                    if (selectedLength.indexOf('3YEAR') > -1) {
                        months = 36; // 3年
                    } else if (selectedLength.indexOf('5YEAR') > -1) {
                        months = 60; // 5年
                    }
                }
                
                var startDate = startDateInput.val();
                
                if (startDate) {
                    try {
                        // 解析开始日期
                        var date = new Date(startDate);
                        
                        // 添加对应的月份
                        date.setMonth(date.getMonth() + months);
                        
                        // 格式化日期为 YYYY-MM-DD
                        var endDate = date.toISOString().split('T')[0];
                        
                        // 设置结束日期
                        endDateInput.val(endDate);
                    } catch (e) {
                        console.error('计算日期时出错:', e);
                    }
                }
            }
            
            // 监听事件
            warrantyLengthSelect.on('change', updateEndDate);
            startDateInput.on('change', updateEndDate);
            
            // 文档加载完成后执行一次
            $(document).ready(function() {
                setTimeout(updateEndDate, 500);
            });
            
            // 在按钮前添加进度条
            var progressContainer = $('<div class="form-group row" id="progress-container" style="display:none">' + 
                '<div class="col-sm-8 offset-sm-2">' +
                '<div class="progress" style="height: 20px; margin-bottom: 10px;">' +
                '<div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" style="width: 0%" id="progress-bar"></div>' +
                '</div>' +
                '<p id="progress-text" class="text-center">准备导入...</p>' +
                '</div>' +
                '</div>');
            
            // 将进度条插入到提交按钮前面
            $('.form-group.row:last').before(progressContainer);
            
            // 处理表单提交，使用Ajax上传并提示结果
            $('#sn-import-btn').click(function() {
                var entryId = $('select[name="entry_id"]').val();
                var fileInput = $('input[name="sn_file"]');
                if (!entryId) { Dcat.error('请选择入库单'); return; }
                if (fileInput.get(0).files.length === 0) { Dcat.error('请选择要上传的文件'); return; }
                // 显示进度条
                $('#progress-container').show();
                $('#progress-bar').css('width', '5%');
                $('#progress-text').text('上传文件中...');
                // 禁用按钮
                $(this).prop('disabled', true).text('导入中...');
                // 构造FormData并Ajax上传
                var data = new FormData();
                data.append('_token', $('#csrf_token').val());
                data.append('entry_id', entryId);
                data.append('sn_file', fileInput.get(0).files[0]);
                data.append('warranty_length', $('select[name="warranty_length"]').val());
                data.append('warranty_start_date', $('input[name="warranty_start_date"]').val());
                data.append('warranty_end_date', $('input[name="warranty_end_date"]').val());
                data.append('warranty_notes', $('textarea[name="warranty_notes"]').val());
                $.ajax({
                    url: '{$this->getActionUrl()}',
                    type: 'POST',
                    data: data,
                    processData: false,
                    contentType: false,
                    success: function(res) {
                        if (res.status) {
                            Dcat.success(res.message, '', {time: 3000});
                        } else {
                            Dcat.error('导入失败：' + res.message, '', {time: 5000});
                        }
                        // 更新进度至完成
                        $('#progress-bar').css('width', '100%');
                        $('#progress-text').text('上传完成');
                        // 延迟刷新页面，给用户时间查看提示
                        setTimeout(function() {
                            window.location.reload();
                        }, 3500);
                    },
                    error: function() {
                        Dcat.error('导入异常，请查看日志', '', {time: 5000});
                        $(this).prop('disabled', false).text('提交');
                    }
                });
            });
            
            // 检查进度的函数
            function startProgressCheck() {
                // 设置一个间隔，每2秒检查一次进度
                var progressInterval = setInterval(function() {
                    $.ajax({
                        url: '/admin/r_warehouse_entries_h3c/sn/import-progress',
                        type: 'GET',
                        dataType: 'json',
                        success: function(response) {
                            if (response.status) {
                                var percentage = response.data.percentage;
                                var description = response.data.description;
                                
                                $('#progress-bar').css('width', percentage + '%');
                                $('#progress-text').text(description || '处理中...');
                                
                                // 如果进度为100%，停止检查
                                if (percentage >= 100) {
                                    clearInterval(progressInterval);
                                    $('#progress-text').html('<span class="text-success"><i class="fa fa-check-circle"></i> 导入完成!</span>');
                                    
                                    // 2秒后重置
                                    setTimeout(function() {
                                        $.post('/admin/r_warehouse_entries_h3c/sn/reset-progress', {
                                            _token: $('#csrf_token').val()
                                        });
                                        
                                        // 重新启用提交按钮
                                        $('#sn-import-btn').prop('disabled', false).text('提交');
                                        
                                        // 5秒后刷新页面显示结果
                                        setTimeout(function() {
                                            window.location.reload();
                                        }, 3000);
                                    }, 2000);
                                }
                            }
                        }
                    });
                }, 2000);
            }
        });
JS;
        Admin::script($script);
        
        // 返回 HTML 卡片，避免 Dcat Form 默认保存逻辑
        return new Card(Admin::html($html));
    }

    /**
     * 获取CSRF令牌
     */
    protected function getCsrfToken()
    {
        return csrf_token();
    }
    
    /**
     * 获取表单提交地址
     */
    protected function getActionUrl()
    {
        return admin_url('r_warehouse_entries_h3c/sn/import-by-entry');
    }
    
    /**
     * 基于入库单导入SN
     */
    public function importByEntry(Request $request)
    {
        // 重置进度条
        TecH3cWarehouseEntrySNBatchImport::updateProgress(0, '准备导入');
        
        // 检查是否是Ajax请求（JS前端可能设置了X-Requested-With头）
        $isAjaxRequest = $request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest';
        
        // 记录请求开始
        Log::info('开始处理SN导入请求', [
            'user' => Admin::user()->id ?? 'unknown',
            'params' => $request->except(['sn_file']), // 不记录文件内容
            'has_file' => $request->hasFile('sn_file'),
            'files' => $request->file() ? array_keys($request->file()) : []
        ]);
        
        try {
            // 验证请求
            $validator = Validator::make($request->all(), [
                'entry_id' => 'required|exists:t_warehouse_entries_h3c,id',
                'sn_file' => 'required|file|mimes:xlsx,xls,csv',
                'warranty_notes' => 'nullable|string|max:500'
            ], [
                'entry_id.required' => '请选择入库单',
                'entry_id.exists' => '选择的入库单不存在',
                'sn_file.required' => '请上传SN文件',
                'sn_file.file' => '请上传有效的文件',
                'sn_file.mimes' => '文件格式错误，请上传Excel文件(xlsx,xls)或CSV文件',
            ]);
            
            if ($validator->fails()) {
                Log::error('SN导入验证失败', [
                    'errors' => $validator->errors()->toArray()
                ]);
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }
            
            // 记录验证通过
            Log::info('SN导入验证通过');
            
            // 获取入库单信息
            $entryId = intval($request->entry_id);
            $entry = TecH3cWarehouseEntryModel::findOrFail($entryId);
            Log::info('获取入库单成功', ['entry_id' => $entry->id, 'entry_no' => $entry->entry_no ?? 'unknown']);
            
            // 获取维保信息
            $warrantySetting = [
                'length' => $request->input('warranty_length', PurchaseConfig::MAINTENANCE_1YEAR),
                'start_date' => $request->input('warranty_start_date', now()->format('Y-m-d')),
                'end_date' => $request->input('warranty_end_date', now()->addYear()->format('Y-m-d')),
                'notes' => $request->input('warranty_notes'),
                'activation' => 'auto', // 默认自动激活
            ];
            
            Log::info('维保信息', [
                'start_date' => $warrantySetting['start_date'],
                'end_date' => $warrantySetting['end_date']
            ]);
            
            // 检查文件上传
            if (!$request->hasFile('sn_file')) {
                throw new \Exception('请上传SN文件');
            }
            
            $file = $request->file('sn_file');
            Log::info('文件上传成功', [
                'original_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getMimeType(),
                'size' => $file->getSize(),
                'path' => $file->getPathname()
            ]);
            
            // 使用导入类处理Excel导入
            DB::beginTransaction();
            
            try {
                Log::info('开始导入Excel', ['file_path' => $file->getPathname()]);
                
                // 尝试直接读取Excel文件内容
                try {
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file->getPathname());
                    $reader->setReadDataOnly(true);
                    $spreadsheet = $reader->load($file->getPathname());
                    
                    // 找到SN_LIST工作表
                    if (in_array('SN_LIST', $spreadsheet->getSheetNames())) {
                        $worksheet = $spreadsheet->getSheetByName('SN_LIST');
                        $highestRow = $worksheet->getHighestRow();
                        $highestColumn = $worksheet->getHighestColumn();
                        
                        Log::info("直接读取: SN_LIST工作表信息: 最大行={$highestRow}, 最大列={$highestColumn}");
                        
                        // 获取表头
                        $headerRow = $worksheet->rangeToArray('A1:' . $highestColumn . '1', null, true, false)[0];
                        Log::info("直接读取: 表头行数据: " . json_encode($headerRow));
                        
                        // 找到产品代码和序列号所在的列
                        $productCodeCol = null;
                        $serialNumberCol = null;
                        foreach ($headerRow as $colIndex => $colName) {
                            $normalized = strtolower(trim($colName));
                            if ($normalized == 'product code') {
                                $productCodeCol = $colIndex;
                            } else if ($normalized == 'product serial number') {
                                $serialNumberCol = $colIndex;
                            }
                        }
                        
                        Log::info("直接读取: 产品代码列索引: " . ($productCodeCol ?? '未找到'));
                        Log::info("直接读取: 序列号列索引: " . ($serialNumberCol ?? '未找到'));
                        
                        // 获取产品信息
                        $entry = TecH3cWarehouseEntryModel::with(['items.product'])->find($entryId);
                        $productCodeMapping = [];
                        
                        foreach ($entry->items as $item) {
                            if ($item->product) {
                                // 记录产品信息用于调试
                                Log::info("直接读取: 产品信息: ID={$item->product_id}, 代码=" . ($item->product->product_code ?? '空') . ", BOM=" . ($item->product->product_bom ?? '空'));
                                
                                // 使用BOM字段作为匹配项，因为Excel中的"Product Code"实际对应的是BOM
                                $productBom = $item->product->product_bom;
                                if (isset($productBom) && $productBom !== null && $productBom !== '-') {
                                    $productBom = trim($productBom);
                                    if (!empty($productBom)) {
                                        $productCodeMapping[$productBom] = $item->product_id;
                                    }
                                }
                                
                                // 尝试同时映射Item列，因为有些Excel中同时有这两列
                                $productCode = $item->product->product_code;
                                if (isset($productCode) && $productCode !== null) {
                                    $productCodeTrimmed = trim($productCode);
                                    if (!empty($productCodeTrimmed)) {
                                        $productCodeMapping[$productCodeTrimmed] = $item->product_id;
                                    }
                                }
                            }
                        }
                        
                        Log::info("直接读取: 产品代码映射: " . json_encode($productCodeMapping));
                        
                        // 如果能找到产品代码列和序列号列，尝试手动导入
                        if (null !== $productCodeCol && null !== $serialNumberCol && !empty($productCodeMapping)) {
                            $successCount = 0;
                            $failCount = 0;
                            $processedSNs = [];
                            
                            for ($row = 2; $row <= min($highestRow, 5); $row++) { // 先只处理前5行测试
                                $rowData = $worksheet->rangeToArray('A'.$row.':'.$highestColumn.$row, null, true, false)[0];
                                $productCode = trim($rowData[$productCodeCol] ?? '');
                                $serialNumber = trim($rowData[$serialNumberCol] ?? '');
                                
                                Log::info("直接读取: 行 {$row}: 产品代码={$productCode}, 序列号={$serialNumber}");
                                
                                if (empty($productCode) || empty($serialNumber)) {
                                    Log::info("直接读取: 行 {$row} 跳过: 产品代码或序列号为空");
                                    $failCount++;
                                    continue;
                                }
                                
                                $productId = $productCodeMapping[$productCode] ?? null;
                                if (!$productId) {
                                    Log::info("直接读取: 行 {$row} 跳过: 产品代码 '{$productCode}' 无法匹配");
                                    $failCount++;
                                    continue;
                                }
                                
                                Log::info("直接读取: 行 {$row} 匹配成功: 产品ID={$productId}");
                                $successCount++;
                            }
                            
                            Log::info("直接读取: 处理结果: 成功={$successCount}, 失败={$failCount}");
                        }
                    }
                } catch (\Exception $e) {
                    Log::error("直接读取Excel失败: " . $e->getMessage());
                }
                
                // 创建导入类实例并导入
                $import = new TecH3cWarehouseEntrySNBatchImport($entryId, $warrantySetting, $file->getPathname());
                $import->import($file);
                
                // 获取导入结果
                $result = $import->getResult();
                
                DB::commit();
                Log::info('数据库事务提交成功');
                
                Log::info('Excel导入完成', [
                    'success' => $result['success'],
                    'failed' => $result['failed'],
                    'error_count' => count($result['errors'] ?? [])
                ]);
                
                // 构建结果消息
                $message = "导入成功，共导入 {$result['success']} 个SN<br>";
                if ($result['failed'] > 0) {
                    $message .= "，失败 {$result['failed']} 个";
                    if (!empty($result['errors'])) {
                        $errorSummary = implode("<br>", array_slice($result['errors'], 0, 5));
                        if (count($result['errors']) > 5) {
                            $errorSummary .= "<br>...等共" . count($result['errors']) . "个错误";
                        }
                        $message .= "<br>错误详情: " . $errorSummary;
                    }
                }
                
                // 如果存在跳过处理的消息，逐行追加换行符显示
                if (!empty($result['skippedMessages'])) {
                    $message .= "\n" . implode("\n", $result['skippedMessages']);
                }
                
                Log::info('SN导入成功完成', [
                    'success_count' => $result['success'],
                    'failed_count' => $result['failed']
                ]);

                // 设置导入完成进度为100%
                TecH3cWarehouseEntrySNBatchImport::updateProgress(100, '导入完成');
                
                // 如果是Ajax请求，返回JSON结果
                if ($isAjaxRequest) {
                    return response()->json([
                        'status' => true,
                        'message' => $message,
                        'data' => $result
                    ]);
                }
                
                // 否则，返回常规重定向
                admin_success('导入成功', $message);
                return redirect()->back();
                
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('SN导入失败', [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                // 设置导入失败进度
                TecH3cWarehouseEntrySNBatchImport::updateProgress(100, '导入失败: ' . $e->getMessage());
                
                // 如果是Ajax请求，返回JSON错误
                if ($isAjaxRequest) {
                    return response()->json([
                        'status' => false,
                        'message' => '导入失败: ' . $e->getMessage()
                    ], 500);
                }
                
                // 否则，返回常规重定向
                admin_error('导入失败', $e->getMessage());
                return redirect()->back()->withInput();
            }
            
        } catch (\Exception $e) {
            Log::error('SN导入失败', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            admin_error('导入失败', $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * 获取导入进度
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getImportProgress()
    {
        $progress = TecH3cWarehouseEntrySNBatchImport::getProgress();
        
        return response()->json([
            'status' => true,
            'data' => $progress
        ]);
    }
    
    /**
     * 重置导入进度
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetImportProgress()
    {
        TecH3cWarehouseEntrySNBatchImport::updateProgress(0, '');
        
        return response()->json([
            'status' => true,
            'message' => '进度已重置'
        ]);
    }
    
    /**
     * 发送错误响应
     */
    protected function sendError($message, $data = [])
    {
        return response()->json([
            'status' => false,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 显示入库单详情
     */
    public function show($id, Content $content)
    {
        $entry = TecH3cWarehouseEntryModel::with(['order', 'items.product'])->findOrFail($id);
        
        // 获取每个产品的SN数量统计
        $snCounts = TecH3cWarehouseEntrySNModel::where('entry_id', $id)
            ->groupBy('product_id')
            ->select('product_id', DB::raw('count(*) as sn_count'))
            ->get()
            ->pluck('sn_count', 'product_id');
            
        // 按BOM分组整理数据
        $bomGroups = [];
        foreach ($entry->items as $item) {
            $bom = $item->product ? ($item->product->product_bom ?? '-') : '-';
            $quantity = $item->actual_quantity ?? 0;
            $snCount = $snCounts[$item->product_id] ?? 0;
            
            if (!isset($bomGroups[$bom])) {
                $bomGroups[$bom] = [
                    'quantity' => $quantity,
                    'sn_count' => $snCount
                ];
            } else {
                $bomGroups[$bom]['quantity'] += $quantity;
                $bomGroups[$bom]['sn_count'] += $snCount;
            }
        }
        
        // 构建显示标题
        $title = $entry->entry_no;
        if ($entry->order) {
            $title .= " - " . $entry->order->order_no;
        }
        
        // 添加BOM和数量信息
        $bomInfo = [];
        foreach ($bomGroups as $bom => $info) {
            $bomInfo[] = "BOM:{$bom} 数量:{$info['quantity']}, SN记录{$info['sn_count']}条";
        }
        if (!empty($bomInfo)) {
            $title .= " (" . implode(', ', $bomInfo) . ")";
        }
        
        return $content
            ->title($title)
            ->description('查看入库单详情')
            ->body($this->detail($entry));
    }
} 
