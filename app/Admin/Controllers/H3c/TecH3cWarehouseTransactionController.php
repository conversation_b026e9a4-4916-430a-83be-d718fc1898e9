<?php

namespace App\Admin\Controllers\H3c;

use App\Models\TecH3cWarehouseTransactionModel;
use App\Models\TecWarehouseModel;
use App\Models\TecWarehouseLocationModel;
use App\Models\TecProductUnitModel;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;

class TecH3cWarehouseTransactionController extends AdminController
{
    /**
     * 标题
     * 
     * @var string
     */
    protected $title = '仓库事务记录';

    protected function grid()
    {
        return Grid::make(new TecH3cWarehouseTransactionModel(), function (Grid $grid) {
            // 预加载关联模型，提高查询性能并解决空字段问题
            $grid->model()->with([
                'product', 
                'warehouse', 
                'location', 
                'unit', 
                'operator'
            ]);

            // 设置默认排序
            $grid->model()->orderBy('created_at', 'desc');

            // 基础信息
            $grid->column('transaction_no', '交易编号')->width('180px');
            $grid->column('type', '类型')->width('100px')
                ->using(TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE)
                ->label(TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_COLOR);
            $grid->column('source_type', '来源类型')->width('120px')->using([
                'warehouse_entry' => '入库单',
                'warehouse_exit' => '出库单'
            ]);
            $grid->column('source_no', '来源单号')->width('180px');

            // 来源信息
            $grid->column('order_type', '订单类型')->using([
                'purchase' => '采购订单',
                'sale' => '销售订单'
            ]);
            $grid->column('order_no', '订单号');

            // 仓储信息
            $grid->column('product.product', '产品');
            $grid->column('product.product_bom', '产品BOM')
                ->display(function($bom) {
                    // 获取产品属性
                    $productZokusei = $this->product->product_zokusei ?? 'normal';
                    $isInspection = $this->product->is_inspection_machine ?? false;
                    
                    // 使用GridStyleHelper的方法获取带颜色的产品BOM
                    return \App\Support\GridStyleHelper::getColoredProductBom($bom, $productZokusei, $isInspection);
                });
            $grid->column('warehouse.name', '仓库');
            $grid->column('location.location_name', '货位');
            $grid->column('unit.unit_name', '单位');
            $grid->column('batch_no', '批次号');

            // 数量信息
            $grid->column('quantity', '操作数量');
            $grid->column('before_quantity', '操作前数量');
            $grid->column('after_quantity', '操作后数量');

            // 操作信息
            $grid->column('operator.name', '操作人');
            $grid->column('remarks', '备注');
            $grid->column('created_at', '创建时间');

            // 固定前两列和最后两列
            $grid->fixColumns(1, -1);

            // 设置筛选器
            $grid->filter(function (Grid\Filter $filter) {
                // 禁用默认的加载方式
                $filter->withoutLoading();

                // 使用面板布局
                $filter->panel();

                $filter->like('transaction_no', '交易编号')->width(3);
                $filter->equal('type', '类型')->select([
                    TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE[TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_IN],
                    TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE[TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_OUT],
                    TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE[TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_CANCEL_IN],
                    TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE[TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_CANCEL_OUT]
                ])->width(3);
                $filter->equal('source_type', '来源类型')->select([
                    'warehouse_entry' => '入库单',
                    'warehouse_exit' => '出库单'
                ])->width(3);
                $filter->like('source_no', '来源单号')->width(3);
                
                $filter->like('order_no', '订单号')->width(3);
                $filter->like('product_id', '产品')->select(
                    \App\Models\TecH3cProductListModel::pluck('product', 'id')
                )->width(3);
                $filter->like('product_bom', '产品BOM')->select(
                    \App\Models\TecH3cProductListModel::pluck('product_bom', 'id')
                )->width(3);
                $filter->equal('warehouse_id', '仓库')->select(
                    TecWarehouseModel::pluck('name', 'id')
                )->width(3);
            });

            // 禁用创建按钮
            $grid->disableCreateButton();
            // 禁用行编辑和删除
            $grid->disableActions();
            // 允许导出
            $grid->export();
        });
    }

    protected function detail($id)
    {
        return Show::make($id, new TecH3cWarehouseTransactionModel(), function (Show $show) {
            $show->field('transaction_no', '交易编号');
            $show->field('type', '类型')->using([
                TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE[TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_IN],
                TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE[TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_OUT],
                TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE[TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_CANCEL_IN],
                TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE[TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_CANCEL_OUT]
            ]);
            
            // 来源信息
            $show->field('source_type', '来源类型');
            $show->field('source_no', '来源单号');
            
            // 订单信息
            $show->field('order_type', '订单类型');
            $show->field('order_no', '订单号');
            
            // 仓储信息
            $show->field('product.product', '产品');
            $show->field('warehouse.name', '仓库');
            $show->field('location.location_name', '货位');
            $show->field('unit.unit_name', '单位');
            $show->field('batch_no', '批次号');
            
            // 数量信息
            $show->field('quantity', '操作数量');
            $show->field('before_quantity', '操作前数量');
            $show->field('after_quantity', '操作后数量');
            
            // 操作信息
            $show->field('operator.name', '操作人');
            $show->field('remarks', '备注');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }
}
