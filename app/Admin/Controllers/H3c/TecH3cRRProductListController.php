<?php

namespace App\Admin\Controllers\H3c;

use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\TecH3cRRProductListModel;
use App\Models\TecH3cProductListModel;
use App\Models\TecH3cProductCategoryModel;
use App\Models\TecH3cWarehouseEntrySNModel; // 引入SN模型
use App\Admin\Config\PurchaseConfig;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * RR制品信息管理 控制器
 * 负责管理 t_product_lists_rr_h3c 表的后台页面
 */
class TecH3cRRProductListController extends AdminController
{
    /**
     * 列表页
     */
    public function index(Content $content)
    {
        return $content->header('RR制品列表')
            ->description('RR制品信息管理')
            ->body($this->grid());
    }

    /**
     * 编辑页
     */
    public function edit($id, Content $content)
    {
        return $content->header('编辑RR制品')
            ->description('编辑RR制品信息')
            ->body($this->form()->edit($id));
    }

    /**
     * 创建页
     */
    public function create(Content $content)
    {
        return $content->header('新增RR制品')
            ->description('新增RR制品信息')
            ->body($this->form());
    }

    /**
     * 构建Grid
     */
    protected function grid()
    {
        return Grid::make(TecH3cRRProductListModel::with('product.category'), function (Grid $grid) {
            $grid->scrollbarX();
            
            // 按在库状态排序
            $grid->model()->orderBy('stock_status_id', 'asc');
            $grid->column('id', 'ID')->sortable();
            // 产品分类/名称/BOM
            $grid->column('product.category.title', '分类');
            $grid->column('product.product', '产品名')->sortable();
            $grid->column('product.product_bom', 'BOM')->sortable();
            $grid->column('sn_no', 'SN编号');
            $grid->column('rma_sn_no', 'RMA报修SN');
            // 根据状态显示不同的颜色
            $grid->column('stock_status_id', '在库状态')
                ->using(PurchaseConfig::getStockStatus())
                ->dot([
                    PurchaseConfig::STOCK_STATUS_IN => 'success', // 在库
                    PurchaseConfig::STOCK_STATUS_OUT => 'warning', // 报修
                    PurchaseConfig::STOCK_STATUS_SCRAPPED => 'danger',  // 备注/废弃
                ])
                ->sortable();
            $grid->column('remark', '备注');
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('stock_status_id', '在库状态')->select(PurchaseConfig::getStockStatus())->width(2);
                // 产品下拉（只显示当前RR产品清单实际存在的产品名）
                $filter->equal('product_id', '产品')->select(
                    TecH3cRRProductListModel::with('product')
                        ->get()
                        ->pluck('product.product', 'product_id')
                        ->unique()
                )->width(2);
                // BOM下拉（只显示当前RR产品清单实际存在的BOM）
                $filter->equal('product_bom', 'BOM')->select(
                    TecH3cRRProductListModel::with('product')
                        ->get()
                        ->pluck('product.product_bom', 'product.product_bom')
                        ->unique()
                )->width(2);
                $filter->like('sn_no', 'SN编号')->width(2);
                $filter->like('rma_sn_no', 'RMA报修SN')->width(2);
            });
            $grid->disableViewButton();
        });
    }

    /**
     * 构建Form
     */
    protected function form()
    {
        return Form::make(new TecH3cRRProductListModel(), function (Form $form) {
            // $form->display('id', 'ID');
            // 自定义卡片式选择商品
            // 按 category_id 排序
            $products = TecH3cProductListModel::where('product_zokusei', 'rr')
                ->orderBy('category_id')
                ->with('category:id,title,background_color')
                ->get()
                ->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'text' => $item->product . ' ( ' . $item->product_bom . ' )',
                        'bg' => $item->category->background_color ?: '#eee'
                    ];
                })->values();
            // 获取当前选中商品ID（编辑时预选）
            $rawId = $form->model()->getAttribute('product_id');
            $selectedId = is_scalar($rawId) ? (string)$rawId : '';
            
            // 注册隐藏字段，确保 product_id 字段参与表单保存
            $form->hidden('product_id')->default($selectedId);
            Log::info('编辑RR制品页面 selectedId=' . $selectedId);
            // 产品卡片选择视图会手动渲染隐藏字段 product_id
            // 卡片选择视图，传入 products 与 selectedId
            $form->html(view('admin.h3c.tec_h3c_rr_product_card_select', [
                'products' => $products,
                'selectedId' => $selectedId,
            ])->render());
            // SN编号下拉列表：取RR仓库下所有SN，并剔除已在本表中使用的SN
            // 获取当前记录ID，编辑时排除自身（确保为标量值）
            $rawId = $form->model()->getKey();
            $currentId = is_scalar($rawId) ? $rawId : null;
            
            // 查询已使用的SN编号
            $query = TecH3cRRProductListModel::query();
            if ($currentId) {
                $query->where('id', '<>', $currentId);
            }
            $usedSn = $query->pluck('sn_no')->toArray();
            // 获取RR仓库下所有SN
            $snOptions = TecH3cWarehouseEntrySNModel::whereHas('entry.warehouse', function ($q) {
                $q->where('code', 'RR');
            })->pluck('sn_no', 'sn_no')->toArray();
            // 剔除已使用的SN编号
            foreach ($snOptions as $sn => $label) {
                if (in_array($sn, $usedSn)) {
                    unset($snOptions[$sn]);
                }
            }
            // SN编号下拉列表，唯一校验
            $snField = $form->select('sn_no', 'SN编号');
            $snField->options($snOptions);
            // 新增时唯一校验，编辑时排除当前ID
            // 获取主键，防止 Fluent 类型错误
            $rawSnId = $form->model()->getKey();
            $snId = is_scalar($rawSnId) ? (string)$rawSnId : '';
            $snRule = $snId !== ''
                ? "required|unique:t_product_lists_rr_h3c,sn_no,{$snId},id"
                : 'required|unique:t_product_lists_rr_h3c,sn_no';
            $snField->rules($snRule);

            // 追加自定义校验：SN编号的BOM段必须与所选产品卡片的BOM号一致
            $form->saving(function (Form $form) {
                // 获取提交的字段值
                $sn = request()->input('sn_no');
                $productId = request()->input('product_id');
                
                // 确保 product_id 被正确保存到表单数据中
                $form->input('product_id', $productId);
                // 直接从数据库查找产品BOM号
                $product = 
                    $productId ? 
                    \App\Models\TecH3cProductListModel::find($productId) : null;
                $bom = $product ? $product->product_bom : '';
                // SN第3位起9位为BOM段
                $snBom = mb_substr($sn, 2, 8);
                if ($bom && $snBom !== $bom) {
                    throw new \Exception("SN编号与所选产品的BOM号不匹配！(SN BOM: {$snBom}, 产品BOM: {$bom})");
                }
            });
            // 在库状态：2 显示 RMA报修SN，3 显示备注
            $stockField = $form->select('stock_status_id', '在库状态');
            $stockField->options(PurchaseConfig::getStockStatus());
            $stockField->rules('required');
            $stockField->default(PurchaseConfig::STOCK_STATUS_IN);
            $stockField->when(PurchaseConfig::STOCK_STATUS_OUT, function (Form $form) {
                $form->text('rma_sn_no', 'RMA报修SN')
                    ->rules('required_if:stock_status_id,' . PurchaseConfig::STOCK_STATUS_OUT)
                    ->setLabelClass(['asterisk']);
            });
            $stockField->when(PurchaseConfig::STOCK_STATUS_SCRAPPED, function (Form $form) {
                $form->text('remark', '备注');
            });
            
            // $form->display('created_at', '创建时间');
            // $form->display('updated_at', '更新时间');
        });
    }
}
