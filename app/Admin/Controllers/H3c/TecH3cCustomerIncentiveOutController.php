<?php

namespace App\Admin\Controllers\H3c;

use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\TecH3cCustomerIncentiveOutModel;
use App\Models\TecSndCompanyModel;
use App\Models\TecSndContactModel;
use App\Repositories\TecH3cCustomerIncentiveOutRepository;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Illuminate\Support\Facades\Storage;

class TecH3cCustomerIncentiveOutController extends AdminController
{
    /**
     * @var TecH3cCustomerIncentiveOutRepository
     */
    protected $repo;

    /**
     * 构造函数
     */
    public function __construct(TecH3cCustomerIncentiveOutRepository $repo)
    {
        $this->repo = $repo;
    }

    /**
     * 标题
     *
     * @return string
     */
    protected function title()
    {
        return 'インセンティブ支出管理';
    }

    /**
     * 列表页
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header($this->title())
            ->description('列表')
            ->body($this->grid());
    }

    /**
     * 详情页
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content)
    {
        return $content
            ->header($this->title())
            ->description('详情')
            ->body($this->detail($id));
    }

    /**
     * 编辑页
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function edit($id, Content $content)
    {
        return $content
            ->header($this->title())
            ->description('编辑')
            ->body($this->form()->edit($id));
    }

    /**
     * 创建页
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content)
    {
        return $content
            ->header($this->title())
            ->description('创建')
            ->body($this->form());
    }

    /**
     * 列表配置
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new TecH3cCustomerIncentiveOutModel());
        $grid->scrollbarX();

        // 筛选
        $grid->filter(function ($filter) {
            // 右侧展示
            $filter->expand(true)->style('font-size: 1em;');
            $filter->panel()->display(true);
            $filter->disableIdFilter();
            
            $filter->equal('sndCompany_id', '公司')->select(
                TecSndCompanyModel::get()->mapWithKeys(function($company) {
                    $displayName = $company->name;
                    if (!empty($company->notes)) {
                        $displayName .= " ({$company->notes})";
                    }
                    return [$company->id => $displayName];
                })->toArray()
            )->width(4);
            
            $filter->equal('contact_person', '联系人')->select(function ($value) {
                // 如果选择了公司，则只显示该公司的联系人
                if ($value) {
                    return TecSndContactModel::where('sndCompany_id', $value)->pluck('name', 'id');
                }
            })->load('contact_person', 'api/r_h3c_customer_orders/get-contacts-by-company', 'sndCompany_id')->width(2);
            
            $filter->between('date', '发生日期')->date()->width(3);
            $filter->like('title', '标题')->width(2);
            // 事由/用途下拉列表，自动从数据库去重提取
            $reasonList = 
                \App\Models\TecH3cCustomerIncentiveOutModel::query()
                ->distinct('reason')
                ->pluck('reason', 'reason')
                ->filter(function($item) { return !empty($item); })
                ->toArray();
            $filter->equal('reason', '事由/用途')->select($reasonList)->width(2);
            $filter->equal('receipt', '是否有收据')->select([0 => '无', 1 => '有'])->width(2);
        });

        // 预加载关联
        $grid->model()->with(['sndCompany:id,name,notes,company_color', 'contact']);
        // 列配置
        $grid->column('id', 'ID')->sortable();
        $grid->column('sndCompany.name', '公司')->display(function () {
            $company = $this->sndCompany;
            if ($company) {
                $displayName = $company->name;
                if (!empty($company->notes)) {
                    $displayName .= " ({$company->notes})";
                }
                
                // 使用公司颜色
                $color = $company->company_color ?? '#3498db';
                return "<span style='color: {$color}; font-weight: bold;'>{$displayName}</span>";
            }
            return '-';
        });
        $grid->column('contact_person', '联系人')->display(function ($id) {
            $contact = \App\Models\TecSndContactModel::find($id);
            return $contact ? $contact->full_name : '-';
        });
        $grid->column('date', '发生日期')->sortable();
        $grid->column('title', '标题');
        $grid->column('reason', '事由/用途');
        $grid->column('unit_price', '单价')->sortable()->display(function ($value) {
            return number_format($value, 0);
        });
        $grid->column('quantity', '数量')->sortable();
        $grid->column('total_amount', '总额')->sortable()->display(function ($value) {
            return number_format($value, 0);
        })->style('color: red;font-weight: bold;');

        $grid->column('receipt', '是否有收据')->using([0 => '无', 1 => '有'])->dot([0 => 'danger', 1 => 'success']);
        $grid->column('remark1', '备注1');
        $grid->column('remark2', '备注2');
        $grid->column('remark3', '备注3');
        $grid->column('remark4', '备注4');
        

        // 操作栏
        $grid->actions(function ($actions) {
            $actions->disableView();
            $actions->disableDelete(false); // 启用删除按钮
        });

        // 批量操作
        $grid->batchActions(function ($batch) {
            $batch->disableDelete();
        });

        // 工具栏
        $grid->tools(function ($tools) {
            $tools->batch(function ($batch) {
                $batch->disableDelete();
            });
        });

        // 导出按钮
        $grid->tools(function ($tools) {
            $tools->append(new \App\Admin\Actions\Grid\TecH3cCustomerIncentiveOut\TecH3cCustomerIncentiveOutExportAction());
        });

        return $grid;
    }

    /**
     * 详情配置
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(TecH3cCustomerIncentiveOutModel::findOrFail($id));

        $show->field('id', 'ID');
        $show->field('sndCompany.name', '公司');
        $show->field('contact.name', '联系人');
        $show->field('date', '发生日期');
        $show->field('title', '标题');
        $show->field('reason', '事由/用途');
        $show->field('unit_price', '单价');
        $show->field('quantity', '数量');
        $show->field('total_amount', '总额');
        $show->field('receipt', '是否有收据')->using([0 => '无', 1 => '有']);
        $show->field('remark1', '备注1');
        $show->field('remark2', '备注2');
        $show->field('remark3', '备注3');
        $show->field('remark4', '备注4');

        return $show;
    }

    /**
     * 表单配置
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new TecH3cCustomerIncentiveOutModel());

        // ======= 分行美化布局 =======
        $reasonOptions = ['代理店販促费', 'H3C販促费', '顧問费', 'その他'];
        $form->row(function ($row) {
            $row->width(6)->select('sndCompany_id', '公司')
                ->options(\App\Models\TecSndCompanyModel::getOptionsWithNotes()->toArray())
                ->load('contact_person', 'r_h3c_customer_orders/get-contacts-by-company')
                ->required();
            $row->width(3)->select('contact_person', '联系人')
                ->options(function ($id) { return []; })
                ->required();
            $row->width(3)->date('date', '发生日期')->required()->default(date('Y-m-d'));

        });
        $form->row(function ($row) {
            $row->width(6)->text('title', '标题')->required();
            $row->width(2)->switch('receipt', '是否有收据')->default(1);
            $row->width(2)->text('reason', '事由/用途')
            ->attribute([
                'list' => 'reason-list',
                'placeholder' => '请选择或输入事由/用途'
            ])
            ->required();
            $row->html('<datalist id="reason-list">
                <option value="代理店販促费">
                <option value="H3C販促费">
                <option value="顧問费">
                <option value="その他">
            </datalist>')->width(2);

        });
        $form->row(function ($row) {
            $row->width(4)->currency('unit_price', '单价')->symbol('¥')->digits(0)->required();
            $row->width(4)->number('quantity', '数量')->min(1)->default(1)->required();
            $row->width(4)->currency('total_amount', '总额')->symbol('¥')->readonly()->digits(0)->help('总额将根据单价和数量自动计算');
        });
        $form->row(function ($row) {
            $row->width(3)->text('remark1', '备注1');
            $row->width(3)->text('remark2', '备注2');
            $row->width(3)->text('remark3', '备注3');
            $row->width(3)->text('remark4', '备注4');

        });



        // 保存前回调 - 自动计算总额
        $form->saving(function (Form $form) {
            $unitPrice = floatval(preg_replace('/[^\d.\-]/', '', $form->unit_price));
            $quantity = floatval(preg_replace('/[^\d.\-]/', '', $form->quantity));
            $form->total_amount = $unitPrice * $quantity;
        });
        
        // 添加前端JavaScript实现实时计算总额
        Admin::script(
            <<<JS
            $(function () {
                function toNum(val) {
                    val = (val||'').toString().replace(/,/g, '');
                    var n = parseFloat(val);
                    return isNaN(n) ? 0 : n;
                }
                function calculateTotal() {
                    var unitPrice = toNum($('input[name="unit_price"]').val());
                    var quantity = toNum($('input[name="quantity"]').val());
                    var total = unitPrice * quantity;
                    $('input[name="total_amount"]').val(total ? total.toFixed(2) : '0');
                }
                // 监听单价变化
                $('input[name="unit_price"]').on('input change', function() {
                    calculateTotal();
                });
                // 监听数量变化（包括input、change、加减按钮、所有事件）
                var qtyRow = $('input[name="quantity"]').closest('.row, .form-row, .dcat-admin-row');
                if (qtyRow.length === 0) qtyRow = $(document);
                qtyRow.on('input change click', 'input[name="quantity"], button, input[type=button]', function() {
                    setTimeout(calculateTotal, 50);
                });
                calculateTotal();
            });
            JS
        );

        // 表单底部
        $form->footer(function ($footer) {
            $footer->disableViewCheck();
            $footer->disableEditingCheck();
            $footer->disableCreatingCheck();
        });

        return $form;
    }

    /**
     * 导出数据
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function export(Request $request)
    {
        try {
            // 创建一个新的Spreadsheet对象
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置标题行
            $sheet->setCellValue('A1', 'ID');
            $sheet->setCellValue('B1', '公司');
            $sheet->setCellValue('C1', '联系人');
            $sheet->setCellValue('D1', '发生日期');
            $sheet->setCellValue('E1', '标题');
            $sheet->setCellValue('F1', '事由/用途');
            $sheet->setCellValue('G1', '单价');
            $sheet->setCellValue('H1', '数量');
            $sheet->setCellValue('I1', '总额');
            $sheet->setCellValue('J1', '是否有收据');
            $sheet->setCellValue('K1', '备注1');
            $sheet->setCellValue('L1', '备注2');
            $sheet->setCellValue('M1', '备注3');
            $sheet->setCellValue('N1', '备注4');
            $sheet->setCellValue('O1', '创建时间');
            $sheet->setCellValue('P1', '更新时间');

            // 获取数据
            $data = TecH3cCustomerIncentiveOutModel::with(['sndCompany', 'contact'])->get();

            // 填充数据
            $row = 2;
            foreach ($data as $item) {
                $sheet->setCellValue('A' . $row, $item->id);
                $sheet->setCellValue('B' . $row, optional($item->sndCompany)->name);
                // 联系人全名
                $contact = optional($item->contact);
                $fullName = $contact ? ($contact->last_name . ' ' . $contact->first_name) : '';
                $sheet->setCellValue('C' . $row, trim($fullName));
                $sheet->setCellValue('D' . $row, $item->date);
                $sheet->setCellValue('E' . $row, $item->title);
                $sheet->setCellValue('F' . $row, $item->reason);
                $sheet->setCellValue('G' . $row, $item->unit_price);
                $sheet->setCellValue('H' . $row, $item->quantity);
                $sheet->setCellValue('I' . $row, $item->total_amount);
                $sheet->setCellValue('J' . $row, $item->receipt ? '有' : '无');
                $sheet->setCellValue('K' . $row, $item->remark1);
                $sheet->setCellValue('L' . $row, $item->remark2);
                $sheet->setCellValue('M' . $row, $item->remark3);
                $sheet->setCellValue('N' . $row, $item->remark4);
                $sheet->setCellValue('O' . $row, $item->created_at);
                $sheet->setCellValue('P' . $row, $item->updated_at);
                $row++;
            }

            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(10);
            $sheet->getColumnDimension('B')->setWidth(20);
            $sheet->getColumnDimension('C')->setWidth(15);
            $sheet->getColumnDimension('D')->setWidth(15);
            $sheet->getColumnDimension('E')->setWidth(15);
            $sheet->getColumnDimension('F')->setWidth(15);
            $sheet->getColumnDimension('G')->setWidth(10);
            $sheet->getColumnDimension('H')->setWidth(15);
            $sheet->getColumnDimension('I')->setWidth(10);
            $sheet->getColumnDimension('J')->setWidth(20);
            $sheet->getColumnDimension('K')->setWidth(20);
            $sheet->getColumnDimension('L')->setWidth(20);
            $sheet->getColumnDimension('M')->setWidth(20);
            $sheet->getColumnDimension('N')->setWidth(20);
            $sheet->getColumnDimension('O')->setWidth(20);
            $sheet->getColumnDimension('P')->setWidth(20);
            
            // 设置标题行样式
            $sheet->getStyle('A1:P1')->getFont()->setBold(true);

            // 生成文件名
            $filename = 'インセンティブ支出_' . date('YmdHis') . '.xlsx';
            $path = storage_path('app/exports/' . $filename);

            // 确保目录存在
            if (!file_exists(storage_path('app/exports'))) {
                mkdir(storage_path('app/exports'), 0755, true);
            }

            // 保存文件
            $writer = new Xlsx($spreadsheet);
            $writer->save($path);

            // 返回下载链接
            return response()->download($path, $filename, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ])->deleteFileAfterSend(true);

        } catch (\Exception $e) {
            Log::error('导出インセンティブ支出数据异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => '导出失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除记录
     *
     * @param int $id
     * @return mixed
     */
    public function destroy($id)
    {
        try {
            $result = $this->repo->delete($id);
            if ($result) {
                return response()->json([
                    'status' => true,
                    'message' => '删除成功'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => '删除失败'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('删除インセンティブ支出记录异常', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => '删除失败: ' . $e->getMessage()
            ]);
        }
    }
}
