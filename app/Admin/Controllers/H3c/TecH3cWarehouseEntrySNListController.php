<?php

declare(strict_types=1);

/**
 * TecH3cWarehouseEntrySNListController
 *
 * 用于管理H3C入库SN列表的Dcat Admin控制器。
 * 支持SN列表的分页、筛选、只读展示、维保状态动态计算、
 * 以及与产品、入库单、采购单等多表的关联数据展示。
 *
 * <AUTHOR>
 * @date 2025-04-20
 */

namespace App\Admin\Controllers\H3c;

use Illuminate\Support\Facades\Log; // 日志
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\Tools\QuickEdit;
use Dcat\Admin\Grid\Displayers\Actions;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\TecH3cWarehouseEntrySNModel;
use App\Models\TecH3cWarehouseEntryModel;
use App\Models\TecH3cProductListModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Admin\Actions\Grid\TecH3cWarehouseEntry\TecH3cWarehouseEntrySNExportAction;

/**
 * H3C入库SN列表页面控制器
 *
 * 主要功能：
 * - 展示SN入库明细，支持分页和多条件筛选
 * - 关联展示入库单号、产品名称、BOM、采购订单等信息
 * - 维保状态根据当前日期动态判断并高亮显示
 * - 只读列表，禁用新增、编辑、删除、查看等操作
 */
class TecH3cWarehouseEntrySNListController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = 'H3C入库SN列表';

    /**
     * 列表首页
     */
    /**
     * 列表页入口
     *
     * @param Content $content 页面内容对象
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->title($this->title)
            ->description('最近SN导入记录')
            ->body($this->grid());
    }

    /**
     * 构建SN列表Grid
     *
     * @return Grid
     * @var \App\Models\TecH3cWarehouseEntrySNModel $this 当前行为SN模型实例
     *
     * 主要逻辑：
     * - 关联入库单、产品、采购单等多表数据
     * - 产品名称、采购订单等字段如未取到，自动补充查表
     * - 维保状态根据当前日期与到期日动态判断
     * - 禁用所有写操作，列表只读
     */
    protected function grid()
    {
        $grid = new Grid(new TecH3cWarehouseEntrySNModel());
        $grid->scrollbarX();

        $grid->model()->with(['customerOrder', 'warehouseExit'])->orderBy('id', 'asc'); 

        $grid->id('ID')->sortable();      
        // 入库单号列，直接取关联入库表的entry_no
        // 入库单号列，带跳转链接
        $grid->column('entry.entry_no', '入库单号')->display(function ($entryNo) {
            if ($entryNo) {
                $url = "/admin/r_warehouse_entries_h3c?entry_no=" . urlencode($entryNo);
                return "<a href='{$url}' target='_blank'>{$entryNo}</a>";
            }
            return $entryNo;
        })->sortable(); 

        // 入库仓库列，通过入库单关联获取
        $grid->column('entry.warehouse.name', '入库仓库')->display(function ($warehouseName) {
            return $warehouseName ?: '';
        });

        // 产品名称列
        $grid->column('product_name', '产品名称')->display(function ($name) {
            return $name;
        });
        // 产品BOM列，直接显示
        $grid->column('product.product_bom', 'BOM')->display(function ($bom) {

            return $bom;
        })->sortable();
        // 启用可编辑列，需要在控制器中添加 update 方法来处理
        $grid->column('sn_no', 'SN编号')->editable()->sortable();

        $grid->column('warranty_start_date', '维保开始日期')->sortable();
        $grid->column('warranty_end_date', '维保结束日期')->sortable();
        // 维保状态列，非数据库字段，按当前日期与warranty_end_date动态判断
        $grid->column('warranty_status', '维保状态')->display(function () {
            $now = date('Y-m-d');
            $end = $this->warranty_end_date;
            if (!$end) {
                return '<span style="color:#888">未知</span>';
            }
            $nowTime = strtotime($now);
            $endTime = strtotime($end);
            $diff = $endTime - $nowTime;
            $day = 86400;
            $threeMonth = 90 * $day;
            $halfYear = 180 * $day;
            if ($diff < 0) {
                return '<span style="color:#e74c3c;font-weight:bold">已过期</span>';
            } elseif ($diff <= $threeMonth) {
                return '<span style="color:#ff9800;font-weight:bold">3个月内到期</span>';
            } elseif ($diff <= $halfYear) {
                return '<span style="color:#fbc02d;font-weight:bold">半年内到期</span>';
            } else {
                return '<span style="color:#43a047;font-weight:bold">正常</span>';
            }
        });
        // 采购订单列，通过模型关联获取
        $grid->column('purchaseOrder.order_no', '采购订单')->display(function ($orderNo) {
            // 如果直接关联为空，尝试通过入库单关联获取
            if (empty($orderNo) && $this->entry_id) {
                $entry = TecH3cWarehouseEntryModel::find($this->entry_id);
                if ($entry && $entry->order) {
                    $orderNo = $entry->order->order_no;
                }
            }
            if ($orderNo) {
                $url = "/admin/r_h3c_purchase_orders/type/all?order_no=" . urlencode($orderNo);
                return "<a href='{$url}' target='_blank'>{$orderNo}</a>";
            }
            return '';
        })->sortable();
        $grid->column('customer_order.order_no', '销售订单')->sortable();
        $grid->column('warehouseExit.exit_no', '出库单')->sortable();
        $grid->column('remarks', '备注');

        // 自定义行操作，仅保留删除按钮
        $grid->actions(function (Actions $actions) {
            $actions->disableView();
            $actions->disableEdit();
            $actions->disableDelete(false);
        });

        // 列表筛选条件配置
        $grid->filter(function (Grid\Filter $filter) {
            $filter->equal('entry_id', '入库单')->select(function () {
                $entries = TecH3cWarehouseEntryModel::with(['order', 'items.product'])
                    ->orderBy('id', 'desc')
                    ->get();
                $options = [];
                foreach ($entries as $entry) {
                    // 只列出有待解绑的入库单
                    $pendingCount = TecH3cWarehouseEntrySNModel::where('entry_id', $entry->id)
                        ->whereNull('warehouse_exit_id')
                        ->count();
                    if ($pendingCount === 0) {
                        continue;
                    }
                    // 构建标题：入库单号 + 采购单号
                    $title = $entry->entry_no;
                    if ($entry->order) {
                        $title .= ' - ' . $entry->order->order_no;
                    }
                    // 统计各产品 SN 数
                    $snCounts = TecH3cWarehouseEntrySNModel::where('entry_id', $entry->id)
                        ->groupBy('product_id')
                        ->selectRaw('product_id, count(*) as sn_count')
                        ->pluck('sn_count', 'product_id');
                    $details = [];
                    foreach ($entry->items as $item) {
                        $bom = $item->product_bom ?: ($item->product->product_bom ?? 'Unknown');
                        $quantity = $item->actual_quantity ?? 0;
                        $snCount = $snCounts[$item->product_id] ?? 0;
                        if ($quantity > 0) {
                            $details[] = "BOM:{$bom} 数量:{$quantity}, SN记录:{$snCount}";
                        }
                    }
                    if (!empty($details)) {
                        $title .= ' (' . implode(', ', $details) . ')';
                    }
                    $options[$entry->id] = $title;
                }
                return $options;
            })->width(8);
            $filter->like('sn_no', 'SN编号')->width(8);
            $filter->between('warranty_start_date', '维保开始日期')->datetime()->width(6);
            $filter->between('warranty_end_date', '维保结束日期')->datetime()->width(6);
        });

        // 禁用新增、行选择、编辑、查看等操作，列表全只读
        $grid->disableCreateButton();
        // 添加自定义导出按钮
        $grid->tools(function (Grid\Tools $tools) {
            $tools->append(new TecH3cWarehouseEntrySNExportAction());
        });
        // 移除行操作中的编辑按钮
        $grid->disableViewButton();
        $grid->disableEditButton();
        $grid->disableQuickEditButton();
        $grid->disableDeleteButton();
        
        return $grid;
    }

    /**
     * 处理 SN 编号的 inline 编辑保存
     *
     * @param int $id SN 记录 ID
     * @return \Dcat\Admin\Response\JsonResponse
     */
    public function update($id)
    {
        // 调试请求：原始JSON、所有参数与请求头
        Log::info('SN Inline Raw Body', ['content' => request()->getContent()]);
        Log::info('SN Inline Params', request()->all());
        Log::info('SN Inline Headers', [
            'content-type' => request()->header('Content-Type'),
            'accept'       => request()->header('Accept'),
        ]);
        // 根据字段调试后确定正确的 key: 先尝试 'value', 未命中则取全部第一个元素
        $all = request()->all();
        $newValue = $all['value'] ?? reset($all) ?? null;

        // 获取并校验前端传来的新值
        // 校验非空
        if (empty($newValue)) {
            return response()->json([
                'status'  => false,
                'data'    => [
                    'message' => 'SN编号不能为空',
                ],
                'refresh' => false,
            ]);
        }
        // 校验唯一性
        if (TecH3cWarehouseEntrySNModel::where('sn_no', $newValue)->where('id', '!=', $id)->exists()) {
            return response()->json([
                'status'  => false,
                'data'    => [ 'message' => 'SN编号已存在' ],
                'refresh' => false,
            ]);
        }
        try {
            $model = TecH3cWarehouseEntrySNModel::findOrFail($id);
            $model->sn_no = $newValue;
            $model->save();
            return response()->json([
                'status'  => true,
                'data'    => [
                    'message' => 'SN 更新成功',
                ],
                'refresh' => true,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status'  => false,
                'data'    => [
                    'message' => '更新失败：' . $e->getMessage(),
                ],
                'refresh' => false,
            ]);
        }
    }

    /**
     * 导出 SN 列表
     * 
     * @param Request $request 请求对象
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportAction(Request $request)
    {
        // 使用导出器类处理导出逻辑
        $fileName = 'SN列表_' . date('YmdHis') . '.xlsx';
        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Admin\Exporters\TecH3cWarehouseEntrySNExporter($request),
            $fileName
        );
    }
}
