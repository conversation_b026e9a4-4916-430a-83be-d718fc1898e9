<?php

namespace App\Admin\Controllers\H3c;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Admin;
use App\Models\TecH3cProductListModel;
use Illuminate\Support\Facades\Log;
use App\Models\TecH3cProductCategoryModel;
use App\Models\TecProductMajorCategoryModel;
use App\Admin\Repositories\TecH3cProductListRepo;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use App\Admin\Actions\Post\Restore;
use App\Admin\Actions\Grid\TecH3cProductList\TecH3cProductListCopyAction;
use App\Support\GridStyleHelper;
use App\Admin\Config\PurchaseConfig;

class TecH3cProductListController extends AdminController
{   
    
    protected ?string $product_page = null;

    /**
     * 配置页面基本信息
     * 
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header('H3C製品リスト')
            ->description('製品リスト管理')
            ->body($this->grid());
    }

    /**
     * 配置一览表格
     * 
     * @return Grid
     */
    protected function grid()
    {
        // 判断是否为回收站作用域
        $isTrashedScope = request('_scope_') === 'trashed';

        return Grid::make(new TecH3cProductListModel(), function (Grid $grid) use ($isTrashedScope) {
            $grid->scrollbarX();

            // 预加载关联关系避免N+1查询
            $grid->model()->with([
                'category:id,title,background_color',
                'unitInfo:id,unit_name'
            ]);

            //$grid->column('id', 'ID')->sortable(); //->width(20);

            // 产品类别
            GridStyleHelper::productCategoryColumnConfig()($grid);

            // 製品属性列
            GridStyleHelper::productZokuseiColumnConfig()($grid);

            // インセンティブ計算フラグ
            $grid->column('need_incentive_calc', 'ｲﾝｾﾝ<br>ﾃｨﾌﾞ')->display(function ($value) {
                return $value 
                    ? '<span style="color: #4CAF50; font-weight: bold;">✓</span>' 
                    : '<span style="color: #F44336; font-weight: bold;">×</span>';
            })->sortable();

            // 商品列 - 设置更宽的宽度，并使用小号字体
            $grid->column('product', '商品')
                ->width(80)
                ->style('font-size: 0.85em; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;');

            // BOM列
            $grid->column('product_bom', label: 'BOM')->sortable()->width(100)
            ->style('font-size: 0.85em; font-weight: bold; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;');

            // 貿易条件列
            GridStyleHelper::incotermsColumnConfig()($grid);


            // H3C仕入単価列,小数点2位显示美元符号
            $grid->column('standard_price', '仕入<br>単価')->display(function ($standard_price) {
                return GridStyleHelper::coloredText('＄' . number_format($standard_price, 2));
            })->sortable();
            
            
            // 実際販売単価列
            $grid->column('standard_selling_price', '販売<br>単価')->display(function ($standard_selling_price) {
                return GridStyleHelper::coloredText('￥' . number_format($standard_selling_price, 0));
            })->sortable()
            ->style('font-size: 0.85em; color: var(--blue); font-weight: bold;');

            // 商品スペック列 - 增加宽度
            $grid->column('spec', '商品スペック')->link(function () {
                return $this->product_page;
            }, '商品ページ')->width(240)->style('font-size: 0.8em;');

            // 单位列
            $grid->column('unit', '単位')->display(function ($value) {
                if (!$value) return '-';

                // 使用当前行的关联数据
                if ($this->unitInfo) {
                    return $this->unitInfo->unit_name;
                }

                // 备用方案：直接查询
                $unit = \App\Models\TecProductUnitModel::find($value);
                return $unit ? $unit->unit_name : '-';
            })->width(60)->style('font-size: 0.85em; text-align: center;');

            // 商品画像列
            $grid->column('product_image', '商品画像')->image('', 50, 50)->width(80);

            // 备注列 - 增加宽度
            $grid->column('other', '备注')->width(100)->style('font-size: 0.8em;');

            // ====================================================================================
            // 配置筛选条件
            $grid->filter(function (Grid\Filter $filter) use ($grid) {
                // 展开筛选器
                $filter->expand(true)->style('font-size: 1em;');
                
                // $filter->rightSide();

                $grid->quickSearch(['product', 'product_bom']);

                // 筛选产品分类
                $filter->equal('category_id', '产品分类')
                    ->select(TecH3cProductListModel::query()
                        ->whereNotNull('category_id')
                        ->join('t_product_categories_h3c', 't_product_lists_h3c.category_id', '=', 't_product_categories_h3c.id')
                        ->distinct()
                        ->pluck('t_product_categories_h3c.title', 't_product_categories_h3c.id')
                        ->toArray())
                    ->default('')->width(3);

                $filter->like('product_bom', '商品BOM')
                    ->select(function() {
                        return TecH3cProductListModel::distinct('product_bom')
                            ->pluck('product_bom', 'product_bom')
                            ->toArray();
                    })
                    ->default('')->width(3);

                $filter->like('product', '商品')->width(3);
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            // 禁用查看按钮
            $grid->disableViewButton();
            // 禁用删除按钮
            $grid->disableDeleteButton();

            // 复制功能
            if (!$isTrashedScope) {
                $grid->batchActions([
                    new TecH3cProductListCopyAction()
                ]);
            }

            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecH3cProductListModel::class));
                }

            });

            // 确保停售产品最后显示
            $grid->model()->orderByRaw('FIELD(status, "on_sale", "discontinued")');
            $grid->model()->orderBy('category_id', 'asc')->orderBy('product_bom', 'asc');

            // 启用横向滚动条
            $grid->scrollbarX();

            // 设置默认每页显示50条记录
            $grid->paginate(50);
        });
    }

    /**
     * 配置详细表单
     * 
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecH3cProductListRepo(), function (Form $form) {

            // 隐藏 is_rr_product 和 is_inspection_machine 字段
            $form->hidden('is_rr_product');
            $form->hidden('is_inspection_machine');

            // 第一行
            $form->row(function (Form\Row $row) {
                // 获取 H3C 业务分类
                $h3cCategory = TecProductMajorCategoryModel::getH3CCategory()->first();

                // 添加空值检查
                if (!$h3cCategory) {
                    // 如果没有找到H3C分类,创建一个默认值
                    $h3cCategory = new TecProductMajorCategoryModel();
                    $h3cCategory->id = 1; // 设置默认ID
                    $h3cCategory->major_category = 'H3C'; // 设置默认名称
                }

                // 分类选择字段
                $row->width(2)->select('major_category_id', '業務分類')
                    ->options([$h3cCategory->id => $h3cCategory->major_category])
                    ->default($h3cCategory->id)
                    ->required();

                // 分类选择字段
                $row->width(4)->select('category_id', '商品分类')
                    ->options(TecH3cProductCategoryModel::getCategoryOptions())
                    ->required();

                // 製品属性
                $row->width(4)->radio('product_zokusei', '製品属性')
                    ->options([
                        'rr' => PurchaseConfig::PRODUCT_ZOKUSEI_RR,
                        'inspection' => PurchaseConfig::PRODUCT_ZOKUSEI_INSPECTION, 
                        'normal' => PurchaseConfig::PRODUCT_ZOKUSEI_NORMAL,
                    ])
                    ->default('normal')
                    ->help('RR製品：百台ごとに1台を追加購入対象製品。');
                    
                // 产品状态开关
                $row->width(2)->switch('status', '产品状态')
                    ->customFormat(function ($value) {
                        return $value === 'on_sale' ? 1 : 0;
                    })
                    ->saving(function ($value) {
                        return $value ? 'on_sale' : 'discontinued';
                    })
                    ->default(1)
                    ->help('开启为在售，关闭为停售');

            });

            // 第二行
            $form->row(function (Form\Row $row)  use ($form) {

                // 商品
                $row->width(3)->text('product', '商品')
                    ->required()
                    // ->attribute(['oninput' => 'this.value = this.value.toUpperCase()'])
                    ->placeholder('WA6320-JP');

                // 商品bom
                $row->width(3)->text('product_bom', '商品bom')
                    ->required()
                    ->attribute(['oninput' => 'this.value = this.value.toUpperCase()'])
                    ->placeholder('9801A2YF');

                $row->width(3)->select('maintenance', '保守')->options(function () {
                    return config('purchase_config.maintenance');

                });

                $row->width(2)->switch('need_incentive_calc', 'ｲﾝｾﾝﾃｨﾌﾞ')->default(1);
            });

            // 第三行
            $form->row(function (Form\Row $row) {
                //H3C仕入単価（USD）小数点2位
                $row->width(3)->currency('standard_price', 'H3C仕入単価')
                    ->symbol('USD')
                    ->digits(2);

                // 実際販売単価（円）小数点2位
                $row->width(3)->currency('standard_selling_price', '実際販売単価')
                    ->symbol('JPY')
                    ->digits(0);

                // CIPコスト（円）
                $row->width(3)->currency('cip_cost', 'CIPコスト')
                    ->symbol('JPY')
                    ->digits(0)
                    ->default(1000);

                //標準原価利益率 整数 默认15%
                $row->width(3)->rate('standard_profit_rate', '標準原価利益率')
                    ->default(15);
            });

            // 第四行
            $form->row(function (Form\Row $row) {
                // 贸易条件
                $row->width(3)->select('incoterms', '貿易条件')
                    ->options([
                        'DDP' => PurchaseConfig::INCOTERMS_DDP,
                        'CIP' => PurchaseConfig::INCOTERMS_CIP,
                    ])
                    ->required();

                // 商品スペック
                $row->width(6)->text('spec', '商品スペック');

                // 单位
                $row->width(3)->select('unit', '単位')
                    ->options(\App\Models\TecProductUnitModel::getUnitOptions())
                    ->default(1)
                    ->required();
            });
            // 第五行
            $form->row(function (Form\Row $row) {
                // 商品ページ
                $row->width(6)->url('product_page', '商品ページ');

                // 商品画像
                $row->width(6)->url('product_image', '商品画像');
            });

            // 第六行
            $form->row(function (Form\Row $row) {
                // 备注
                $row->width(12)->text('other', '备注')->saveAsString();
            });


            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮

            $form->tools(function (Form\Tools $tools) {
                //$tools->disableDelete();
                $tools->disableView();
                //$tools->disableList();
            });

            $form->saving(function (Form $form) {
                // 设置 is_rr_product 和 is_inspection_machine
                $is_rr_product = ($form->product_zokusei === 'rr') ? 1 : 0;
                $is_inspection_machine = ($form->product_zokusei === 'inspection') ? 1 : 0;
                $form->input('is_rr_product', $is_rr_product);
                $form->input('is_inspection_machine', $is_inspection_machine);
                // 修正cip_cost为0时不会为null
                $cip = $form->cip_cost;
                if ($cip === null || $cip === '' || $cip === false) {
                    $form->cip_cost = 0;
                }
            });
        });
    }
}
