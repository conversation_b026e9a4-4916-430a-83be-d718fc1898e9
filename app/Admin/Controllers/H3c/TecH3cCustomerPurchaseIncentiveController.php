<?php

namespace App\Admin\Controllers\H3c;

use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Services\H3c\TecH3cCustomerPurchaseIncentiveService;
use App\Support\GridStyleHelper;
use App\Models\TecH3cCustomerPurchaseIncentiveModel;
use App\Models\TecSndCompanyModel;
use App\Models\TecH3cCustomerPurchaseIncentiveItemModel;
use App\Admin\Renderable\TecH3cCustomerIncentiveItemsTable;
use App\Admin\Actions\Grid\TecH3cCustomerPurchaseIncentive\TecH3cCustomerIncentiveExportAction;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class TecH3cCustomerPurchaseIncentiveController extends AdminController
{
    /**
     * @var TecH3cCustomerPurchaseIncentiveService
     */
    protected $service;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->service = new TecH3cCustomerPurchaseIncentiveService();
    }

    /**
     * 标题
     *
     * @return string
     */
    protected function title()
    {
        return 'インセンティブ管理';
    }

    /**
     * 列表页
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header($this->title())
            ->description('列表')
            ->body($this->grid());
    }

    /**
     * 详情页
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content)
    {
        return $content
            ->header($this->title())
            ->description('详情')
            ->body($this->detail($id));
    }

    /**
     * 编辑页
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function edit($id, Content $content)
    {
        return $content
            ->header($this->title())
            ->description('编辑')
            ->body($this->form()->edit($id));
    }

    /**
     * 创建页
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content)
    {
        return $content
            ->header($this->title())
            ->description('创建')
            ->body($this->form());
    }

    /**
     * 列表配置
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new TecH3cCustomerPurchaseIncentiveModel());
        $grid->scrollbarX();
        $grid->model()->with([
            'request.sndCompany:id,name,notes,company_color', 
            'request.contact:id,first_name,last_name', 
            'customerOrder:id,order_no,business_date', 
            'incentiveItems:id,incentive_id,product_id,standard_selling_price,qty_requested,standard_price,exchange_rate,cip_cost,standard_profit_rate,need_incentive_calc'
        ])->orderBy('created_at', 'desc'); // 默认按录入时间降序排序
        $grid->disableCreateButton(); // 禁用创建按钮，因为激励表是自动创建的

        // 筛选
        $grid->filter(function ($filter) {
            // 右侧展示
            
            // 展开筛选器
            $filter->expand(true)->style('font-size: 1em;');
            
            $filter->panel()->display(true);
            
            $filter->disableIdFilter();
            $filter->equal('request.sndCompany_id', '客户公司')->select(function() {
                return TecSndCompanyModel::where('snd_role', 'client')
                    ->select('id', 'name')
                    ->get()
                    ->pluck('name', 'id')
                    ->toArray();
            })->width(4);
            $filter->like('request.request_no', '请求单号')->width(2);
            $filter->like('customerOrder.order_no', '订单号')->width(2);
            $filter->between('request.request_date', '请求日期')->date()->width(3);
            $filter->between('customerOrder.business_date', '下单日期')->date()->width(3);
        });

        // 列配置
        $grid->column('company_name', '客户公司')->display(function() {
            // 使用预加载的关联数据，避免N+1查询
            if ($this->relationLoaded('request') && $this->request && $this->request->relationLoaded('sndCompany')) {
                $company = $this->request->sndCompany;
                if ($company) {
                    $displayName = $company->name;
                    if (!empty($company->notes)) {
                        $displayName .= " ({$company->notes})";
                    }
                    
                    // 使用公司颜色
                    $color = $company->company_color ?? '#3498db';
                    return "<span style='color: {$color}; font-weight: bold;'>{$displayName}</span>";
                }
            }
            return '-';
        });
        $grid->column('contact_name', '联系人')->display(function() {
            // 使用预加载的关联数据，避免N+1查询
            if ($this->relationLoaded('request') && $this->request && $this->request->relationLoaded('contact')) {
                $contact = $this->request->contact;
                if ($contact) {
                    return $contact->getFullNameAttribute();
                }
            }
            return '-';
        });
        $grid->column('request.request_no', '请求单号')->sortable();
        $grid->column('request.request_date', '请求日期')->sortable();

        $grid->column('customerOrder.order_no', '订单号')->display(function ($orderNo) {
            if (!$orderNo) return '-';
            $url = admin_url('r_customer_orders_trace?order%5Border_no%5D=' . urlencode($orderNo));
            return "<a href='{$url}' target='_blank' style='color: #007bff; text-decoration: none;'>{$orderNo}</a>";
        })->sortable();
        $grid->column('customerOrder.business_date', '下单日')->sortable();


        // 总金额
        $grid->column('total_amount', '请求金额')->display(function () {
            return $this->getFormattedTotalAmount();
        })->sortable();
        
        $grid->column('incentive_amount', 'ｲﾝｾﾝﾃｨﾌﾞ')
        ->display(function ($value) {
            // 只格式化金额，返回格式化后的金额字符串
            return $this->getFormattedIncentiveAmount();
        })
        ->expand(TecH3cCustomerIncentiveItemsTable::class, 'id');
        
        $grid->column('created_at', '录入时间')->display(function($value) {
            return $value ? date('Y-m-d', strtotime($value)) : '';
        })->sortable();
        // 操作
        $grid->actions(function ($actions) {
            $actions->disableView(); // 禁用查看按钮
            $actions->disableEdit(); // 禁用编辑按钮
            $actions->disableQuickEdit(); // 禁用快速编辑按钮
            $actions->disableDelete(); // 禁用默认删除按钮
        });
        
        // 批量操作
        $grid->batchActions(function ($batch) {
            $batch->disableDelete();
        });
        
        // 导出按钮
        $grid->tools(function (Grid\Tools $tools) {
            $tools->append(new TecH3cCustomerIncentiveExportAction());
        });
        
        // 添加JS脚本，用于弹窗显示明细表格
        $grid->footer(function () {
            return <<<HTML
            <style>
            .modal-xl {
                max-width: 1200px;
            }
            </style>
            <script>
            $(function() {
                // 确保过滤器默认展开
                setTimeout(function() {
                    if ($('.filter-box').length > 0) {
                        $('.filter-box').addClass('show');
                        $('.filter-box .card-header').click();
                    }
                }, 500);
                
                // 点击查看明细按钮时的事件处理
                $(document).on('click', '.show-incentive-details', function() {
                    var id = $(this).data('id');
                    var modal = Dcat.modal.dialog({
                        title: 'インセンティブ明细',
                        width: 'xl', // 使用更大的模态框
                        body: $('<div class="p-2"><div class="loading text-center"><i class="feather icon-refresh-cw fa-spin"></i> 加载中...</div></div>'),
                    });
                    
                    // 加载明细表格
                    modal.find('.modal-body').html('<div id="incentive-items-' + id + '"></div>');
                    
                    // 使用LazyRenderable加载表格
                    $("#incentive-items-" + id).load('/admin/r_h3c_customer_incentives/renderable/incentive-items?key=' + id);
                });
            });
            </script>
            HTML;
        });

        return $grid;
    }

    /**
     * 详情配置
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(TecH3cCustomerPurchaseIncentiveModel::with(['customerOrder', 'request', 'incentiveItems', 'incentiveItems.product'])->findOrFail($id));

        return $show;
    }

    /**
     * 表单配置
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new TecH3cCustomerPurchaseIncentiveModel());
        // 禁用删除按钮
        $form->tools(function (Form\Tools $tools) {
            $tools->disableDelete();
        });
        
        return $form;
    }

    /**
     * 导出数据
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function export(Request $request)
    {
        try {
            $filters = $request->all();
            Log::info('导出激励数据', ['filters' => $filters]);
            
            // 检查是否是测试请求
            if ($request->has('test')) {
                return response()->json([
                    'status' => true, 
                    'message' => '导出接口测试成功'
                ]);
            }
            
            $data = $this->service->exportData($filters);
            
            if (empty($data)) {
                return response()->json([
                    'status' => false, 
                    'message' => '没有符合条件的数据'
                ]);
            }
            
            // 创建Excel文件
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 设置表头
            $headers = [
                'A1' => '请求单号', 
                'B1' => '客户公司', 
                'C1' => '订单号',
                'D1' => '产品型号', 
                'E1' => '产品名称',
                'F1' => '数量', 
                'G1' => '进货单价(美元)', 
                'H1' => '汇率',
                'I1' => '销售价格(日元)', 
                'J1' => 'CIP成本(日元)',
                'K1' => '标准利润率(%)', 
                'L1' => '每台成本', 
                'M1' => '每台利润',
                'N1' => '每台ｲﾝｾﾝﾃｨﾌﾞ', 
                'O1' => '总成本',
                'P1' => '总利润', 
                'Q1' => 'ｲﾝｾﾝﾃｨﾌﾞ合计'
            ];
            
            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(15);
            $sheet->getColumnDimension('B')->setWidth(20);
            $sheet->getColumnDimension('C')->setWidth(15);
            $sheet->getColumnDimension('D')->setWidth(15);
            $sheet->getColumnDimension('E')->setWidth(20);
            $sheet->getColumnDimension('F')->setWidth(10);
            $sheet->getColumnDimension('G')->setWidth(15);
            $sheet->getColumnDimension('H')->setWidth(10);
            $sheet->getColumnDimension('I')->setWidth(15);
            $sheet->getColumnDimension('J')->setWidth(15);
            $sheet->getColumnDimension('K')->setWidth(10);
            $sheet->getColumnDimension('L')->setWidth(15);
            $sheet->getColumnDimension('M')->setWidth(15);
            $sheet->getColumnDimension('N')->setWidth(15);
            $sheet->getColumnDimension('O')->setWidth(15);
            $sheet->getColumnDimension('P')->setWidth(15);
            $sheet->getColumnDimension('Q')->setWidth(15);
            
            // 写入表头
            foreach ($headers as $cell => $value) {
                $sheet->setCellValue($cell, $value);
            }
            
            // 加粗表头
            $sheet->getStyle('A1:Q1')->getFont()->setBold(true);
            
            // 写入数据
            $row = 2;
            foreach ($data as $item) {
                // 填充数据
                $sheet->setCellValue("A{$row}", $item['request_no'] ?? '');
                $sheet->setCellValue("B{$row}", $item['company_name'] ?? '');
                $sheet->setCellValue("C{$row}", $item['order_no'] ?? '');
                $sheet->setCellValue("D{$row}", $item['product_code'] ?? '');
                $sheet->setCellValue("E{$row}", $item['product_name'] ?? '');
                $sheet->setCellValue("F{$row}", $item['qty_requested'] ?? 0);
                $sheet->setCellValue("G{$row}", $item['standard_price'] ?? 0);
                $sheet->setCellValue("H{$row}", $item['exchange_rate'] ?? 0);
                $sheet->setCellValue("I{$row}", $item['standard_selling_price'] ?? 0);
                $sheet->setCellValue("J{$row}", $item['cip_cost'] ?? 0);
                $sheet->setCellValue("K{$row}", $item['profit_rate'] ?? 0);
                $sheet->setCellValue("L{$row}", $item['cost_per'] ?? 0);
                $sheet->setCellValue("M{$row}", $item['profit_per'] ?? 0);
                $sheet->setCellValue("N{$row}", $item['incentive_per'] ?? 0);
                $sheet->setCellValue("O{$row}", $item['total_cost'] ?? 0);
                $sheet->setCellValue("P{$row}", $item['total_profit'] ?? 0);
                $sheet->setCellValue("Q{$row}", $item['total_incentive'] ?? 0);
                
                $row++;
            }
            
            // 设置金额格式
            $numberFormat = '#,##0';
            $priceFormat = '#,##0.00';
            $sheet->getStyle('F2:Q'.$row)->getNumberFormat()->setFormatCode($numberFormat);
            $sheet->getStyle('G2:G'.$row)->getNumberFormat()->setFormatCode($priceFormat);
            
            // 保存文件
            $filename = 'インセンティブ一覧_' . date('YmdHis') . '.xlsx';
            $filePath = storage_path('app/exports/' . $filename);
            
            // 确保目录存在
            $dir = storage_path('app/exports');
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
            }
            
            $writer = new Xlsx($spreadsheet);
            $writer->save($filePath);
            
            // 返回文件下载
            return response()->download($filePath, $filename, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ])->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            Log::error('导出激励数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            if ($request->has('test')) {
                return response()->json([
                    'status' => false, 
                    'message' => '导出接口测试失败: ' . $e->getMessage()
                ]);
            }
            
            return response()->json([
                'status' => false, 
                'message' => '导出失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新激励明细项
     *
     * @param Request $request
     * @param int $id 明细项ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateItem(Request $request, $id)
    {
        try {
            // 从请求中获取需要更新的数据
            $data = $request->except(['_token', '_method', 'id']);
            
            // 如果是通过x-editable插件提交的，则包含name和value字段
            if ($request->has('name') && $request->has('value')) {
                $name = $request->input('name');
                $value = $request->input('value');
                $data = [$name => $value];
            }
            
            // 记录日志
            Log::info('更新激励明细项', ['id' => $id, 'data' => $data]);
            
            // 确保数值类型字段转换为数值
            $numericFields = ['standard_price', 'exchange_rate', 'standard_selling_price', 'standard_profit_rate', 'cip_cost', 'qty_requested'];
            foreach ($numericFields as $field) {
                if (isset($data[$field])) {
                    // 替换掉格式化字符（例如逗号）
                    $numericValue = str_replace(',', '', $data[$field]);
                    $data[$field] = is_numeric($numericValue) ? (float)$numericValue : 0;
                }
            }
            
            // 查找并更新项目
            $item = TecH3cCustomerPurchaseIncentiveItemModel::find($id);
            if (!$item) {
                return response()->json(['status' => false, 'message' => '未找到指定记录']);
            }
            
            $updated = $item->update($data);
            
            if ($updated) {
                // 重新获取更新后的数据
                $item = $item->fresh();
                
                // 使用统一的计算方法
                $values = TecH3cCustomerPurchaseIncentiveModel::calculateItemValues($item);
                
                return response()->json([
                    'status' => true, 
                    'message' => '更新成功',
                    'data' => [
                        'id' => $item->id,
                        'cost_per' => $values['per_unit']['cost'],
                        'profit_per' => $values['per_unit']['profit'],
                        'incentive_per' => $values['per_unit']['incentive'],
                        'total_cost' => $values['total']['cost'],
                        'total_profit' => $values['total']['profit'],
                        'total_incentive' => $values['total']['incentive']
                    ]
                ]);
            } else {
                return response()->json(['status' => false, 'message' => '更新失败']);
            }
        } catch (\Exception $e) {
            Log::error('更新激励明细项异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return response()->json(['status' => false, 'message' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 批量更新激励明细项
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchUpdateItems(Request $request)
    {
        try {
            // 获取批量更新数据
            $items = $request->input('items', []);
            
            Log::info('批量更新激励明细项', ['items_count' => count($items)]);
            
            // 用于记录成功和失败的项
            $success = [];
            $failed = [];
            
            // 处理每一项
            foreach ($items as $item) {
                $id = isset($item['id']) ? intval($item['id']) : 0;
                $field = isset($item['field']) ? $item['field'] : '';
                $value = isset($item['value']) ? $item['value'] : '';
                
                // 验证数据有效性
                if ($id <= 0 || empty($field)) {
                    $failed[] = [
                        'id' => $id,
                        'field' => $field,
                        'value' => $value,
                        'reason' => '无效的ID或字段名'
                    ];
                    continue;
                }
                
                // 确保是数值类型
                if (!is_numeric($value)) {
                    $failed[] = [
                        'id' => $id,
                        'field' => $field,
                        'value' => $value,
                        'reason' => '非数值类型的值'
                    ];
                    continue;
                }
                
                // 查找项目
                $incentiveItem = TecH3cCustomerPurchaseIncentiveItemModel::find($id);
                if (!$incentiveItem) {
                    $failed[] = [
                        'id' => $id,
                        'field' => $field,
                        'value' => $value,
                        'reason' => '未找到对应的记录'
                    ];
                    continue;
                }
                
                // 允许更新的字段列表
                $allowedFields = [
                    'standard_price',
                    'exchange_rate',
                    'standard_selling_price',
                    'standard_profit_rate',
                    'cip_cost'
                ];
                
                // 检查字段是否允许更新
                if (!in_array($field, $allowedFields)) {
                    $failed[] = [
                        'id' => $id,
                        'field' => $field,
                        'value' => $value,
                        'reason' => '不允许更新该字段'
                    ];
                    continue;
                }
                
                try {
                    // 数值处理
                    $numericValue = str_replace(',', '', $value);
                    $floatValue = is_numeric($numericValue) ? (float)$numericValue : 0;
                    
                    // 更新字段
                    $incentiveItem->$field = $floatValue;
                    $incentiveItem->save();
                    
                    // 记录成功
                    $success[] = [
                        'id' => $id,
                        'field' => $field,
                        'value' => $floatValue
                    ];
                } catch (\Exception $e) {
                    // 记录失败
                    $failed[] = [
                        'id' => $id,
                        'field' => $field,
                        'value' => $value,
                        'reason' => $e->getMessage()
                    ];
                    
                    Log::error('更新明细项异常', [
                        'id' => $id,
                        'field' => $field,
                        'value' => $value,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // 返回结果
            return response()->json([
                'status' => count($failed) === 0,
                'message' => count($failed) === 0 ? '更新成功' : '部分更新失败',
                'data' => [
                    'success' => $success,
                    'failed' => $failed
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('批量更新激励明细项异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => [
                    'success' => [],
                    'failed' => []
                ]
            ]);
        }
    }
    
    /**
     * 渲染激励明细表格
     *
     * @param Request $request
     * @return \Illuminate\Contracts\View\View
     */
    public function renderableIncentiveItems(Request $request)
    {
        $id = $request->get('key');
        return \App\Admin\Renderable\TecH3cCustomerIncentiveItemsTable::make()->payload(['id' => $id]);
    }
    
    /**
     * 快速编辑明细项字段
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function quickEditItem(Request $request)
    {
        try {
            // 从请求中获取ID和字段名和值
            $pk = $request->input('pk');
            $name = $request->input('name');
            $value = $request->input('value');
            
            // 记录日志
            Log::info('快速编辑激励明细项字段', [
                'pk' => $pk,
                'name' => $name,
                'value' => $value,
                'raw_request' => $request->all()
            ]);
            
            if (empty($pk) || empty($name)) {
                return response()->json(['status' => false, 'message' => '缺少必要参数']);
            }
            
            // 确保pk是整数
            $pk = (int)$pk;
            if ($pk <= 0) {
                return response()->json(['status' => false, 'message' => '无效的ID值']);
            }
            
            // 更新数据
            $data = [$name => $value];
            $result = $this->service->updateIncentiveItem($pk, $data);
            
            if ($result) {
                return response()->json(['status' => true, 'message' => '更新成功']);
            } else {
                return response()->json(['status' => false, 'message' => '更新失败']);
            }
        } catch (\Exception $e) {
            Log::error('快速编辑激励明细项异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return response()->json(['status' => false, 'message' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 下载导出文件
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadExport(Request $request)
    {
        $path = $request->get('path');
        $filename = $request->get('filename');
        
        // 日志记录
        Log::info('开始下载激励导出文件', [
            'path' => $path,
            'filename' => $filename
        ]);
        
        // 验证路径安全性
        if (!file_exists($path) || !is_file($path)) {
            Log::error('激励导出文件不存在', ['path' => $path]);
            abort(404, '文件不存在');
        }
        
        // 下载文件
        return response()->download($path, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }
}
