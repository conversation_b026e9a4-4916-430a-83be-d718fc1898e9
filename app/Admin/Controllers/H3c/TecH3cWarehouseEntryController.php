<?php

declare(strict_types=1);

namespace App\Admin\Controllers\H3c;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use Dcat\Admin\Form\NestedForm;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\TecH3cWarehouseEntryModel;
use App\Models\TecH3cPurchaseOrderModel;
use App\Models\TecH3cPurchaseOrderItemModel;
use App\Models\TecWarehouseModel;
use App\Models\TecWarehouseLocationModel;
use App\Models\H3c\TecPurchaseItemH3cModel;
use App\Models\H3c\TecWarehouseEntryItemH3cModel;
use App\Models\TecH3cProductListModel;
use App\Models\TecProductUnitModel;
use App\Models\TecH3cPurchaseBaseModel;
use App\Models\TecH3cWarehouseEntryItemModel;
use App\Admin\Renderable\TecH3cPurchaseOrderItemsTable;
use App\Admin\Renderable\TecH3cWarehouseEntriesTable;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\Grid\TecH3cWarehouseEntry\TecH3cWarehouseEntryBatchApproveAction;
use App\Admin\Actions\Grid\TecH3cWarehouseEntry\TecH3cWarehouseEntryBatchRejectAction;
use App\Support\GridStyleHelper;
use App\Models\PurchaseBaseModel;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Grid\Actions\Edit;
use App\Admin\Repositories\TecH3cWarehouseEntryRepo;
use App\Admin\Repositories\TecH3cWarehouseEntryItemRepo;
use function Dcat\Admin\admin_view;
use function Dcat\Admin\admin_error;
use App\Models\TecH3cWarehouseEntryLogModel;
use Illuminate\Container\Container;
use App\Admin\Config\PurchaseConfig;
use App\Services\Warehouse\WarehouseChangeService;
use App\Services\Warehouse\Entry\TecH3cWarehouseEntryService;
use App\Services\Warehouse\Entry\TecH3cWarehouseEntryItemService;
use App\Services\Warehouse\Entry\TecH3cWarehouseEntryFormService;
use App\Services\Warehouse\Entry\TecH3cWarehouseEntryPrintService;
use App\Models\TecH3cWarehouseTransactionModel;
use App\Models\TecH3cWarehouseInventoryModel;

class TecH3cWarehouseEntryController extends AdminController
{
    /**
     * @var TecH3cWarehouseEntryRepo
     */
    protected $entryRepo;

    /**
     * @var TecH3cWarehouseEntryItemRepo
     */
    protected $tecH3cWarehouseEntryItemRepo;
    
    /**
     * @var TecH3cWarehouseEntryService
     */
    protected $entryService;
    
    /**
     * @var TecH3cWarehouseEntryItemService
     */
    protected $entryItemService;
    
    /**
     * @var TecH3cWarehouseEntryFormService
     */
    protected $entryFormService;
    
    /**
     * @var TecH3cWarehouseEntryPrintService
     */
    protected $entryPrintService;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 使用容器解析服务类依赖
        $this->entryService = app(TecH3cWarehouseEntryService::class);
        $this->entryItemService = app(TecH3cWarehouseEntryItemService::class);
        $this->entryFormService = app(TecH3cWarehouseEntryFormService::class);
        $this->entryPrintService = app(TecH3cWarehouseEntryPrintService::class);
    }
    /**
     * 标题
     * 
     * @var string
     */
    protected $title = 'H3C入库单';
    /**
     * 获取入库单仓库
     * 
     * @return TecH3cWarehouseEntryRepo
     */
    protected function getEntryRepo()
    {
        if (!$this->entryRepo) {
            $model = new TecH3cWarehouseEntryModel();
            $this->entryRepo = new TecH3cWarehouseEntryRepo($model);
            Log::info('入库单仓库初始化成功', ['class' => self::class]);
        }
        return $this->entryRepo;
    }

    /**
     * 获取入库单明细仓库
     * 
     * @return TecH3cWarehouseEntryItemRepo
     */
    protected function getEntryItemRepo()
    {
        if (!$this->tecH3cWarehouseEntryItemRepo) {
            $model = new TecH3cWarehouseEntryItemModel();
            $this->tecH3cWarehouseEntryItemRepo = new TecH3cWarehouseEntryItemRepo($model);
            Log::info('入库明细仓库初始化成功', ['class' => self::class]);
        }
        return $this->tecH3cWarehouseEntryItemRepo;
    }

    /**
     * 列表页面
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecH3cWarehouseEntryModel(), function (Grid $grid) {
            $this->configureGrid($grid);
        });
    }
    
    /**
     * 入库单列表页
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        // 处理入库单号搜索逻辑
        if (request()->has('entry_no') && !empty(request('entry_no'))) {
            $searchEntryNo = request('entry_no');
            Log::info('入库单搜索 - 开始搜索', ['entry_no' => $searchEntryNo]);
            
            // 1. 使用精确搜索找到对应的入库单
            $entry = TecH3cWarehouseEntryModel::where('entry_no', $searchEntryNo)->first();
            if (!$entry) {
                // 如果精确匹配失败，尝试模糊匹配
                $entry = TecH3cWarehouseEntryModel::where('entry_no', 'like', '%' . $searchEntryNo . '%')->first();
            }
            
            if ($entry) {
                Log::info('入库单搜索 - 找到入库单', [
                    'entry_id' => $entry->id, 
                    'entry_no' => $entry->entry_no,
                    'order_id' => $entry->order_id
                ]);
                
                // 2. 根据入库单找到对应的采购订单
                $orderId = $entry->order_id;
                
                // 3. 找到该采购订单的第一条入库记录
                $firstEntry = TecH3cWarehouseEntryModel::where('order_id', $orderId)
                    ->orderBy('id', 'asc')
                    ->first();
                    
                if ($firstEntry && $firstEntry->id != $entry->id) {
                    Log::info('入库单搜索 - 找到采购订单的首条入库记录', [
                        'first_entry_id' => $firstEntry->id, 
                        'first_entry_no' => $firstEntry->entry_no
                    ]);
                    
                    // 重定向到首条入库记录
                    admin_toastr('已为您找到采购订单的首条入库记录', 'info');
                    return redirect(admin_url('r_warehouse_entries_h3c?entry_no='.$firstEntry->entry_no));
                }
            } else {
                Log::info('入库单搜索 - 未找到匹配的入库单', ['search_term' => $searchEntryNo]);
            }
        }
        
        // 正常显示列表页
        return $content->title($this->title())
            ->description($this->description()['index'] ?? trans('admin.list'))
            ->body($this->grid());
    }

    /**
     * 配置列表页面
     *
     * @param Grid $grid
     */
    protected function configureGrid(Grid $grid)
    {
        // 预加载关联关系避免N+1查询
        $grid->model()->with([
            'order:id,order_no,case_name,warehousing_status,business_date,sndCompany_id',
            'order.sndCompany:id,name,company_color,notes',
            'order.items:id,order_id,product,quantity,unit',
            'order.items.productlist:id,product,product_bom,product_zokusei,is_inspection_machine',
            'order.items.unitInfo:id,unit_name',
            'order.entries:id,order_id,entry_no,entry_date,warehouse_id',
            'order.entries.items:id,entry_id,product_id,actual_quantity',
            'items:id,entry_id,order_id,order_item_id,product_id,actual_quantity',
            'items.product:id,product,product_bom,product_zokusei,is_inspection_machine',
            'warehouse:id,name'
        ]);

        // 获取当前请求的类型参数
        $type = request()->route('type');

        // 根据类型过滤入库单
        if ($type) {
            switch ($type) {
                case 'partial_delivery':
                    $grid->model()->whereHas('order', function($query) {
                        $query->where('warehousing_status', TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS_PARTIAL);
                    });
                    break;
                case 'finished_delivery':
                    $grid->model()->whereHas('order', function($query) {
                        $query->where('warehousing_status', TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS_COMPLETED);
                    });
                    break;
            }
        }

        // 使用 scopeGroupByOrder 来过滤相同采购订单号的入库单
        $grid->model()->groupByOrder();
        
        // 设置默认排序为创建时间倒序
        $grid->model()->orderBy('created_at', 'desc');
        
        // 配置列
        $this->configureGridColumns($grid);
        
        // 配置过滤器
        $this->configureGridFilter($grid);
        
        // 配置操作
        $this->configureGridActions($grid);
        
        // 配置工具栏
        $this->configureGridTools($grid);
    }

    /**
     * 配置列表列
     *
     * @param Grid $grid
     */
    protected function configureGridColumns(Grid $grid)
    {
        // 添加入库情况概要的CSS样式
        \Dcat\Admin\Admin::style("
            .warehouse-summary {
                font-size: 0.9em;
            }
            .total-progress {
                font-weight: bold;
                margin-bottom: 8px;
                padding: 3px 0;
            }
            .product-progress {
                margin-left: 10px;
                margin-bottom: 5px;
                line-height: 1.5;
            }

            .received, .ordered {
                color: #28a745;
            }
            .ratio {
                color: #6c757d;
                margin-left: 2px;
            }
            .more-info {
                margin-left: 10px;
                color: #6c757d;
                font-style: italic;
                margin-top: 3px;
            }
            .progress-label {
                margin-right: 5px;
            }
            .received-partial, .ratio-partial {
                color: #dc3545; /* 红色，表示部分完成 */
            }
            .received-full, .ratio-full {
                color: #28a745; /* 绿色，表示全部完成 */
            }
            .progress-label {
                display: inline-block;
                min-width: 70px;
            }
        ");

        // 案件名和供应商公司（合并显示）
        $grid->column('order.case_name', '案件信息')
        ->display(function ($value) {
            $caseName = $this->order->case_name ?? null;
            $supplierCompany = $this->order->sndCompany ?? null;
            
            if (!$caseName) {
                return '-';
            }
            
            // 如果有供应商公司，显示案件名和供应商公司，否则只显示案件名
            if ($supplierCompany) {
                $companyName = $supplierCompany->name;
                $companyColor = $supplierCompany->company_color ?: '#333';
                
                // 添加公司备注（如果有的话）
                if (!empty($supplierCompany->notes)) {
                    $companyName .= " ({$supplierCompany->notes})";
                }
                
                return "<div style='line-height: 1.4;'>
                            <div style='font-weight: bold; color: #333;font-size: 1.1em;'>{$caseName}</div>
                            <div style='color: {$companyColor};font-size: 0.9em;'>{$companyName}</div>
                        </div>";
            } else {
                return "<div style='font-weight: bold; color: #333;font-size: 1.1em;'>{$caseName}</div>";
            }
        })
        ->width('250px')
        ->style('white-space: nowrap;');
    
        // 采购订单号
        $grid->column('order.order_no', '采购订单号')
            ->display(function ($value) {
                return $value ?? '-';
            })
            ->expand(function ($model) {
                return TecH3cPurchaseOrderItemsTable::make()->payload([
                    'order_id' => $this->getOriginal('order_id')
                ]);
            })
            ->style('white-space: nowrap;font-size: 0.9em;');
            

        // 入库情况概要
        $grid->column('order.overview', '入库情况')
            ->display(function ($value) {
                // 获取订单ID
                $orderId = $this->getOriginal('order_id');
                if (!$orderId) return '-';
                
                // 使用预加载的订单信息，避免额外查询
                $order = $this->order;
                if (!$order) return '-';
                
                // 使用预加载的入库单数据，避免额外查询
                $entries = $order->entries;
                if (!$entries) return '-';
                
                // 计算入库情况
                $totalOrdered = 0;
                $totalReceived = 0;
                $productCounts = [];
                
                // 统计订单总数量
                foreach ($order->items as $item) {
                    $productId = $item->product;
                    $totalOrdered += $item->quantity;
                    
                    if (!isset($productCounts[$productId])) {
                        // 使用预加载的产品信息，避免N+1查询
                        $productInfo = $item->productlist;
                        
                        $productCounts[$productId] = [
                            'name' => $productInfo ? $productInfo->product : '未知产品',
                            'bom' => $productInfo ? $productInfo->product_bom : '-',
                            'ordered' => $item->quantity,
                            'received' => 0,
                            'zokusei' => $productInfo ? $productInfo->product_zokusei : 'normal',
                            'is_inspection' => $productInfo ? (bool)$productInfo->is_inspection_machine : false
                        ];
                    }
                }
                
                // 统计已入库数量
                foreach ($entries as $entry) {
                    foreach ($entry->items as $item) {
                        $productId = $item->product_id;
                        $totalReceived += $item->actual_quantity;
                        
                        if (isset($productCounts[$productId])) {
                            $productCounts[$productId]['received'] += $item->actual_quantity;
                        }
                    }
                }
                
                // 计算入库比例
                $ratio = $totalOrdered > 0 ? round(($totalReceived / $totalOrdered) * 100) : 0;
                
                // 生成概要HTML
                $html = "<div class='warehouse-summary'>";
                $html .= "<div class='total-progress'><span class='progress-label'>总体进度:</span> ";
                
                // 根据入库进度添加不同的CSS类
                $receivedClass = $ratio < 100 ? 'received-partial' : 'received-full';
                $ratioClass = $ratio < 100 ? 'ratio-partial' : 'ratio-full';
                
                $html .= "<span class='received {$receivedClass}'>{$totalReceived}</span>/<span class='ordered'>{$totalOrdered}</span> ";
                $html .= "<span class='ratio {$ratioClass}'>({$ratio}%)</span></div>";
                
                // 显示所有产品的详情
                foreach ($productCounts as $productId => $data) {
                    $productRatio = $data['ordered'] > 0 ? round(($data['received'] / $data['ordered']) * 100) : 0;
                    $html .= "<div class='product-progress'>";

                    // 使用预加载的产品信息生成彩色BOM，避免额外查询
                    $coloredBom = \App\Support\GridStyleHelper::getColoredProductBom($data['bom'], $data['zokusei'], $data['is_inspection']);

                    $html .= "{$coloredBom} ";
                    $html .= "{$data['name']}: ";

                    // 为产品入库数量和进度添加动态CSS类
                    $receivedClass = $productRatio < 100 ? 'received-partial' : 'received-full';
                    $ratioClass = $productRatio < 100 ? 'ratio-partial' : 'ratio-full';

                    $html .= "<span class='received {$receivedClass}'>{$data['received']}</span>/<span class='ordered'>{$data['ordered']}</span> ";
                    $html .= "<span class='ratio {$ratioClass}'>({$productRatio}%)</span>";
                    $html .= "</div>";
                }
                
                $html .= "</div>";
                return $html;
            })
            ->style('white-space: normal;font-size: 0.9em;');
            
        // 入库单号
        $grid->column('entry_no', '入库单号')
            ->sortable()
            ->expand(function ($model) {
                return TecH3cWarehouseEntriesTable::make()->payload([
                    'order_id' => $this->getOriginal('order_id')
                ]);
            });

        // 入库日期
        $grid->column('entry_date', '入库日期')->sortable();
        
        // 入库状态
        $grid->column('order.warehousing_status', '入库状态')
            ->using(TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS)
            ->label([
                TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS_PENDING => 'danger',
                TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS_PARTIAL => 'warning',
                TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS_COMPLETED => 'success',
            ]);

        // 禁用所有操作
        $grid->disableActions();
        
        // 禁用批量操作
        $grid->disableBatchActions();
        
        // 禁用创建按钮
        // $grid->disableCreateButton();
        
        // 禁用导出按钮
        // $grid->disableExport();
    }

    /**
     * 配置过滤器
     *
     * @param Grid $grid
     */
    protected function configureGridFilter(Grid $grid)
    {

        $grid->filter(function (Grid\Filter $filter) {
            // 使用基础模型中定义的状态常量
            $filter->equal('order.warehousing_status', '入库状态')
                ->select(TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS)
                ->width(3);
            
            // 入库日期filter范围指定为最近一年
            $filter->between('entry_date', '入库日期')
                ->date()
                ->default([
                    'start' => now()->subYear()->toDateString(),
                    'end' => now()->toDateString()
                ])
                ->width(3);
                
            // 入库单号搜索
            $filter->equal('entry_no', '入库单号')->width(3);
        });
        // 快速搜索
        $grid->quickSearch(['entry_no', 'batch_no', 'order.order_no']);
    }

    /**
     * 配置行操作
     * @param Grid $grid
     */
    protected function configureGridActions(Grid $grid)
    {
        $grid->actions(function (Grid\Displayers\Actions $actions) {
            // 基础操作
            $actions->disableView();
            $actions->disableDelete();
            $actions->disableQuickEdit();
            $actions->disableEdit();
            
        });
    }

    /**
     * 配置工具栏
     *
     * @param Grid $grid
     */
    protected function configureGridTools(Grid $grid)
    {
        $grid->tools(function (Grid\Tools $tools) {
            $tools->append(new class extends \Dcat\Admin\Grid\Tools\AbstractTool {
                protected function html()
                {
                    // 只在列表页面加载CSS，不加载JS
                    Admin::css('/css/h3c-warehouse-entry.css');
                    return '';
                }
            });
        });
    }

    /**
     * 表单页面
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecH3cWarehouseEntryModel(), function (Form $form) {
            $this->configureForm($form);
        });
    }

    /**
     * 配置表单
     *
     * @param Form $form
     */
    protected function configureForm(Form $form)
    {
        try {
            // 确保仓库已初始化
            $this->getEntryRepo();
            $this->getEntryItemRepo();

            // 使用表单服务配置表单
            $this->entryFormService->configureForm($form);
            
            // 添加原始仓库ID隐藏字段
            $model = $form->model();
            if ($model && isset($model->id)) {
                $form->hidden('original_warehouse_id')->value($model->warehouse_id);
            }

            // 设置保存后的回调
            $controller = $this;
            $form->saved(function (Form $form) use ($controller) {
                try {
                    // 获取入库单ID
                    $entryId = $form->getKey();

                    // 记录入库单保存信息
                    Log::info('入库单保存成功', [
                        'entry_id' => $entryId,
                        'form_data' => $form->input(),
                        'processed_items' => $form->input('processed_items') ?: $form->input('_processed_items')
                    ]);

                    // 获取仓库对象
                    $repo = $controller->getEntryItemRepo();
                    
                    // 验证仓库对象
                    if (!($repo instanceof TecH3cWarehouseEntryItemRepo)) {
                        throw new \Exception('入库明细仓库对象类型错误');
                    }

                    // 验证模型对象
                    $model = $repo->getModel();
                    if (!$model) {
                        throw new \Exception('入库明细仓库模型获取失败');
                    }

                    Log::info('入库明细仓库对象状态', [
                        'repo_class' => get_class($repo),
                        'model_class' => get_class($model)
                    ]);

                    // 优先尝试获取不带下划线的字段值
                    $processedItemsJson = $form->input('processed_items');
                    
                    // 如果没有,再尝试获取带下划线的字段值
                    if (empty($processedItemsJson)) {
                        $processedItemsJson = $form->input('_processed_items');
                        if (!empty($processedItemsJson)) {
                            Log::info('从_processed_items字段获取数据', [
                                'processed_items' => $processedItemsJson
                            ]);
                        }
                    }
                    
                    // 如果还是没有,尝试从请求中获取
                    if (empty($processedItemsJson)) {
                        $processedItemsJson = request()->input('processed_items', request()->input('_processed_items'));
                        if (!empty($processedItemsJson)) {
                            Log::info('从请求中获取数据', [
                                'processed_items' => $processedItemsJson
                            ]);
                        }
                    }
                    
                    // 如果表单中没有processed_items,尝试从会话中获取
                    if (empty($processedItemsJson)) {
                        $processedItemsJson = session('processed_items', session('_processed_items'));
                        Log::info('从会话中获取处理项数据', [
                            'session_processed_items' => $processedItemsJson
                        ]);
                    }
                    
                    // 尝试从表单原始数据构建处理项
                    if (empty($processedItemsJson)) {
                        $actualQuantities = request()->input('actual_quantity', []);
                        $locationIds = request()->input('location_id', []);
                        
                        $processedItemsArray = [];
                        foreach ($actualQuantities as $orderItemId => $quantity) {
                            if (!empty($quantity) && floatval($quantity) > 0) {
                                $locationId = $locationIds[$orderItemId] ?? '';
                                $processedItemsArray[] = [
                                    'order_item_id' => $orderItemId,
                                    'actual_quantity' => floatval($quantity),
                                    'location_id' => $locationId
                                ];
                            }
                        }
                        
                        if (!empty($processedItemsArray)) {
                            $processedItemsJson = json_encode($processedItemsArray);
                            Log::info('从表单原始数据构建处理项', [
                                'processed_items' => $processedItemsJson
                            ]);
                        }
                    }
                    
                    // 记录处理前的JSON数据
                    Log::info('处理入库明细JSON数据', [
                        'entry_id' => $entryId,
                        'has_processed_items' => !empty($processedItemsJson),
                        'processed_items_json' => $processedItemsJson
                    ]);
                    
                    // 添加空值检查
                    if (empty($processedItemsJson)) {
                        throw new \Exception('未提供入库明细数据(_processed_items为空)');
                    }
                    
                    $processedItems = json_decode($processedItemsJson, true);
                    if (empty($processedItems)) {
                        throw new \Exception('未找到有效的入库明细数据');
                    }

                    // 获取入库单实例
                    $entry = TecH3cWarehouseEntryModel::findOrFail($entryId);

                    // 处理入库明细
                    $controller->processEntryItems($entry, $processedItems);

                    return $form;

                } catch (\Exception $e) {
                    Log::error('入库单保存失败', [
                        'error_message' => $e->getMessage(),
                        'error_trace' => $e->getTraceAsString(),
                        'request_data' => request()->all()
                    ]);
                    return $form->response()->error($e->getMessage());
                }
            });

        } catch (\Exception $e) {
            Log::error('表单配置失败', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 配置表单基础字段
     *
     * @param Form $form
     * @param bool $isEditing
     */
    protected function configureFormBasicFields(Form $form, bool $isEditing)
    {
        // 委托给服务类处理
        $this->entryFormService->configureFormBasicFields($form, $isEditing);
    }

    /**
     * 配置表单明细表格
     *
     * @param Form $form
     * @param bool $isEditing
     */
    protected function configureFormDetailTable(Form $form, bool $isEditing)
    {
        // 委托给服务类处理
        $this->entryFormService->configureFormDetailTable($form, $isEditing);
    }

    /**
     * 配置表单回调方法
     * 
     * @param Form $form
     */
    protected function configureFormCallbacks(Form $form)
    {
        // 委托给服务类处理
        $this->entryFormService->configureFormCallbacks($form);
    }

    /**
     * 处理入库明细项
     * 
     * @param TecH3cWarehouseEntryModel $entry
     * @param array $processedItems
     * @return array
     */
    protected function processEntryItems(TecH3cWarehouseEntryModel $entry, array $processedItems)
    {
        // 创建唯一处理标识，避免重复处理
        static $processedEntryItems = [];
        $processKey = "entry_{$entry->id}_" . md5(json_encode($processedItems));
        
        if (isset($processedEntryItems[$processKey])) {
            Log::warning('跳过重复处理相同的入库明细项', [
                'entry_id' => $entry->id,
                'items_count' => count($processedItems),
                'process_key' => $processKey
            ]);
            return $processedEntryItems[$processKey];
        }
        
        $repo = $this->getEntryItemRepo();
        $items = [];

        // 获取原有的入库明细项
        $existingItems = $repo->getModel()::where('entry_id', $entry->id)->get();
        $existingItemMap = $existingItems->keyBy('order_item_id');

        Log::info('开始处理入库单明细', [
            'entry_id' => $entry->id,
            'entry_no' => $entry->entry_no,
            'processed_items_count' => count($processedItems),
            'existing_items_count' => $existingItems->count(),
            'process_key' => $processKey
        ]);

        // 防御性检查：确保每个数量大于0的项都有有效的货位ID
        foreach ($processedItems as $index => $processedItem) {
            $orderItemId = $processedItem['order_item_id'];
            $actualQuantity = $processedItem['actual_quantity'];
            $locationId = $processedItem['location_id'] ?? '';

            // 如果数量大于0但货位ID为空，记录警告并尝试修复
            if ($actualQuantity > 0 && (empty($locationId) || $locationId === '' || $locationId === '0')) {
                Log::warning('发现数量大于0但货位ID为空的明细项', [
                    'entry_id' => $entry->id,
                    'order_item_id' => $orderItemId,
                    'actual_quantity' => $actualQuantity,
                    'location_id' => $locationId
                ]);

                // 检查是否有现有记录的货位ID可用
                if ($existingItemMap->has($orderItemId)) {
                    $existingItem = $existingItemMap->get($orderItemId);
                    if (!empty($existingItem->location_id)) {
                        // 使用现有记录的货位ID
                        $processedItems[$index]['location_id'] = $existingItem->location_id;
                        Log::info('使用现有记录的货位ID修复空货位', [
                            'entry_id' => $entry->id,
                            'order_item_id' => $orderItemId,
                            'original_location_id' => $locationId,
                            'fixed_location_id' => $existingItem->location_id
                        ]);
                    }
                }
            }
        }

        foreach ($processedItems as $processedItem) {
            $orderItemId = $processedItem['order_item_id'];
            $actualQuantity = $processedItem['actual_quantity'];
            $locationId = $processedItem['location_id'] ?? '';

            // 检查是否需要处理此项（数量为0且没有现有记录可以跳过）
            if ($actualQuantity <= 0 && !$existingItemMap->has($orderItemId)) {
                Log::info('跳过数量为0且无现有记录的明细项', [
                    'entry_id' => $entry->id,
                    'order_item_id' => $orderItemId
                ]);
                continue;
            }

            // 再次检查并确保数量大于0的项有货位ID
            if ($actualQuantity > 0 && (empty($locationId) || $locationId === '' || $locationId === '0')) {
                Log::error('无法修复空货位ID的明细项，但将继续处理', [
                    'entry_id' => $entry->id,
                    'order_item_id' => $orderItemId,
                    'actual_quantity' => $actualQuantity
                ]);
                // 使用默认货位ID或者第一个可用的货位
                // 这里可以实现额外的逻辑来查找默认货位
            }

            // 如果存在原有记录
            if ($existingItemMap->has($orderItemId)) {
                $existingItem = $existingItemMap->get($orderItemId);
                
                // 记录原始数据
                $originalQuantity = $existingItem->actual_quantity;
                $originalLocationId = $existingItem->location_id;
                
                // 更新明细项
                $existingItem->actual_quantity = $actualQuantity;
                if (!empty($locationId)) {
                    $existingItem->location_id = $locationId;
                } elseif ($actualQuantity > 0 && empty($existingItem->location_id)) {
                    // 如果数量大于0但没有货位ID，尝试设置一个默认值或保留原值
                    // 这里我们保留原有值，如果原有值也为空，将记录错误
                    Log::warning('更新明细项时发现空货位ID，但将继续使用原有值', [
                        'entry_id' => $entry->id,
                        'order_item_id' => $orderItemId,
                        'original_location_id' => $originalLocationId
                    ]);
                }
                $existingItem->save();
                
                Log::info('更新入库明细项', [
                    'entry_id' => $entry->id,
                    'entry_item_id' => $existingItem->id,
                    'order_item_id' => $orderItemId,
                    'original_quantity' => $originalQuantity,
                    'new_quantity' => $actualQuantity,
                    'original_location_id' => $originalLocationId,
                    'new_location_id' => $existingItem->location_id
                ]);
                
                continue;
            }

            // 获取订单明细信息
            $orderItem = TecH3cPurchaseOrderItemModel::with(['productlist', 'unitInfo'])
                ->findOrFail($orderItemId);

            // 检查数量和货位是否有效
            if ($actualQuantity > 0 && (empty($locationId) || $locationId === '' || $locationId === '0')) {
                // 尝试查找仓库的默认货位
                $warehouseId = $entry->warehouse_id;
                if ($warehouseId) {
                    $defaultLocation = TecWarehouseLocationModel::where('warehouse_id', $warehouseId)
                        ->where('status', 1) // 假设1是有效状态
                        ->first();
                    
                    if ($defaultLocation) {
                        $locationId = $defaultLocation->id;
                        Log::info('使用仓库默认货位创建入库明细项', [
                            'entry_id' => $entry->id,
                            'order_item_id' => $orderItemId,
                            'warehouse_id' => $warehouseId,
                            'default_location_id' => $locationId
                        ]);
                    }
                }
            }

            // 创建新的入库明细项
            $item = $repo->getModel()::create([
                'entry_id' => $entry->id,
                'order_id' => $entry->order_id,
                'order_item_id' => $orderItemId,
                'product_id' => $orderItem->product,
                'product_bom' => $orderItem->productlist->product_bom ?? '-',
                'location_id' => $locationId,
                'unit_id' => $orderItem->unit,
                'actual_quantity' => $actualQuantity,
                'created_by' => Admin::user()->id,
                'updated_by' => Admin::user()->id,
            ]);
            
            Log::info('创建新入库明细项', [
                'entry_id' => $entry->id,
                'entry_item_id' => $item->id,
                'order_item_id' => $orderItemId,
                'product_id' => $orderItem->product,
                'quantity' => $actualQuantity,
                'location_id' => $locationId
            ]);

            $items[] = $item;
        }
        
        // 记录已处理的入库项
        $processedEntryItems[$processKey] = $items;
        
        Log::info('入库单明细处理完成', [
            'entry_id' => $entry->id,
            'items_processed' => count($items),
            'process_key' => $processKey
        ]);

        return $items;
    }

    /**
     * 生成入库单号
     *
     * @return string
     */
    protected function generateEntryNo()
    {
        // 委托给服务类处理
        return TecH3cWarehouseEntryModel::generateEntryNo();
    }

    /**
     * 获取订单明细
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getOrderItems(Request $request)
    {
        try {
            $orderId = $request->get('order_id') ?: $request->get('q');
            
            Log::info('获取订单明细请求参数', [
                'request_data' => $request->all(),
                'order_id' => $orderId,
                'raw_q' => $request->get('q'),
                'raw_order_id' => $request->get('order_id')
            ]);
            
            if (!$orderId || $orderId == '0') {
                return response()->json([
                    'status' => false,
                    'message' => '无效的订单ID',
                    'data' => []
                ]);
            }
            
            $items = $this->entryItemService->getOrderItems($orderId);
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $items
            ]);
        } catch (\Exception $e) {
            Log::error('获取订单明细失败', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取订单明细失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 获取入库单明细
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getEntryItems(Request $request)
    {
        try {
            $entryId = $request->input('entry_id');
            $orderId = $request->input('order_id');
            
            if (!$entryId || !$orderId) {
                return response()->json([
                    'status' => false,
                    'message' => '入库单ID和订单ID不能为空',
                    'data' => []
                ], 200);
            }
            
            // 将参数转换为整数
            $entryId = intval($entryId);
            $orderId = intval($orderId);
            
            // 获取入库单明细
            $items = $this->entryItemService->getEntryItemsForJson($entryId);
            
            return response()->json([
                'status' => true,
                'message' => '获取入库单明细成功',
                'data' => $items
            ]);
        } catch (\Exception $e) {
            Log::error('获取入库单明细失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取入库单明细失败: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 取消入库单
     *
     * @param int $id
     * @return JsonResponse
     */
    public function cancelEntry($id)
    {
        try {
            $entry = TecH3cWarehouseEntryModel::findOrFail($id);
            
            // 使用服务类处理取消逻辑
            $result = $this->entryService->cancelEntry($entry);
            
            if ($result) {
                return response()->json([
                    'status' => true,
                    'message' => '入库单取消成功'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => '入库单取消失败,可能已被审核或状态不允许取消'
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('取消入库单失败', [
                'entry_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '取消入库单失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成PDF
     *
     * @param int $id
     * @return mixed
     */
    public function generatePdf($id)
    {
        try {
            $pdf = $this->entryPrintService->generatePdf($id);
            
            return $pdf->download('入库单_' . date('YmdHis') . '.pdf');
        } catch (\Exception $e) {
            Log::error('生成入库单PDF失败', [
                'entry_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return admin_error('生成PDF失败', $e->getMessage());
        }
    }
    
    /**
     * 预览打印
     *
     * @param int $id
     * @return Content
     */
    public function previewPrint($id)
    {
        try {
            $html = $this->entryPrintService->generateHtmlPreview($id);
            
            return admin_view('preview', compact('html'));
        } catch (\Exception $e) {
            Log::error('预览入库单失败', [
                'entry_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return admin_error('预览入库单失败', $e->getMessage());
        }
    }

    /**
     * 获取入库单详情
     *
     * @param int $id 入库单ID
     * @return JsonResponse
     */
    public function getEntryDetails($id)
    {
        try {
            // 使用 findOrFail 确保入库单存在
            $entry = TecH3cWarehouseEntryModel::with([
                'order',
                'items' => function($query) {
                    $query->with([
                        'orderItem.productlist',  // 加载订单明细的产品信息
                        'orderItem.unitInfo',     // 加载单位信息
                        'product',                // 加载产品信息
                        'location'                // 加载货位信息
                    ]);
                }
            ])->select(['id', 'order_id', 'warehouse_id', 'entry_date', 'remarks'])->findOrFail($id);

            // 记录详细的调试日志
            Log::info('获取入库单详情 - 原始数据', [
                'entry_id' => $id,
                'entry' => $entry->toArray()
            ]);

            // 准备返回的数据
            $details = [
                'entry' => [
                    'id' => $entry->id,
                    'order_id' => $entry->order_id,
                    'warehouse_id' => $entry->warehouse_id,
                    'entry_date' => $entry->entry_date,
                    'remarks' => $entry->remarks
                ],
                'items' => $entry->items->map(function ($item) {
                    // 获取产品信息
                    $product = optional($item->orderItem)->productlist;
                    
                    return [
                        'id' => $item->id,
                        'order_item_id' => $item->order_item_id,
                        'product_id' => $item->product_id,
                        'location_id' => $item->location_id,
                        'location_name' => optional($item->location)->name ?? '未指定货位',
                        'current_quantity' => $item->actual_quantity,  // 当前入库数量
                        'actual_quantity' => $item->actual_quantity,  // 新的入库数量默认为当前数量
                        'product_name' => $product ? $product->product : '未知商品',
                        'product_bom' => $product ? $product->product_bom : '-',
                        'product_zokusei' => $product ? $product->product_zokusei : '-',
                        'unit_name' => optional($item->orderItem->unitInfo)->unit_name ?? '-',
                        'planned_quantity' => $item->planned_quantity ?? 0
                    ];
                })->toArray()
            ];

            // 记录最终返回的数据
            Log::info('获取入库单详情 - 返回数据', [
                'entry_id' => $id,
                'details' => $details
            ]);

            return response()->json($details);
        } catch (\Exception $e) {
            Log::error('获取入库单详情失败', [
                'entry_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => '获取入库单详情失败',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * 调试订单明细获取
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function debugOrderItems(Request $request)
    {
        try {
            $orderId = $request->input('order_id');
            if (!$orderId) {
                Log::warning('调试订单明细：未提供订单ID');
                return response()->json([
                    'status' => false,
                    'message' => '订单ID不能为空',
                    'data' => []
                ], 400);
            }

            // 使用更安全的查询方式
            $query = TecH3cPurchaseOrderItemModel::query()
                ->where('order_id', $orderId);

            // 记录查询信息
            Log::info('调试订单明细查询', [
                'order_id' => $orderId,
                'query_sql' => $query->toSql(),
                'query_bindings' => $query->getBindings()
            ]);

            $items = $query->get();

            // 检查是否有数据
            if ($items->isEmpty()) {
                Log::warning('调试订单明细：未找到相关数据', [
                    'order_id' => $orderId
                ]);
                return response()->json([
                    'status' => false,
                    'message' => '未找到该订单的明细数据',
                    'data' => []
                ], 404);
            }

            // 获取关联的产品信息，增加更多防御性编程
            $productDetails = $items->map(function($item) {
                try {
                    $productlist = $item->productlist;
                    $unitInfo = $item->unitInfo;

                    return [
                        'item_id' => $item->id ?? null,
                        'order_id' => $item->order_id ?? null,
                        'product_id' => $item->product ?? null,
                        'unit_id' => $item->unit ?? null,
                        'quantity' => $item->quantity ?? 0,
                        'productlist' => $productlist ? [
                            'id' => $productlist->id ?? null,
                            'product' => $productlist->product ?? '未知商品',
                            'product_bom' => $productlist->product_bom ?? null
                        ] : null,
                        'unitInfo' => $unitInfo ? [
                            'id' => $unitInfo->id ?? null,
                            'unit_name' => $unitInfo->unit_name ?? '未知单位'
                        ] : null
                    ];
                } catch (\Exception $e) {
                    Log::error('处理单个订单明细项时发生错误', [
                        'error' => $e->getMessage(),
                        'item_id' => $item->id ?? 'unknown'
                    ]);
                    return null;
                }
            })->filter(); // 移除可能的 null 值

            // 记录处理结果
            Log::info('调试订单明细处理结果', [
                'order_id' => $orderId,
                'total_items' => $items->count(),
                'processed_items_count' => count($productDetails)
            ]);

            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $productDetails
            ]);
        } catch (\Exception $e) {
            // 记录完整的异常信息
            Log::error('调试订单明细获取失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'status' => false,
                'message' => '获取订单明细失败：' . $e->getMessage(),
                'data' => [],
                'debug_info' => config('app.debug') ? [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ] : null
            ], 500);
        }
    }

    /**
     * 详情页面
     *
     * @param int $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecH3cWarehouseEntryModel(), function (Show $show) {
            // 配置详情页面展示的字段
            $show->field('id', 'ID');
            $show->field('entry_no', '入库单号');
            $show->field('order_id', '采购订单ID');
            $show->field('warehouse_id', '仓库ID');
            $show->field('entry_date', '入库日期');
            $show->field('remarks', '备注');
            
            // 关联关系展示
            $show->relation('order', function ($model) {
                return $model->order_no;
            }, '采购订单');
            
            $show->relation('warehouse', function ($model) {
                return $model->name;
            }, '入库仓库');
        });
    }

    /**
     * 打印入库单
     *
     * @param int $id
     * @return mixed
     */
    public function print($id)
    {
        try {
            // 获取入库单数据
            $entry = TecH3cWarehouseEntryModel::with([
                'items' => function($query) {
                    $query->with([
                        'productlist',
                        'orderItem.unitInfo',
                        'location',
                        'product'
                    ]);
                },
                'warehouse',
                'order',
            ])//->select(['id', 'entry_no', 'warehouse_id', 'order_id', 'created_by', 'remarks'])
              ->findOrFail($id);
            
            // 详细日志记录
            Log::info('打印入库单详细信息', [
                'entry_id' => $id,
                'entry_no' => $entry->entry_no,
                'warehouse' => optional($entry->warehouse)->name,
                'remarks' => $entry->remarks,
                'items_count' => $entry->items->count(),
                'items' => $entry->items->map(function ($item) {
                    return [
                        'product' => optional($item->productlist)->product,
                        'product_zokusei' => optional($item->productlist)->product_zokusei,
                        'unit' => optional($item->orderItem->unitInfo)->unit_name,
                        'location' => optional($item->location)->location_name,
                    ];
                })->toArray()
            ]);
            
            // 获取公司信息
            $companyInfo = \App\Models\TecH3cCompanyInfoModel::first();

            // 返回确认视图
            return view('admin.h3c.tec_h3c_warehouse_entry_confirm', compact('entry', 'companyInfo'));
            
        } catch (\Exception $e) {
            Log::error('处理单个订单明细项时发生错误', [
                        'error' => $e->getMessage(),
                        'item_id' => $item->id ?? 'unknown'
                    ]);
        }
    }
    
    /**
     * 取消入库单
     *
     * @param int $id 入库单ID
     * @return JsonResponse|RedirectResponse
     */
    public function cancel($id)
    {
        try {
            // 开始事务处理
            return DB::transaction(function() use ($id) {
                // 获取入库单信息
                $entry = TecH3cWarehouseEntryModel::with('items')->findOrFail($id);
                
                // 
                Log::info('开始取消入库单', [
                    'entry_id' => $id,
                    'entry_no' => $entry->entry_no,
                    'warehouse_id' => $entry->warehouse_id,
                    'order_id' => $entry->order_id,
                    'items_count' => $entry->items->count()
                ]);
                
                // 退回入库单关联的库存
                foreach ($entry->items as $item) {
                    $this->reverseInventory($entry, $item);
                    
                    // 标记明细项已处理过库存退回，避免删除时再次减少库存
                    DB::table('t_warehouse_entry_items_h3c')
                        ->where('id', $item->id)
                        ->update(['processed_inventory_return' => 1]);
                    
                    Log::info('已标记入库明细库存已退回', [
                        'entry_item_id' => $item->id,
                        'product_id' => $item->product_id
                    ]);
                }
                
                // 删除入库单明细
                foreach ($entry->items as $item) {
                    // 先记录要删除的信息
                    Log::info('准备删除入库单明细', [
                        'entry_item_id' => $item->id,
                        'product_id' => $item->product_id,
                        'location_id' => $item->location_id,
                        'quantity' => $item->actual_quantity,
                        'processed_inventory_return' => true
                    ]);
                    
                    // 然后删除明细
                    $item->delete();
                }
                
                // 删除入库单
                $entryNo = $entry->entry_no;
                $entry->delete();
                
                Log::info('入库单取消成功', [
                    'entry_id' => $id,
                    'entry_no' => $entryNo
                ]);
                
                // 返回结果
                if (request()->ajax() || request()->wantsJson()) {
                    return response()->json([
                        'status' => true,
                        'message' => '入库单取消成功'
                    ]);
                }
                
                admin_success('入库单取消成功');
                return redirect()->route('h3c.warehouse-entries.index');
            });
            
        } catch (\Exception $e) {
            Log::error('取消入库单失败', [
                'entry_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            if ($e instanceof \Illuminate\Database\QueryException
                && strpos($e->getMessage(), 't_h3c_warehouse_entry_sn') !== false
            ) {
                if (request()->ajax() || request()->wantsJson()) {
                    return response()->json([
                        'status' => false,
                        'message' => '请先解绑SN',
                    ], 400);
                }
                admin_error('请先解绑SN');
                return back();
            }
            
            if (request()->ajax() || request()->wantsJson()) {
                return response()->json([
                    'status' => false,
                    'message' => '取消入库单失败: ' . $e->getMessage()
                ], 500);
            }
            
            admin_error('取消入库单失败: ' . $e->getMessage());
            return back();
        }
    }
    
    /**
     * 退回库存记录
     *
     * @param TecH3cWarehouseEntryModel $entry 入库单
     * @param TecH3cWarehouseEntryItemModel $item 入库单明细
     * @return bool
     * @throws \Exception
     */
    protected function reverseInventory(TecH3cWarehouseEntryModel $entry, TecH3cWarehouseEntryItemModel $item)
    {
        // 查找库存记录
        $inventory = TecH3cWarehouseInventoryModel::where([
            'product_id' => $item->product_id,
            'warehouse_id' => $entry->warehouse_id,
            'location_id' => $item->location_id,
            'batch_no' => $entry->batch_no
        ])->first();
        
        if (!$inventory) {
            Log::warning('取消入库单时未找到对应的库存记录', [
                'entry_item_id' => $item->id,
                'product_id' => $item->product_id,
                'warehouse_id' => $entry->warehouse_id,
                'location_id' => $item->location_id
            ]);
            return false;
        }
        
        // 记录当前库存
        $beforeQuantity = $inventory->quantity;
        $deductQuantity = $item->actual_quantity;
        
        // 检查库存是否足够
        if ($inventory->available_quantity < $deductQuantity) {
            // 获取产品信息
            $product = \App\Models\TecH3cProductListModel::find($item->product_id);
            $productName = $product ? $product->product : '';
            $productBom = $product ? $product->product_bom : '';
            // 获取货位信息
            $locationName = '';
            if ($item->location_id) {
                $location = \App\Models\TecWarehouseLocationModel::find($item->location_id);
                $locationName = $location ? $location->location_name : '';
            }
            throw new \Exception(
                sprintf('可用库存不足，无法取消入库单。商品ID: %d, BOM: %s, 商品名: %s, 货位: %s, 当前可用库存: %d, 需要减少: %d',
                    $item->product_id, $productBom, $productName, $locationName, $inventory->available_quantity, $deductQuantity)
            );
        }
        
        // 减少库存
        if (!$inventory->decreaseQuantity($deductQuantity)) {
            throw new \Exception('减少库存失败: 商品ID=' . $item->product_id);
        }
        
        // 记录日志
        Log::info('取消入库单时减少库存成功', [
            'entry_id' => $entry->id,
            'entry_item_id' => $item->id,
            'inventory_id' => $inventory->id,
            'product_id' => $item->product_id,
            'quantity_deducted' => $deductQuantity,
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $inventory->quantity
        ]);
        
        // 获取产品的默认单位ID
        $unitId = $item->unit_id;
        if (empty($unitId)) {
            // 如果商品明细中没有单位ID，尝试从产品中获取
            $product = TecH3cProductListModel::find($item->product_id);
            $unitId = $product ? $product->unit_id : null;
            
            Log::info('单位', [
                'entry_item_id' => $item->id,
                'product_id' => $item->product_id,
                'default_unit_id' => $unitId
            ]);

            // 如果还是没有，使用系统中第一个单位作为默认值
            if (empty($unitId)) {
                $defaultUnit = TecProductUnitModel::first();
                $unitId = $defaultUnit ? $defaultUnit->id : 1; // 最后使用1作为默认值
            }
            
            Log::info('取消入库单时未找到单位ID，使用默认单位', [
                'entry_item_id' => $item->id,
                'product_id' => $item->product_id,
                'default_unit_id' => $unitId
            ]);
        }
        
        // 确保批次号有值
        $batchNo = $entry->batch_no ?: '无批次';
        
        // 获取有效的操作员ID
        $adminId = auth()->id();
        
        // 如果当前没有登录用户，获取系统中的第一个管理员作为默认操作员
        if (empty($adminId)) {
            $admin = \Dcat\Admin\Models\Administrator::first();
            $adminId = $admin ? $admin->id : null;
            
            if (empty($adminId)) {
                throw new \Exception('无法获取有效的操作员ID，请确保系统中至少有一个管理员账户');
            }
            
            Log::info('取消入库单时未找到当前用户ID，使用默认管理员', [
                'entry_id' => $entry->id,
                'default_admin_id' => $adminId
            ]);
        }
        
        // 创建取消入库的库存交易记录
        TecH3cWarehouseTransactionModel::create([
            'transaction_no' => TecH3cWarehouseTransactionModel::generateTransactionNo(),
            'type' => TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_CANCEL_IN, // 取消入库类型
            'source_type' => 'warehouse_entry',
            'source_id' => $entry->id,
            'source_no' => $entry->entry_no,
            'source_item_id' => $item->id,
            'order_type' => 'purchase', // 采购订单
            'order_id' => $entry->order_id,
            'order_no' => optional($entry->order)->order_no,
            'order_item_id' => $item->order_item_id,
            'product_id' => $item->product_id,
            'warehouse_id' => $entry->warehouse_id,
            'location_id' => $item->location_id,
            'unit_id' => $unitId,
            'batch_no' => $batchNo,
            'quantity' => -$deductQuantity, // 负数表示减少
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $inventory->quantity,
            'operator_id' => $adminId,
            'remarks' => sprintf('取消入库单 %s 自动生成的库存交易记录', $entry->entry_no)
        ]);
        
        return true;
    }

    /**
     * 处理仓库变更请求
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleWarehouseChanged($id, Request $request)
    {
        try {
            $newWarehouseId = $request->input('new_warehouse_id');
            
            if (!$newWarehouseId) {
                return response()->json([
                    'status' => false,
                    'message' => '缺少新仓库ID参数'
                ]);
            }
            
            // 获取入库单
            $entry = TecH3cWarehouseEntryModel::findOrFail($id);
            $oldWarehouseId = $entry->warehouse_id;
            
            // 记录仓库变更请求
            Log::info('仓库变更请求', [
                'entry_id' => $id,
                'old_warehouse_id' => $oldWarehouseId,
                'new_warehouse_id' => $newWarehouseId,
                'request_data' => $request->all()
            ]);
            
            // 更新仓库ID
            $entry->warehouse_id = $newWarehouseId;
            
            // 添加is_edit_change标记,明确标识这是编辑界面的变更
            $request->merge(['is_edit_change' => 1]);
            
            // 保存入库单,触发Observer
            $entry->save();
            
            // 获取默认货位
            $defaultLocation = TecWarehouseLocationModel::where('warehouse_id', $newWarehouseId)
                ->where('status', 'available')
                ->first();
                
            if (!$defaultLocation) {
                return response()->json([
                    'status' => false,
                    'message' => '新仓库没有可用的货位，请先添加货位'
                ]);
            }
            
            Log::info('仓库变更处理完成', [
                'entry_id' => $id,
                'new_warehouse_id' => $newWarehouseId,
                'default_location_id' => $defaultLocation->id
            ]);
            
            return response()->json([
                'status' => true,
                'message' => '仓库变更成功，请为商品选择新的货位',
                'refresh' => true
            ]);
        } catch (\Exception $e) {
            Log::error('仓库变更处理失败', [
                'entry_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '仓库变更失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 修复指定产品的库存可用数量
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function fixInventoryAvailableQuantity(Request $request)
    {
        try {
            $productId = $request->input('product_id');
            $warehouseId = $request->input('warehouse_id');
            
            $entryItemService = app(\App\Services\Warehouse\Entry\TecH3cWarehouseEntryItemService::class);
            $count = $entryItemService->fixInventoryAvailableQuantity($productId, $warehouseId);
            
            return response()->json([
                'status' => true,
                'message' => "成功修复{$count}条库存记录的可用数量",
                'count' => $count
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '修复失败: ' . $e->getMessage()
            ], 500);
        }
    }

}
