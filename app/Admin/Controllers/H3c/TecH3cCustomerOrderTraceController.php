<?php

namespace App\Admin\Controllers\H3c;

use App\Admin\Config\PurchaseConfig;
use App\Models\TecH3cCustomerPurchaseOrderItemModel;
use App\Models\TecH3cCustomerPurchaseOrderModel;
use App\Models\TecH3cProductListModel;
use App\Models\TecWarehouseLocationModel;
use App\Models\TecWarehouseModel;
use App\Models\TecH3cWarehouseExitItemModel;
use App\Models\TecH3cWarehouseExitModel;
use App\Models\TecH3cWarehouseInventoryModel;
use App\Models\TecH3cWarehouseTransactionModel;
use App\Models\TecH3cWarehouseExitSourceModel;
use App\Models\TecSndCompanyModel;
use App\Models\TecSndContactModel;
use App\Models\TecProductUnitModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\DB;

/**
 * H3C受注订单追溯控制器
 * 
 * 基于受注订单的追溯视图,可以显示受注订单-出库单的完整链路
 */
class TecH3cCustomerOrderTraceController extends AdminController
{
    /**
     * 标题
     * 
     * @var string
     */
    protected $title = '受注订单追溯';

    /**
     * 获取产品属性对应的颜色
     *
     * @param string $zokusei 属性值
     * @return string 颜色名称
     */
    protected function getZokuseiColor($zokusei)
    {
        $colors = [
            'normal' => 'primary',
            'rr' => 'warning',
            'inspection' => 'info',
            'N' => 'success',
            'R' => 'danger',
            'I' => 'info'
        ];
        
        return $colors[$zokusei] ?? 'default';
    }

    /**
     * 批量预加载数据以优化性能
     */
    private function preloadBulkData()
    {
        // 使用缓存避免重复查询，只选择必要字段
        $warehouses = \Illuminate\Support\Facades\Cache::remember('customer_trace_warehouses', 3600, function() {
            return TecWarehouseModel::select('id', 'name')->get()->keyBy('id');
        });
        
        $locations = \Illuminate\Support\Facades\Cache::remember('customer_trace_locations', 3600, function() {
            return TecWarehouseLocationModel::select('id', 'area_number', 'shelf_number', 'level_number', 'warehouse_id')->get()->keyBy('id');
        });
        
        $units = \Illuminate\Support\Facades\Cache::remember('customer_trace_units', 3600, function() {
            return TecProductUnitModel::select('id', 'unit_name')->get()->keyBy('id');
        });
        
        $companies = \Illuminate\Support\Facades\Cache::remember('customer_trace_companies', 3600, function() {
            return TecSndCompanyModel::select('id', 'name', 'company_color', 'notes')->get()->keyBy('id');
        });
        
        $contacts = \Illuminate\Support\Facades\Cache::remember('customer_trace_contacts', 3600, function() {
            return TecSndContactModel::select('id', 'first_name', 'last_name', 'phone', 'phone_landline', 'email')->get()->keyBy('id');
        });
        
        // 将数据存储在服务容器中供后续使用
        app()->instance('bulk_warehouses', $warehouses);
        app()->instance('bulk_locations', $locations);
        app()->instance('bulk_units', $units);
        app()->instance('bulk_companies', $companies);
        app()->instance('bulk_contacts', $contacts);
        
        // 预加载库存和出库数据
        $this->preloadInventoryData();
        $this->preloadExitData();
    }
    
    /**
     * 预加载库存数据
     */
    private function preloadInventoryData()
    {
        // 获取所有相关的库存数据
        $inventories = TecH3cWarehouseInventoryModel::select([
            'id', 'product_id', 'location_id', 'warehouse_id', 'quantity', 
            'available_quantity', 'locked_quantity', 'status'
        ])->get();
        
        // 按产品ID分组
        $inventoryByProduct = $inventories->groupBy('product_id');
        
        app()->instance('bulk_inventories', $inventoryByProduct);
    }
    
    /**
     * 预加载出库数据
     */
    private function preloadExitData()
    {
        // 获取所有出库明细（包含基本的仓库货位信息）
        $exitItems = TecH3cWarehouseExitItemModel::with([
            'exit:id,exit_no,exit_date,exit_type,warehouse_id',
            'sources:id,exit_item_id,purchase_order_item_id,quantity,purchase_order_no'
        ])->get();
        
        // 按客户订单明细ID分组
        $exitItemsByOrderItem = $exitItems->groupBy('customer_order_item_id');
        
        app()->instance('bulk_exit_items', $exitItemsByOrderItem);
    }

    /**
     * 首页
     * 
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header($this->title)
            ->description('从受注订单追溯出库记录')
            ->body($this->grid());
    }

    /**
     * 详情页
     * 
     * @param int $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content)
    {
        return $content
            ->header($this->title)
            ->description('详情')
            ->body($this->detail($id));
    }

    /**
     * 表格
     * 
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecH3cCustomerPurchaseOrderItemModel(), function (Grid $grid) {
            $grid->scrollbarX();
            // 添加边框线
            $grid->withBorder();

            // 预加载批量数据
            $this->preloadBulkData();

            // 优化关联加载 - 添加更多预加载关系
            $grid->model()->with([
                'order:id,order_no,case_name,business_date,sndCompany_id,contact_person',
                'order.supplier:id,name,company_color',
                'order.contact:id,first_name,last_name,phone,phone_landline,email',
                'productlist:id,product,product_bom,product_zokusei'
            ])->orderBy('order_id', 'asc');
            
            // 受注单信息
            $grid->column('order.order_no', '受注单号')->display(function($orderNo) {
                $order = $this->order ?? null;
                if (!$order) return $orderNo;
                
                // 添加链接到受注单列表并过滤
                $url = admin_url('r_h3c_customer_orders') . '?order_no=' . $orderNo;
                $html = "<div style='font-weight:bold;'><a href='{$url}' target='_blank'>{$orderNo}</a></div>";
                $html .= "<div>项目: {$order->case_name}</div>";
                $html .= "<div>日期: " . date('Y-m-d', strtotime($order->business_date ?? now())) . "</div>";
                
                return $html;
            });
            
            // 客户信息 - 添加公司颜色支持
            $grid->column('order.supplier.name', '客户名称')->display(function($name) {
                $order = $this->order ?? null;
                if (!$order) return $name;
                
                $supplier = $order->supplier ?? null;
                $contact = $order->contact ?? null;
                
                // 获取公司颜色
                $companyColor = $supplier->company_color ?? '#007bff';
                
                // 添加备注信息到公司名称
                $displayName = $name;
                if (!empty($supplier->notes)) {
                    $displayName .= " ({$supplier->notes})";
                }
                
                // 使用公司颜色作为边框和文字颜色
                $html = "<div style='font-weight:bold; border-left: 4px solid {$companyColor}; padding-left: 8px; color: {$companyColor};'>{$displayName}</div>";
                
                if ($contact) {
                    $html .= "<div style='padding-left: 12px;'>联系人: {$contact->full_name}</div>";
                    $html .= "<div style='padding-left: 12px;'>电话: " . ($contact->phone ?: ($contact->phone_landline ?: '-')) . "</div>";
                    $html .= "<div style='padding-left: 12px;'>邮箱: " . ($contact->email ?: '-') . "</div>";
                }
                
                return $html;
            });
            
            // 产品信息 - 使用预加载的产品数据
            $grid->column('product_info', '产品信息')->display(function() {
                try {
                    // 尝试从关联的产品表获取信息
                    $product = $this->productlist ?? null;
                    
                    // 获取产品名称 - 只从关联产品表获取
                    $productName = '';
                    if ($product) {
                        $productName = $product->product ?? '';
                    }
                    
                    // 获取 BOM - 优先从订单明细表，然后从关联产品表
                    $productBom = $this->product_bom ?? '';
                    if (empty($productBom) && $product) {
                        $productBom = $product->product_bom ?? '';
                    }
                    
                    $html = '';
                    
                    // 显示产品名称
                    if (!empty($productName)) {
                        $html .= "<div style='font-weight:bold; margin-bottom:2px;'>{$productName}</div>";
                    }
                    
                    // 显示 BOM
                    if (!empty($productBom)) {
                        $html .= "<div style='color:#666; font-size:0.9em;'>BOM: {$productBom}</div>";
                    }
                    
                    // 如果都没有内容，返回默认值
                    if (empty($html)) {
                        return '<span style="color:#999;">无产品信息</span>';
                    }
                    
                    return $html;
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('产品信息显示错误: ' . $e->getMessage());
                    return '<span style="color:#f00;">产品信息加载失败</span>';
                }
            });
            
            // 产品属性
            $grid->column('product_zokusei', '产品属性')->display(function () {
                try {
                    $product = $this->product ?? null;
                    if (!$product) {
                        return '-';
                    }
                    
                    // 首先尝试从关联的产品表获取属性，然后从订单明细表获取
                    $product = $this->productlist ?? null;
                    $rawZokusei = $product->product_zokusei ?? $this->product_zokusei ?? 'normal';
                    
                    // 使用 PurchaseConfig 的规范化方法
                    $productZokusei = PurchaseConfig::normalizeProductZokusei($rawZokusei);
                    
                    if (empty($productZokusei)) {
                        return '-';
                    }
                    
                    // 使用 PurchaseConfig 获取正确的中文文本
                    $zokuseiMap = PurchaseConfig::getProductZokusei();
                    $zokuseiText = $zokuseiMap[$productZokusei] ?? $productZokusei;
                    
                    // 统一使用 badge 样式，与库存状态和出库状态保持一致
                    $badgeClass = match($productZokusei) {
                        'rr' => 'badge-primary',
                        'inspection' => 'badge-warning', 
                        'normal' => 'badge-secondary',
                        default => 'badge-secondary'
                    };
                    
                    return "<span class='badge {$badgeClass}'>{$zokuseiText}</span>";
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('产品属性显示错误: ' . $e->getMessage());
                    return '-';
                }
            });
            
            // 订单数量
            $grid->column('quantity', '订单数量');
            
            // 库存状态 - 使用预加载的库存数据
            $grid->column('inventory_status', '库存状态')->display(function() {
                try {
                    $productId = $this->product_id ?? $this->product ?? null;
                    if (empty($productId)) {
                        return '-';
                    }
                    
                    // 从预加载的数据中获取库存
                    $inventoryByProduct = app('bulk_inventories');
                    $inventories = $inventoryByProduct->get($productId, collect());
                    
                    $totalQuantity = $inventories->sum('quantity');
                    
                    if ($totalQuantity > 0) {
                        return "<span class='badge badge-success'>在库: {$totalQuantity}</span>";
                    } else {
                        return "<span class='badge badge-secondary'>无库存</span>";
                    }
                } catch (\Exception $e) {
                    return '-';
                }
            });
            
            // 出库状态 - 使用预加载的出库数据
            $grid->column('exit_status', '出库状态')->display(function() {
                try {
                    $id = $this->getKey();
                    
                    // 从预加载的数据中获取出库明细
                    $exitItemsByOrderItem = app('bulk_exit_items');
                    $exitItems = $exitItemsByOrderItem->get($id, collect());
                    
                    if ($exitItems->isEmpty()) {
                        return '<span class="badge badge-secondary">未出库</span>';
                    }
                    
                    $totalQuantity = $exitItems->sum('quantity');
                    
                    if ($totalQuantity >= ($this->quantity ?? 0)) {
                        return '<span class="badge badge-success">已出库</span>';
                    } else {
                        $percent = floor(($totalQuantity / ($this->quantity ?? 1)) * 100);
                        return "<span class='badge badge-warning'>部分出库({$percent}%)</span>";
                    }
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('出库状态显示错误: ' . $e->getMessage());
                    return '-';
                }
            });
            
            // 出库信息 - 使用预加载的数据
            $grid->column('exit_info', '出库信息')->display(function() {
                try {
                    $id = $this->getKey();
                    
                    // 从预加载的数据中获取出库明细
                    $exitItemsByOrderItem = app('bulk_exit_items');
                    $exitItems = $exitItemsByOrderItem->get($id, collect());
                    
                    // 如果没有出库记录
                    if ($exitItems->isEmpty()) {
                        return '-';
                    }
                    
                    // 获取预加载的数据
                    $warehouses = app('bulk_warehouses');
                    $locations = app('bulk_locations');
                    
                    // 构建HTML
                    $html = '';
                    
                    // 显示出库信息
                    foreach ($exitItems as $item) {
                        $exit = $item->exit ?? null;
                        if (!$exit) continue;
                        
                        $html .= "<div class='mb-2'>";
                        
                        // 添加链接到出库单详情
                        $exitUrl = admin_url('r_h3c_warehouse_exit') . '?exit_no=' . $exit->exit_no;
                        $html .= "<div><strong><a href='{$exitUrl}' target='_blank'>{$exit->exit_no}</a></strong></div>";
                        $html .= "<div>出库日期: " . ($exit->exit_date ? date('Y-m-d', strtotime($exit->exit_date)) : '-') . "</div>";
                        $html .= "<div>出库数量: " . intval($item->quantity ?? 0) . "</div>";
                        $html .= "<div>出库类型: " . ($exit->exit_type === 'sales' ? 'sales' : $exit->exit_type) . "</div>";
                        
                        // 添加仓库货位信息 - 使用出库明细表中的信息
                        $warehouseLocationInfo = '';
                        
                        // 首先尝试从出库明细表获取仓库货位信息
                        if ($item->warehouse_id && $item->location_id) {
                            $warehouse = $warehouses->get($item->warehouse_id);
                            $location = $locations->get($item->location_id);
                            
                            $warehouseName = $warehouse ? $warehouse->name : '未知仓库';
                            $locationName = $location ? $location->location_name : '未知货位';
                            
                            $warehouseLocationInfo .= "<div>仓库: {$warehouseName}</div>";
                            $warehouseLocationInfo .= "<div>货位: {$locationName}</div>";
                        }
                        // 如果出库明细没有仓库货位信息，尝试从出库单获取仓库信息
                        elseif ($exit->warehouse_id) {
                            $warehouse = $warehouses->get($exit->warehouse_id);
                            if ($warehouse) {
                                $warehouseLocationInfo .= "<div>仓库: {$warehouse->name}</div>";
                            }
                        }
                        
                        // 添加仓库货位信息
                        if (!empty($warehouseLocationInfo)) {
                            $html .= $warehouseLocationInfo;
                        }
                        
                        $html .= "</div>";
                        
                        if ($exitItems->count() > 1) {
                            $html .= "<hr style='margin:5px 0;'>";
                        }
                    }
                    
                    return empty($html) ? '-' : $html;
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('出库信息显示错误: ' . $e->getMessage());
                    return '-';
                }
            });
            
            // 禁用创建按钮
            $grid->disableCreateButton();
            
            // 禁用编辑按钮
            $grid->disableEditButton();
            $grid->disableQuickEditButton();
            
            // 禁用删除按钮
            $grid->disableDeleteButton();
            
            // 禁用批量删除
            $grid->disableBatchDelete();
            $grid->disableActions();
            $grid->disableBatchActions();
            
            // 设置每页显示条数
            $grid->paginate(20);
            
            // 设置默认排序
            $grid->model()->orderBy('id', 'desc');
            
            // 添加筛选器
            $grid->filter(function($filter) {
                // 展开筛选器
                $filter->expand();
                
                // 订单号筛选
                $filter->like('order.order_no', '受注单号')->width(2);
                
                // 客户筛选 - 改为下拉列表
                $filter->equal('order.sndCompany_id', '客户名称')->select(
                    \App\Models\TecSndCompanyModel::where('snd_role', 'client')
                        ->get()
                        ->mapWithKeys(function($company) {
                            $displayName = $company->name;
                            if (!empty($company->notes)) {
                                $displayName .= " ({$company->notes})";
                            }
                            return [$company->id => $displayName];
                        })
                        ->toArray()
                )->width(3);
            
            // 产品筛选
            $filter->like('product.product', '产品名称')->width(2);
            $filter->like('product.product_bom', '产品BOM')->width(2);
            
            // 出库状态筛选
            $filter->where('出库状态', function($query) {
                $value = request()->input('value');
                $query->whereHas('exitItems', function($q) use ($value) {
                    if ($value === '0') {
                        $q->whereNull('id');
                    } elseif ($value === '1') {
                        $q->whereNotNull('id');
                    }
                });
            })->select([
                '0' => '未出库',
                '1' => '已出库'
            ])->width(2);
            });
        });
    }

    /**
     * 详情
     * 
     * @param int $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecH3cCustomerPurchaseOrderItemModel(), function (Show $show) {
            // 预加载批量数据
            $this->preloadBulkData();
            
            // 禁用编辑按钮
            $show->disableEditButton();
            
            // 禁用删除按钮
            $show->disableDeleteButton();
            
            // 订单信息
            $show->field('order.order_no', '受注单号')->as(function($orderNo) {
                if (empty($orderNo)) return '-';
                $url = admin_url('r_h3c_customer_orders') . '?order_no=' . $orderNo;
                return "<a href='{$url}' target='_blank'>{$orderNo}</a>";
            });
            
            // 客户名称 - 添加公司颜色支持
            $show->field('order.supplier.name', '客户名称')->as(function($name) {
                $order = $this->order ?? null;
                if (!$order) return $name;
                
                $supplier = $order->supplier ?? null;
                if (!$supplier) return $name;
                
                $companyColor = $supplier->company_color ?? '#007bff';
                
                return "<span style='border-left: 4px solid {$companyColor}; padding-left: 8px; color: {$companyColor}; font-weight: bold;'>{$name}</span>";
            });
            
            $show->field('order.contact.full_name', '联系人');
            $show->field('order.case_name', '案件名');
            $show->field('order.business_date', '业务日期');
            
            // 产品信息
            $show->field('product_info', '产品信息')->as(function() {
                $product = $this->productlist ?? null;
                if (!$product) return '-';
                
                $productName = $product->product ?? '';
                $productBom = $this->product_bom ?? $product->product_bom ?? '';
                
                $html = "<div style='margin-bottom:10px;'><strong>名称:</strong> {$productName}</div>";
                
                if (!empty($productBom)) {
                    $html .= "<div style='margin-bottom:10px;'><strong>BOM:</strong> {$productBom}</div>";
                }
                
                // 产品属性
                // 首先尝试从关联的产品表获取属性，然后从订单明细表获取
                $rawZokusei = $product->product_zokusei ?? $this->product_zokusei ?? 'normal';
                
                // 使用 PurchaseConfig 的规范化方法
                $productZokusei = PurchaseConfig::normalizeProductZokusei($rawZokusei);
                
                if (!empty($productZokusei)) {
                    // 使用 PurchaseConfig 获取正确的中文文本
                    $zokuseiMap = PurchaseConfig::getProductZokusei();
                    $zokuseiText = $zokuseiMap[$productZokusei] ?? $productZokusei;
                    
                    // 统一使用 badge 样式，与其他状态列保持一致
                    $badgeClass = match($productZokusei) {
                        'rr' => 'badge-primary',
                        'inspection' => 'badge-warning', 
                        'normal' => 'badge-secondary',
                        default => 'badge-secondary'
                    };
                    
                    $html .= "<div style='margin-bottom:10px;'><strong>属性:</strong> <span class='badge {$badgeClass}'>{$zokuseiText}</span></div>";
                }
                
                // 订单数量
                $quantity = $this->quantity ?? 0;
                $html .= "<div><strong>订单数量:</strong> {$quantity}</div>";
                
                // 添加库存汇总信息
                $html .= $this->getInventorySummary($product->id);
                
                return $html;
            });
            
            // 出库状态
            $show->field('exit_status', '出库状态')->as(function() {
                $id = $this->getKey();
                
                // 从预加载的数据中获取出库明细
                $exitItemsByOrderItem = app('bulk_exit_items');
                $exitItems = $exitItemsByOrderItem->get($id, collect());
                
                if ($exitItems->isEmpty()) {
                    return '<span class="badge badge-secondary">未出库</span>';
                }
                
                $totalQuantity = $exitItems->sum('quantity');
                
                if ($totalQuantity >= ($this->quantity ?? 0)) {
                    return '<span class="badge badge-success">已出库</span>';
                } else {
                    return '<span class="badge badge-warning">部分出库</span>';
                }
            });
            
            // 出库信息
            $show->field('exit_info', '出库信息')->as(function() {
                $id = $this->getKey();
                $productId = $this->product_id ?? $this->product ?? null;
                
                // 从预加载的数据中获取出库明细
                $exitItemsByOrderItem = app('bulk_exit_items');
                $exitItems = $exitItemsByOrderItem->get($id, collect());
                
                // 获取预加载的数据
                $warehouses = app('bulk_warehouses');
                $locations = app('bulk_locations');
                $inventoryByProduct = app('bulk_inventories');
                
                // 构建HTML
                $html = '';
                
                // 添加库存信息
                if ($productId) {
                    $inventories = $inventoryByProduct->get($productId, collect());
                    
                    if ($inventories->isNotEmpty()) {
                        $html .= '<div class="card mb-4">';
                        $html .= '<div class="card-header bg-light"><strong>库存位置信息</strong></div>';
                        $html .= '<div class="card-body p-0">';
                        $html .= '<table class="table table-bordered mb-0">';
                        $html .= '<thead><tr><th>仓库</th><th>货位</th><th>数量</th><th>状态</th></tr></thead>';
                        $html .= '<tbody>';
                        
                        foreach ($inventories as $inventory) {
                            $warehouse = $warehouses->get($inventory->warehouse_id);
                            $location = $locations->get($inventory->location_id);
                            
                            $warehouseName = $warehouse ? $warehouse->name : '未知仓库';
                            $locationName = $location ? $location->location_name : '未知货位';
                            
                            // 状态标签
                            $statusClass = 'success';
                            $statusText = '正常';
                            
                            if ($inventory->status == 'locked') {
                                $statusClass = 'warning';
                                $statusText = '锁定';
                            } elseif ($inventory->status == 'disabled') {
                                $statusClass = 'danger';
                                $statusText = '禁用';
                            }
                            
                            $html .= '<tr>';
                            $html .= '<td>' . $warehouseName . '</td>';
                            $html .= '<td>' . $locationName . '</td>';
                            $html .= '<td>' . ($inventory->quantity ?? 0) . '</td>';
                            $html .= '<td><span class="badge badge-' . $statusClass . '">' . $statusText . '</span></td>';
                            $html .= '</tr>';
                        }
                        
                        $html .= '</tbody></table>';
                        $html .= '</div>';
                        $html .= '</div>';
                    }
                }
                
                // 添加出库单信息
                if ($exitItems->isEmpty()) {
                    $html .= '<div class="alert alert-secondary">无出库记录</div>';
                } else {
                    $html .= '<div class="card-group" style="display:block;">';
                    
                    foreach ($exitItems as $item) {
                        $exit = $item->exit ?? null;
                        if (!$exit) continue;
                        
                        $exitDate = $exit->exit_date ? date('Y-m-d', strtotime($exit->exit_date)) : '-';
                        $exitQuantity = $item->quantity ?? 0;
                        $exitType = $exit->exit_type ?? 'sales';
                        
                        $html .= '<div class="card mb-3">';
                        $exitUrl = admin_url('r_h3c_warehouse_exit') . '?exit_no=' . $exit->exit_no;
                        $html .= '<div class="card-header bg-primary text-white"><strong>出库单: <a href="'.$exitUrl.'" target="_blank" style="color:white;text-decoration:underline;">' . $exit->exit_no . '</a></strong></div>';
                        $html .= '<div class="card-body">';
                        $html .= '<table class="table table-bordered mb-0">';
                        $html .= '<tr><th style="width:120px;">出库日期</th><td>' . $exitDate . '</td></tr>';
                        $html .= '<tr><th>出库数量</th><td>' . $exitQuantity . '</td></tr>';
                        $html .= '<tr><th>出库类型</th><td>' . $exitType . '</td></tr>';
                        
                        // 添加出库货位信息 - 使用出库明细表中的信息
                        $warehouseLocationInfo = '';
                        
                        // 首先尝试从出库明细表获取仓库货位信息
                        if ($item->warehouse_id && $item->location_id) {
                            $warehouse = $warehouses->get($item->warehouse_id);
                            $location = $locations->get($item->location_id);
                            
                            $warehouseName = $warehouse ? $warehouse->name : '未知仓库';
                            $locationName = $location ? $location->location_name : '未知货位';
                            
                            $warehouseLocationInfo .= "{$warehouseName} - {$locationName}<br>";
                        }
                        // 如果出库明细没有仓库货位信息，尝试从出库单获取仓库信息
                        elseif ($exit->warehouse_id) {
                            $warehouse = $warehouses->get($exit->warehouse_id);
                            if ($warehouse) {
                                $warehouseLocationInfo = $warehouse->name;
                            }
                        }
                        
                        $html .= '<tr><th>出库货位</th><td>' . ($warehouseLocationInfo ?: '-') . '</td></tr>';
                        
                        $html .= '</table>';
                        $html .= '</div>';
                        $html .= '</div>';
                    }
                    
                    $html .= '</div>';
                }
                
                return $html;
            });
        });
    }
    
    /**
     * 获取产品库存汇总信息
     *
     * @param int $productId 产品ID
     * @return string HTML代码
     */
    private function getInventorySummary($productId)
    {
        if (empty($productId)) {
            return '';
        }
        
        // 从预加载的数据中获取库存
        $inventoryByProduct = app('bulk_inventories');
        $inventories = $inventoryByProduct->get($productId, collect());
        
        if ($inventories->isEmpty()) {
            return '';
        }
        
        // 汇总库存数据
        $totalQuantity = $inventories->sum('quantity');
        $totalAvailable = $inventories->sum('available_quantity');
        $totalLocked = $inventories->sum('locked_quantity');
        
        $html = "<div style='margin-top:15px;'>";
        $html .= "<div><strong>库存汇总:</strong></div>";
        $html .= "<div style='margin-top:5px;'>";
        $html .= '<span class="badge badge-primary mr-1">总库存: ' . $totalQuantity . '</span> ';
        $html .= '<span class="badge badge-success mr-1">可用: ' . $totalAvailable . '</span> ';
        $html .= '<span class="badge badge-warning">锁定: ' . $totalLocked . '</span>';
        $html .= "</div>";
        $html .= "</div>";
        
        return $html;
    }
} 
