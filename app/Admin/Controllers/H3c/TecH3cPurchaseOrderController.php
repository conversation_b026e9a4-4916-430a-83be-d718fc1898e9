<?php

/*
 * // +----------------------------------------------------------------------
 * // | erp
 * // +----------------------------------------------------------------------
 * // | Copyright (c) 2006~2020 erp All rights reserved.
 * // +----------------------------------------------------------------------
 * // | Licensed ( LICENSE-1.0.0 )
 * // +----------------------------------------------------------------------
 * // | Author: yxx <<EMAIL>>
 * // +----------------------------------------------------------------------
 */

declare(strict_types=1);

namespace App\Admin\Controllers\H3c;

use App\Admin\Actions\Grid\BatchCreatePurInOrderSave;
use App\Admin\Actions\Grid\BatchOrderPrint;
use App\Admin\Actions\Grid\EditOrder;
use App\Admin\Actions\Grid\TecH3cPurchaseOrder\TecH3cPurchaseOrderCopyAction;
use App\Admin\Actions\Grid\TecH3cPurchaseOrder\TecH3cOrderItemDeleteAction;
use App\Admin\Actions\Grid\TecH3cPurchaseOrder\TecH3cPurchaseOrderPermanentDeleteAction;
use App\Admin\Actions\Post\Restore;
use App\Admin\Extensions\Form\Order\TecH3cOrderController;
use App\Admin\Extensions\Form\ReviewIconH3c;
use App\Admin\Repositories\TecH3cPurchaseOrderRepo;
use App\Models\BaseModel;
use App\Models\TecH3cPurchaseOrderItemModel;
use App\Models\TecH3cProductCategoryModel;
use App\Models\TecH3cProductListModel;
use App\Models\TecProductMajorCategoryModel;
use App\Models\TecProductUnitModel;
use App\Models\TecSndCompanyModel;
use App\Models\TecSndContactModel;
use App\Models\TecUserModel;
use App\Support\GridStyleHelper;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\Tools\BatchActions;
use App\Admin\Actions\Grid\TecH3cPurchaseOrder\TecH3cPurchaseOrderExportAction;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Yxx\LaravelQuick\Exceptions\Api\ApiRequestException;
use App\Admin\Renderable\TecH3cPurchaseOrderItemsTable;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\TecH3cPurchaseOrderExportExcel;
use App\Models\PurchaseBaseModel;
use App\Admin\Config\PurchaseConfig;
use App\Observers\TecH3cPurchaseOrderObserver;
use App\Admin\Exporters\TecH3cPurchaseOrderExporter;
use Illuminate\Support\Collection;
use Illuminate\Http\Response;
use App\Admin\Actions\Grid\TecH3cPurchaseOrder\TecH3cPurchaseOrderPaymentAction;
use App\Models\TecH3cPurchaseOrderModel;
use App\Models\TecH3cPurchaseBaseModel;

/**
 * H3C 采购订单控制器
 * 创建模式：form() -> setForm() -> creating()
 * 编辑模式：form() -> editing() -> setForm()
 * 
 */
class TecH3cPurchaseOrderController extends TecH3cOrderController
{
    /**
     * 批量付款处理接口
     * 接收POST请求，批量将选中采购单的payment_status字段更新为2
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchPaymentAction(Request $request)
    {
        $ids = $request->input('ids', []);
        $paymentDate = $request->input('payment_date', date('Y-m-d'));

        if (empty($ids) || !is_array($ids)) {
            return response()->json([
                'status' => false,
                'message' => '未选择任何数据',
            ]);
        }

        // 批量更新付款状态和日期
        $count = DB::table('t_purchase_order_h3c')
            ->whereIn('id', $ids)
            ->update([
                'payment_status' => 2,
                'payment_date' => $paymentDate
            ]);

        if ($count > 0) {
            return response()->json([
                'status' => true,
                'message' => "成功处理 {$count} 条付款状态",
            ]);
        } else {
            return response()->json([
                'status' => false,
                'message' => '未更新任何数据',
            ]);
        }
    }

        /**
     * 页面标题
     *
     * @return string
     */
    protected $title = 'H3C采购订单';
    
    protected $order_model = TecH3cPurchaseOrderModel::class;
    protected $order_item_model = TecH3cPurchaseOrderItemModel::class;


    /**
     * 处理更新请求
     */
    public function update($id)
    {
        return parent::update($id);
    }

    /**
     * 处理删除请求
     */
    public function destroy($ids)
    {
        return parent::destroy($ids);
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecH3cPurchaseOrderModel(), function (Grid $grid) {
            $grid->scrollbarX();

            // 获取当前请求的类型参数
            $type = request()->route('type');
            
            // 如果是已下单状态，禁用新增按钮
            if ($type === 'ordered') {
                $grid->disableCreateButton();
            }
            
            // 根据类型过滤订单
            if ($type) {
                switch ($type) {
                    case 'pending':
                        $grid->model()->where('review_status', TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_WAIT);
                        break;
                    case 'approved':
                        $grid->model()->where('review_status', TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK)
                                      ->where('order_status', TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING);
                        break;
                    case 'rejected':
                        $grid->model()->where('review_status', TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_NG);
                        break;
                    // order_status 为 H3C_ORDER_STATUS_ORDERED的订单    
                    case 'ordered':
                        $grid->model()->where('order_status', TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_ORDERED);
                        break;
                    // warehousing_status 为 H3C_WAREHOUSING_STATUS_PENDING 并且 order_status 为 H3C_ORDER_STATUS_ORDERED的订单
                    case 'awaiting_delivery':
                        $grid->model()->where('order_status', TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_ORDERED)
                                      ->where('warehousing_status', TecH3cPurchaseBaseModel::H3C_WAREHOUSING_STATUS_PENDING);
                        break;
                    case 'all':
                        // 不添加过滤条件，显示所有订单
                        break;
                }
            }
            
            // 预载入 items 及其关联的产品列表和单位信息，优化概述展示
            $grid->model()->with([
                'items.productlist:id,product,product_bom,product_zokusei,is_inspection_machine,product_image,cip_cost',
                'items.unitInfo:id,unit_name',
                'sndCompany:id,name,notes,company_color',
                'contact:id,first_name,last_name'
            ]);
            
            // 案件名和概要（合并显示）
            $grid->column('case_name', '案件信息')
                ->display(function($value) {
                    $caseName = $this->case_name;
                    $summary = $this->summary;
                    
                    if (!$caseName) {
                        return '-';
                    }
                    
                    // 直接显示完整的案件名
                    $displayCaseName = $caseName;
                    
                    // 如果有概要，显示案件名和概要，否则只显示案件名
                    if ($summary) {
                        return "<div style='line-height: 1.4;'>
                                    <div style='font-weight: bold; color: #333;font-size: 1.1em;'>{$displayCaseName}</div>
                                    <div style='color:#777777;font-size: 0.9em;'>{$summary}</div>
                                </div>";
                    } else {
                        return "<div style='font-weight: bold; color: #333;font-size: 1.1em;'>{$displayCaseName}</div>";
                    }
                })
                ->width('250px')
                ->style('white-space: nowrap;');
                
            // 公司名称、备注和联系人（合并显示）
            $grid->column('sndCompany_id', '供应商信息')
                ->display(function ($value) {
                    $company = $this->sndCompany;
                    $contact = $this->contact;
                    
                    if (!$company) {
                        return '-';
                    }
                    
                    $companyName = $company->name;
                    $contactName = $contact ? $contact->full_name : '';
                    $companyNotes = $company->notes;
                    
                    // 获取公司颜色，如果没有则使用默认颜色
                    $companyColor = $company->company_color ?: '#333';
                    
                    // 构建显示内容
                    $html = "<div style='line-height: 1.4;font-size: 0.9em;'>";
                    $html .= "<div style='font-weight: bold; color: {$companyColor};'>{$companyName}</div>";
                    
                    // 如果有备注，显示备注
                    if ($companyNotes) {
                        $html .= "<div style='color:#666666; font-size: 0.85em;'>{$companyNotes}</div>";
                    }
                    
                    // 如果有联系人，显示联系人
                    if ($contactName) {
                        $html .= "<div style='color:#777777;'>{$contactName}</div>";
                    }
                    
                    $html .= "</div>";
                    
                    return $html;
                })
                ->width('200px')
                ->style('white-space: nowrap;');

            // 贸易条件和币种（合并显示）
            $grid->column('incoterms', '交易条件')
                ->display(function ($value) {
                    $incoterms = $this->incoterms;
                    $currency = $this->currency;
                    
                    if (!$incoterms) {
                        return '-';
                    }
                    
                    // 获取贸易条件的显示文本和背景色
                    $incotermsText = config('purchase_config.incoterms')[$incoterms] ?? $incoterms;
                    $incotermsBgColor = \App\Support\GridStyleHelper::getStyleMappings('label')[$incoterms] ?? 'default';
                    $incotermsBgStyle = \App\Support\GridStyleHelper::getStyleMappings('background')[$incotermsBgColor] ?? 'var(--gray)';
                    
                    // 获取币种的样式
                    $currencyStyle = \App\Support\GridStyleHelper::getStyleMappings('color')[$currency] ?? 'color: var(--gray);';
                    
                    // 如果有币种，显示贸易条件和币种，否则只显示贸易条件
                    if ($currency) {
                        return "<div style='line-height: 1.4;font-size: 0.85em;'>
                                    <span style='background-color: {$incotermsBgStyle}; color: white; padding: 2px 4px; margin-right: 3px; border-radius: 3px; display: inline-block; font-size: 11px; font-weight: bold;'>{$incotermsText}</span>
                                    <br>
                                    <span style='{$currencyStyle}; font-weight: bold;'>{$currency}</span>
                                </div>";
                    } else {
                        return "<span style='background-color: {$incotermsBgStyle}; color: white; padding: 2px 4px; margin-right: 3px; border-radius: 3px; display: inline-block; font-size: 11px; font-weight: bold;'>{$incotermsText}</span>";
                    }
                })
                ->width('80px')
                ->style('white-space: nowrap; overflow: hidden; text-overflow: ellipsis;');

            // PO单号 不能改行
            $grid->column('order_no', 'PO单号')->expand(TecH3cPurchaseOrderItemsTable::make())->style('white-space: nowrap;font-size: 0.9em;');
            

            
            // 订单日期 只显示年月日
            GridStyleHelper::businessDateColumnConfig()($grid);

            // 订单状态
            GridStyleHelper::orderStatusColumnConfig('order_status', '订单<br>状态', [
                'style' => 'width: 70px;',     // 自定义宽度
                'sortable' => false,            // 禁用排序
                'filter' => false               // 禁用过滤器
            ])($grid);

            // 审核状态
            GridStyleHelper::reviewStatusColumnConfig('review_status', '审核<br>状态', [
                'style' => 'width: 70px;',     // 自定义宽度
                'sortable' => false,            // 禁用排序
                'filter' => false               // 禁用过滤器
            ])($grid);

            // 付款状态
            $grid->column('payment_status', GridStyleHelper::LABEL_PAYMENT_STATUS)
                ->using(TecH3cPurchaseBaseModel::H3C_PAYMENT_STATUS)
                ->dot(array_map(function($item) {
                    return $item['color'];
                }, GridStyleHelper::h3cPaymentStatusStyleMappings()))->style('white-space: nowrap;font-size: 0.8em;');

            // 付款日期
            $grid->column('payment_date', '付款日')->sortable()->style('white-space: nowrap;font-size: 0.8em;');

            // 过滤器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed(); // 回收站过滤
                // 添加更多筛选条件
                $filter->equal('sndCompany_id', '供应商')
                ->select(TecSndCompanyModel::where('snd_role', 'supplier')->get()->mapWithKeys(function($company) {
                    $displayName = $company->name;
                    if (!empty($company->notes)) {
                        $displayName .= " ({$company->notes})";
                    }
                    return [$company->id => $displayName];
                })->toArray())
                ->width(3);
                
                // 订单日期范围筛选，默认最近三个月
                $filter->between('business_date', '订单日期')
                    ->date()  // 使用date而不是datetime
                    ->width(3);
                    // ->default([
                    //     'start' => now()->subMonths(3)->format('Y-m-d'),
                    //     'end' => now()->format('Y-m-d')
                    // ]);

                $filter->like('case_name', '案件名')
                    ->width(3);

                $filter->like('order_no', 'PO单号')
                    ->width(3);


                $filter->equal('incoterms', '贸易条件')
                    ->select(config('purchase_config.incoterms'))
                    ->width(2);
                // 添加订单状态筛选
                $filter->equal('order_status', '订单状态')
                    ->select(TecH3cPurchaseBaseModel::H3C_ORDER_STATUS)
                    ->width(2);
                
                // 添加审核状态筛选
                $filter->equal('review_status', '审核状态')
                    ->select(TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS)
                    ->width(2);

                // 添加付款状态筛选
                $filter->equal('payment_status', '付款状态')
                    ->select(TecH3cPurchaseBaseModel::H3C_PAYMENT_STATUS)
                    ->width(2);

            });

            // 根据不同状态显示不同的操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) use ($type) {
                $actions->disableView();
                $actions->disableEdit();
                $actions->disableDelete();

                // 获取当前行的订单
                $order = $actions->row;

                // 统一风格的按钮HTML（Bootstrap btn-sm + icon，无文字）
                $editBtn = '<a href="' . admin_url('r_h3c_purchase_orders/' . $order->id . '/edit') . '" class="btn btn-sm btn-success" style="margin-right:6px;"><i class="fa fa-edit"></i></a>';
                $deleteBtn = '<a href="javascript:void(0);" data-id="' . $order->id . '" class="btn btn-sm btn-danger delete-order"><i class="fa fa-trash"></i></a>';
                $viewBtn = '<a href="' . admin_url('r_h3c_purchase_orders/' . $order->id . '/edit') . '" class="btn btn-sm btn-primary"><i class="fa fa-eye"></i></a>';

                // 根据订单状态和列表类型显示不同的按钮
                switch ($type) {
                    case 'pending':
                        $actions->append($editBtn);
                        $actions->append($deleteBtn);
                        break;
                    case 'rejected':
                        $actions->append($editBtn);
                        $actions->append($deleteBtn);
                        break;
                    case 'approved':
                        $actions->append($viewBtn);
                        break;
                    case 'ordered':
                        $actions->append($viewBtn);
                        break;
                    case 'all':
                        if ($order->review_status === TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_WAIT) {
                            $actions->append($editBtn);
                            $actions->append($deleteBtn);
                        } elseif ($order->review_status === TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_NG) {
                            $actions->append($editBtn);
                            $actions->append($deleteBtn);
                        } else {
                            $actions->append($viewBtn);
                        }
                        break;
                }
            });

            // 添加删除按钮的 JavaScript 处理
            Admin::script(<<<JS
            $('.delete-order').on('click', function() {
                var id = $(this).data('id');
                Dcat.confirm('确定要删除这条订单吗？', null, function() {
                    $.ajax({
                        method: 'DELETE',
                        url: '/admin/r_h3c_purchase_orders/' + id,
                        data: {
                            _token: Dcat.token
                        },
                        success: function(data) {
                            Dcat.success('删除成功');
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        },
                        error: function(x, e) {
                            Dcat.error('删除失败：' + (x.responseJSON.message || '未知错误'));
                        }
                    });
                });
            });
            JS);

            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) use ($type) {
                // 如果不是回收站且不是已下单状态，显示复制订单操作
                if (request('_scope_') != 'trashed' && $type !== 'ordered') {
                    $batch->add(new TecH3cPurchaseOrderCopyAction());
                }
                
                // 如果在回收站，则添加永久删除操作
                if (request('_scope_') == 'trashed') {
                    $batch->add(new TecH3cPurchaseOrderPermanentDeleteAction());
                }
            });

            // 排序
            // 根据订单日期排序
            $grid->model()->orderBy('business_date', 'desc');

            // 快速搜索
            $grid->quickSearch(['case_name', 'order_no']);

            // 禁用快速编辑按钮
            $grid->disableQuickEditButton();

            // 添加导出按钮
            $grid->tools(function (Grid\Tools $tools) {
                // 添加自定义导出工具按钮
                $tools->append(new TecH3cPurchaseOrderExportAction());
                // 批量付款处理按钮
                $tools->append(new TecH3cPurchaseOrderPaymentAction());
            });

        });
    }


    public function iFrameGrid()
    {
        return Grid::make(new TecH3cPurchaseOrderRepo(['user', 'supplier']), function (Grid $grid) {
            $grid->model()->where([
                'order_status'  => TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_WAIT,
                'review_status' => TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK
            ])->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            //            $grid->column('check_status')->using(PurchaseOrderModel::CHECK_STATUS);

            $grid->column('order_no');
            $grid->column('other')->emp();
            $grid->column('order_status', '订单状态')->using(TecH3cPurchaseBaseModel::H3C_ORDER_STATUS)->label(TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_COLOR);
            $grid->column('review_status', '审核状态')->using(TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS)->label(TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_COLOR);
            $grid->column('supplier.name', '供应商名称')->emp();
            $grid->column('user.username', '创建用户');
            $grid->column('created_at');
            $grid->column('finished_at')->emp();
            $grid->disableQuickEditButton();
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->tools(BatchCreatePurInOrderSave::make());
            $grid->filter(function (Grid\Filter $filter) {});
        });
    }

   

    /**
     * 设置通用表单配置
     * 
     * 此方法用于配置表单的公共元素和行为，适用于新增和编辑场景
     * 主要职责：
     * 1. 配置表单的基本属性
     * 2. 设置表单提交后的处理逻辑
     * 3. 添加通用的 JavaScript 脚本
     * 
     * @param Form $form Dcat Admin 表单对象
     * @return void
     */
    protected function setForm(Form &$form): void
    {
        // 获取当前订单ID
        $orderId = request()->route('r_h3c_purchase_order');
       
        // 如果存在订单ID，说明是编辑或查看模式
        if ($orderId) {
            $this->order = TecH3cPurchaseOrderModel::with(['items.productlist', 'supplier', 'contact'])->find($orderId);
        }

        // 判断是新增、编辑还是查看
        if ($form->isCreating()) {
            $this->setFormForCreate($form);
        } elseif (request()->route()->getName() === 'h3c_purchase_orders.show') {
            // 查看模式
            $this->setFormForShow($form);
        } else {
            // 编辑模式
            $this->setFormForEdit($form);
        }

        // 表单验证钩子：验证明细项（仅在创建和编辑时验证）
        if (request()->route()->getName() !== 'h3c_purchase_orders.show') {
            $form->saving(function (Form $form) {
                $items = $form->input('items', []);
                if (empty($items)) {
                    return $form->responseValidationMessages('items', '请至少添加一个商品明细');
                }
                return true;
            });
        }

        // 保存后处理明细项（仅在创建和编辑时处理）
        if (request()->route()->getName() !== 'h3c_purchase_orders.show') {
            $form->saved(function (Form $form) {
                try {
                    // 获取订单ID
                    $orderId = $form->getKey(); // 使用 getKey() 方法获取主键值
                    
                    // 如果还是获取不到ID，尝试从模型获取
                    if (!$orderId) {
                        $orderId = $form->model()->id;
                    }
                    
                    // 再次确认ID存在
                    if (!$orderId) {
                        Log::error('保存采购订单明细失败：无法获取订单ID');
                        return;
                    }

                    // 获取明细数据
                    $items = $form->input('items') ?: request()->input('items', []);
                    
                    if (empty($items)) {
                        Log::warning('没有采购订单明细', ['order_id' => $orderId]);
                        return;
                    }

                    // 使用事务管理
                    DB::transaction(function () use ($orderId, $items) {
                        // 先删除原有的明细
                        TecH3cPurchaseOrderItemModel::where('order_id', $orderId)->delete();

                        // 保存新的明细
                        $savedItems = collect($items)
                            ->reject(fn($item) => !empty($item['_remove_']) && $item['_remove_'] === '1')
                            ->map(function ($itemData) use ($orderId) {
                                // 记录每个明细项的数据
                                Log::info('Processing item data:', [
                                    'order_id' => $orderId,
                                    'raw_data' => $itemData,
                                    'category_id' => $itemData['category_id'] ?? null,
                                    'product' => $itemData['product'] ?? null,
                                ]);

                                return TecH3cPurchaseOrderItemModel::create([
                                    'order_id' => $orderId,
                                    'category_id' => $itemData['category_id'] ?? null,
                                    'product' => $itemData['product'] ?? null,
                                    'product_bom' => $itemData['product_bom'] ?? '-',
                                    'product_zokusei' => $itemData['product_zokusei'] ?? '常规',
                                    'unit' => $itemData['unit'] ?? null,
                                    'quantity' => $itemData['quantity'] ?? 0,
                                    'standard_price' => $itemData['standard_price'] ?? 0,
                                    'cip_cost' => $itemData['cip_cost'] ?? 0,
                                ]);
                            });

                        Log::info('采购订单明细保存完成', [
                            'order_id' => $orderId,
                            'saved_count' => $savedItems->count(),
                            'saved_items' => $savedItems->toArray()
                        ]);
                    });
                } catch (Exception $e) {
                    Log::error('保存采购订单明细异常', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            });
        }

        // 禁用整个底部
        // $form->disableFooter();

        // 禁用特定按钮
        $form->disableViewCheck();
        $form->disableEditingCheck();
        $form->disableCreatingCheck();
    }

    /**
     * 配置新增表单
     * 
     * 此方法专门处理新增采购订单的表单配置
     * 主要职责：
     * 1. 初始化新增表单的特定字段
     * 2. 设置默认值和必填项
     * 3. 配置动态加载和联动逻辑
     * 
     * @param Form $form Dcat Admin 表单对象
     * @return void
     */
    protected function setFormForCreate(Form &$form): void
    {
        // 添加隐藏字段，用于存储初始供应商ID，但不保存到数据库
        $form->html('<input type="hidden" name="sndCompany_id_hidden" value="0">');
        
        // 新增时 第一行：业务分类和供应商选择
        $form->row(function (Form\Row $row) {

            // 获取 H3C 业务分类
            $h3cCategory = TecProductMajorCategoryModel::getH3CCategory()->first(); // 获取第一条记录

            // 分类选择字段，支持动态加载供应商和联系人
            $row->width(2)->select('major_category_id', '業務分類')
                ->options(TecProductMajorCategoryModel::getH3CCategory()->pluck('major_category', 'id')) // 使用 pluck 提取数组
                ->default($h3cCategory->id) // 设置默认选中为 H3C 分类的 ID
                ->required()
                ->loadSndCompanyAndContactPerson(
                    route('dcat.admin.api.sndCompaniesAndContactPersonByBusinessCategory')
                );;

            // 供应商
            $row->width(4)->select('sndCompany_id', '供应商')
                ->options([]) // 初始为空，待动态加载
                ->required();

            // 担当者
            $row->width(3)->select('contact_person', '担当者')
                ->options([]) // 初始为空，待动态加载
                ->required();

            $row->width(3)->select('order_status', '下单状态')
                ->options([
                    TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING => TecH3cPurchaseBaseModel::H3C_ORDER_STATUS[TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING]
                ])
                ->default(TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING)
                ->required()
                ->readonly();
        });


        // 新增时 第二行：订单关联和币种信息
        $form->row(function (Form\Row $row) use ($form) {
            $row->width(2)->text('incoterms', '貿易条件')
                ->value('')
                ->required()
                ->readonly();
            
            $row->width(2)->text('currency', '交易币种')
                ->value(function() {
                    // 如果已经选择了供应商，获取其交易币种
                    $sndCompanyId = request()->input('sndCompany_id');
                    if ($sndCompanyId) {
                        $sndCompany = TecSndCompanyModel::find($sndCompanyId);
                        return $sndCompany ? $sndCompany->transaction_currency : '-';
                    }
                    return '-'; // 默认返回 USD
                })
                ->required()
                ->readonly();

            $row->width(2)->select('delivery_term', '纳期')
                ->options(\App\Admin\Config\PurchaseConfig::getDeliveryTerms())
                ->default(2)
                ->required();

            $row->width(3)->number('exchange_rate', '当期参照汇率')->default(160);
            // $orders = TecH3cPurchaseOrderModel::getUnassociatedOrders();
            // $row->width(5)->select('associated_id', '关联订单')
            // ->options($orders);
            
            // 业务日期 - 使用date而不是datetime，精确到日即可
            $row->width(3)->date('business_date', '业务日期')->default(now())->required();
        });

        // 新增时 第三行
        $form->row(function (Form\Row $row) {
            // PO单号
            $row->width(4)->text('order_no', 'PO单号')->required();
            //案件名
            $row->width(4)->text('case_name', '案件名')->required();
            // 备注
            $row->width(4)->text('other', '备注')->saveAsString();

        });

        // 新增时 第四行
        $form->row(function (Form\Row $row) {
            // // 供应商报价单号
            // $row->width(4)->text('quotation_no', '供应商报价单号');

            // 备注
            // $row->width(12)->text('other', '备注')->saveAsString();
        });
        
        // 添加 JavaScript 脚本
        Admin::script(<<<JS
        $(function () {
            console.log('Script is running'); // 调试日志
            console.log('页面加载时隐藏字段的值: ' + $('input[name="sndCompany_id_hidden"]').val());
            var select = $('select[name="sndCompany_id"]');
            var currencySelect = $('select[name="currency"]');
            var hiddenField = $('input[name="sndCompany_id_hidden"]');
            var initialValue = select.val();
            var isChanging = false;
            var lastSelectedCurrency = 'JPY'; // 默认初始化为 JPY

            // 移除所有现有的事件监听器
            select.off('change select2:select');

            // 确保初始值被保存到隐藏字段
            if (select.val()) {
                hiddenField.val(select.val());
                window.currentCompanyId = select.val();
                console.log('初始供应商ID已保存到隐藏字段:', select.val());
                console.log('初始供应商ID已保存到全局变量:', select.val());
            }

            // 使用事件委托和标志位控制
            select.on('change', function (e) {
                // 如果正在处理变更，阻止重复触发
                if (isChanging) {
                    return;
                }

                var currentValue = $(this).val(); 
                
                console.log('供应商变更事件触发');
                console.log('当前选择值: ' + currentValue);
                console.log('初始值: ' + initialValue);

                // 保存当前供应商ID到隐藏字段
                hiddenField.val(currentValue);
                console.log('供应商ID已更新到隐藏字段:', currentValue);
                
                // 更新全局变量
                window.currentCompanyId = currentValue;
                console.log('全局供应商ID变量已更新:', currentValue);

                // 如果初始值为空或null，直接更新并返回
                if (initialValue === null || initialValue === '') {
                    initialValue = currentValue;
                    console.log('首次选择，更新初始值');
                    // 清空商品信息
                    $('select[name^="items"][name$="[category_id]"]').val(null).trigger('change');
                    $('select[name^="items"][name$="[product]"]').empty();
                    $('input[name^="items"][name$="[product_bom]"]').val('-');
                    $('input[name^="items"][name$="[product_zokusei]"]').val('-');
                    $('select[name^="items"][name$="[unit]"]').val(null).trigger('change');
                    $('input[name^="items"][name$="[quantity]"]').val('0');
                    $('input[name^="items"][name$="[standard_price]"]').val('0');
                    $('input[name^="items"][name$="[cip_cost]"]').val('0');
                    return;
                }

                // 如果值相同，直接返回
                if (currentValue === initialValue) {
                    console.log('供应商未发生变化');
                    return;
                }

                // 设置变更标志
                isChanging = true;

                // 还原为初始值
                $(this).val(initialValue).trigger('change');

                // 弹出确认对话框
                Dcat.confirm('您确定要更改供应商吗？', '更改供应商将清空所有商品信息', function () {
                    console.log('用户确认更改供应商');

                    // 更新初始值
                    initialValue = currentValue;
                    
                    // 更新隐藏字段值
                    hiddenField.val(currentValue);
                    console.log('供应商ID已更新到隐藏字段:', currentValue);
                    
                    // 更新全局变量
                    window.currentCompanyId = currentValue;
                    console.log('全局供应商ID变量已更新:', currentValue);

                    // 清空商品信息
                    $('select[name^="items"][name$="[category_id]"]').val(null).trigger('change');
                    $('select[name^="items"][name$="[product]"]').empty();
                    $('input[name^="items"][name$="[product_bom]"]').val('-');
                    $('input[name^="items"][name$="[product_zokusei]"]').val('-');
                    $('select[name^="items"][name$="[unit]"]').val(null).trigger('change');
                    $('input[name^="items"][name$="[quantity]"]').val('0');
                    $('input[name^="items"][name$="[standard_price]"]').val('0');
                    $('input[name^="items"][name$="[cip_cost]"]').val('0');

                    // 更新供应商选择
                    select.val(currentValue).trigger('change');

                    // 重置变更标志
                    isChanging = false;

                    // 延迟执行，确保 AJAX 请求完成后更新
                    setTimeout(function() {
                        var currency = currencySelect.val();
                        console.log('获取币种值:', currency);
                        updatePriceLabel(currency);
                    }, 200);
                }, function() {
                    console.log('用户取消更改供应商');
                    
                    // 重置变更标志
                    isChanging = false;
                }, {
                    // 添加这个配置来解决 SweetAlert2 的警告
                    showLoaderOnConfirm: false
                });
            });

            // 更新进货单价标签
            function updatePriceLabel(currency) {
                // 如果没有传入币种，尝试从选择器获取
                if (!currency) {
                    currency = currencySelect.val();
                }

                // 如果仍然没有币种，使用上一次选择的币种
                if (!currency) {
                    currency = lastSelectedCurrency;
                }

                var labelText = currency === 'USD' ? '进货单价(USD)' : '进货单价(JPY)';
                console.log('当前币种:', currency, '标签文本:', labelText);
                
                // 更新表头的标签
                $('table.table-has-many thead tr th').each(function() {
                    var currentText = $(this).text();
                    if (currentText.includes('进货单价')) {
                        console.log('更新表头标签:', currentText, '→', labelText);
                        $(this).text(labelText);
                    }
                });

                // 更新模板中的标签
                $('template.items-tpl div.form-field div.text-capitalize.hidden.control-label span').each(function() {
                    var currentText = $(this).text();
                    if (currentText === '进货单价') {
                        console.log('更新模板标签:', currentText, '→', labelText);
                        $(this).text(labelText);
                    }
                });

                // 更新输入框的 placeholder
                $('input[name^="items"][name$="[standard_price]"]').each(function() {
                    $(this).attr('placeholder', '输入 ' + labelText);
                });

                // 记录最后选择的币种
                lastSelectedCurrency = currency;
            }

            // 初始化时更新标签
            updatePriceLabel();

            // 币种变更时更新标签
            currencySelect.on('change', function() {
                updatePriceLabel();
            });

            // 数量验证的前端校验
            $('input[name^="items"][name$="[quantity]"]').on('input', function() {
                var value = parseFloat($(this).val());
                if (isNaN(value) || value < 0) {
                    $(this).val('0');
                }
            });

            // 页面加载后延迟触发一次select的change事件，确保所有值正确初始化
            setTimeout(function() {
                if (select.val()) {
                    // 保存当前供应商ID到隐藏字段
                    hiddenField.val(select.val());
                    console.log('页面加载后，供应商ID已保存到隐藏字段:', select.val());
                }
            }, 1000);

            // 添加特殊监听，确保每次供应商选择变化后更新隐藏字段
            $(document).on('select2:select', 'select[name="sndCompany_id"]', function(e) {
                var data = e.params.data;
                if (data && data.id) {
                    hiddenField.val(data.id);
                    console.log('Select2:select事件触发，供应商ID已更新到隐藏字段:', data.id);
                }
            });

        });
        JS);
        
        // 添加业务日期变化监听事件
        Admin::script($this->getBusinessDateChangeScript());
    }

    /**
     * 添加业务日期变化监听JavaScript代码
     * 纯前端方法，不依赖PHP变量传递
     * 
     * @return string
     */
    protected function getBusinessDateChangeScript(): string
    {
        return <<<'JS'
        $(function() {
            console.log('初始化业务日期变化监听');
            
            // 定义全局变量存储供应商ID
            window.currentCompanyId = null;
            
            // 获取DOM元素
            var businessDateInput = $('input[name="business_date"]');
            var orderNoInput = $('input[name="order_no"]');
            var hiddenCompanyIdField = $('input[name="sndCompany_id_hidden"]');
            
            // 从各种可能的来源获取供应商ID
            function updateGlobalCompanyId() {
                // 尝试从隐藏字段获取
                if (hiddenCompanyIdField.length && hiddenCompanyIdField.val() && hiddenCompanyIdField.val() !== '0') {
                    window.currentCompanyId = hiddenCompanyIdField.val();
                    console.log('从隐藏字段更新全局供应商ID:', window.currentCompanyId);
                    return;
                }
                
                // 尝试从select获取
                var companySelect = $('select[name="sndCompany_id"]');
                if (companySelect.length && companySelect.val()) {
                    window.currentCompanyId = companySelect.val();
                    console.log('从下拉框更新全局供应商ID:', window.currentCompanyId);
                    return;
                }
                
                // 尝试从隐藏的供应商ID字段获取
                var hiddenSndCompanyId = $('input[name="sndCompany_id"]');
                if (hiddenSndCompanyId.length && hiddenSndCompanyId.val()) {
                    window.currentCompanyId = hiddenSndCompanyId.val();
                    console.log('从sndCompany_id隐藏字段更新全局供应商ID:', window.currentCompanyId);
                    return;
                }
                
                // 从data属性获取
                var savedId = $('body').data('company-id');
                if (savedId) {
                    window.currentCompanyId = savedId;
                    console.log('从body data属性更新全局供应商ID:', window.currentCompanyId);
                    return;
                }
                
                console.log('无法获取供应商ID');
            }
            
            // 初始化时尝试获取供应商ID
            updateGlobalCompanyId();
            
            // 监听供应商下拉框变化，同步更新全局ID和隐藏字段
            $(document).on('change', 'select[name="sndCompany_id"]', function() {
                var companyId = $(this).val();
                if (companyId) {
                    window.currentCompanyId = companyId;
                    hiddenCompanyIdField.val(companyId);
                    // 同时保存到body的data属性作为备份
                    $('body').data('company-id', companyId);
                    console.log('供应商变更事件，更新全局ID:', companyId);
                }
            });
            
            // 额外监听Select2事件
            $(document).on('select2:select', 'select[name="sndCompany_id"]', function(e) {
                if (e.params && e.params.data && e.params.data.id) {
                    window.currentCompanyId = e.params.data.id;
                    hiddenCompanyIdField.val(e.params.data.id);
                    // 同时保存到body的data属性作为备份
                    $('body').data('company-id', e.params.data.id);
                    console.log('供应商Select2:select事件，更新全局ID:', e.params.data.id);
                }
            });
            
            if (businessDateInput.length === 0) {
                console.warn('未找到业务日期输入框');
                return;
            }
            
            console.log('找到业务日期输入框:', businessDateInput.val());
            
            // 保存初始业务日期值
            var initialDateValue = businessDateInput.val();
            businessDateInput.data('initial-value', initialDateValue);
            
            // 业务日期变化监听函数
            function handleBusinessDateChange() {
                var dateValue = businessDateInput.val();
                var initialValue = businessDateInput.data('initial-value');
                
                // 如果值没有变化，不处理
                if (dateValue === initialValue) {
                    console.log('业务日期未变化');
                    return;
                }
                
                console.log('业务日期已变化:', initialValue, '->', dateValue);
                
                // 确保我们有最新的供应商ID
                updateGlobalCompanyId();
                console.log('当前全局供应商ID:', window.currentCompanyId);
                
                if (!window.currentCompanyId || !dateValue) {
                    console.log('供应商或日期未选择，无法更新订单号');
                    return;
                }
                
                // 更新初始日期值
                businessDateInput.data('initial-value', dateValue);
                
                // 直接发送AJAX请求生成新订单号，无需确认
                $.ajax({
                    url: '/admin/api/generate_order_no',
                    type: 'GET',
                    data: {
                        company_id: window.currentCompanyId,
                        business_date: dateValue
                    },
                    success: function(response) {
                        console.log('获取订单号响应:', response);
                        if (response && response.status && response.order_no) {
                            orderNoInput.val(response.order_no);
                            Dcat.success('订单号已更新为: ' + response.order_no);
                        } else {
                            console.error('生成订单号失败:', response);
                            Dcat.error('生成订单号失败: ' + (response.message || '未知错误'));
                        }
                    },
                    error: function(xhr) {
                        console.error('生成订单号请求失败:', xhr);
                        Dcat.error('生成订单号请求失败');
                    }
                });
            }
            
            // 绑定业务日期变化事件
            businessDateInput.on('change', handleBusinessDateChange);
            
            // 监听日期选择器的各种事件
            $(document).on('click', function(e) {
                // 检查是否点击了日期选择相关元素
                var isDatepickerClick = $(e.target).closest('.layui-laydate').length > 0;
                
                if (isDatepickerClick) {
                    console.log('日期选择器点击事件');
                    
                    // 延迟检查日期值是否变化
                    setTimeout(function() {
                        var currentValue = businessDateInput.val();
                        var storedValue = businessDateInput.data('initial-value');
                        
                        console.log('检查日期值:', {
                            stored: storedValue,
                            current: currentValue
                        });
                        
                        if (currentValue !== storedValue) {
                            console.log('日期值已变化，触发change事件');
                            businessDateInput.trigger('change');
                        }
                    }, 200);
                }
            });
            
            // 辅助监听 - 定时检查业务日期值是否变化
            setInterval(function() {
                var currentValue = businessDateInput.val();
                var lastCheckedValue = businessDateInput.data('last-checked-value');
                
                // 首次运行时保存当前值
                if (lastCheckedValue === undefined) {
                    businessDateInput.data('last-checked-value', currentValue);
                    return;
                }
                
                // 如果值变化了，触发change事件
                if (currentValue !== lastCheckedValue) {
                    console.log('定时检测到业务日期值变化:', lastCheckedValue, '->', currentValue);
                    businessDateInput.data('last-checked-value', currentValue);
                    businessDateInput.trigger('change');
                }
            }, 1000);
        });
        JS;
    }
    
    /**
     * 配置编辑表单
     * 
     * 此方法专门处理编辑现有采购订单的表单配置
     * 主要职责：
     * 1. 加载并显示当前订单的详细信息
     * 2. 设置字段的只读和默认值
     * 3. 处理编辑特有的业务逻辑
     * 
     * @param Form $form Dcat Admin 表单对象
     * @return void
     */
    protected function setFormForEdit(Form &$form): void
    {
        // 记录编辑过程的详细日志
        $this->order = TecH3cPurchaseOrderModel::find(request()->route('r_h3c_purchase_order'));
        
        // 设置表单宽度为80%
        $form->width('80%');
        
        // 编辑时 第一行：供应商和联系人信息
        $form->row(function (Form\Row $row) {
            // 防御性检查
            if (!$this->order) {
                return;
            }

            $row->hidden('sndCompany_id', '供应商id')->readonly();

            // 安全地获取供应商联系人和名称
            $sndCompanyId = $this->order ? $this->order->sndCompany_id : null;
            
            // 获取供应商联系人
            $contactPersons = TecSndContactModel::getContactsByCompany($sndCompanyId);
            
            // 获取供应商名称
            $supplierName = TecSndCompanyModel::getNameById($sndCompanyId);

            // 業務分類
            $row->width(3)->text('major_category_display', '業務分類')
                ->value("H3C仕入")
                ->readonly();

            // 供应商名称
            $row->width(6)->text('supplier_name', '供应商')
                ->value($supplierName)
                ->readonly();

            // 担当者
            $row->width(3)->select('contact_person', '担当者')
                ->options($contactPersons)
                ->default($this->order->contact_person_id ?? null);
        });

        // 编辑时 第二行：订单关联和币种信息
        $form->row(function (Form\Row $row) use ($form) {
            // 防御性检查
            if (!$this->order) {
                return;
            }

            $row->width(2)->text('incoterms', '貿易条件')
                ->value(config('purchase_config.incoterms')[$this->order->incoterms] ?? '')
                ->required()
                ->readonly();
            
            $row->width(2)->text('currency', '交易币种')
                ->value($this->order->currency)
                ->required()
                ->readonly();

            $row->width(2)->select('delivery_term', '纳期')
                ->options(\App\Admin\Config\PurchaseConfig::getDeliveryTerms())
                ->default($this->order->delivery_term ?? 2)
                ->required();

            $row->width(3)->number('exchange_rate', '当期参照汇率')->default(160);

            $row->width(3)->date('business_date', '业务日期')->required();
        });

        // 编辑时 第三行
        $form->row(function (Form\Row $row) {
            $row->width(4)->text('order_no', 'PO单号')->required();
            $row->width(4)->text('case_name', '案件名')->required();
            $row->width(2)->text('status_text', '发货状态')
                ->value(TecH3cPurchaseBaseModel::H3C_ORDER_STATUS[$this->order->order_status] ?? '未知状态')
                ->readonly();
        });

        // 编辑时 第四行
        $form->row(function (Form\Row $row) {
            $row->width(4)->text('quotation_no', '供应商报价单号');
            $row->width(6)->text('other', '备注')->saveAsString();
        });
        
        // 添加审核图标
        $form->reviewiconh3c('review_status', '审核状态')
            ->value($this->order->review_status ?? TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_WAIT)
            ->width(2);

        $form->disableResetButton();
        Log::info('configureEditForm------------');
        // 如果订单已审核，禁用所有字段
        if ($this->order && $this->order->review_status === TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK) {
            $form->disableSubmitButton();
            $form->disableResetButton();
        }
    }

    /**
     * 配置采购订单创建表单的明细项（items）
     * 
     * 此方法在创建新的采购订单时被调用，主要用于：
     * 1. 配置可重复的明细表单项
     * 2. 设置商品分类和商品的动态加载逻辑
     * 3. 实现前端交互的联动效果
     * 
     * 核心流程：
     * - 创建可重复的明细表单项
     * - 配置商品分类下拉选择
     * - 添加 JavaScript 脚本实现分类与商品的联动
     * - 根据选择的分类和贸易条件动态加载商品列表
     * 
     * @param Form $form Dcat Admin 表单对象的引用
     * @return void
     */
    protected function creating(Form &$form): void
    {
        // 设置默认状态为待收货（0）
        $form->order_status = TecH3cPurchaseBaseModel::H3C_ORDER_STATUS_PENDING;

        // 获取当前业务分类（可能在后续逻辑中使用）
        $businessCategory = $form->business_category;

        // 创建表单行，配置可重复的明细项
        $form->row(function (Form\Row $row) {
            // 使用 hasMany 创建可重复的明细表单
            // 'items' 是明细表单的字段名，用于存储多个明细项
            $row->hasMany('items', '', function (Form\NestedForm $table) {
                // 第一行：配置商品分类和商品选择
                $table->row(function (Form\Row $row) {
                    // 商品分类下拉选择
                    // 从数据库获取所有有产品的商品分类
                    $row->width(6)->select('category_id', '商品分类')
                        ->options(TecH3cProductListModel::with('category')
                            ->select('category_id')
                            ->distinct()
                            ->get()
                            ->map(function ($item) {
                                return [
                                    'id' => $item->category_id,
                                    'title' => $item->category->title ?? ''
                                ];
                            })
                            ->pluck('title', 'id')
                            ->filter()
                            ->toArray())
                        ->required()
                        ->attribute('style', 'width: 250px;')
                        ->loadH3CProductDetails(route('dcat.admin.api.h3c.product.find'))
                        ->script("
                            $(document).on('change', 'select[name$=\"[category_id]\"]', function () {
                                var categoryId = $(this).val(); 
                                var incoterms = $('input[name=\"incoterms\"]').val();
                                var productSelect = $(this).closest('.form-group').next().find('select');
                                
                                $.get('" . route('dcat.admin.api.h3c.product.find') . "', {
                                    category_id: categoryId,
                                    incoterms: incoterms
                                }, function(data) {
                                    productSelect.empty();
                                    $.each(data, function(id, name) {
                                        productSelect.append(new Option(name, id));
                                    });
                                });
                            });
                        ");
                    
                    $row->width(6)->select('product', '商品')
                        ->options([])
                        ->required()
                        ->width(4);
                });

                // 第二行
                $table->row(function (Form\Row $row) {
                    $units = TecProductUnitModel::all()->pluck('unit_name', 'id')->toArray();
                    $row->ipt('product_bom', 'BOM')->rem(6)->default('-')->disable();
                    $row->ipt('product_zokusei', '制品属性')->rem(3)->default('常规')->disable()->width(4);
                    $row->select('unit', '单位')->options($units)->required()->width(3)->default(1);
                    //数量要大于0
                    $row->ipt('quantity', '数量')->rem(4)->default('0')->attribute('type', 'number')->rules('required|gt:0');
                    //进货单价 小数,必须大于等于0   
                    $row->ipt('standard_price', '进货单价')->rem(3)->default('0')->attribute('type', 'number')->attribute('step', '0.01')->rules('required|numeric|min:0'); // 使用min:0替代gte:0
                    //CIP成本 小数,必须大于等于0
                    // $row->ipt('cip_cost', 'CIP成本(日元)')->rem(3)->default('0')->attribute('type', 'number')->rules('required|gte:0')->display(false);
    
                });
            })->useTable()->width(12)->enableHorizontal();
        });
    }

    /**
     * 更新订单项
     */
    public function updateItem($orderId, $itemId)
    {
        try {
            // 查找订单明细项
            $item = TecH3cPurchaseOrderItemModel::where('order_id', $orderId)
                ->where('id', $itemId)
                ->firstOrFail();

            // 获取提交的数据
            $inputData = request()->all();
            
            // 记录输入数据
            Log::info('UpdateItem Input', [
                'item_id' => $itemId,
                'input_data' => $inputData
            ]);

            // 验验证输入数据
            $validator = Validator::make($inputData, [
                'quantity' => 'required|numeric|min:1',
                'standard_price' => 'required|numeric|min:0',
                'cip_cost' => 'required|numeric|min:0'
            ], [
                'quantity.required' => '数量不能为空',
                'quantity.numeric' => '数量必须是数字',
                'quantity.min' => '数量必须大于0',
                'standard_price.required' => '标准价格不能为空',
                'standard_price.numeric' => '标准价格必须是数字',
                'standard_price.min' => '标准价格不能小于0',
                'cip_cost.required' => 'CIP成本不能为空',
                'cip_cost.numeric' => 'CIP成本必须是数字',
                'cip_cost.min' => 'CIP成本不能小于0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first()
                ]);
            }

            // 更新数据
            $result = $item->update($validator->validated());

            if ($result) {
                return response()->json([
                    'status' => true,
                    'message' => '更新成功'
                ]);
            }

            return response()->json([
                'status' => false,
                'message' => '更新失败'
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'status' => false,
                'message' => '未找到对应的订单明细'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Update Order Item Error', [
                'order_id' => $orderId,
                'item_id' => $itemId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => false,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }
        
    /**
     * 删除多个订单明细项
     */
    public function deleteItems(Request $request)
    {
        Log::info('TecH3cPurchaseOrderController deleteItems called', [
            'request_data' => $request->all(),
        ]);

        try {
            $action = new TecH3cOrderItemDeleteAction();
            $response = $action->handle($request);
            
            Log::info('Delete items response', [
                'response' => $response->getData(true)
            ]);

            return $response;
        } catch (Exception $e) {
            Log::error('Delete items error', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            return $this->response()->error($e->getMessage());
        }
    }

    /**
     * 设置订单明细表格
     * 
     * 此方法用于配置订单明细表格的列和行为
     * 主要职责：
     * 1. 定义表格列
     * 2. 设置列的显示和编辑逻辑
     * 
     * @param Grid $grid Dcat Admin 表格对象
     * @return void
     */
    protected function setItems(Grid &$grid): void
    {
        $order = $this->order;

        // 确保预加载关联关系
        $grid->model()->with(['productlist', 'productlist.category', 'category', 'unitInfo']);

        // 预加载单位信息避免N+1查询
        $units = TecProductUnitModel::pluck('unit_name', 'id')->toArray();

        $grid->column('category.title', '商品分类', 'text');

        $grid->column('productlist.product', '商品', 'text');
        $grid->column('productlist.product_bom', 'BOM', 'text');
        $grid->column('productlist.product_zokusei', '产品属性', 'text')->display(function ($value) {
            $zokusei_map = [
                'normal' => PurchaseConfig::PRODUCT_ZOKUSEI_NORMAL,
                'rr' => PurchaseConfig::PRODUCT_ZOKUSEI_RR,
                'inspection' => PurchaseConfig::PRODUCT_ZOKUSEI_INSPECTION,
            ];
            return $zokusei_map[$value] ?? $value;
        });

        // 根据订单币种动态设置进货单价列标题
        $columnTitle = '进货单价';
        if ($order) {
            $columnTitle = '进货单价(' . $order->currency . ')';
        }
        //单位
        $grid->column('unit', '单位', 'text')->display(function ($value) use ($units) {
            // 优先使用预加载的unitInfo关联
            if ($this->unitInfo) {
                return $this->unitInfo->unit_name;
            }
            // 回退到预加载的单位数组
            return $units[$value] ?? $value;
        });

        $grid->column('quantity', '数量', 'number')->display(function() use ($order) {
            if ($order && $order->review_status === TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK) {
                return $this->getAttribute('quantity');
            }
            return "<input type='number' class='form-control grid-item-edit' name='quantity' value='{$this->getAttribute('quantity')}' data-id='{$this->getAttribute('id')}' style='width:100px'>";
        })->width('100px');

        $grid->column('standard_price', $columnTitle, 'number:2')->display(function() use ($order) {
            if ($order && $order->review_status === TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK) {
                return $this->getAttribute('standard_price');
            }
            return "<input type='number' step='0.01' class='form-control grid-item-edit' name='standard_price' value='{$this->getAttribute('standard_price')}' data-id='{$this->getAttribute('id')}' style='width:120px'>";
        })->width('120px');
    
        // $grid->column('cip_cost', 'CIP成本(日元)', 'number:2')->display(function() use ($order) {
        //     if ($order && $order->review_status === TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK) {
        //         return $this->getAttribute('cip_cost');
        //     }
        //     return "<input type='number' step='0.01' class='form-control grid-item-edit' name='cip_cost' value='{$this->getAttribute('cip_cost')}' data-id='{$this->getAttribute('id')}' style='width:120px'>";
        // })->width('120px');

        // 添加 JavaScript 监听表单提交
        if ($order && $order->review_status !== TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK) {
            Admin::script(<<<JS
                $(function() {
                    // 监听输入变化
                    $('.grid-item-edit').on('change', function() {
                        var \$input = $(this);
                        var id = \$input.data('id');
                        var name = \$input.attr('name');
                        var value = \$input.val();
                        
                        // 验证数量
                        if (name === 'quantity') {
                            var quantity = parseInt(value);
                            if (isNaN(quantity) || quantity <= 0) {
                                Dcat.warning('数量必须大于0');
                                \$input.val('');
                                return;
                            }
                        }
                        
                        // 验证价格
                        if (name === 'standard_price' || name === 'cip_cost') {
                            var price = parseFloat(value);
                            if (isNaN(price) || price < 0) {
                                Dcat.warning('价格不能为负数');
                                \$input.val('');
                                return;
                            }
                        }
                        
                        // 获取当前行的所有输入值
                        var rowData = {
                            quantity: parseFloat(\$input.closest('tr').find('input[name="quantity"]').val()) || 0,
                            standard_price: parseFloat(\$input.closest('tr').find('input[name="standard_price"]').val()) || 0,
                            cip_cost: parseFloat(\$input.closest('tr').find('input[name="cip_cost"]').val()) || 0
                        };
                        
                        // 发送更新请求
                        $.ajax({
                            url: '/admin/r_h3c_purchase_orders/{$order->id}/items/' + id,
                            type: 'PUT',
                            data: rowData,
                            success: function(response) {
                                if (response.status) {
                                    // 显示成功消息
                                    Dcat.success('更新成功');
                                    
                                    // 更新表格中的值，但不刷新页面
                                    var td = form.closest('td');
                                    td.find('.editable-container').remove();
                                    td.find('.editable').show().html(response.value);
                                } else {
                                    Dcat.error(response.message);
                                }
                            },
                            error: function(xhr) {
                                var message = xhr.responseJSON ? (xhr.responseJSON.message || '更新失败') : '更新失败';
                                Dcat.error(message);
                            }
                        });
                    });
                });
            JS);
        }


    }

    /**
     * 处理创建请求
     */
    public function store()
    {
        try {
            $input = request()->all();
            
            // 创建订单(观察者会处理验证和状态设置)
            $result = DB::transaction(function () use ($input) {
                // 移除items字段，单独处理订单项
                $orderData = collect($input)->except(['items', '_token', '_previous_'])->toArray();
                
                // 创建订单
                $order = TecH3cPurchaseOrderModel::create($orderData);

                // 创建订单项(如果有)
                if (isset($input['items'])) {
                    $orderItems = [];
                    foreach ($input['items'] as $item) {
                        if (isset($item['_remove_']) && $item['_remove_'] === '1') {
                            continue;
                        }
                        $orderItems[] = new TecH3cPurchaseOrderItemModel($item);
                    }
                    if (!empty($orderItems)) {
                        $order->items()->saveMany($orderItems);
                    }
                }

                return $order;
            });

            return $this->form()
                ->response()
                ->success('创建成功')
                ->refresh()
                ->redirect(admin_url('r_h3c_purchase_orders/type/all'));
        } catch (\Exception $e) {
            return $this->form()
                ->response()
                ->error($e->getMessage());
        }
    }

    protected function script()
    {
        $script = <<<'JS'
$(document).on('submit', '.grid-edit-form', function (e) {
    e.preventDefault();
    var form = $(this);
    var url = form.attr('action');
    var data = form.serialize();

    $.ajax({
        url: url,
        type: 'POST',
        data: data,
        success: function (response) {
            if (response.status) {
                // 显示成功消息
                Dcat.success(response.message);
                
                // 更新表格中的值，但不刷新页面
                var td = form.closest('td');
                td.find('.editable-container').remove();
                td.find('.editable').show().html(response.value);
            } else {
                Dcat.warning(response.message);
            }
        },
        error: function (xhr) {
            Dcat.warning(xhr.responseJSON.message);
        }
    });

    return false;
});
JS;
        Admin::script($script);
    }

    public function __construct()
    {
        parent::__construct();
        $this->script();
        
        // 添加全局日期选择器监听
        Admin::script($this->getDatepickerEnhancementScript());
    }
    
    /**
     * 获取日期选择器增强脚本
     *
     * @return string
     */
    protected function getDatepickerEnhancementScript(): string
    {
        return <<<'JS'
        $(function() {
            // 日历选择器增强监听
            function enhanceDatepicker() {
                console.log('初始化日期选择器增强监听');
                
                // 监听所有日期输入字段的变化
                $(document).on('input', 'input[type="date"], input.layui-input[name="business_date"]', function() {
                    console.log('日期输入框值变化:', $(this).val());
                    $(this).trigger('change');
                });
                
                // 针对laydate实例的监听
                $(document).on('click', function(e) {
                    // 检查是否点击了日期选择器中的元素
                    if ($(e.target).closest('.layui-laydate').length) {
                        console.log('点击了日期选择器元素');
                        
                        // 检查是否是日期单元格
                        if ($(e.target).closest('.layui-laydate-content td').length ||
                            $(e.target).closest('.laydate-btns-confirm').length ||
                            $(e.target).closest('.laydate-btns-time').length ||
                            $(e.target).closest('.layui-laydate-footer span').length) {
                                
                            console.log('点击了日期选择元素，等待日期变化');
                            
                            // 延迟触发变化检查
                            setTimeout(function() {
                                $('input[name="business_date"]').each(function() {
                                    // 主动触发change事件
                                    console.log('主动触发业务日期变化:', $(this).val());
                                    $(this).trigger('change');
                                });
                            }, 300);
                        }
                    }
                });
                
                // 连接原生日期控件变化
                if (window.laydate) {
                    // Hook成功渲染后的事件
                    var originalRender = window.laydate.render;
                    window.laydate.render = function(options) {
                        var originalDone = options.done;
                        options.done = function(value, date) {
                            console.log('Laydate选择完成:', value, date);
                            
                            // 调用原始回调
                            if (typeof originalDone === 'function') {
                                originalDone(value, date);
                            }
                            
                            // 找到对应的输入框并触发change
                            var elem = $(options.elem);
                            if (elem.length) {
                                console.log('Laydate触发change:', elem.val(), '→', value);
                                elem.val(value).trigger('change');
                            }
                        };
                        return originalRender.call(this, options);
                    };
                    console.log('已Hook Laydate渲染函数');
                } else {
                    console.log('Laydate未定义，跳过Hook');
                }
            }
            
            // 初始化增强
            enhanceDatepicker();
            
            // 定时检查业务日期输入框的值变化
            setInterval(function() {
                // 使用data属性存储上一次检查的值
                $('input[name="business_date"]').each(function() {
                    var $this = $(this);
                    var lastValue = $this.data('last-checked-value');
                    var currentValue = $this.val();
                    
                    // 如果值发生变化，触发change事件
                    if (lastValue !== undefined && lastValue !== currentValue) {
                        console.log('业务日期值变化检测:', lastValue, '→', currentValue);
                        $this.trigger('change');
                    }
                    
                    // 存储当前值，用于下次比较
                    $this.data('last-checked-value', currentValue);
                });
            }, 1000);
        });
JS;
    }
    
    /**
     * 配置编辑表单的特定内容
     * 
     * @param Form $form
     * @return void
     */
    protected function configureEditForm(Form &$form): void
    {
        // 记录编辑过程的详细日志
        $this->order = TecH3cPurchaseOrderModel::find(request()->route('r_h3c_purchase_order'));

        Log::info('配置编辑表单详细日志', [
            'order_id' => $this->order ? $this->order->id : null,
            'review_status' => $this->order ? $this->order->review_status : null,
            'review_status_name' => $this->order ? TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS[$this->order->review_status] ?? '未知状态' : '无订单'
        ]);

        // 设置表单宽度为80%
        $form->width('80%');
        
        // 编辑时 第一行：供应商和联系人信息
        $form->row(function (Form\Row $row) {
            // 防御性检查
            if (!$this->order) {
                return;
            }

            $row->hidden('sndCompany_id', '供应商id')->readonly();

            // 安全地获取供应商联系人和名称
            $sndCompanyId = $this->order ? $this->order->sndCompany_id : null;
            
            // 获取供应商名称和联系人
            $contactPersons = TecSndContactModel::getContactsByCompany($sndCompanyId);
            
            // 获取供应商名称
            $supplierName = TecSndCompanyModel::getNameById($sndCompanyId);

            // 業務分類
            $row->width(3)->text('major_category_display', '業務分類')
                ->value("H3C仕入")
                ->readonly();

            // 供应商
            $row->width(6)->text('supplier_name', '供应商')
                ->value($supplierName)
                ->readonly();

            // 担当者
            $row->width(3)->select('contact_person', '担当者')
                ->options($contactPersons)
                ->default($this->order->contact_person_id ?? null)
                ->required();
        });

        // 编辑时 第二行：订单关联和币种信息
        $form->row(function (Form\Row $row) use ($form) {
            // 防御性检查
            if (!$this->order) {
                return;
            }

            $row->width(2)->text('incoterms', '貿易条件')
                ->value(config('purchase_config.incoterms')[$this->order->incoterms] ?? '')
                ->required()
                ->readonly();
            
            $row->width(2)->text('currency', '交易币种')
                ->value($this->order->currency)
                ->required()
                ->readonly();

            $row->width(2)->select('delivery_term', '纳期')
                ->options(\App\Admin\Config\PurchaseConfig::getDeliveryTerms())
                ->default($this->order->delivery_term ?? 2)
                ->required();

            $row->width(3)->number('exchange_rate', '当期参照汇率')->default(160);

            $row->width(3)->date('business_date', '业务日期')->required();
        });

        // 编辑时 第三行
        $form->row(function (Form\Row $row) {
            $row->width(4)->text('case_name', '案件名')->required();
            $row->width(4)->text('order_no', 'PO单号')->required();
            $row->width(2)->text('status_text', '发货状态')
                ->value(TecH3cPurchaseBaseModel::H3C_ORDER_STATUS[$this->order->order_status] ?? '未知状态')
                ->readonly();
        });

        // 编辑时 第四行
        $form->row(function (Form\Row $row) {
            $row->width(4)->text('quotation_no', '供应商报价单号');
            $row->width(6)->text('other', '备注')->saveAsString();
        });
        
        // 添加审核状态图标
        $form->row(function (Form\Row $row) {
            $row->width(2)->reviewiconh3c('review_status', '审核状态')
                ->value($this->order->review_status);
        });

        Log::info('configureEditForm------------');

        // 如果订单已审核，禁用所有字段
        if ($this->order && $this->order->review_status === TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS_OK) {
            $form->disableSubmitButton();
            $form->disableResetButton();
        }
    }

    /**
     * 处理订单审核
     */
    public function review(Request $request)
    {
        try {
            $orderId = $request->input('id');
            $status = (int)$request->input('status');
            
            // 获取订单
            $order = TecH3cPurchaseOrderModel::findOrFail($orderId);
            
            // 使用观察者处理审核
            app(TecH3cPurchaseOrderObserver::class)->review($order, $status);

            return response()->json([
                'status' => true,
                'message' => '审核成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理订单状态变更
     */
    public function changeOrderStatus(Request $request)
    {
        try {
            $orderId = $request->input('id');
            $status = (int)$request->input('status');
            
            // 获取订单
            $order = TecH3cPurchaseOrderModel::findOrFail($orderId);
            
            // 使用观察者处理状态变更
            app(TecH3cPurchaseOrderObserver::class)->changeOrderStatus($order, $status);

            return response()->json([
                'status' => true,
                'message' => '状态变更成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理订单复制
     */
    public function copy($id)
    {
        try {
            // 获取订单
            $order = TecH3cPurchaseOrderModel::findOrFail($id);
            
            // 使用观察者处理复制
            $newOrder = app(TecH3cPurchaseOrderObserver::class)->copy($order);

            return response()->json([
                'status' => true,
                'message' => '复制成功',
                'data' => [
                    'id' => $newOrder->id,
                    'order_no' => $newOrder->order_no
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 导出Excel
     */
    public function export($id)
    {
        try {
            $order = TecH3cPurchaseOrderModel::with([
                'items.productlist', 
                'items.unitInfo',  // 添加单位关联
                'supplier', 
                'contact'
            ])->findOrFail($id);
            $filename = 'purchase_order_' . $order->order_no . '.xlsx';
            
            // 创建临时文件
            $tempFile = storage_path('app/temp/' . $filename);
            if (!file_exists(storage_path('app/temp'))) {
                mkdir(storage_path('app/temp'), 0755, true);
            }
            
            // 记录导出操作
            Log::info('导出采购订单', [
                'order_id' => $id,
                'order_no' => $order->order_no,
                'temp_file' => $tempFile,
                'time' => now()
            ]);
            
            // 导出到临时文件
            Excel::store(new TecH3cPurchaseOrderExportExcel($order), 'temp/' . $filename, 'local');
            
            // 设置响应头
            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                'Cache-Control' => 'no-store, no-cache, must-revalidate, max-age=0',
                'Cache-Control' => 'post-check=0, pre-check=0',
                'Pragma' => 'no-cache',
                'Expires' => '0',
            ];
            
            // 返回文件并删除临时文件
            return response()->download($tempFile, $filename, $headers)->deleteFileAfterSend(true);
        } catch (ModelNotFoundException $e) {
            Log::error('导出订单失败 - 未找到订单', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            admin_error('错误', '未找到指定的订单');
            return back();
        } catch (\Exception $e) {
            Log::error('导出订单失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            admin_error('错误', '导出Excel时发生错误：' . $e->getMessage());
            return back();
        }
    }

    /**
     * 打印订单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\View\View|\Illuminate\Http\JsonResponse
     */
    public function print($id)
    {
        $order = TecH3cPurchaseOrderModel::with([
            'items.productlist',
            'items.unitInfo', // 添加单位关联
            'supplier.paymentTerm',
            'contact'
        ])->findOrFail($id);

        // 获取公司信息
        $companyInfo = \App\Models\TecH3cCompanyInfoModel::first();

        Log::info('Print Order Data:', [
            'order_id' => $id,
            'supplier' => $order->supplier,
            'payment_term' => $order->supplier->paymentTerm ?? null,
            'contact' => $order->contact ?? null,
            'incoterms' => $order->incoterms
        ]);

        return view('admin.h3c.tec_h3c_purchase_order_confirm', compact('order', 'companyInfo'));
    }
    /**
     * 配置查看表单
     * 
     * @param Form $form
     * @return void
     */
    protected function setFormForShow(Form &$form): void
    {
        // 禁用所有表单功能
        // $form->disableHeader();
        // $form->disableFooter();
        // $form->disableSubmitButton();
        // $form->disableResetButton();
        
        // 第一行：业务分类和供应商信息
        $form->row(function (Form\Row $row) {
            $row->width(3)->display('major_category_display', '業務分類')
                ->value("H3C仕入");
            $row->width(6)->display('supplier_name', '供应商')
                ->value($this->order->supplier ? $this->order->supplier->name : '-');
            $row->width(3)->display('contact_person', '担当者')
                ->value($this->order->contact ? $this->order->contact->name : '-');
        });

        // 第二行：订单关联和币种信息
        $form->row(function (Form\Row $row) {
            $row->width(2)->display('incoterms', '貿易条件')
                ->value(config('purchase_config.incoterms')[$this->order->incoterms] ?? '');
            $row->width(2)->display('currency', '交易币种')
                ->value($this->order->currency);
            $row->width(2)->display('delivery_term', '纳期')
                ->value(\App\Admin\Config\PurchaseConfig::getDeliveryTerms()[$this->order->delivery_term] ?? '');
            $row->width(3)->display('exchange_rate', '当期参照汇率')
                ->value($this->order->exchange_rate);
            $row->width(3)->display('business_date', '业务日期')
                ->value($this->order->business_date);
        });

        // 第三行
        $form->row(function (Form\Row $row) {
            $row->width(4)->display('case_name', '案件名')
                ->value($this->order->case_name);
            $row->width(4)->display('order_no', 'PO单号')
                ->value($this->order->order_no);
            $row->width(2)->display('review_status_text', '审核状态')
                ->value(TecH3cPurchaseBaseModel::H3C_REVIEW_STATUS[$this->order->review_status] ?? '未知状态')
                ->readonly();
            $row->width(2)->display('status_text', '发货状态')
                ->value(TecH3cPurchaseBaseModel::H3C_ORDER_STATUS[$this->order->order_status] ?? '未知状态')
                ->readonly();
        });

        // 第四行
        $form->row(function (Form\Row $row) {
            $row->width(4)->display('quotation_no', '供应商报价单号')
                ->value($this->order->quotation_no);
            $row->width(6)->text('other', '备注')
                ->value($this->order->other);
        });

        // 添加审核状态图标
        $form->row(function (Form\Row $row) {
            $row->width(2)->reviewiconh3c('review_status', '审核状态')
                ->value($this->order->review_status);
        });

        // 显示订单明细
        $form->row(function (Form\Row $row) {
            $row->width(12)->html(view('admin.h3c.tec_h3c_purchase_order_items', ['items' => $this->order->items]));
        });
    }

    /**
     * 新的导出 Action 处理方法
     *
     * @param Request $request
     * @return StreamedResponse|\Illuminate\Http\JsonResponse
     */
    public function exportAction(Request $request)
    {
        // 开启查询日志
        DB::enableQueryLog();
        
        // 记录导出请求
        Log::info('导出请求开始', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user' => Admin::user() ? Admin::user()->username : '未登录用户'
        ]);
        //调用TecH3cPurchaseOrderExporter的exportAction
        $exporter = new TecH3cPurchaseOrderExporter();
        return $exporter->exportAction($request);   
    }  

    /**
     * 为导出准备数据
     *
     * @param Collection $orders 订单集合
     * @return array 准备好的数据数组
     */
    protected function prepareExportDataForAction(Collection $orders): array
    {
        Log::info('开始准备导出数据', ['orders_count' => $orders->count()]);
        $rows = [];
        
        // 记录所有订单的contact_person
        $contactIds = $orders->pluck('contact_person')->filter()->toArray();
        Log::info('需要查询的联系人IDs', ['contact_ids' => $contactIds]);
        
        // 直接预加载联系人数据
        TecSndContactModel::findWithLog($contactIds);
        
        foreach ($orders as $order) {
            Log::info('处理订单', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'supplier_id' => $order->sndCompany_id,
                'contact_person_id' => $order->contact_person,
                'raw_order' => $order->toArray()
            ]);
            
            // 获取供应商信息
            $supplier = $order->supplier;
            Log::info('获取到供应商', [
                'supplier_found' => $supplier ? true : false,
                'supplier_data' => $supplier ? $supplier->toArray() : null
            ]);
            
            // 手动获取联系人对象
            $contact = null;
            if ($order->contact_person) {
                $contact = TecSndContactModel::findWithLog($order->contact_person);
                Log::info('直接查询联系人结果', [
                    'contact_id' => $order->contact_person,
                    'contact_found' => $contact ? true : false,
                    'contact_data' => $contact ? $contact->toArray() : null,
                    'sql' => DB::getQueryLog()[count(DB::getQueryLog()) - 1] ?? 'No query log'
                ]);
            }
            
            // 获取关联的联系人
            $contactRelation = $order->contact;
            Log::info('关联获取联系人', [
                'contact_found' => $contactRelation ? true : false,
                'contact_data' => $contactRelation ? $contactRelation->toArray() : null,
                'sql' => DB::getQueryLog()[count(DB::getQueryLog()) - 1] ?? 'No query log'
            ]);

            // 使用预加载的订单项，而不是重新查询
            $items = $order->items;
            Log::info('获取到订单项', ['items_count' => $items->count()]);
            
            // 如果没有订单项，添加一行基本信息
            if ($items->isEmpty()) {
                $rows[] = [
                    'case_name' => $order->case_name,
                    'order_no' => $order->order_no,
                    'supplier_name' => $supplier ? $supplier->name : '',
                    'supplier_abbr' => $supplier ? $supplier->abbreviation : '',
                    'contact_name' => $contact ? $contact->getFullNameAttribute() : '未找到联系人',
                    'business_date' => $order->business_date->format('Y-m-d'),
                    'incoterms' => $order->incoterms,
                    'currency' => $order->currency,
                    'exchange_rate' => $order->exchange_rate,
                    'product_name' => '',
                    'product_model' => '',
                    'quantity' => '',
                    'unit_price' => '',
                    'amount' => '',
                    'remarks' => $order->other,
                ];
                continue;
            }

            // 处理每个订单项
            foreach ($items as $item) {
                // 使用预加载的产品信息
                $productName = $item->productlist ? $item->productlist->product : '';
                $productModel = $item->productlist ? $item->productlist->product_bom : '';
                
                $rows[] = [
                    'case_name' => $order->case_name,
                    'order_no' => $order->order_no,
                    'supplier_name' => $supplier ? $supplier->name : '',
                    'supplier_abbr' => $supplier ? $supplier->abbreviation : '',
                    'contact_name' => $contact ? $contact->getFullNameAttribute() : '未找到联系人',
                    'business_date' => $order->business_date->format('Y-m-d'),
                    'incoterms' => $order->incoterms,
                    'currency' => $order->currency,
                    'exchange_rate' => $order->exchange_rate,
                    'product_name' => $productName,
                    'product_model' => $productModel,
                    'quantity' => $this->preventFormulaInjection($item->quantity),
                    'unit_price' => $this->preventFormulaInjection($item->standard_price),
                    'amount' => $this->preventFormulaInjection($item->quantity * $item->standard_price),
                    'remarks' => $item->remarks ?? '',
                ];
            }
        }
        
        Log::info('数据准备完成', ['total_rows' => count($rows)]);
        return $rows;
    }
    
    /**
     * 防止Excel公式注入
     * 通过在数字值前添加单引号，防止被Excel解析为公式
     *
     * @param mixed $value 需要处理的值
     * @return string 处理后的值
     */
    protected function preventFormulaInjection($value): string
    {
        // 如果值不是字符串，转换为字符串
        if (!is_string($value)) {
            $value = (string)$value;
        }
        
        // 检查是否以等号、加号或减号开头（可能是公式）
        if (preg_match('/^[=\+\-@]/', $value)) {
            // 在值前添加单引号防止被解析为公式
            return "'" . $value;
        }
        
        return $value;
    }

}
