<?php

namespace App\Admin\Controllers\H3c;

use App\Models\TecH3cWarehouseInventoryModel;
use App\Models\TecH3cProductListModel;
use App\Models\TecWarehouseModel;
use App\Models\TecWarehouseLocationModel;
use App\Models\TecProductUnitModel;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use App\Models\TecH3cWarehouseTransactionModel;
use App\Services\Warehouse\Entry\TecH3cWarehouseEntryItemService;
use Illuminate\Http\Request;

/**
 * H3C库存主表控制器
 * 
 * 负责库存信息的显示、查询和管理
 */
class TecH3cWarehouseInventoryController extends AdminController
{
    /**
     * 页面标题
     *
     * @return string
     */
    protected $title = '库存管理';

    /**
     * 列表页面
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecH3cWarehouseInventoryModel(), function (Grid $grid) {
            $grid->scrollbarX();
            // 预加载关联模型，提高查询性能并解决空字段问题
            $grid->model()->with([
                'product', 
                'warehouse', 
                'location', 
                'unit'
            ]);
            // 只显示可用数据不为0的数据
            $grid->model()->where('available_quantity', '>', 0);

            // 设置默认排序
            $grid->model()->orderBy('warehouse_id', 'asc')->orderBy('product_id', 'asc');

            // 禁用批量删除功能
            $grid->disableBatchDelete();


            // 基础信息
            $grid->column('id', 'ID')->sortable();
            $grid->column('product.product', '产品名称')
                ->display(function($productName) {
                    // 获取产品属性
                    $productZokusei = $this->product->product_zokusei ?? 'normal';
                    $isInspection = $this->product->is_inspection_machine ?? false;
                    
                    // 使用GridStyleHelper的方法获取颜色
                    $textColor = \App\Support\GridStyleHelper::getProductColorByAttribute($productZokusei, $isInspection);
                    return "<span style='color:{$textColor};'>{$productName}</span>";
                });
            $grid->column('product.product_bom', '产品BOM')
                ->display(function($bom) {
                    // 获取产品属性
                    $productZokusei = $this->product->product_zokusei ?? 'normal';
                    $isInspection = $this->product->is_inspection_machine ?? false;
                    
                    // 使用GridStyleHelper的方法获取带颜色的产品BOM
                    return \App\Support\GridStyleHelper::getColoredProductBom($bom, $productZokusei, $isInspection);
                });
            $grid->column('warehouse.name', '仓库');
            $grid->column('location.location_name', '货位');
            
            // 数量信息
            $grid->column('quantity', '总数量')->sortable();
            $grid->column('available_quantity', '可用数量')->sortable();
            $grid->column('locked_quantity', '锁定数量')->sortable();
            
            // 其他属性
            $grid->column('unit.unit_name', '单位');
            $grid->column('order_no', '订单号');
            $grid->column('status', '状态')->using(TecH3cWarehouseInventoryModel::$statusMap)
                ->label([
                    'normal' => 'success',
                    'locked' => 'danger'
                ]);
            
            // 时间信息
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');

            // 设置快速搜索框
            $grid->quickSearch(['product.product', 'product.product_bom', 'batch_no']);

            // 固定前两列和最后两列
            // $grid->fixColumns(2, -1);
            // 禁用创建按钮
            $grid->disableCreateButton();
            // 禁用行编辑和删除
            $grid->disableActions();
            // 允许导出
            $grid->export();

            // 设置筛选器
            $grid->filter(function (Grid\Filter $filter) {
                // 禁用默认的加载方式
                $filter->withoutLoading();

                // 使用面板布局
                $filter->panel();

                $filter->equal('product_id', '产品')->select(function () {
                    return TecH3cWarehouseInventoryModel::with('product')
                        ->distinct()
                        ->whereNotNull('product_id')
                        ->get()
                        ->pluck('product.product', 'product_id')
                        ->filter()
                        ->toArray();
                })->width(3);
                $filter->equal('product_bom', '产品BOM')->select(function () {
                    return TecH3cWarehouseInventoryModel::distinct()
                        ->whereNotNull('product_bom')
                        ->where('product_bom', '!=', '')
                        ->pluck('product_bom', 'product_bom')
                        ->toArray();
                })->width(3);


                $filter->equal('warehouse_id', '仓库')->select(function () {
                    return TecH3cWarehouseInventoryModel::with('warehouse')
                        ->distinct()
                        ->whereNotNull('warehouse_id')
                        ->get()
                        ->pluck('warehouse.name', 'warehouse_id')
                        ->filter()
                        ->toArray();
                })->width(3);

                $filter->equal('location_id', '货位')->select(function () {
                    return TecH3cWarehouseInventoryModel::with('location')
                        ->distinct()
                        ->whereNotNull('location_id')
                        ->get()
                        ->pluck('location.location_name', 'location_id')
                        ->filter()
                        ->toArray();
                })->width(3);

                $filter->equal('status', '状态')->select(
                    TecH3cWarehouseInventoryModel::$statusMap
                )->width(3);

                // $filter->equal('batch_no', '批次号')->width(3);
                
                // 库存状态筛选 - 使用自定义字段
                $filter->where('has_stock', function ($query) {
                    $hasStock = request('has_stock');
                    if ($hasStock === '1') {
                        $query->where('quantity', '>', 0);
                    } elseif ($hasStock === '0') {
                        $query->where('quantity', '<=', 0);
                    }
                }, '库存状态')->select([
                    '1' => '有库存',
                    '0' => '无库存'
                ])->width(3);

                $filter->between('created_at', '创建时间')->datetime()->width(3);
            });
        });
    }

    /**
     * 详情页面
     *
     * @param $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecH3cWarehouseInventoryModel(), function (Show $show) {
            // 基础信息
            $show->field('id', 'ID');
            $show->field('product.product', '产品名称');
            $show->field('product.product_bom', '产品BOM');
            $show->field('warehouse.name', '仓库');
            $show->field('location.location_name', '货位');
            
            // 数量信息
            $show->field('quantity', '总数量');
            $show->field('available_quantity', '可用数量');
            $show->field('locked_quantity', '锁定数量');
            
            // 其他属性
            $show->field('unit.unit_name', '单位');
            $show->field('order_no', '订单号');
            $show->field('status', '状态')->using(TecH3cWarehouseInventoryModel::$statusMap)
                ->label([
                    'normal' => 'success',
                    'locked' => 'danger'
                ]);
            
            // 时间信息
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            // 禁用删除按钮
            $show->disableDeleteButton();
            
            // 查询该库存相关的交易记录
            $show->divider('库存交易记录');
            $show->relation('transactions', function ($model) {
                $grid = new Grid(new TecH3cWarehouseTransactionModel());
                
                $grid->model()->where([
                    'product_id' => $model->product_id,
                    'location_id' => $model->location_id,
                ])->when($model->batch_no, function ($query) use ($model) {
                    $query->where('batch_no', $model->batch_no);
                })->orderBy('created_at', 'desc');
                
                $grid->column('transaction_no', '交易编号');
                $grid->column('type', '类型')
                    ->using(TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE)
                    ->label(TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_COLOR);
                $grid->column('quantity', '操作数量');
                $grid->column('before_quantity', '操作前数量');
                $grid->column('after_quantity', '操作后数量');
                $grid->column('operator.name', '操作人');
                $grid->column('created_at', '操作时间');
                
                // 禁用操作列
                $grid->disableActions();
                // 禁用创建按钮
                $grid->disableCreateButton();
                // 禁用批量操作
                $grid->disableBatchActions();
                
                return $grid;
            });
        });
    }

    /**
     * 表单页面
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecH3cWarehouseInventoryModel(), function (Form $form) {
            $form->display('id', 'ID');
            
            // 选择产品
            $form->select('product_id', '产品')
                ->options(TecH3cProductListModel::pluck('product', 'id'))
                ->required();
            
            // 选择仓库
            $form->select('warehouse_id', '仓库')
                ->options(TecWarehouseModel::pluck('name', 'id'))
                ->required();
            
            // 选择货位
            $form->select('location_id', '货位')
                ->options(function () {
                    return TecWarehouseLocationModel::all()
                        ->pluck('location_name', 'id')
                        ->toArray();
                })
                ->required();
            
            // 数量信息
            $form->decimal('quantity', '总数量')->default(0);
            $form->decimal('available_quantity', '可用数量')->default(0);
            $form->decimal('locked_quantity', '锁定数量')->default(0)->readonly();
            
            // 其他属性
            $form->select('unit_id', '单位')
                ->options(TecProductUnitModel::pluck('unit_name', 'id'))
                ->required();
            $form->text('batch_no', '批次号');
            $form->select('status', '状态')
                ->options(TecH3cWarehouseInventoryModel::$statusMap)
                ->default('normal');
            
            // 时间信息
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            
            // 数量验证
            $form->saving(function (Form $form) {
                // 确保总数量 = 可用数量 + 锁定数量
                if ($form->isEditing()) {
                    $model = $form->model();
                    
                    // 修改: 总是确保可用数量 = 总数量
                    $form->available_quantity = $form->quantity;
                    
                    // 如果总数量小于锁定数量，则报错
                    if ($form->quantity < $form->locked_quantity) {
                        return $form->response()->error('总数量不能小于锁定数量');
                    }
                } else {
                    // 新建记录，确保数量一致
                    $form->available_quantity = $form->quantity;
                    $form->locked_quantity = 0;
                }
            });
            
            // 禁止删除
            $form->disableDeleteButton();
            
            // 编辑时禁止修改某些字段
            if ($form->isEditing()) {
                $form->select('product_id', '产品')->readOnly();
                $form->select('warehouse_id', '仓库')->readOnly();
                $form->select('location_id', '货位')->readOnly();
                $form->select('unit_id', '单位')->readOnly();
                $form->text('batch_no', '批次号')->readOnly();
            }
        });
    }
    
    /**
     * 锁定库存
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function lock($id)
    {
        $inventory = TecH3cWarehouseInventoryModel::find($id);
        
        if (!$inventory) {
            return response()->json(['message' => '库存记录不存在'], 404);
        }
        
        $inventory->status = TecH3cWarehouseInventoryModel::STATUS_LOCKED;
        $inventory->save();
        
        return response()->json(['message' => '库存已锁定']);
    }
    
    /**
     * 解锁库存
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function unlock($id)
    {
        $inventory = TecH3cWarehouseInventoryModel::find($id);
        
        if (!$inventory) {
            return response()->json(['message' => '库存记录不存在'], 404);
        }
        
        $inventory->status = TecH3cWarehouseInventoryModel::STATUS_NORMAL;
        $inventory->save();
        
        return response()->json(['message' => '库存已解锁']);
    }

    /**
     * 获取在库产品信息（可用库存大于0）
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInventoryProducts(Request $request)
    {
        $q = $request->get('q');
        $warehouseId = $request->get('warehouse_id');
        
        // 使用模型方法获取有可用库存的产品
        $query = TecH3cWarehouseInventoryModel::getProductsWithAvailableStock($q, $warehouseId);
        
        // 使用get方法代替paginate,因为Select2只需要数据集合
        $results = $query->get(['id', 'product as text']);
        
        return response()->json([
            'status' => true,
            'results' => $results,
            'pagination' => ['more' => false]
        ]);
    }

    /**
     * 获取指定产品在指定货位的库存数量
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInventoryStock(Request $request)
    {
        $productId = $request->get('product_id');
        $locationId = $request->get('location_id');
        $warehouseId = $request->get('warehouse_id');
        $orderNo = $request->get('order_no');
        
        \Illuminate\Support\Facades\Log::info('获取库存数量请求', [
            'product_id' => $productId,
            'location_id' => $locationId,
            'warehouse_id' => $warehouseId,
            'order_no' => $orderNo,
            'request_data' => $request->all()
        ]);
        
        if (!$productId || !$locationId || !$warehouseId) {
            return response()->json([
                'status' => false,
                'message' => '参数不完整',
                'available' => 0,
                'locked' => 0,
                'total' => 0
            ]);
        }
        
        // 构建查询条件
        $conditions = [
            'product_id' => $productId,
            'location_id' => $locationId,
            'warehouse_id' => $warehouseId,
        ];
        
        // 如果提供了订单号,添加到查询条件
        if (!empty($orderNo)) {
            $conditions['order_no'] = $orderNo;
        }
        
        // 查询指定条件的库存数量，优先返回有可用库存的记录
        $inventory = TecH3cWarehouseInventoryModel::where($conditions)
            ->orderBy('available_quantity', 'desc')
            ->first();
        
        if (!$inventory) {
            // 如果指定了订单号但没找到,尝试不使用订单号再查询一次
            if (!empty($orderNo)) {
                unset($conditions['order_no']);
                $inventory = TecH3cWarehouseInventoryModel::where($conditions)->first();
            }
            
            // 仍未找到记录
            if (!$inventory) {
                return response()->json([
                    'status' => false,
                    'message' => '未找到库存记录',
                    'available' => 0,
                    'locked' => 0,
                    'total' => 0
                ]);
            }
        }
        
        \Illuminate\Support\Facades\Log::info('查询到库存记录', [
            'inventory_id' => $inventory->id,
            'available' => $inventory->available_quantity,
            'order_no' => $inventory->order_no
        ]);
        
        return response()->json([
            'status' => true,
            'message' => '获取成功',
            'available' => $inventory->available_quantity,
            'locked' => $inventory->locked_quantity,
            'total' => $inventory->quantity,
            'unit' => $inventory->unit->unit_name ?? '',
            'order_no' => $inventory->order_no ?? '',
            'inventory_id' => $inventory->id
        ]);
    }

    /**
     * 获取指定产品在仓库中的货位列表
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductLocations(Request $request)
    {
        try {
            $productId = $request->get('product_id');
            $warehouseId = $request->get('warehouse_id');
            $withOrder = $request->get('with_order', 0);
            
            \Illuminate\Support\Facades\Log::info('获取产品货位请求', [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'with_order' => $withOrder,
                'request_data' => $request->all()
            ]);
            
            if (empty($productId) || empty($warehouseId)) {
                return response()->json([
                    'status' => false,
                    'message' => '产品ID和仓库ID不能为空',
                    'data' => []
                ]);
            }
            
            // 查询该产品在指定仓库中的所有货位
            $query = TecH3cWarehouseInventoryModel::where('product_id', $productId)
                ->where('warehouse_id', $warehouseId);

            // 添加调试信息 - 记录查询条件和SQL
            \Illuminate\Support\Facades\Log::info('获取产品货位 - 查询条件', [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]);

            // 加载关联模型
            if ($withOrder) {
                $inventories = $query->with(['location'])->get();
            } else {
                $inventories = $query->with(['location'])->get();
            }

            // 添加调试信息 - 记录查询结果
            \Illuminate\Support\Facades\Log::info('获取产品货位 - 查询结果', [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'inventories_count' => $inventories->count(),
                'first_inventory' => $inventories->first() ? [
                    'id' => $inventories->first()->id,
                    'location_id' => $inventories->first()->location_id,
                    'available_quantity' => $inventories->first()->available_quantity,
                    'has_location' => $inventories->first()->location ? true : false,
                    'location_name' => $inventories->first()->location ? $inventories->first()->location->location_name : null
                ] : null
            ]);
                
            $locations = [];
            foreach ($inventories as $inventory) {
                if ($inventory->location && $inventory->location->id) {
                    $locationData = [
                        'id' => $inventory->location->id,
                        'text' => $inventory->location->location_name . ' (可用:' . $inventory->available_quantity . ')',
                        'available_quantity' => $inventory->available_quantity,
                        'warehouse_id' => $inventory->warehouse_id,
                        // 确保返回订单信息
                        'order_no' => $inventory->order_no,
                        'order_id' => $inventory->order_id,
                        'batch_no' => $inventory->batch_no
                    ];

                    $locations[] = $locationData;
                } else {
                    // 记录没有关联货位的库存记录
                    \Illuminate\Support\Facades\Log::warning('库存记录缺少货位关联', [
                        'inventory_id' => $inventory->id,
                        'product_id' => $inventory->product_id,
                        'warehouse_id' => $inventory->warehouse_id,
                        'location_id' => $inventory->location_id,
                        'available_quantity' => $inventory->available_quantity
                    ]);
                }
            }

            // 如果没有找到货位，尝试备用查询
            if (empty($locations)) {
                \Illuminate\Support\Facades\Log::info('主查询未找到货位，尝试备用查询', [
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId
                ]);

                // 备用查询：检查是否有该产品的库存记录（不考虑仓库）
                $allInventories = TecH3cWarehouseInventoryModel::where('product_id', $productId)
                    ->with(['location', 'warehouse'])
                    ->get();

                \Illuminate\Support\Facades\Log::info('备用查询结果', [
                    'product_id' => $productId,
                    'all_inventories_count' => $allInventories->count(),
                    'sample_inventory' => $allInventories->first() ? [
                        'id' => $allInventories->first()->id,
                        'warehouse_id' => $allInventories->first()->warehouse_id,
                        'warehouse_name' => $allInventories->first()->warehouse ? $allInventories->first()->warehouse->name : null,
                        'location_id' => $allInventories->first()->location_id,
                        'location_name' => $allInventories->first()->location ? $allInventories->first()->location->location_name : null,
                        'available_quantity' => $allInventories->first()->available_quantity
                    ] : null
                ]);
            }
            
            \Illuminate\Support\Facades\Log::info('获取产品货位成功', [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'locations_count' => count($locations),
                'with_order' => $withOrder,
                'sample_data' => !empty($locations) ? $locations[0] : null
            ]);
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $locations
            ]);
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取产品货位失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取产品货位失败: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 获取产品信息
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductInfo(Request $request)
    {
        try {
            $productId = (int)$request->get('product_id');
            
            \Illuminate\Support\Facades\Log::info('获取产品信息请求', [
                'product_id' => $productId,
                'request_data' => $request->all()
            ]);
            
            if (empty($productId)) {
                return response()->json([
                    'status' => false,
                    'message' => '产品ID不能为空',
                    'data' => null
                ]);
            }
            
            // 获取产品信息
            $product = TecH3cProductListModel::find($productId);
            
            if (!$product) {
                return response()->json([
                    'status' => false,
                    'message' => '未找到产品信息',
                    'data' => null
                ]);
            }
            
            // 获取产品单位信息
            $unitId = $product->unit_id ?? 1; // 默认为1
            $unit = TecProductUnitModel::find($unitId);
            $unitName = $unit ? $unit->unit_name : '个';
            
            $result = [
                'product_id' => $product->id,
                'product_name' => $product->product,
                'product_code' => $product->product_bom ?? '',
                'product_bom' => $product->product_bom ?? '',
                'unit_id' => $unitId,
                'unit_name' => $unitName,
                'product_status' => $product->status ?? 'normal'
            ];
            
            \Illuminate\Support\Facades\Log::info('获取产品信息成功', [
                'product_id' => $productId,
                'product_name' => $product->product,
                'unit_id' => $unitId
            ]);
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取产品信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取产品信息失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取产品单位信息
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductUnit(Request $request)
    {
        try {
            $productId = $request->get('product_id');
            
            \Illuminate\Support\Facades\Log::info('获取产品单位请求', [
                'product_id' => $productId,
                'request_data' => $request->all()
            ]);
            
            if (empty($productId)) {
                return response()->json([
                    'status' => false,
                    'message' => '产品ID不能为空',
                    'data' => null
                ]);
            }
            
            // 获取产品信息
            $product = TecH3cProductListModel::find($productId);
            
            if (!$product) {
                return response()->json([
                    'status' => false,
                    'message' => '未找到产品信息',
                    'data' => null
                ]);
            }
            
            // 获取产品单位信息
            $unitId = $product->unit_id ?? 1; // 默认为1
            $unit = TecProductUnitModel::find($unitId);
            
            $result = [
                'unit_id' => $unitId,
                'unit_name' => $unit ? $unit->unit_name : '个'
            ];
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取产品单位失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取产品单位失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
