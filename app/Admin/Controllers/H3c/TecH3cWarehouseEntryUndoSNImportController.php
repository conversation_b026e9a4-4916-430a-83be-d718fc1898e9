<?php

declare(strict_types=1);

namespace App\Admin\Controllers\H3c;

use App\Admin\Controllers\H3c\TecH3cWarehouseEntrySNImportController as BaseController;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Imports\TecH3cWarehouseEntrySNBatchImport;
use App\Models\TecH3cWarehouseEntryModel;
use App\Config\PurchaseConfig;
use App\Models\TecH3cWarehouseEntrySNModel;

class TecH3cWarehouseEntryUndoSNImportController extends BaseController
{
    // 复用导入 SN 控制器所有逻辑，实现撤销导入功能时可继承并按需覆盖。
    // 若需调整行为，可重写 importByEntry 方法。
    /**
     * 标题
     * 
     * @var string
     */
    protected $title = 'H3C入库单解绑SN';
    // 获取表单提交地址（解绑）
    protected function getActionUrl()
    {
        return admin_url('r_warehouse_entries_h3c/sn/undo-import-by-entry');
    }

    /**
     * 基于入库单解绑SN（删除未出库的SN记录）
     */
    public function importByEntry(Request $request)
    {
        // 验证入库单参数
        $request->validate([
            'entry_id' => 'required|integer|exists:t_warehouse_entries_h3c,id'
        ]);
        $entryId = intval($request->entry_id);
        $isAjax = $request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest';
        try {
            DB::beginTransaction();
            // 删除 warehouse_exit_id 为空的记录
            $deleted = TecH3cWarehouseEntrySNModel::where('entry_id', $entryId)
                ->whereNull('warehouse_exit_id')
                ->delete();
            DB::commit();
            Log::info("解绑SN完成: entry_id={$entryId}, deleted={$deleted}");
            $message = "成功解绑 {$deleted} 条SN记录";
            if ($isAjax) {
                return response()->json(['status' => true, 'message' => $message]);
            }
            admin_success('解绑成功', $message);
            return redirect()->back();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('解绑SN失败: ' . $e->getMessage());
            if ($isAjax) {
                return response()->json(['status' => false, 'message' => '解绑失败: ' . $e->getMessage()], 500);
            }
            admin_error('解绑失败', $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * 入库单解绑表单
     */
    protected function importByEntryForm()
    {
        // 生成自定义解绑表单 HTML
        $html = <<<HTML
<input type="hidden" id="csrf_token" value="{$this->getCsrfToken()}">
<div class="form-group row">
    <label for="entry_id" class="col-sm-2 control-label">入库单</label>
    <div class="col-sm-8">
        <select class="form-control" name="entry_id" required>
            <option value="">请选择入库单</option>
HTML;
        // 获取所有入库单及关联数据
        $entries = TecH3cWarehouseEntryModel::with(['order', 'items.product'])->orderBy('id', 'desc')->get();
        foreach ($entries as $entry) {
            // 统计待解绑的SN记录数
            $pendingCount = TecH3cWarehouseEntrySNModel::where('entry_id', $entry->id)
                ->whereNull('warehouse_exit_id')
                ->count();
            if ($pendingCount === 0) {
                continue;
            }
            // 构建显示标题
            $title = $entry->entry_no;
            if ($entry->order) {
                $title .= ' - ' . $entry->order->order_no;
            }
            // 产品信息统计
            $snCounts = TecH3cWarehouseEntrySNModel::where('entry_id', $entry->id)
                ->groupBy('product_id')
                ->select('product_id', DB::raw('count(*) as sn_count'))
                ->get()
                ->pluck('sn_count', 'product_id');
            $details = [];
            foreach ($entry->items as $item) {
                $bom = $item->product_bom ?: ($item->product->product_bom ?? 'Unknown');
                $quantity = $item->actual_quantity ?? 0;
                $snCount = $snCounts[$item->product_id] ?? 0;
                if ($quantity > 0) {
                    $details[] = "BOM:{$bom} 数量:{$quantity}, SN记录:{$snCount}";
                }
            }
            if (!empty($details)) {
                $title .= ' (' . implode(', ', $details) . ')';
            }
            $html .= "<option value=\"{$entry->id}\">{$title}</option>";
        }
        $html .= <<<HTML
        </select>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-8 offset-sm-2">
        <button type="button" id="sn-undo-btn" class="btn btn-danger">解绑</button>
    </div>
</div>
HTML;
        // 进度条容器
        $script = <<<JS
$(function() {
    // CSRF
    $.ajaxSetup({ headers: { 'X-CSRF-TOKEN': $('#csrf_token').val() } });
    // 插入进度条
    var progress = $('<div class="form-group row" id="progress-container" style="display:none">' +
        '<div class="col-sm-8 offset-sm-2">' +
        '<div class="progress" style="height:20px;margin-bottom:10px;">' +
        '<div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-warning" role="progressbar" style="width:0%"></div>' +
        '</div>' +
        '<p id="progress-text" class="text-center">准备解绑...</p>' +
        '</div>' +
        '</div>');
    $('.form-group.row:last').before(progress);
    $('#sn-undo-btn').click(function() {
        var entryId = $('select[name="entry_id"]').val();
        if (!entryId) { Dcat.error('请选择入库单'); return; }
        $('#progress-container').show();
        $('#progress-bar').css('width','5%');
        $('#progress-text').text('解绑中...');
        $(this).prop('disabled',true).text('解绑中...');
        var data = new FormData();
        data.append('_token',$('#csrf_token').val());
        data.append('entry_id',entryId);
        $.ajax({
            url: '{$this->getActionUrl()}',
            type:'POST', processData:false, contentType:false,
            data:data,
            success:function(res){
                if(res.status) Dcat.success(res.message);
                else Dcat.error(res.message);
                $('#progress-bar').css('width','100%');
                $('#progress-text').text('完成');
                setTimeout(function(){window.location.reload();},2000);
            },
            error:function(){ Dcat.error('解绑失败'); }
        });
    });
});
JS;
        return $html . Admin::script($script);
    }

    // 如需自定义路由地址，可重写 getActionUrl 方法。当前继承自父类。
}
