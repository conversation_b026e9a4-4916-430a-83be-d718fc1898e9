<?php

namespace App\Admin\Controllers\H3c;

use App\Models\TecH3cWarehouseInventoryModel;
use App\Models\TecH3cWarehouseTransactionModel;
use App\Models\TecWarehouseModel;
use App\Models\TecH3cWarehouseEntryModel;
use App\Models\TecH3cWarehouseEntryItemModel;
use App\Models\TecH3cPurchaseOrderModel;
use App\Models\TecH3cPurchaseOrderItemModel;
use App\Models\TecWarehouseLocationModel;
use App\Models\TecH3cProductListModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Admin\Config\PurchaseConfig;

/**
 * 库存来源追溯控制器
 * 
 * 用于查询和显示库存记录的来源,包括入库单和采购订单信息
 */
class TecH3cWarehouseInventorySourceController extends AdminController
{
    /**
     * 异常处理中间件，捕获任何方法中的异常避免崩溃
     *
     * @param \Closure $next
     * @return mixed
     */
    public function __invoke($method, $parameters)
    {
        try {
            return parent::__invoke($method, $parameters);
        } catch (\Exception $e) {
            // 记录异常
            Log::error('库存来源追溯控制器异常', [
                'method' => $method,
                'parameters' => $parameters,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 判断请求类型，API请求返回JSON，否则返回错误页面
            if (request()->expectsJson()) {
                return response()->json(['error' => '系统错误: ' . $e->getMessage()], 500);
            }
            
            return admin_error('系统错误', '操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 标题
     *
     * @return string
     */
    protected $title = '库存来源追溯';

    /**
     * 列表页
     *
     * @return Grid
     */
    protected function grid()
    {
        try {
            // 清除缓存避免关联问题
            \Illuminate\Support\Facades\Cache::flush();
            
            return Grid::make(new TecH3cWarehouseInventoryModel(), function (Grid $grid) {
                // 安全的关联加载，避免使用entryTransactions
                $grid->model()->with([
                    'product',
                    'warehouse',
                    'location'
                ])->orderBy('id', 'desc');
                
                // 库存基本信息
                $grid->column('id', 'ID')->sortable();
                
                // 产品基本信息 - 聚合显示
                $grid->column('产品信息')->display(function() {
                    $product_id = $this->product_id ?? null;
                    
                    if (empty($product_id)) {
                        return '<span class="badge badge-secondary">无产品信息</span>';
                    }
                    
                    try {
                        // 直接从数据库获取产品信息
                        $product = TecH3cProductListModel::find($product_id);
                        if (!$product) {
                            return '<span class="badge badge-secondary">产品不存在</span>';
                        }
                        
                        // 获取产品属性
                        $zokusei = $product->product_zokusei ?? 'normal';
                        $zokuseiMap = PurchaseConfig::getProductZokusei();
                        $zokuseiText = $zokuseiMap[$zokusei] ?? $zokusei;
                        
                        // 定义状态标签颜色
                        $labels = [
                            'normal' => 'success',
                            'rr' => 'warning',
                            'inspection' => 'info'
                        ];
                        
                        $label = isset($labels[$zokusei]) ? $labels[$zokusei] : 'default';
                        
                        return sprintf(
                            "<strong>%s</strong><br>%s<br><span class='label label-%s'>%s</span>",
                            $product->product ?? '未知产品',
                            $product->product_bom ?? '-',
                            $label,
                            $zokuseiText
                        );
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('产品信息显示错误: ' . $e->getMessage());
                        return '产品信息加载失败';
                    }
                });
                
                // 库存状态和数量 - 聚合显示
                $grid->column('库存状态')->display(function() {
                    try {
                        $statusMap = TecH3cWarehouseInventoryModel::$statusMap ?? [
                            'normal' => '正常',
                            'locked' => '锁定',
                            'available' => '可用(回滚)',
                            'reserved' => '已预留',
                            'disabled' => '禁用',
                            'pending' => '待处理'
                        ];
                        
                        $status = $this->getAttribute('status');
                        $text = $statusMap[$status] ?? $status;
                        
                        // 定义状态标签颜色
                        $labels = [
                            'normal' => 'success',
                            'locked' => 'warning',
                            'available' => 'info',
                            'reserved' => 'info',
                            'disabled' => 'danger',
                            'pending' => 'default'
                        ];
                        
                        $label = isset($labels[$status]) ? $labels[$status] : 'default';
                        
                        return sprintf(
                            "<span class='label label-%s'>%s</span><br>总数量: <b>%s</b><br>可用: %s<br>锁定: %s",
                            $label,
                            $text,
                            $this->getAttribute('quantity') ?? 0,
                            $this->getAttribute('available_quantity') ?? 0,
                            $this->getAttribute('locked_quantity') ?? 0
                        );
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('库存状态显示错误: ' . $e->getMessage());
                        return $this->getAttribute('status');
                    }
                });
                
                // 仓库和货位信息 - 聚合显示
                $grid->column('仓库货位')->display(function() {
                    $warehouse_id = $this->warehouse_id;
                    $location_id = $this->location_id;
                    
                    // 手动获取关联数据
                    $warehouse = $warehouse_id ? TecWarehouseModel::find($warehouse_id) : null;
                    $location = $location_id ? TecWarehouseLocationModel::find($location_id) : null;
                    
                    $warehouseName = $warehouse ? $warehouse->name : '-';
                    $locationName = $location ? ($location->location_name ?? $location->text ?? $location->code) : '-';
                    $warehouseAddress = $warehouse ? ($warehouse->address ?? '') : '';
                    
                    return sprintf(
                        "<strong>%s</strong><br>%s<br>%s",
                        $warehouseName,
                        $locationName,
                        !empty($warehouseAddress) ? "<small>{$warehouseAddress}</small>" : ""
                    );
                });
                
                // 采购单号信息
                $grid->column('采购单来源')->display(function() {
                    $product_id = $this->product_id ?? null;
                    $warehouse_id = $this->warehouse_id ?? null;
                    $location_id = $this->location_id ?? null;
                    
                    if (empty($product_id) || empty($warehouse_id) || empty($location_id)) {
                        return '-';
                    }
                    
                    try {
                        // 使用原生查询获取事务记录
                        $transactions = DB::table('t_warehouse_transactions_h3c')
                            ->where('product_id', $product_id)
                            ->where('warehouse_id', $warehouse_id)
                            ->where('location_id', $location_id)
                            ->where('type', 'in')
                            ->whereNotNull('order_id')
                            ->limit(5)
                            ->get();
                        
                        if ($transactions->isEmpty()) {
                            return '<span class="badge badge-warning">无关联采购单</span>';
                        }
                        
                        // 保存已处理的order_id,避免重复
                        $processedOrderIds = [];
                        $result = [];
                        
                        foreach($transactions as $transaction) {
                            if (empty($transaction->order_id)) continue;
                            
                            // 避免重复处理
                            if (in_array($transaction->order_id, $processedOrderIds)) continue;
                            $processedOrderIds[] = $transaction->order_id;
                            
                            $order = DB::table('t_purchase_order_h3c')->find($transaction->order_id);
                            if (!$order || empty($order->order_no)) continue;
                            
                            // 获取供应商信息
                            $supplier = !empty($order->sndCompany_id) ? 
                                DB::table('t_snd_companies')->where('id', $order->sndCompany_id)->first() : null;
                            $supplierName = $supplier ? ($supplier->name ?? '-') : '-';
                            
                            // 生成采购单链接
                            $orderLink = sprintf(
                                "<a href='/admin/r_h3c_purchase_orders?order_no=%s' target='_blank' class='label label-primary'>%s</a>",
                                $order->order_no,
                                $order->order_no
                            );
                            
                            // 采购单追溯链接
                            $traceLink = sprintf(
                                "<a href='/admin/h3c/purchase-trace?采购单号=%s' target='_blank' class='btn btn-sm btn-info'><i class='feather icon-search'></i> 追溯</a>",
                                $order->order_no
                            );
                            
                            // 构建完整信息
                            $result[] = sprintf(
                                "%s<br>供应商: <strong>%s</strong><br>日期: %s<br>项目: %s<br>%s",
                                $orderLink,
                                $supplierName,
                                $order->business_date ? date('Y-m-d', strtotime($order->business_date)) : '-',
                                $order->case_name ?? '-',
                                $traceLink
                            );
                        }
                        
                        return !empty($result) ? implode("<hr>", $result) : '<span class="badge badge-warning">无关联采购单</span>';
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('采购单来源显示错误: ' . $e->getMessage());
                        return '信息加载失败: ' . $e->getMessage();
                    }
                });
                
                // 入库单信息 - 使用最简单的查询方式
                $grid->column('入库信息')->display(function() {
                    $product_id = $this->product_id ?? null;
                    $location_id = $this->location_id ?? null;
                    
                    if (empty($product_id) || empty($location_id)) {
                        return '-';
                    }
                    
                    try {
                        // 直接使用原生SQL查询入库明细，避免关联
                        $entryItems = DB::table('t_warehouse_entry_items_h3c')
                            ->where('product_id', $product_id)
                            ->where('location_id', $location_id)
                            ->limit(5)
                            ->get();
                        
                        if ($entryItems->isEmpty()) {
                            return '<span class="badge badge-warning">无入库记录</span>';
                        }
                        
                        $result = [];
                        foreach ($entryItems as $item) {
                            if (empty($item->entry_id)) continue;
                            
                            $entry = DB::table('t_warehouse_entries_h3c')->find($item->entry_id);
                            if (!$entry || empty($entry->entry_no)) continue;
                            
                            // 生成入库单链接
                            $entryLink = sprintf(
                                "<a href='/admin/r_h3c_warehouse_entries?entry_no=%s' target='_blank' class='label label-success'>%s</a>",
                                $entry->entry_no,
                                $entry->entry_no
                            );
                            
                            // 构建完整信息
                            $result[] = sprintf(
                                "%s<br>日期: %s<br>类型: %s<br>入库数量: <strong>%s</strong><br>备注: %s",
                                $entryLink,
                                $entry->entry_date ? date('Y-m-d', strtotime($entry->entry_date)) : '-',
                                $entry->entry_type ?? '-',
                                $item->quantity ?? $item->actual_quantity ?? '-',
                                $entry->remark ?? '-'
                            );
                        }
                        
                        if (count($entryItems) >= 5) {
                            $result[] = '<span class="badge badge-info">仅显示前5条记录</span>';
                        }
                        
                        return !empty($result) ? implode("<hr>", $result) : '<span class="badge badge-warning">无入库记录</span>';
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('入库信息显示错误: ' . $e->getMessage());
                        return '信息加载失败: ' . $e->getMessage();
                    }
                });
                
                // 出库信息
                $grid->column('出库信息')->display(function() {
                    $product_id = $this->product_id ?? null;
                    $warehouse_id = $this->warehouse_id ?? null;
                    $location_id = $this->location_id ?? null;
                    
                    if (empty($product_id) || empty($warehouse_id) || empty($location_id)) {
                        return '-';
                    }
                    
                    try {
                        // 使用原生查询
                        $transactions = DB::table('t_warehouse_transactions_h3c')
                            ->where('product_id', $product_id)
                            ->where('warehouse_id', $warehouse_id)
                            ->where('location_id', $location_id)
                            ->where('type', 'out')
                            ->limit(5)
                            ->get();
                        
                        if ($transactions->isEmpty()) {
                            return '<span class="badge badge-info">未出库</span>';
                        }
                        
                        $result = [];
                        $exitedQuantity = 0;
                        
                        foreach($transactions as $transaction) {
                            if (empty($transaction->source_id) || empty($transaction->source_type) || 
                                $transaction->source_type != 'warehouse_exit') {
                                continue;
                            }
                            
                            $exit = DB::table('t_warehouse_exit_h3c')->find($transaction->source_id);
                            if (!$exit || empty($exit->exit_no)) continue;
                            
                            // 生成出库单链接
                            $exitLink = sprintf(
                                "<a href='/admin/r_h3c_warehouse_exits?exit_no=%s' target='_blank' class='label label-danger'>%s</a>",
                                $exit->exit_no,
                                $exit->exit_no
                            );
                            
                            // 记录出库数量
                            $exitedQuantity += $transaction->quantity ?? 0;
                            
                            // 构建完整信息
                            $result[] = sprintf(
                                "%s<br>日期: %s<br>类型: %s<br>出库数量: <strong>%s</strong>",
                                $exitLink,
                                $exit->exit_date ? date('Y-m-d', strtotime($exit->exit_date)) : '-',
                                $exit->exit_type ?? '-',
                                $transaction->quantity ?? '-'
                            );
                        }
                        
                        if (count($transactions) >= 5) {
                            $result[] = '<span class="badge badge-info">仅显示前5条记录</span>';
                        }
                        
                        if (empty($result)) {
                            return '<span class="badge badge-info">未出库</span>';
                        }
                        
                        return sprintf('<span class="badge badge-warning">已出库 %s</span><hr>', $exitedQuantity) . implode("<hr>", $result);
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('出库信息显示错误: ' . $e->getMessage());
                        return '信息加载失败: ' . $e->getMessage();
                    }
                });
                
                // 创建/更新时间
                $grid->column('created_at', '创建时间')->sortable();
                $grid->column('updated_at', '更新时间')->sortable();
                
                // 修改筛选器,添加更多实用筛选条件
                $grid->filter(function (Grid\Filter $filter) {
                    $filter->panel();
                    $filter->expand(true); // 默认展开筛选面板
                    
                    // 基本信息筛选
                    $filter->equal('id', 'ID')->width(2);
                    
                    // 产品筛选
                    $filter->equal('product_id', '产品ID')->width(2);
                    $filter->where('产品名称', function ($query, $value) {
                        $productIds = DB::table('t_product_lists_h3c')
                            ->where('product', 'like', "%{$value}%")
                            ->pluck('id')
                            ->toArray();
                        
                        if (!empty($productIds)) {
                            $query->whereIn('product_id', $productIds);
                        } else {
                            $query->where('id', 0);
                        }
                    }, '产品名称');
                    
                    // 产品BOM筛选
                    $filter->where('产品BOM', function ($query, $value) {
                        $productIds = DB::table('t_product_lists_h3c')
                            ->where('product_bom', 'like', "%{$value}%")
                            ->pluck('id')
                            ->toArray();
                        
                        if (!empty($productIds)) {
                            $query->whereIn('product_id', $productIds);
                        } else {
                            $query->where('id', 0);
                        }
                    }, '产品BOM');
                    
                    // 库存状态筛选
                    $filter->equal('status', '库存状态')->select(TecH3cWarehouseInventoryModel::$statusMap)->width(3);
                    
                    // 仓库筛选
                    $filter->equal('warehouse_id', '仓库')->select(function() {
                        return DB::table('t_warehouses')
                            ->pluck('name', 'id')
                            ->toArray();
                    })->width(3);
                    
                    // 时间筛选
                    $filter->between('created_at', '创建时间')->datetime()->width(4);
                });
                
                // 表格布局优化
                $grid->disableCreateButton();
                $grid->disableRowSelector();
                $grid->disableBatchActions();
                $grid->paginate(15);
                
                // 添加详情按钮
                $grid->actions(function (Grid\Displayers\Actions $actions) {
                    $actions->disableDelete();
                    $actions->disableEdit();
                    $actions->disableQuickEdit();
                    
                    // 使用简单的详情页链接
                    $actions->append('<a href="'.admin_url('r_exit_sources/'.$actions->getKey()).'" target="_blank" class="btn btn-sm btn-primary" style="margin-right:5px;"><i class="feather icon-eye"></i> 查看详情</a>');
                    
                    // 添加采购单追溯链接
                    $product_id = $actions->row->product_id ?? null;
                    $warehouse_id = $actions->row->warehouse_id ?? null;
                    $location_id = $actions->row->location_id ?? null;
                    
                    if ($product_id && $warehouse_id && $location_id) {
                        // 查找最近的入库事务
                        $transaction = DB::table('t_warehouse_transactions_h3c')
                            ->where('product_id', $product_id)
                            ->where('warehouse_id', $warehouse_id)
                            ->where('location_id', $location_id)
                            ->where('type', 'in')
                            ->whereNotNull('order_id')
                            ->orderBy('id', 'desc')
                            ->first();
                            
                        if ($transaction && !empty($transaction->order_id)) {
                            $order = DB::table('t_purchase_order_h3c')->find($transaction->order_id);
                            if ($order && !empty($order->order_no)) {
                                $actions->append('<a href="'.admin_url('h3c/purchase-trace?采购单号='.$order->order_no).'" class="btn btn-sm btn-info" target="_blank"><i class="feather icon-search"></i> 采购追溯</a>');
                            }
                        }
                    }
                });
                
                // 添加导出功能
                $grid->export()->rows(function ($rows) {
                    foreach ($rows as $index => $row) {
                        // 处理产品信息
                        $product = DB::table('t_product_lists_h3c')->find($row['product_id'] ?? null);
                        $row['product_name'] = $product ? $product->product : '-';
                        $row['product_bom'] = $product ? $product->product_bom : '-';
                        
                        // 处理仓库信息
                        $warehouse = DB::table('t_warehouses')->find($row['warehouse_id'] ?? null);
                        $row['warehouse_name'] = $warehouse ? $warehouse->name : '-';
                        
                        // 处理货位信息
                        $location = DB::table('t_warehouse_locations')->find($row['location_id'] ?? null);
                        $row['location_name'] = $location ? ($location->location_name ?? $location->text ?? $location->code) : '-';
                        
                        $rows[$index] = $row;
                    }
                    return $rows;
                })->extension('xlsx')
                  ->filename('库存来源追溯_' . date('YmdHis'));
            });
        } catch (\Exception $e) {
            // 记录错误并返回友好的错误页面
            Log::error('加载库存来源追溯表格失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return admin_error('系统错误', '无法加载数据: ' . $e->getMessage());
        }
    }

    /**
     * 详情页
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        // 记录日志
        Log::info('访问库存详情页', ['id' => $id]);
        
        try {
            // 直接从数据库获取库存记录
            $inventory = TecH3cWarehouseInventoryModel::find($id);
            if (!$inventory) {
                return admin_error('错误', '未找到ID为' . $id . '的库存记录');
            }
            
            // 手动获取关联数据，避免自动关联导致的问题
            $productInfo = $inventory->product_id ? TecH3cProductListModel::find($inventory->product_id) : null;
            $warehouseInfo = $inventory->warehouse_id ? TecWarehouseModel::find($inventory->warehouse_id) : null;
            $locationInfo = $inventory->location_id ? TecWarehouseLocationModel::find($inventory->location_id) : null;
            
            // 创建Show实例
            $show = new Show($inventory);
            
            // 设置详情面板标题
            $show->panel()->title('库存详情');
            
            // 添加库存基本信息字段
            $show->field('id', 'ID');
            $show->field('product_id', '产品ID');
            
            // 添加产品信息
            $show->field('产品信息')->unescape()->as(function() use ($productInfo) {
                if (!$productInfo) {
                    return '<span class="label label-warning">未找到产品信息</span>';
                }
                
                $zokusei = $productInfo->product_zokusei ?? 'normal';
                $zokuseiMap = PurchaseConfig::getProductZokusei();
                $zokuseiText = $zokuseiMap[$zokusei] ?? $zokusei;
                
                // 定义状态标签颜色
                $labels = [
                    'normal' => 'success',
                    'rr' => 'warning',
                    'inspection' => 'info'
                ];
                
                $label = isset($labels[$zokusei]) ? $labels[$zokusei] : 'default';
                
                return sprintf(
                    "<div class='callout callout-info'>
                        <h5>产品名称: <strong>%s</strong></h5>
                        <p>产品BOM: %s</p>
                        <p>属性: <span class='label label-%s'>%s</span></p>
                    </div>",
                    $productInfo->product ?? '未知',
                    $productInfo->product_bom ?? '-',
                    $label,
                    $zokuseiText
                );
            });
            
            // 添加库存状态信息
            $show->field('库存状态')->unescape()->as(function() use ($inventory) {
                $statusMap = TecH3cWarehouseInventoryModel::$statusMap;
                $status = $inventory->status;
                $statusText = $statusMap[$status] ?? $status;
                
                // 定义状态标签颜色
                $labels = [
                    'normal' => 'success',
                    'locked' => 'warning',
                    'available' => 'info'
                ];
                
                $label = isset($labels[$status]) ? $labels[$status] : 'default';
                
                return sprintf(
                    "<div class='callout callout-%s'>
                        <h5>状态: <span class='label label-%s'>%s</span></h5>
                        <p>总数量: <strong>%s</strong></p>
                        <p>可用数量: %s</p>
                        <p>锁定数量: %s</p>
                    </div>",
                    $label,
                    $label,
                    $statusText,
                    $inventory->quantity ?? 0,
                    $inventory->available_quantity ?? 0,
                    $inventory->locked_quantity ?? 0
                );
            });
            
            // 添加仓库和货位信息
            $show->field('仓库货位')->unescape()->as(function() use ($warehouseInfo, $locationInfo) {
                $warehouseName = $warehouseInfo ? $warehouseInfo->name : '-';
                $locationName = $locationInfo ? ($locationInfo->location_name ?? $locationInfo->text ?? $locationInfo->code) : '-';
                $warehouseAddress = $warehouseInfo && !empty($warehouseInfo->address) ? $warehouseInfo->address : '';
                
                return sprintf(
                    "<div class='callout callout-success'>
                        <h5>仓库: <strong>%s</strong></h5>
                        <p>货位: %s</p>
                        %s
                    </div>",
                    $warehouseName,
                    $locationName,
                    $warehouseAddress ? "<p>地址: {$warehouseAddress}</p>" : ''
                );
            });
            
            // 添加订单相关信息
            $show->field('订单信息')->unescape()->as(function() use ($inventory) {
                $orderNo = $inventory->order_no ?: '-';
                $orderLink = !empty($inventory->order_no) ? 
                    "<a href='/admin/r_h3c_purchase_orders?order_no={$inventory->order_no}' target='_blank'>{$inventory->order_no}</a>" : 
                    '-';
                
                return sprintf(
                    "<div class='callout callout-info'>
                        <h5>订单号: %s</h5>
                        <p>订单ID: %s</p>
                        <p>订单明细ID: %s</p>
                    </div>",
                    $orderLink,
                    $inventory->order_id ?: '-',
                    $inventory->order_item_id ?: '-'
                );
            });
            
            // 批次和日期信息
            $show->field('其他信息')->unescape()->as(function() use ($inventory) {
                return sprintf(
                    "<div class='callout callout-default'>
                        <p>批次号: %s</p>
                        <p>创建时间: %s</p>
                        <p>更新时间: %s</p>
                    </div>",
                    $inventory->batch_no ?: '-',
                    $inventory->created_at ? $inventory->created_at->format('Y-m-d H:i:s') : '-',
                    $inventory->updated_at ? $inventory->updated_at->format('Y-m-d H:i:s') : '-'
                );
            });
            
            // 获取入库事务信息，使用原生SQL避免模型关联问题
            $show->field('入库记录')->unescape()->as(function() use ($inventory) {
                try {
                    // 如果产品ID、仓库ID或货位ID为空，则返回无入库信息
                    if (empty($inventory->product_id) || empty($inventory->warehouse_id) || empty($inventory->location_id)) {
                        return '<span class="label label-warning">无入库信息</span>';
                    }
                    
                    // 使用DB门面直接查询，避免模型关联
                    $transactions = DB::table('t_warehouse_transactions_h3c')
                        ->where('product_id', $inventory->product_id)
                        ->where('warehouse_id', $inventory->warehouse_id)
                        ->where('location_id', $inventory->location_id)
                        ->where('type', 'in') // 使用正确的入库类型常量
                        ->limit(10) // 限制记录数量，避免数据量过大
                        ->get();
                    
                    if ($transactions->isEmpty()) {
                        return '<span class="label label-warning">无入库记录</span>';
                    }
                    
                    $html = '<div class="table-responsive"><table class="table table-bordered table-hover">';
                    $html .= '<thead><tr>
                        <th>入库单号</th>
                        <th>入库日期</th>
                        <th>入库数量</th>
                        <th>采购单号</th>
                    </tr></thead><tbody>';
                    
                    foreach ($transactions as $trans) {
                        // 通过事务ID获取入库单信息
                        $entry = null;
                        if (!empty($trans->entry_id)) {
                            $entry = DB::table('t_warehouse_entries_h3c')->find($trans->entry_id);
                        }
                        
                        // 通过事务ID获取订单信息
                        $order = null;
                        if (!empty($trans->order_id)) {
                            $order = DB::table('t_purchase_order_h3c')->find($trans->order_id);
                        }
                        
                        $entryNo = $entry && $entry->entry_no ? 
                            "<a href='/admin/r_h3c_warehouse_entries?entry_no={$entry->entry_no}' target='_blank'>{$entry->entry_no}</a>" : 
                            '-';
                        
                        $entryDate = $entry && $entry->entry_date ? date('Y-m-d', strtotime($entry->entry_date)) : '-';
                        $quantity = $trans->quantity ?? '-';
                        
                        $orderNo = $order && $order->order_no ? 
                            "<a href='/admin/r_h3c_purchase_orders?order_no={$order->order_no}' target='_blank'>{$order->order_no}</a>" : 
                            '-';
                        
                        $html .= "<tr>
                            <td>{$entryNo}</td>
                            <td>{$entryDate}</td>
                            <td>{$quantity}</td>
                            <td>{$orderNo}</td>
                        </tr>";
                    }
                    
                    $html .= '</tbody></table></div>';
                    
                    if (count($transactions) == 10) {
                        $html .= '<div class="text-center"><span class="label label-info">只显示最近10条记录</span></div>';
                    }
                    
                    return $html;
                } catch (\Exception $e) {
                    Log::error('获取入库记录失败', [
                        'id' => $inventory->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    return '<span class="label label-danger">加载入库记录失败: ' . $e->getMessage() . '</span>';
                }
            });
            
            // 禁用编辑和删除按钮
            $show->panel()->tools(function ($tools) {
                $tools->disableEdit();
                $tools->disableDelete();
                // 添加返回按钮
                $tools->append('<a href="'.admin_url('r_exit_sources').'" class="btn btn-sm btn-default"><i class="feather icon-arrow-left"></i> 返回列表</a>');
                // 添加采购追溯按钮
                $tools->append('<a href="javascript:void(0);" onclick="window.print();" class="btn btn-sm btn-info"><i class="feather icon-printer"></i> 打印</a>');
            });
            
            return $show;
        } catch (\Exception $e) {
            // 记录异常
            Log::error('库存详情页异常', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回友好的错误页面
            return admin_error('系统错误', '获取库存详情出错: ' . $e->getMessage());
        }
    }

    /**
     * 表单(仅用于查看,不提供编辑功能)
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new \App\Models\TecH3cWarehouseInventoryModel(), function (Form $form) {
            $form->display('id', 'ID');
            
            // 只读模式,禁止修改
            $form->disableCreatingCheck();
            $form->disableEditingCheck();
            $form->disableViewCheck();
            
            // 禁用提交和重置按钮
            $form->disableResetButton();
            $form->disableSubmitButton();
        });
    }
    
    /**
     * 处理任何未定义的方法请求
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     */
    public function __call($name, $arguments)
    {
        // 记录未定义方法的调用
        Log::warning('调用了未定义的方法', [
            'controller' => 'TecH3cWarehouseInventorySourceController',
            'method' => $name,
            'arguments' => $arguments,
            'url' => request()->fullUrl(),
            'ip' => request()->ip()
        ]);
        
        // 返回错误页面
        return admin_error('页面未找到', '您访问的操作不存在');
    }
} 