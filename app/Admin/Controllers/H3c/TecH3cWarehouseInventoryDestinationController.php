<?php

namespace App\Admin\Controllers\H3c;

use App\Models\TecH3cWarehouseTransactionModel;
use App\Models\TecWarehouseLocationModel;
use App\Models\TecH3cWarehouseExitItemModel;
use App\Models\TecH3cCustomerPurchaseOrderItemModel;
use App\Models\TecH3cProductListModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Admin\Config\PurchaseConfig;

/**
 * 库存目的地追溯控制器
 * 
 * 用于查询和显示库存记录的去向,包括出库单和客户订单信息
 */
class TecH3cWarehouseInventoryDestinationController extends AdminController
{
    /**
     * 异常处理中间件，捕获任何方法中的异常避免崩溃
     *
     * @param \Closure $next
     * @return mixed
     */
    public function __invoke($method, $parameters)
    {
        try {
            return parent::__invoke($method, $parameters);
        } catch (\Exception $e) {
            // 记录异常
            Log::error('库存去向追溯控制器异常', [
                'method' => $method,
                'parameters' => $parameters,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 判断请求类型，API请求返回JSON，否则返回错误页面
            if (request()->expectsJson()) {
                return response()->json(['error' => '系统错误: ' . $e->getMessage()], 500);
            }
            
            return admin_error('系统错误', '操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 标题
     *
     * @return string
     */
    protected $title = '库存去向追溯';

    /**
     * 列表页
     *
     * @return Grid
     */
    protected function grid()
    {
        try {
            // 清除缓存避免关联问题
            \Illuminate\Support\Facades\Cache::flush();
            
            // 直接使用目的地表作为查询基础
            return Grid::make(new \App\Models\TecH3cWarehouseExitDestinationModel(), function (Grid $grid) {
                // 预加载关联数据，减少N+1查询问题
                $grid->model()->with([
                    'exitItem.exit.warehouse', // 预加载出库单及其仓库
                    'customerOrderItem.productInfo', // 预加载订单明细的产品信息
                    'customerOrder' // 预加载客户订单
                ])->orderBy('id', 'desc');
                
                // 设置分页大小
                $grid->paginate(100);
                
                $grid->column('id', 'ID')->sortable();
                
                // 客户订单信息
                $grid->column('customer_order_no', '客户订单号');
                $grid->column('customer_order_item_id', '订单明细ID');
                
                // 产品信息 - 仅显示名称和BOM
                $grid->column('产品信息')->display(function() {
                    try {
                        // 通过关联获取customerOrderItem
                        $customerOrderItemId = $this->getAttribute('customer_order_item_id');
                        if (empty($customerOrderItemId)) {
                            return '-';
                        }
                        
                        $customerOrderItem = \App\Models\TecH3cCustomerPurchaseOrderItemModel::with('productInfo')->find($customerOrderItemId);
                        if (!$customerOrderItem || !$customerOrderItem->productInfo) {
                            return '<span class="badge badge-secondary">产品不存在</span>';
                        }
                        
                        $product = $customerOrderItem->productInfo;
                        $bom = $product->product_bom ?? '';
                        $name = $product->product ?? '';
                        
                        if (empty($bom) && empty($name)) {
                            return '<span class="badge badge-secondary">无产品信息</span>';
                        }
                        
                        return sprintf("<strong>%s</strong><br>%s", $name, $bom);
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('产品信息显示错误: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                        return '产品信息加载失败';
                    }
                });
                
                // 产品属性 - 单独列显示
                $grid->column('产品属性')->display(function() {
                    try {
                        // 通过关联获取customerOrderItem
                        $customerOrderItemId = $this->getAttribute('customer_order_item_id');
                        if (empty($customerOrderItemId)) {
                            return '-';
                        }
                        
                        $customerOrderItem = \App\Models\TecH3cCustomerPurchaseOrderItemModel::with('productInfo')->find($customerOrderItemId);
                        if (!$customerOrderItem || !$customerOrderItem->productInfo) {
                            return '-';
                        }
                        
                        $product = $customerOrderItem->productInfo;
                        
                        // 获取产品属性
                        $zokusei = $product->product_zokusei ?? 'normal';
                        
                        if (empty($zokusei)) {
                            return '-';
                        }
                        
                        $zokuseiMap = PurchaseConfig::getProductZokusei();
                        $zokuseiText = $zokuseiMap[$zokusei] ?? $zokusei;
                        
                        // 定义状态标签颜色
                        $labels = [
                            'normal' => 'success',
                            'rr' => 'warning',
                            'inspection' => 'info'
                        ];
                        
                        $label = isset($labels[$zokusei]) ? $labels[$zokusei] : 'default';
                        
                        return "<span class='label label-{$label}'>{$zokuseiText}</span>";
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('产品属性显示错误: ' . $e->getMessage());
                        return '-';
                    }
                });
                
                // 客户信息
                $grid->column('customer_name', '客户名称');
                $grid->column('case_name', '案件名');
                $grid->column('contact_person', '联系人');
                $grid->column('contact_phone', '联系电话');
                
                // 数量信息
                $grid->column('quantity', '出库数量');
                $grid->column('customerOrderItem.quantity', '订单总数量');
                $grid->column('customerOrderItem.delivered_quantity', '已发货数量');
                
                // 出库单号信息
                $grid->column('出库单号')->display(function () {
                    $product_id = $this->product_id ?? null;
                    
                    if (empty($product_id)) {
                        return '-';
                    }
                    
                    try {
                        $data = [];
                        $exitInfoData = [];
                        
                        // 查询所有出库单记录
                        $exitItems = \App\Models\TecH3cWarehouseExitItemModel::where('product_id', $product_id)->get();
                        
                        if (!$exitItems->isEmpty()) {
                            \Illuminate\Support\Facades\Log::info("找到出库明细记录数量: " . $exitItems->count());
                            
                            foreach ($exitItems as $item) {
                                if (!$item || empty($item->exit_id)) continue;
                                
                                // 查找关联的出库单
                                $exit = \App\Models\TecH3cWarehouseExitModel::find($item->exit_id);
                                if (!$exit) continue;
                                
                                // 记录出库单号
                                if (!empty($exit->exit_no)) {
                                    $data[] = $exit->exit_no;
                                    
                                    // 查找目的地信息
                                    $destinations = \App\Models\TecH3cWarehouseExitDestinationModel::where('exit_item_id', $item->id)->get();
                                    $destinationInfo = '';
                                    
                                    if (!$destinations->isEmpty()) {
                                        $destData = [];
                                        foreach ($destinations as $dest) {
                                            if (!empty($dest->contact_person) || !empty($dest->contact_phone) || !empty($dest->address)) {
                                                $destData[] = sprintf(
                                                    "联系人: %s, 电话: %s, 地址: %s",
                                                    $dest->contact_person ?? '-',
                                                    $dest->contact_phone ?? '-',
                                                    $dest->address ?? '-'
                                                );
                                            }
                                        }
                                        if (!empty($destData)) {
                                            $destinationInfo = "<br>收货信息:<br>" . implode("<br>", $destData);
                                        }
                                    }
                                    
                                    // 收集更多出库单信息
                                    $exitInfo = sprintf(
                                        "出库单号: %s<br>出库日期: %s<br>出库类型: %s<br>出库数量: %s %s<br>备注: %s%s",
                                        $exit->exit_no,
                                        $exit->exit_date ? date('Y-m-d', strtotime($exit->exit_date)) : '-',
                                        $exit->exit_type_name ?? $exit->exit_type ?? '-',
                                        $item->quantity ?? '-',
                                        $item->unit ?? '个',
                                        $exit->remark ?? '-',
                                        $destinationInfo
                                    );
                                    $exitInfoData[$exit->exit_no] = $exitInfo;
                                }
                            }
                        } else {
                            \Illuminate\Support\Facades\Log::info("没有找到出库明细记录");
                        }
                        
                        // 如果通过出库单明细没有找到出库单信息，尝试通过事务记录查找
                        if (empty($data)) {
                            \Illuminate\Support\Facades\Log::info("通过出库单明细未找到出库单信息，尝试通过事务记录查找");
                            
                            // 使用产品ID查询出库事务
                            $transactions = TecH3cWarehouseTransactionModel::where('product_id', $product_id)
                                ->where('type', TecH3cWarehouseTransactionModel::H3C_TRANSACTION_TYPE_OUT)
                                ->get();
                            
                            if ($transactions->isEmpty()) {
                                \Illuminate\Support\Facades\Log::info("没有找到事务记录");
                                return '-';
                            }
                            
                            foreach($transactions as $transaction) {
                                if (!$transaction) continue;
                                
                                // 从事务的source_id获取出库单
                                if (!empty($transaction->source_id) && ($transaction->source_type == 'warehouse_exit' || empty($transaction->source_type))) {
                                    $exit = \App\Models\TecH3cWarehouseExitModel::find($transaction->source_id);
                                    if ($exit && !empty($exit->exit_no)) {
                                        $data[] = $exit->exit_no;
                                        
                                        // 收集更多出库单信息
                                        $exitInfo = sprintf(
                                            "出库单号: %s<br>出库日期: %s<br>出库类型: %s<br>备注: %s",
                                            $exit->exit_no,
                                            $exit->exit_date ? date('Y-m-d', strtotime($exit->exit_date)) : '-',
                                            $exit->exit_type_name ?? $exit->exit_type ?? '-',
                                            $exit->remark ?? '-'
                                        );
                                        $exitInfoData[$exit->exit_no] = $exitInfo;
                                    }
                                }
                                // 如果source_type是其他类型，直接从source_no获取
                                else if (!empty($transaction->source_no)) {
                                    $data[] = $transaction->source_no;
                                }
                            }
                        }
                        
                        if (empty($data)) {
                            return '-';
                        }
                        
                        // 去重并组合显示更多出库信息
                        $uniqueExitNos = array_unique($data);
                        $result = [];
                        
                        foreach($uniqueExitNos as $exitNo) {
                            if (isset($exitInfoData[$exitNo])) {
                                $result[] = $exitInfoData[$exitNo];
                            } else {
                                $result[] = $exitNo;
                            }
                        }
                        
                        return !empty($result) ? implode("<br><hr><br>", $result) : '-';
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('出库单号显示错误: ' . $e->getMessage());
                        return '-';
                    }
                });
                
                // 仓库信息 - 合并仓库和货位信息
                $grid->column('仓库货位')->display(function() {
                    try {
                        // 通过ID获取exitItem关联
                        $exitItemId = $this->getAttribute('exit_item_id');
                        if (empty($exitItemId)) {
                            return '-';
                        }
                        
                        $exitItem = \App\Models\TecH3cWarehouseExitItemModel::with('exit.warehouse')->find($exitItemId);
                        if (!$exitItem) {
                            return '出库明细不存在';
                        }
                        
                        if (!$exitItem->exit) {
                            return '出库单不存在';
                        }
                        
                        // 获取仓库信息
                        $exit = $exitItem->exit;
                        $warehouse = $exit->warehouse;
                        $warehouseName = $warehouse ? $warehouse->name : '-';
                        
                        // 获取货位信息
                        $locationName = '-';
                        $locationId = $exitItem->location_id ?? null;
                        
                        if (!empty($locationId)) {
                            $location = \App\Models\TecWarehouseLocationModel::find($locationId);
                            if ($location) {
                                $locationName = $location->text ?: $location->code ?: '-';
                            }
                        }
                        
                        // 构建完整的货位信息
                        $info = sprintf(
                            "<strong>仓库</strong>: %s<br><strong>货位</strong>: %s<br><strong>地址</strong>: %s",
                            $warehouseName,
                            $locationName,
                            ($warehouse && !empty($warehouse->address)) ? $warehouse->address : '-'
                        );
                        
                        return $info;
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('仓库货位信息显示错误: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                        return '仓库货位信息加载失败';
                    }
                });
                
                // 发货信息
                $grid->column('shipping_status', '发货状态')->display(function($value) {
                    $statusMap = [
                        'pending' => ['待发货', 'default'],
                        'shipped' => ['已发货', 'primary'],
                        'delivered' => ['已签收', 'success'],
                        'returned' => ['已退回', 'danger']
                    ];
                    
                    if (isset($statusMap[$value])) {
                        list($text, $type) = $statusMap[$value];
                        return "<span class='label label-{$type}'>{$text}</span>";
                    }
                    
                    return $value ?: '-';
                });
                $grid->column('shipping_date', '发货日期');
                $grid->column('tracking_number', '追踪号');
                
                // 创建时间
                $grid->column('created_at', '创建时间')->sortable();
                
                // 筛选条件
                $grid->filter(function (Grid\Filter $filter) {
                    $filter->panel();
                    $filter->expand();
                    
                    // 客户订单组
                    $filter->group('客户订单信息', function ($group) {
                        $group->like('customer_order_no', '客户订单号')->width(4);
                        $group->equal('customer_order_item_id', '订单明细ID')->width(4);
                        $group->like('case_name', '案件名')->width(4);
                    });
                    
                    // 产品信息组
                    $filter->group('产品信息', function ($group) {
                        $group->like('customerOrderItem.productInfo.product_bom', '产品BOM')->width(4);
                        $group->like('customerOrderItem.productInfo.product', '产品名称')->width(4);
                        $group->equal('customerOrderItem.productInfo.product_zokusei', '产品属性')
                            ->select(PurchaseConfig::getProductZokusei())->width(4);
                    });
                    
                    // 客户信息组
                    $filter->group('客户联系信息', function ($group) {
                        $group->like('customer_name', '客户名称')->width(4);
                        $group->like('contact_person', '联系人')->width(4);
                        $group->like('contact_phone', '联系电话')->width(4);
                    });
                    
                    // 出库信息组
                    $filter->group('出库信息', function ($group) {
                        $group->like('exitItem.exit.exit_no', '出库单号')->width(4);
                        $group->equal('exitItem.exit.exit_date', '出库日期')->date()->width(4);
                        $group->equal('exitItem.exit.exit_type', '出库类型')->select([
                            'sales' => '销售出库',
                            'transfer' => '调拨出库',
                            'other' => '其他出库'
                        ])->width(4);
                    });
                    
                    // 仓库和发货信息组
                    $filter->group('仓库和发货信息', function ($group) {
                        $group->equal('exitItem.exit.warehouse.name', '仓库名称')->width(3);
                        $group->equal('shipping_status', '发货状态')->select([
                            'pending' => '待发货',
                            'shipped' => '已发货',
                            'delivered' => '已签收',
                            'returned' => '已退回'
                        ])->width(3);
                        $group->equal('shipping_date', '发货日期')->date()->width(3);
                        $group->like('tracking_number', '追踪号')->width(3);
                    });
                    
                    // 时间筛选
                    $filter->between('created_at', '创建时间')->datetime()->width(4);
                });
                
                // 表格布局
                $grid->withBorder();
                $grid->withColumnSelector();
                
                // 禁用创建按钮(仅查询功能)
                $grid->disableCreateButton();
                
                // 禁用行操作按钮(编辑和删除)
                $grid->disableActions();
                
                // 允许导出
                $grid->export();
                
                // 工具栏
                $grid->toolsWithOutline(false);
                
                // 设置每页显示选项
                $grid->perPages([100, 200, 300]);
                
                // 表格样式
                $grid->setActionClass(Grid\Displayers\Actions::class);
                $grid->showQuickEditButton();
                $grid->enableDialogCreate();
                $grid->showColumnSelector();
                $grid->setDialogFormDimensions('700px', '620px');
            });
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('库存去向追溯控制器异常: ' . $e->getMessage());
            return admin_error('系统错误', '操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 详情页
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        // 记录日志
        Log::info('访问库存去向详情页', ['id' => $id]);
        
        try {
            // 直接从数据库获取出库目的地记录
            $destination = \App\Models\TecH3cWarehouseExitDestinationModel::find($id);
            if (!$destination) {
                return admin_error('错误', '未找到ID为' . $id . '的库存去向记录');
            }
            
            return Show::make($id, new \App\Models\TecH3cWarehouseExitDestinationModel(), function (Show $show) {
                $show->field('id', 'ID');
                
                // 出库明细信息
                $show->field('exit_item_id', '出库明细ID');
                $show->field('exitItem.exit.exit_no', '出库单号');
                $show->field('exitItem.exit.exit_date', '出库日期');
                $show->field('exitItem.exit.warehouse.name', '出库仓库');
                
                // 客户订单信息
                $show->field('customer_order_no', '客户订单号');
                $show->field('customer_order_item_id', '订单明细ID');
                $show->field('customerOrderItem.product', '产品ID');
                $show->field('customerOrderItem.productInfo.product', '产品名称');
                $show->field('customerOrderItem.productInfo.product_bom', '产品BOM');
                
                // 客户信息
                $show->field('customer_name', '客户名称');
                $show->field('case_name', '案件名');
                
                // 数量信息
                $show->field('quantity', '数量');
                
                // 发货信息
                $show->field('shipping_date', '发货日期');
                $show->field('tracking_number', '追踪号');
                
                // 备注
                $show->field('created_at', '创建时间');
                $show->field('updated_at', '更新时间');
                
                // 禁用编辑和删除按钮
                $show->panel()->tools(function ($tools) {
                    $tools->disableEdit();
                    $tools->disableDelete();
                    // 添加返回按钮
                    $tools->append('<a href="'.admin_url('r_exit_destinations').'" class="btn btn-sm btn-default"><i class="feather icon-arrow-left"></i> 返回列表</a>');
                });
            });
        } catch (\Exception $e) {
            // 记录异常
            Log::error('库存去向详情页异常', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回友好的错误页面
            return admin_error('系统错误', '获取库存去向详情出错: ' . $e->getMessage());
        }
    }

    /**
     * 表单(仅用于查看,不提供编辑功能)
     *
     * @return Form
     */
    protected function form()
    {
        try {
            return Form::make(new \App\Models\TecH3cWarehouseExitDestinationModel(), function (Form $form) {
                $form->display('id', 'ID');
                
                // 出库明细信息
                $form->display('exit_item_id', '出库明细ID');
                $form->display('exitItem.exit.exit_no', '出库单号');
                $form->display('exitItem.exit.exit_date', '出库日期');
                
                // 客户订单信息
                $form->display('customer_order_no', '客户订单号');
                $form->display('customer_order_item_id', '订单明细ID');
                $form->display('customerOrderItem.productInfo.product', '产品名称');
                $form->display('customerOrderItem.productInfo.product_bom', '产品BOM');
                
                // 客户信息
                $form->display('customer_name', '客户名称');
                $form->display('case_name', '案件名');
                $form->display('contact_person', '联系人');
                $form->display('contact_phone', '联系电话');
                
                // 数量信息
                $form->display('quantity', '数量');
                
                // 发货信息分隔线
                $form->divider('发货信息');
                
                // 发货信息
                $form->date('shipping_date', '发货日期');
                $form->text('tracking_number', '追踪号');
                
                // 发货状态
                $form->select('shipping_status', '发货状态')
                    ->options(\App\Models\TecH3cWarehouseExitDestinationModel::$shippingStatusMap)
                    ->default(\App\Models\TecH3cWarehouseExitDestinationModel::STATUS_PENDING);
                
                // 备注
                $form->textarea('remark', '备注')->rows(3);
                
                // 创建和更新时间
                $form->display('created_at', '创建时间');
                $form->display('updated_at', '更新时间');
                
                // 允许编辑，但不显示编辑和删除的切换按钮
                $form->disableEditingCheck();
                $form->disableCreatingCheck();
                $form->disableViewCheck();
                
                // 禁用重置按钮，但允许提交
                $form->disableResetButton();
                
                // 成功回调
                $form->saving(function (Form $form) {
                    // 如果发货状态变更为已发货，且发货日期为空，则自动设置当前日期
                    if ($form->shipping_status == \App\Models\TecH3cWarehouseExitDestinationModel::STATUS_SHIPPED && empty($form->shipping_date)) {
                        $form->shipping_date = date('Y-m-d');
                    }
                    
                    // 记录日志
                    \Illuminate\Support\Facades\Log::info('更新出库目的地发货信息', [
                        'id' => $form->getKey(),
                        'shipping_status' => $form->shipping_status,
                        'shipping_date' => $form->shipping_date,
                        'tracking_number' => $form->tracking_number
                    ]);
                });
            });
        } catch (\Exception $e) {
            // 记录异常
            Log::error('出库目的地表单异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回友好的错误页面
            return admin_error('系统错误', '加载表单出错: ' . $e->getMessage());
        }
    }

    /**
     * 处理任何未定义的方法请求
     *
     * @param string $name
     * @param array $arguments
     * @return mixed
     */
    public function __call($name, $arguments)
    {
        // 记录未定义方法的调用
        Log::warning('调用了未定义的方法', [
            'controller' => 'TecH3cWarehouseInventoryDestinationController',
            'method' => $name,
            'arguments' => $arguments,
            'url' => request()->fullUrl(),
            'ip' => request()->ip()
        ]);
        
        // 返回错误页面
        return admin_error('页面未找到', '您访问的操作不存在');
    }
} 
