<?php

declare(strict_types=1);

namespace App\Admin\Controllers\H3c;

use App\Admin\Controllers\Controller;
use App\Admin\Extensions\Form\H3cInventorySelect;
use App\Admin\Repositories\TecH3cWarehouseExitRepo;
use App\Admin\Requests\H3c\WarehouseExitRequest;
use App\Admin\Tools\GridTools\ExcelExporter;
use App\Admin\Renderable\TecH3cWarehouseExitItemsTable;
use App\Models\TecCityAreaModel;
use App\Models\TecConsigneeAddressModel;
use App\Models\TecCustomerOrderFormItemsModel;
use App\Models\TecCustomerOrderFormModel;
use App\Models\TecExpressCompanyModel;
use App\Models\TecH3cCustomerPurchaseOrderItemModel;
use App\Models\TecH3cCustomerPurchaseOrderModel;
use App\Models\TecH3cLocationModel;
use App\Models\TecH3cProductListModel;
use App\Models\TecH3cPurchaseOrderItemModel;
use App\Models\TecH3cPurchaseOrderModel;
use App\Models\TecH3cWarehouseExitFormItemsModel;
use App\Models\TecH3cWarehouseExitFormModel;
use App\Models\TecH3cWarehouseExitItemModel;
use App\Models\TecH3cWarehouseExitModel;
use App\Models\TecH3cWarehouseExitOrderSourceModel;
use App\Models\TecH3cWarehouseExitDestinationModel;
use App\Models\TecH3cWarehouseInventoryModel;
use App\Models\TecH3cWarehouseLocationModel;
use App\Models\TecH3cWarehouseModel;
use App\Models\TecH3cWarehouseTransactionModel;
use App\Models\TecProductListModel;
use App\Models\TecProductUnitModel;
use App\Models\TecWarehouseLocationModel;
use App\Models\TecWarehouseModel;
use App\Models\TecWholesaleConsigneeModel;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Admin\Actions\Post\Cancel;
use App\Admin\Actions\Post\Process;
use App\Admin\Renderable\OrderItemListModal;
use Carbon\Carbon;
use Yajra\DataTables\Facades\DataTables;

/**
 * H3C出库单控制器
 * 
 * 负责出库单的创建、显示、编辑、审核和处理
 */
class TecH3cWarehouseExitController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '出库管理';

    /**
     * 列表页面
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecH3cWarehouseExitRepo(), function (Grid $grid) {
            $grid->scrollbarX();
            
            // 预加载关联关系避免N+1查询
            $grid->model()->with([
                'warehouse:id,name',
                'items:id,exit_id,product_id,customer_order_item_id,purchase_order_item_id,quantity,order_no,unit_id',
                'items.product:id,product,product_bom,product_zokusei,is_inspection_machine',
                'items.customerOrderItem:id,order_id,product,quantity,delivered_quantity',
                'items.customerOrderItem.order:id,order_no,case_name,business_date',
                'items.unit:id,unit_name'
            ])->orderBy('exit_date', 'desc'); // 默认按出库日期降序排序
            
            $grid->column('exit_no', '出库单号')
                ->expand(TecH3cWarehouseExitItemsTable::class, 'id')
                ->style('font-size: 0.9em; white-space: nowrap; display: flex; align-items: center;')
                ->width('120px');
            
            $grid->column('exit_date', '出库日期')
                ->display(function ($value) {
                    return date('Y-m-d', strtotime($value));
                })
                ->style('font-size: 0.9em;')
                ->width('100px');
            
            $grid->column('exit_type', '出库类型')->using(TecH3cWarehouseExitModel::$exitTypeMap)->style('font-size: 0.9em;')->width('80px')
                ->label([
                    'sales' => 'success',
                    'return' => 'warning',
                    'other' => 'info'
                ]);
            
            // 关联信息
            $grid->column('warehouse.name', '仓库')->style('font-size: 0.9em;')->width('80px');

                        
            $grid->column('summary', '概要')->display(function ($value) {   
                // 直接返回HTML内容，不做任何处理，确保完整渲染HTML
                return $this->getSummaryAttribute();
            })->style('font-size: 0.9em;white-space: normal;');

            $grid->column('customer_order_no', '客户订单')->display(function ($value) {
                // 使用预加载的出库明细数据
                if ($this->relationLoaded('items') && $this->items->isNotEmpty()) {
                    foreach ($this->items as $item) {
                        if ($item->relationLoaded('customerOrderItem') && 
                            $item->customerOrderItem && 
                            $item->customerOrderItem->relationLoaded('order') &&
                            $item->customerOrderItem->order) {
                            $orderNo = $item->customerOrderItem->order->order_no;
                            if ($orderNo) {
                                $url = admin_url('r_h3c_customer_orders') . '?order_no=' . urlencode($orderNo);
                                return "<a href='{$url}' target='_blank' style='color:rgb(27, 152, 64); text-decoration: none;'>{$orderNo}</a>";
                            }
                        }
                    }
                }
                
                return '-';
            })->sortable()->style('font-size: 0.9em;')->width('280px');

            $grid->column('purchase_order', '采购订单')->style('font-size: 0.8em;')->width('150px')->display(function ($value) {
                // 使用预加载的出库明细数据
                if (!$this->relationLoaded('items') || $this->items->isEmpty()) {
                    return '-';
                }

                // 查找采购订单号 - 采购订单号通常以PO-开头
                $purchaseOrders = [];
                foreach ($this->items as $item) {
                    // 从订单号字段获取
                    if (!empty($item->order_no) && strpos($item->order_no, 'PO-') !== false) {
                        $purchaseOrders[] = $item->order_no;
                    }
                }

                // 去重并生成带链接的结果
                $purchaseOrders = array_unique($purchaseOrders);
                if (empty($purchaseOrders)) {
                    return '-';
                }

                // 为每个采购订单号生成链接
                $links = [];
                foreach ($purchaseOrders as $orderNo) {
                    $url = admin_url('r_h3c_purchase_orders/type/all') . '?order_no=' . urlencode($orderNo);
                    $links[] = "<a href='{$url}' target='_blank' style='color: #007bff; text-decoration: none;'>{$orderNo}</a>";
                }

                return implode('<br>', $links);
            })->sortable();

            $grid->column('remark', '备考')->style('font-size: 0.9em;');
            
            // 设置筛选器
            $grid->filter(function (Grid\Filter $filter) {
                // 禁用默认的加载方式
                $filter->withoutLoading();

                // 使用面板布局
                $filter->panel();

                // 基础筛选条件
                $filter->equal('exit_no', '出库单号')->width(3);
                
                $filter->equal('warehouse_id', '仓库')->select(function() {
                    return TecWarehouseModel::pluck('name', 'id')->toArray();
                })->width(3);
                
                $filter->equal('exit_type', '出库类型')->select(
                    TecH3cWarehouseExitModel::$exitTypeMap
                )->width(3);
                
                $filter->between('exit_date', '出库日期')->date()->width(3);

                // 客户订单搜索 - 改用模糊匹配
                $filter->like('items.customerOrderItem.order.order_no', '客户订单号')->width(3);
                
                // 采购订单搜索 - 改用模糊匹配
                $filter->like('items.order_no', '采购订单号')->width(3);
            });
            
            // 直接应用搜索条件到模型
            $inputs = $grid->filter()->inputs();
                
            // 如果有客户订单搜索条件
            if (isset($inputs['customer_order']) && $inputs['customer_order'] !== '') {
                $value = $inputs['customer_order'];
                $grid->model()->whereHas('items.customerOrderItem.order', function ($q) use ($value) {
                    $q->where('order_no', 'like', "%{$value}%");
                });
            }
                
            // 如果有采购订单搜索条件
            if (isset($inputs['purchase_order']) && $inputs['purchase_order'] !== '') {
                $value = $inputs['purchase_order'];
                $grid->model()->whereHas('items', function ($q) use ($value) {
                    $q->where('order_no', 'like', "%{$value}%");
                });
            }
            
            // 设置快速搜索框 - 修复不存在的purchaseOrder关联方法
            $grid->quickSearch(function ($query, $value) {
                if (empty($value)) {
                    return;
                }
                
                $query->where('exit_no', 'like', "%{$value}%")
                    ->orWhereHas('items.customerOrderItem.order', function ($q) use ($value) {
                        $q->where('order_no', 'like', "%{$value}%");
                    })
                    ->orWhereHas('items', function ($q) use ($value) {
                        $q->where('order_no', 'like', "%{$value}%");
                    });
            });
            
            // 禁用所有操作
            $grid->disableActions();
            
            // 禁用批量操作
            $grid->disableBatchActions();
        });
    }

    /**
     * 表单页面
     *
     * @return Form
     */
    protected function form()
    {
        // 确保宏方法已注册并加载JS文件
        // H3cInventorySelect::applyExitFormScripts(new Form(new TecH3cWarehouseExitModel()));
            
        $form = new Form(new TecH3cWarehouseExitModel());
            
        // 添加API基础路径meta标签
        Admin::html('<meta name="api-base-path" content="/admin">');
        
        // 添加CSRF令牌
        Admin::html('<meta name="csrf-token" content="' . csrf_token() . '">');
            
        // 添加告知日志,帮助调试API路径问题
        Log::info('出库单表单初始化', [
            'api_base_path' => '/admin',
            'route_path' => '/admin/api/h3c/warehouse-exit/inventory-stock'
        ]);
            
        // 引入前端资源
        Admin::css('/css/warehouse-exit/source-tags.css?v=' . time());
        
        // 先引入合并后的模块JS文件
        Admin::js('/js/warehouse-exit/warehouse-exit-modules.js?v=' . time());
        
        // 再引入库存跟踪模块(独立文件)
        Admin::js('/js/warehouse-exit/inventory-tracker.js?v=' . time());
        
        // 最后引入主JS文件
        Admin::js('/js/warehouse-exit/index.js?v=' . time());
        
        // 禁用重置按钮
        $form->disableResetButton();
         
        // 工具栏
        $form->tools(function (Form\Tools $tools) {
            $tools->disableDelete();
            $tools->disableView();
        });
        
        // 调用buildMainForm构建表单内容
        $this->buildMainForm($form);
        
        // 添加自定义CSS样式
        Admin::style('
            /* 明细行样式 */
            .has-many-items-form {
                position: relative;
                padding: 15px;
                margin-bottom: 15px;
                border-radius: 5px;
                transition: all 0.3s ease;
            }
            .has-many-items-form:hover {
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            /* 订单信息样式 */
            .help-block {
                color: #3490dc !important;
                font-weight: bold;
            }
            /* 数量输入限制样式 */
            input[name$="[quantity]"][max] {
                border-color: #3490dc;
            }
        ');
        
        return $form;
    }

    /**
     * 构建表单主体部分
     * 
     * @param Form $form
     */
    protected function buildMainForm(Form $form)
    {
        // 设置管理员ID(必须放在开始位置)
        $form->hidden('operator_id')->default(Admin::user()->id)->value(Admin::user()->id);
        
        // 移除默认仓库ID硬编码，让系统使用用户选择的仓库
        // $form->hidden('default_warehouse_id')->default(1)->attribute(['id' => 'default_warehouse_id']);
        
        // 表单ID显示
        $form->display('id', 'ID');
        
        // 表单基本信息区块
        $form->block(12, function (Form\BlockForm $form) {
            $form->column(12, function (Form\BlockForm $form) {
                // 基本信息行
                $form->row(function (Form\Row $row) {
                    // 出库单号
                    $row->width(2)->text('exit_no', '出库单号')
                        ->default(TecH3cWarehouseExitModel::generateExitNo())
                        ->readOnly();
            
                    // 仓库选择
                    $row->width(2)->select('warehouse_id', '仓库')
                        ->options(TecWarehouseModel::pluck('name', 'id'))
                        ->default(1)
                        ->required();
            
                    // 出库类型
                    $row->width(2)->select('exit_type', '出库类型')
                        ->options(TecH3cWarehouseExitModel::$exitTypeMap)
                        ->default(TecH3cWarehouseExitModel::TYPE_SALES)
                        ->required();
            
                    // 出库日期
                    $row->width(2)->text('exit_date', '出库日期')
                        ->default(date('Y-m-d'));
                        
                    // 备注
                    $row->width(4)->text('remark', '备注');
                });
            });
        });
        
        // 明细分隔线
        $form->divider('出库明细');
        
        // 明细表单区域
        $form->block(12, function (Form\BlockForm $form) {
            // hasMany明细表单
            $form->hasMany('items', '', function (Form\NestedForm $form) {
                // 明细主行
                $form->row(function (Form\Row $row) {
                    // 客户订单选择
                    $row->width(4)->select('customer_order_item_id', '客户订单')
                        ->options(function () {
                            // 从模型直接获取待发货和部分发货的订单明细
                            return \App\Models\TecH3cCustomerPurchaseOrderItemModel::getAvailableOrderItems();
                        })
                        ->help('<span class="order-item-info text-muted">请选择客户订单</span>');

                    // 产品选择
                    $row->width(2)->select('product_id', '产品')
                        ->options(function () {
                            return []; // 初始为空，根据选择的客户订单动态加载
                        })
                        ->disable() // 初始禁用，等待客户订单选择后启用
                        ->required();
                    
                    // 货位选择
                    $row->width(4)->select('location_id', '货位')
                        ->options(function () {
                            return []; // 初始为空，根据选择的产品动态加载
                        })
                        ->disable() // 初始禁用，等待产品选择后启用
                        ->required()
                        ->help('显示格式: 货位名称(可用:数量)');

                    // 出库数量
                    $row->width(2)->number('quantity', '出库数量')
                        ->min(1)
                        ->default(1)
                        ->required();
                });

                // 隐藏字段
                $form->hidden('unit_id')->default(1);
                $form->hidden('delivered_quantity');
            });
        });
    }
    
    /**
     * 取消出库单
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel($id)
    {
        try {
            // 将 $id 转换为整数类型
            $id = (int) $id;
            
            // 获取取消原因
            $remark = request('remark');
            
            // 使用Repository处理数据库操作
            $repo = new TecH3cWarehouseExitRepo();
            $result = $repo->cancel($id, $remark);
            
            if ($result['status']) {
                return response()->json([
                    'status' => true,
                    'message' => $result['message'],
                    'data' => []
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => $result['message'],
                    'data' => []
                ]);
            }
        } catch (\Exception $e) {
            Log::error('出库单取消失败', [
                'exit_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => request()->all()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '出库单取消失败：' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 执行出库操作
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function process($id)
    {
        try {
            // 使用Repository处理数据库操作
            $repo = new TecH3cWarehouseExitRepo();
            $result = $repo->process($id, auth()->id());
            
            if ($result['status']) {
                return response()->json([
                    'status' => true,
                    'message' => $result['message'],
                    'data' => []
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => $result['message'],
                    'data' => []
                ]);
            }
        } catch (\Exception $e) {
            Log::error('出库操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => request()->all()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '出库操作失败：' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 获取客户订单信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomerOrders(Request $request)
    {
        try {
            $q = $request->get('q');
            
            // 记录请求日志
            Log::info('获取客户订单信息请求', [
                'q' => $q,
                'request_data' => $request->all()
            ]);
            
            $result = TecH3cCustomerPurchaseOrderModel::getAvailableOrders($q);
            
            // 记录结果日志
            Log::info('获取客户订单信息成功', [
                'q' => $q,
                'order_count' => count($result)
            ]);
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => ['data' => $result]
            ]);
        } catch (\Exception $e) {
            Log::error('获取客户订单信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取客户订单信息失败：' . $e->getMessage(),
                'data' => ['data' => []]
            ]);
        }
    }
    
    /**
     * 获取订单明细信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrderItemInfo(Request $request)
    {
        try {
            $orderItemId = $request->get('order_item_id');
            
            if (empty($orderItemId)) {
                return response()->json([
                    'status' => true,
                    'message' => '未提供订单明细ID',
                    'data' => [
                        'delivered_quantity' => 0,
                        'remaining_quantity' => 0,
                        'total_quantity' => 0
                    ]
                ]);
            }
            
            // 加载订单项及其关联信息
            $orderItem = TecH3cCustomerPurchaseOrderItemModel::with(['order', 'productInfo'])
                ->find($orderItemId);
            
            if (!$orderItem) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到订单明细',
                    'data' => [
                        'delivered_quantity' => 0,
                        'remaining_quantity' => 0,
                        'total_quantity' => 0
                    ]
                ]);
            }
            
            $quantity = $orderItem->quantity ?? 0;
            
            // 查询实际未删除的出库记录数量
            $actualDelivered = DB::table('t_warehouse_exit_items_h3c')
                ->where('customer_order_item_id', $orderItem->id)
                ->whereNull('deleted_at')
                ->sum('quantity');
                
            $remaining = $quantity - $actualDelivered;
            
            // 获取产品信息 - 如果关联加载失败，直接查询产品表
            $product = $orderItem->productInfo;
            
            // 如果关联加载失败，直接查询产品表
            if (!$product && !empty($orderItem->product)) {
                $product = TecH3cProductListModel::find($orderItem->product);
                
                // 记录日志
                Log::info('订单明细关联产品加载失败，直接查询产品信息', [
                    'order_item_id' => $orderItemId,
                    'product_id' => $orderItem->product,
                    'direct_query_result' => $product ? 'success' : 'failed'
                ]);
            }
            
            // 构建产品名称的显示格式 - 不包含BOM
            if ($product) {
                // 使用产品信息 - 只显示产品名称
                $productName = $product->product ?? 'Unknown Product';
            } else {
                // 回退到订单项中的自有字段 - 只显示产品名称
                $productName = $orderItem->product_name ?? 'Unknown Product';
            }
            
            // 获取订单信息
            $order = $orderItem->order;
            $orderNo = $order ? $order->order_no : '';
            $caseName = $order ? $order->case_name : '';
            
            // 记录调试信息 - 详细记录产品信息
            Log::info('订单明细数据详情', [
                'order_item_id' => $orderItemId,
                'product_field' => $orderItem->product, // 根据模型定义，这是产品ID字段
                'productInfo' => $product ? [
                    'id' => $product->id,
                    'product' => $product->product,
                    'product_bom' => $product->product_bom,
                    // 产品表中没有unit_id字段，不再从产品中获取此字段
                ] : null,
                'productName构建结果' => $productName
            ]);
            
            // 使用默认单位ID=1，不再从产品中获取
            $defaultUnitId = 1;
            
            $data = [
                'delivered_quantity' => $actualDelivered,
                'remaining_quantity' => $remaining,
                'total_quantity' => $quantity,
                'product_id' => $orderItem->product, // 修正为正确的字段名product
                'product_name' => $productName,
                'order_no' => $orderNo,
                'case_name' => $caseName,
                'unit_id' => $defaultUnitId // 使用默认单位ID
            ];
            
            Log::info('获取订单明细信息成功', [
                'product_id' => $orderItem->product,
                'product_name' => $productName,
                'unit_id' => $defaultUnitId
            ]);
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $data
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取订单明细信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_item_id' => $request->get('order_item_id')
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取订单明细信息失败',
                'data' => [
                    'delivered_quantity' => 0,
                    'remaining_quantity' => 0,
                    'total_quantity' => 0
                ]
            ]);
        }
    }
    
    /**
     * 获取产品信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProducts(Request $request)
    {
        try {
            $q = $request->get('q');
            $warehouseId = $request->get('warehouse_id');
            
            // 记录请求日志
            Log::info('获取产品信息请求', [
                'q' => $q,
                'warehouse_id' => $warehouseId,
                'request_data' => $request->all()
            ]);
            
            // 使用模型方法获取有可用库存的产品
            $query = TecH3cWarehouseInventoryModel::getProductsWithAvailableStock($q, $warehouseId);
            
            $queryResult = $query->paginate(null, ['id', 'text']);
            
            // 记录结果日志
            Log::info('获取产品信息成功', [
                'q' => $q,
                'warehouse_id' => $warehouseId,
                'product_count' => $queryResult->total()
            ]);
            
            // 返回Select2需要的格式
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => [
                    'results' => $queryResult->items(),
                    'pagination' => [
                        'more' => $queryResult->hasMorePages()
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取产品信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            // 返回空结果但符合Select2格式
            return response()->json([
                'status' => false,
                'message' => '获取产品信息失败',
                'data' => [
                    'results' => [],
                    'pagination' => ['more' => false]
                ]
            ]);
        }
    }
    
    /**
     * 获取货位信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWarehouseLocations(Request $request)
    {
        try {
            // 尝试从不同参数名获取仓库ID（兼容多种调用方式）
            $warehouseId = $request->input('warehouse_id') ?? $request->get('q');
            $productId = $request->input('product_id');
            
            // 记录请求日志
            Log::info('获取货位信息请求', [
                'warehouse_id' => $warehouseId,
                'product_id' => $productId,
                'warehouse_id_from_input' => $request->input('warehouse_id'),
                'warehouse_id_from_q' => $request->get('q'),
                'request_data' => $request->all(),
                'method' => $request->method(),
                'url' => $request->url(),
                'query_string' => $request->getQueryString()
            ]);
            
            if (empty($warehouseId)) {
                Log::warning('获取货位信息失败：仓库ID为空', [
                    'request_data' => $request->all()
                ]);
                
                // 返回Select2格式空结果
                return response()->json([
                    'status' => false,
                    'message' => '仓库ID不能为空',
                    'data' => [
                        'results' => [],
                        'pagination' => ['more' => false]
                    ]
                ]);
            }
            
            // 确保仓库ID为整数
            $warehouseId = intval($warehouseId);
            
            $results = [];
            
            // 如果提供了产品ID，则只返回该产品在该仓库中有库存的货位
            if ($productId) {
                $productId = intval($productId);
                
                // 直接查询该产品在该仓库中的库存记录
                $inventories = TecH3cWarehouseInventoryModel::where('product_id', $productId)
                    ->where('warehouse_id', $warehouseId)
                    ->with(['location'])
                    ->get();
                
                Log::info('查询产品库存记录', [
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'inventories_count' => $inventories->count()
                ]);
                
                // 不合并库存记录，每个库存记录作为独立选项
                foreach ($inventories as $inventory) {
                    if ($inventory->location && $inventory->available_quantity > 0) {
                        $locationId = $inventory->location->id;
                        $locationName = $inventory->location->location_name;
                        $availableQty = $inventory->available_quantity;
                        $orderNo = $inventory->order_no ?? '';
                        
                        // 构建显示文本，包含订单号信息（如果有）
                        $displayText = $locationName . ' (可用:' . $availableQty . ')';
                        if ($orderNo) {
                            $displayText .= ' [' . $orderNo . ']';
                        }
                        
                        $results[] = [
                            'id' => $locationId,
                            'text' => $displayText,
                            'inventory_id' => $inventory->id,
                            'available_quantity' => $availableQty,
                            'order_no' => $orderNo,
                            'location_name' => $locationName
                        ];
                    }
                }
                
                Log::info('基于产品库存获取货位', [
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'locations_count' => count($results)
                ]);
            } else {
                // 如果没有产品ID，则返回该仓库的所有货位
                $locationData = TecWarehouseLocationModel::getLocationsByWarehouse($warehouseId);
                
                // 转换为Select2格式
                foreach ($locationData as $location) {
                    $results[] = [
                        'id' => $location['id'],
                        'text' => $location['text']
                    ];
                }
                
                Log::info('获取仓库所有货位', [
                    'warehouse_id' => $warehouseId,
                    'locations_count' => count($results)
                ]);
            }
            
            // 记录结果日志
            Log::info('获取货位信息成功', [
                'warehouse_id' => $warehouseId,
                'location_count' => count($results)
            ]);
            
            // 记录输出结果
            Log::info('返回货位数据', [
                'count' => count($results),
                'data' => $results
            ]);
            
            // 返回Select2格式的结果
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => [
                    'results' => $results,
                    'pagination' => ['more' => false]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取货位信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            // 返回Select2格式空结果
            return response()->json([
                'status' => false,
                'message' => '获取货位信息失败：' . $e->getMessage(),
                'data' => [
                    'results' => [],
                    'pagination' => ['more' => false]
                ]
            ]);
        }
    }

    /**
     * 获取库存信息
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInventoryStock(Request $request)
    {
        try {
            $productId = $request->get('product_id');
            $warehouseId = $request->get('warehouse_id');
            $locationId = $request->get('location_id');
            $inventoryId = $request->get('inventory_id');
            $orderNo = $request->get('order_no');
            
            // 记录调试信息
            \Illuminate\Support\Facades\Log::info('库存查询请求', [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'location_id' => $locationId,
                'inventory_id' => $inventoryId,
                'order_no' => $orderNo
            ]);

            // 验证必要参数
            if (empty($productId) || empty($warehouseId) || (empty($locationId) && empty($inventoryId))) {
                return response()->json([
                    'status' => false,
                    'message' => '参数不完整',
                    'data' => []
                ]);
            }

            // 构建查询
            $query = TecH3cWarehouseInventoryModel::where('product_id', $productId)
                ->where('warehouse_id', $warehouseId);

            // 根据不同参数进行查询
            if ($inventoryId) {
                $query->where('id', $inventoryId);
            } elseif ($locationId) {
                $query->where('location_id', $locationId);
            }
            
            // 如果有订单号,则附加订单号条件
            if ($orderNo) {
                $query->where('order_no', $orderNo);
            }

            // 获取库存记录，优先返回有可用库存的记录
            $inventory = $query->orderBy('available_quantity', 'desc')->first();

            if (!$inventory) {
                return response()->json([
                    'status' => false,
                    'message' => '未找到库存记录',
                    'data' => []
                ]);
            }

            // 返回库存信息
            $data = [
                'id' => $inventory->id,
                'product_id' => $inventory->product_id,
                'warehouse_id' => $inventory->warehouse_id,
                'location_id' => $inventory->location_id,
                'quantity' => $inventory->quantity,
                'available_quantity' => $inventory->available_quantity,
                'locked_quantity' => $inventory->locked_quantity,
                'order_no' => $inventory->order_no,
                'order_id' => $inventory->order_id,
                'order_item_id' => $inventory->order_item_id
            ];
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取库存信息出错', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 获取产品详细信息API
     * 
     * 此API用于获取产品基本信息和单位信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductInfo(Request $request)
    {
        try {
            $productId = (int)$request->input('product_id');
            
            Log::info('获取产品信息请求', [
                'product_id' => $productId,
                'request_data' => $request->all()
            ]);
            
            if (empty($productId)) {
                return response()->json([
                    'status' => false,
                    'message' => '产品ID不能为空',
                    'data' => []
                ]);
            }
            
            // 获取产品信息
            $product = TecH3cProductListModel::find($productId);
            
            if (!$product) {
                return response()->json([
                    'status' => false,
                    'message' => '未找到产品信息',
                    'data' => []
                ]);
            }
            
            // 获取产品单位信息 - 使用默认单位
            $unitId = 1; // 默认单位ID
            $unit = TecProductUnitModel::find($unitId);
            $unitName = $unit ? $unit->unit_name : '个';
            
            $result = [
                'product_id' => $product->id,
                'product_name' => $product->product,
                'product_code' => $product->product_code ?? '',
                'product_bom' => $product->product_bom ?? '',
                'unit_id' => $unitId, // 使用默认单位ID
                'unit_name' => $unitName,
                'product_status' => $product->status ?? 'normal'
            ];
            
            Log::info('获取产品信息成功', [
                'product_id' => $productId,
                'product_name' => $product->product,
                'unit_id' => $unitId
            ]);
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取产品信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'product_id' => $request->input('product_id')
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取产品信息失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 获取库存来源信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInventorySources(Request $request)
    {
        try {
            $productId = $request->input('product_id');
            $locationId = $request->input('location_id');

            if (!$productId || !$locationId) {
                return response()->json([
                    'status' => false,
                    'message' => '产品和货位参数不能为空',
                    'data' => []
                ]);
            }

            // 获取库存来源数据
            $sources = TecH3cWarehouseInventoryModel::with(['transaction.purchaseOrderItem.purchaseOrder'])
                ->where('product_id', $productId)
                ->where('location_id', $locationId)
                ->where('quantity', '>', 0)
                ->get()
                ->map(function ($inventory) {
                    $transaction = $inventory->transaction;
                    $purchaseItem = $transaction ? $transaction->purchaseOrderItem : null;
                    $purchaseOrder = $purchaseItem ? $purchaseItem->purchaseOrder : null;

                    return [
                        'id' => $inventory->id,
                        'quantity' => $inventory->quantity,
                        'order_no' => $purchaseOrder ? $purchaseOrder->order_no : '未知',
                        'entry_no' => $transaction ? $transaction->source_no : '未知',
                        'entry_date' => $transaction ? $transaction->created_at->format('Y-m-d') : '未知'
                    ];
                })
                ->sortBy('entry_date')
                ->values();

            // 计算总数量
            $totalQuantity = $sources->sum('quantity');

            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => [
                    'sources' => $sources,
                    'total_quantity' => $totalQuantity
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取库存来源失败: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '获取库存来源失败',
                'data' => []
            ]);
        }
    }

    /**
     * 保存出库单
     *
     * @return mixed
     */
    public function store()
    {
        $request = request();
        
        // 记录收到的原始数据，帮助调试
        Log::info('出库单保存请求数据', [
            'raw_items' => $request->input('items'),
            'exit_date' => $request->input('exit_date'),
            'warehouse_id' => $request->input('warehouse_id'),
            'exit_type' => $request->input('exit_type'),
            'all_data' => $request->all()
        ]);
        
        // 验证表单数据
        $validator = Validator::make($request->all(), [
            'exit_date' => 'required|date',
            'warehouse_id' => 'required|integer',
            'exit_type' => 'required|string'
        ]);

        if ($validator->fails()) {
            Log::error('出库单验证失败', [
                'errors' => $validator->errors()->toArray()
            ]);
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first(),
                'data' => []
            ]);
        }
        
        // 尝试处理items数据
        try {
            $items = [];
            $itemsData = $request->input('items');
            
            // 检查items的数据类型及处理方式
            if (is_string($itemsData)) {
                // 如果是JSON字符串，解析为数组
                $items = json_decode($itemsData, true);
                
                // 检查解析结果
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('items JSON字符串解析失败', [
                        'error' => json_last_error_msg(),
                        'raw_data' => $itemsData
                    ]);
                    return response()->json([
                        'status' => false,
                        'message' => 'items数据格式错误: ' . json_last_error_msg(),
                        'data' => []
                    ]);
                }
            } elseif (is_array($itemsData)) {
                // 如果已经是数组，检查是否是Dcat Admin表单嵌套数组格式
                if (isset($itemsData['new_1']) || isset($itemsData[0])) {
                    // 是Dcat Admin的hasMany格式（包含new_1, new_2等索引）
                    Log::info('检测到Dcat Admin hasMany数组格式');
                    
                    foreach ($itemsData as $key => $item) {
                        // 跳过被标记为删除的行
                        if (isset($item['_remove_']) && $item['_remove_'] === '1') {
                            continue;
                        }
                        
                        // 处理location_id可能包含订单号的情况
                        $locationId = $item['location_id'];
                        $orderNo = null;
                        
                        if (is_string($locationId) && strpos($locationId, '__') !== false) {
                            $parts = explode('__', $locationId);
                            $locationId = $parts[0];
                            $orderNo = $parts[1] ?? null;
                        }
                        
                        $item['location_id'] = $locationId;
                        $item['order_no'] = $orderNo;
                        
                        $items[] = $item;
                    }
                } else {
                    // 直接是简单数组格式
                    $items = $itemsData;
                }
            } else {
                Log::error('items数据类型无效', [
                    'type' => gettype($itemsData),
                    'raw_data' => $itemsData
                ]);
                return response()->json([
                    'status' => false,
                    'message' => 'items数据类型无效',
                    'data' => []
                ]);
            }
            
            // 检查是否为空数组
            if (empty($items)) {
                return response()->json([
                    'status' => false,
                    'message' => '至少需要一项出库明细',
                    'data' => []
                ]);
            }
            
            Log::info('items数据处理成功', [
                'count' => count($items),
                'items' => $items
            ]);
            
            // 验证每个出库项的库存是否足够
            foreach ($items as $item) {
                $locationId = $item['location_id'];
                $orderNo = $item['order_no'] ?? null;
                
                // 查询条件
                $inventoryConditions = [
                    'product_id' => $item['product_id'],
                    'location_id' => $locationId,
                    'warehouse_id' => $request->input('warehouse_id')
                ];
                
                // 如果有指定订单号,优先按订单号筛选
                if ($orderNo) {
                    $inventoryConditions['order_no'] = $orderNo;
                    
                    // 尝试根据订单号找到对应的订单ID
                    $purchaseOrder = TecH3cPurchaseOrderModel::where('order_no', $orderNo)->first();
                    if ($purchaseOrder) {
                        $inventoryConditions['order_id'] = $purchaseOrder->id;
                        Log::info('根据订单号找到订单ID', [
                            'order_no' => $orderNo,
                            'order_id' => $purchaseOrder->id
                        ]);
                    }
                }
                
                Log::info('查询库存记录', [
                    'conditions' => $inventoryConditions,
                    'product_id' => $item['product_id'],
                    'location_id' => $locationId,
                    'order_no' => $orderNo
                ]);
                
                // 从库存记录中获取purchase_order_item_id
                $inventory = TecH3cWarehouseInventoryModel::where($inventoryConditions)->first();
                $exitItem = new TecH3cWarehouseExitItemModel();
                if ($inventory) {
                    // 记录库存ID和批次号
                    $exitItem->inventory_id = $inventory->id;
                    
                    // 记录采购订单信息
                    if ($inventory->purchase_order_item_id) {
                        $exitItem->purchase_order_item_id = $inventory->purchase_order_item_id;
                    }
                    
                    // 记录来源订单ID
                    if ($inventory->order_id) {
                        // 这里不需要显式设置order_id，因为出库项模型可能没有这个字段
                        // 但我们可以记录在日志中，以便调试
                        Log::info('关联出库明细与库存记录的订单ID', [
                            'inventory_id' => $inventory->id,
                            'order_id' => $inventory->order_id,
                            'order_no' => $inventory->order_no
                        ]);
                    }
                    
                    Log::info('找到匹配的库存记录', [
                        'inventory_id' => $inventory->id,
                        'available_quantity' => $inventory->available_quantity,
                        'order_no' => $inventory->order_no,
                        'order_id' => $inventory->order_id
                    ]);
                } else {
                    // 如果找不到完全匹配的库存记录，尝试放宽条件
                    $basicConditions = [
                        'product_id' => $item['product_id'],
                        'location_id' => $locationId,
                        'warehouse_id' => $request->input('warehouse_id')
                    ];
                    
                    // 尝试只按产品和位置查找，但确保有足够库存
                    $alternativeInventory = TecH3cWarehouseInventoryModel::where($basicConditions)
                        ->where('available_quantity', '>=', $item['quantity'])
                        ->orderBy('created_at', 'asc')  // 先进先出原则
                        ->first();
                        
                    if ($alternativeInventory) {
                        $exitItem->inventory_id = $alternativeInventory->id;
                        
                        // 记录采购订单信息
                        if ($alternativeInventory->purchase_order_item_id) {
                            $exitItem->purchase_order_item_id = $alternativeInventory->purchase_order_item_id;
                        }
                        
                        // 记录来源订单ID
                        if ($alternativeInventory->order_id) {
                            // 这里不需要显式设置order_id，因为出库项模型可能没有这个字段
                            // 但我们可以记录在日志中，以便调试
                            Log::info('关联出库明细与库存记录的订单ID', [
                                'inventory_id' => $alternativeInventory->id,
                                'order_id' => $alternativeInventory->order_id,
                                'order_no' => $alternativeInventory->order_no
                            ]);
                        }
                        
                        Log::info('找到替代的库存记录', [
                            'inventory_id' => $alternativeInventory->id,
                            'available_quantity' => $alternativeInventory->available_quantity,
                            'order_no' => $alternativeInventory->order_no,
                            'order_id' => $alternativeInventory->order_id,
                            'original_order_no' => $orderNo
                        ]);
                    } else {
                        // 如果库存记录中没有purchase_order_item_id,尝试从交易记录中获取
                        $transactionQuery = TecH3cWarehouseTransactionModel::where([
                            'product_id' => $item['product_id'],
                            'location_id' => $locationId,
                            'warehouse_id' => $request->input('warehouse_id'),
                            'type' => 'in'
                        ]);
                        
                        if ($orderNo) {
                            // 如果有订单号，优先按订单号查询
                            $transactionQuery->where('order_no', $orderNo);
                            
                            // 也尝试按订单ID查询
                            if (isset($purchaseOrder) && $purchaseOrder) {
                                $transactionQuery->orWhere('order_id', $purchaseOrder->id);
                            }
                        }
                        
                        $transaction = $transactionQuery->orderBy('created_at', 'desc')->first();
                        
                        if ($transaction) {
                            if ($transaction->order_type === 'purchase' && $transaction->order_item_id) {
                                $exitItem->purchase_order_item_id = $transaction->order_item_id;
                            }
                            
                            // 记录交易中的订单信息
                            if (!empty($transaction->order_no) && empty($exitItem->order_no)) {
                                $exitItem->order_no = $transaction->order_no;
                            }
                            
                            Log::info('从交易记录获取订单信息', [
                                'transaction_id' => $transaction->id,
                                'order_type' => $transaction->order_type,
                                'order_id' => $transaction->order_id,
                                'order_no' => $transaction->order_no,
                                'order_item_id' => $transaction->order_item_id
                            ]);
                        } else {
                            Log::warning('未找到匹配的库存记录或交易记录', [
                                'product_id' => $item['product_id'],
                                'location_id' => $locationId,
                                'order_no' => $orderNo
                            ]);
                        }
                    }
                }
            }
            
        } catch (\Exception $e) {
            Log::error('处理items数据时出错', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'raw_data' => $request->input('items')
            ]);
            return response()->json([
                'status' => false,
                'message' => '处理items数据时出错: ' . $e->getMessage(),
                'data' => []
            ]);
        }

        DB::beginTransaction();
        try {
            // 创建新的出库单
            $exit = new TecH3cWarehouseExitModel();
            $exit->exit_no = 'CK' . date('YmdHis') . rand(100, 999);
            $exit->exit_date = $request->input('exit_date');
            $exit->warehouse_id = $request->input('warehouse_id');
            $exit->exit_type = $request->input('exit_type');
            $exit->operator_id = Admin::user()->id;
            $exit->remark = $request->input('remark');
            $exit->save();

            // 保存出库明细
            foreach ($items as $item) {
                $exitItem = new TecH3cWarehouseExitItemModel();
                $exitItem->exit_id = $exit->id;
                $exitItem->product_id = $item['product_id'];
                
                // 处理location_id可能包含订单号的情况
                $locationId = $item['location_id'];
                $orderNo = null;
                
                if (is_string($locationId) && strpos($locationId, '__') !== false) {
                    $parts = explode('__', $locationId);
                    $locationId = $parts[0];
                    $orderNo = $parts[1] ?? null;
                }
                
                $exitItem->location_id = $locationId;
                $exitItem->quantity = $item['quantity'];
                $exitItem->unit_id = $item['unit_id'];
                $exitItem->processed_inventory = false; // 默认未处理库存
                
                // 设置订单号
                if ($orderNo) {
                    $exitItem->order_no = $orderNo;
                    Log::info('设置出库明细订单号', [
                        'location_id' => $locationId,
                        'order_no' => $orderNo
                    ]);
                }
                
                // 如果有客户订单明细ID，保存
                if (isset($item['customer_order_item_id']) && !empty($item['customer_order_item_id'])) {
                    $exitItem->customer_order_item_id = $item['customer_order_item_id'];
                }

                // 查询条件
                $inventoryConditions = [
                    'product_id' => $item['product_id'],
                    'location_id' => $locationId,
                    'warehouse_id' => $request->input('warehouse_id')
                ];
                
                // 如果有指定订单号,优先按订单号筛选
                if ($orderNo) {
                    $inventoryConditions['order_no'] = $orderNo;
                    
                    // 尝试根据订单号找到对应的订单ID
                    $purchaseOrder = TecH3cPurchaseOrderModel::where('order_no', $orderNo)->first();
                    if ($purchaseOrder) {
                        $inventoryConditions['order_id'] = $purchaseOrder->id;
                        Log::info('根据订单号找到订单ID', [
                            'order_no' => $orderNo,
                            'order_id' => $purchaseOrder->id
                        ]);
                    }
                }
                
                Log::info('查询库存记录', [
                    'conditions' => $inventoryConditions,
                    'product_id' => $item['product_id'],
                    'location_id' => $locationId,
                    'order_no' => $orderNo
                ]);
                
                // 从库存记录中获取purchase_order_item_id
                $inventory = TecH3cWarehouseInventoryModel::where($inventoryConditions)->first();

                if ($inventory) {
                    // 记录库存ID和批次号
                    $exitItem->inventory_id = $inventory->id;
                    
                    // 记录采购订单信息
                    if ($inventory->purchase_order_item_id) {
                        $exitItem->purchase_order_item_id = $inventory->purchase_order_item_id;
                    }
                    
                    // 记录来源订单ID
                    if ($inventory->order_id) {
                        // 这里不需要显式设置order_id，因为出库项模型可能没有这个字段
                        // 但我们可以记录在日志中，以便调试
                        Log::info('关联出库明细与库存记录的订单ID', [
                            'inventory_id' => $inventory->id,
                            'order_id' => $inventory->order_id,
                            'order_no' => $inventory->order_no
                        ]);
                    }
                    
                    Log::info('找到匹配的库存记录', [
                        'inventory_id' => $inventory->id,
                        'available_quantity' => $inventory->available_quantity,
                        'order_no' => $inventory->order_no,
                        'order_id' => $inventory->order_id
                    ]);
                } else {
                    // 如果找不到完全匹配的库存记录，尝试放宽条件
                    $basicConditions = [
                        'product_id' => $item['product_id'],
                        'location_id' => $locationId,
                        'warehouse_id' => $request->input('warehouse_id')
                    ];
                    
                    // 尝试只按产品和位置查找，但确保有足够库存
                    $alternativeInventory = TecH3cWarehouseInventoryModel::where($basicConditions)
                        ->where('available_quantity', '>=', $item['quantity'])
                        ->orderBy('created_at', 'asc')  // 先进先出原则
                        ->first();
                        
                    if ($alternativeInventory) {
                        $exitItem->inventory_id = $alternativeInventory->id;
                        
                        // 记录采购订单信息
                        if ($alternativeInventory->purchase_order_item_id) {
                            $exitItem->purchase_order_item_id = $alternativeInventory->purchase_order_item_id;
                        }
                        
                        // 使用找到的库存记录的订单号，覆盖用户指定的订单号
                        if (!empty($alternativeInventory->order_no)) {
                            $exitItem->order_no = $alternativeInventory->order_no;
                        }
                        
                        Log::info('找到替代的库存记录', [
                            'inventory_id' => $alternativeInventory->id,
                            'available_quantity' => $alternativeInventory->available_quantity,
                            'order_no' => $alternativeInventory->order_no,
                            'order_id' => $alternativeInventory->order_id,
                            'original_order_no' => $orderNo
                        ]);
                    } else {
                        // 如果库存记录中没有purchase_order_item_id,尝试从交易记录中获取
                        $transactionQuery = TecH3cWarehouseTransactionModel::where([
                            'product_id' => $item['product_id'],
                            'location_id' => $locationId,
                            'warehouse_id' => $request->input('warehouse_id'),
                            'type' => 'in'
                        ]);
                        
                        if ($orderNo) {
                            // 如果有订单号，优先按订单号查询
                            $transactionQuery->where('order_no', $orderNo);
                            
                            // 也尝试按订单ID查询
                            if (isset($purchaseOrder) && $purchaseOrder) {
                                $transactionQuery->orWhere('order_id', $purchaseOrder->id);
                            }
                        }
                        
                        $transaction = $transactionQuery->orderBy('created_at', 'desc')->first();
                        
                        if ($transaction) {
                            if ($transaction->order_type === 'purchase' && $transaction->order_item_id) {
                                $exitItem->purchase_order_item_id = $transaction->order_item_id;
                            }
                            
                            // 记录交易中的订单信息
                            if (!empty($transaction->order_no) && empty($exitItem->order_no)) {
                                $exitItem->order_no = $transaction->order_no;
                            }
                            
                            Log::info('从交易记录获取订单信息', [
                                'transaction_id' => $transaction->id,
                                'order_type' => $transaction->order_type,
                                'order_id' => $transaction->order_id,
                                'order_no' => $transaction->order_no,
                                'order_item_id' => $transaction->order_item_id
                            ]);
                        } else {
                            Log::warning('未找到匹配的库存记录或交易记录', [
                                'product_id' => $item['product_id'],
                                'location_id' => $locationId,
                                'order_no' => $orderNo
                            ]);
                        }
                    }
                }
                
                // 保存出库明细项
                $exitItem->save();
                
                // 记录保存结果
                Log::info('保存出库明细项成功', [
                    'exit_id' => $exit->id,
                    'exit_item_id' => $exitItem->id,
                    'product_id' => $exitItem->product_id,
                    'location_id' => $exitItem->location_id,
                    'quantity' => $exitItem->quantity
                ]);
            }

            DB::commit();
            
            // 使用控制器自己的响应方法
            return response()->json([
                'status' => true,
                'message' => '出库单创建成功',
                'data' => ['exit_id' => $exit->id]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建出库单失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            // 美化错误信息
            $errorMessage = $e->getMessage();
            if (strpos($errorMessage, '产品库存不足') !== false) {
                // 这是库存不足错误,直接显示
                return response()->json([
                    'status' => false,
                    'message' => '创建出库单失败: ' . $errorMessage,
                    'data' => []
                ], 400);
            } else {
                // 其他错误,提供更友好的提示
                return response()->json([
                    'status' => false,
                    'message' => '创建出库单失败: ' . $errorMessage,
                    'data' => []
                ], 500);
            }
        }
    }

    /**
     * 更新出库单
     *
     * @param int $id
     * @return mixed
     */
    public function update($id)
    {
        $request = request();
        // 验证请求数据
        $validator = Validator::make($request->all(), (new WarehouseExitRequest())->rules(), (new WarehouseExitRequest())->messages());
        
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first(),
                'data' => []
            ], 422);
        }

        // 验证库存是否足够
        $validationResult = $this->validateInventory($request->all());
        if (!$validationResult[0]) {
            return response()->json([
                'status' => false,
                'message' => $validationResult[1],
                'data' => []
            ], 400);
        }

        // 开始事务
        DB::beginTransaction();
        try {
            // 更新出库单
            $exit = TecH3cWarehouseExitModel::findOrFail($id);
            $exit->fill($request->all());
            $exit->save();

            // 删除原有出库明细
            TecH3cWarehouseExitItemModel::where('exit_id', $id)->delete();

            // 保存出库明细
            $items = json_decode($request->input('items'), true);
            foreach ($items as $item) {
                $exitItem = new TecH3cWarehouseExitItemModel();
                $exitItem->exit_id = $exit->id;
                $exitItem->product_id = $item['product_id'];
                
                // 处理location_id可能包含订单号的情况
                $locationId = $item['location_id'];
                $orderNo = null;
                
                if (is_string($locationId) && strpos($locationId, '__') !== false) {
                    $parts = explode('__', $locationId);
                    $locationId = $parts[0];
                    $orderNo = $parts[1] ?? null;
                }
                
                $exitItem->location_id = $locationId;
                $exitItem->quantity = $item['quantity'];
                $exitItem->unit_id = $item['unit_id'];
                $exitItem->processed_inventory = false; // 默认未处理库存
                
                // 设置订单号
                if ($orderNo) {
                    $exitItem->order_no = $orderNo;
                    Log::info('设置出库明细订单号', [
                        'location_id' => $locationId,
                        'order_no' => $orderNo
                    ]);
                }
                
                // 如果有客户订单明细ID，保存
                if (isset($item['customer_order_item_id']) && !empty($item['customer_order_item_id'])) {
                    $exitItem->customer_order_item_id = $item['customer_order_item_id'];
                }

                // 查询条件
                $inventoryConditions = [
                    'product_id' => $item['product_id'],
                    'location_id' => $locationId,
                    'warehouse_id' => $exit->warehouse_id
                ];
                
                // 如果有指定订单号,优先按订单号筛选
                if ($orderNo) {
                    $inventoryConditions['order_no'] = $orderNo;
                }
                
                // 从库存记录中获取purchase_order_item_id
                $inventory = TecH3cWarehouseInventoryModel::where($inventoryConditions)->first();

                if ($inventory) {
                    // 记录库存ID和批次号
                    $exitItem->inventory_id = $inventory->id;
                    
                    // 记录采购订单信息
                    if ($inventory->purchase_order_item_id) {
                        $exitItem->purchase_order_item_id = $inventory->purchase_order_item_id;
                    }
                } else {
                    // 如果库存记录中没有purchase_order_item_id,尝试从交易记录中获取
                    $transaction = TecH3cWarehouseTransactionModel::where([
                        'product_id' => $item['product_id'],
                        'location_id' => $locationId,
                        'warehouse_id' => $exit->warehouse_id,
                        'type' => 'in'
                    ]);
                    
                    if ($orderNo) {
                        $transaction->where('order_no', $orderNo);
                    }
                    
                    $transaction = $transaction->orderBy('created_at', 'desc')->first();
                    
                    if ($transaction && $transaction->order_type === 'purchase' && $transaction->order_item_id) {
                        $exitItem->purchase_order_item_id = $transaction->order_item_id;
                    }
                }
                
                $exitItem->save();

                // 保存来源和去向分配
                if (isset($item['allocations']) && !empty($item['allocations'])) {
                    $this->saveSourceAllocations($exitItem, $item['allocations']);
                }
            }

            DB::commit();
            return response()->json([
                'status' => true,
                'message' => '更新成功',
                'data' => ['exit_id' => $exit->id]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新出库单失败: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '更新出库单失败: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 保存来源和去向分配数据
     *
     * @param TecH3cWarehouseExitItemModel $exitItem 出库明细项
     * @param array $allocations 分配数据
     * @return void
     */
    protected function saveSourceAllocations(TecH3cWarehouseExitItemModel $exitItem, $allocationsJson)
    {
        // 解析JSON
        $allocations = is_string($allocationsJson) ? json_decode($allocationsJson, true) : $allocationsJson;
        
        if (!is_array($allocations)) {
            Log::warning('无效的来源分配数据: ' . $allocationsJson);
            return;
        }
        
        // 保存每个来源分配
        foreach ($allocations as $allocation) {
            // 跳过无效数据
            if (!isset($allocation['quantity']) || $allocation['quantity'] <= 0) {
                continue;
            }
            
            // 查询采购订单明细
            $purchaseItem = TecH3cPurchaseOrderItemModel::find($allocation['id'] ?? null);
            
            if (!$purchaseItem) {
                Log::warning('找不到采购订单明细: ' . json_encode($allocation));
                continue;
            }
            
            // 更新出库明细的purchase_order_item_id
            $exitItem->purchase_order_item_id = $purchaseItem->id;
            $exitItem->save();
            
            // 创建来源记录
            $source = new TecH3cWarehouseExitOrderSourceModel();
            $source->exit_item_id = $exitItem->id;
            $source->purchase_order_item_id = $purchaseItem->id;
            $source->quantity = $allocation['quantity'];
            $source->purchase_order_no = $purchaseItem->purchaseOrder->order_no;
            $source->case_name = $purchaseItem->purchaseOrder->case_name ?? null;
            
            // 查询入库信息
            $transaction = TecH3cWarehouseTransactionModel::where([
                'product_id' => $exitItem->product_id,
                'location_id' => $exitItem->location_id,
                'order_type' => 'purchase',
                'order_item_id' => $purchaseItem->id,
                'type' => 'in'
            ])->first();
            
            if ($transaction) {
                $source->entry_no = $transaction->source_no;
                $source->entry_date = $transaction->created_at;
            }
            
            // 保存来源记录
            $source->save();
            
            Log::info('保存来源分配成功', [
                'exit_item_id' => $exitItem->id,
                'purchase_order_item_id' => $purchaseItem->id,
                'quantity' => $allocation['quantity']
            ]);
        }
    }

    /**
     * 记录库存交易
     *
     * @param TecH3cWarehouseExitItemModel $item 出库明细项
     * @return void
     */
    protected function recordInventoryTransaction(TecH3cWarehouseExitItemModel $item)
    {
        // 获取出库单信息
        $exit = $item->exit;
        
        // 查询库存记录
        $inventory = TecH3cWarehouseInventoryModel::where([
            'product_id' => $item->product_id,
            'warehouse_id' => $exit->warehouse_id,
            'location_id' => $item->location_id
        ])->first();
        
        if (!$inventory) {
            Log::error('未找到库存记录，无法记录交易', [
                'product_id' => $item->product_id,
                'warehouse_id' => $exit->warehouse_id,
                'location_id' => $item->location_id
            ]);
            return;
        }
        
        // 记录交易前的数量
        $beforeQuantity = $inventory->quantity;
        
        // 更新库存数量
        $inventory->quantity = max(0, $inventory->quantity - $item->quantity);
        $inventory->available_quantity = max(0, $inventory->available_quantity - $item->quantity);
        $inventory->save();
        
        // 记录库存交易
        TecH3cWarehouseTransactionModel::create([
            'transaction_no' => 'OUT' . date('YmdHis') . rand(1000, 9999),
            'type' => 'out',
            'source_type' => 'warehouse_exit',
            'source_id' => $exit->id,
            'source_no' => $exit->exit_no,
            'source_item_id' => $item->id,
            'order_id' => $inventory->order_id,
            'order_no' => $inventory->order_no,
            'order_item_id' => $inventory->order_item_id,
            'order_type' => 'exit',
            'product_id' => $item->product_id,
            'product_bom' => $inventory->product_bom,
            'warehouse_id' => $exit->warehouse_id,
            'location_id' => $item->location_id,
            'unit_id' => $item->unit_id,
            'quantity' => -$item->quantity,
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $inventory->quantity,
            'operator_id' => Admin::user()->id,
            'remarks' => '出库单出库'
        ]);
        
        Log::info('成功记录出库交易', [
            'exit_id' => $exit->id,
            'exit_no' => $exit->exit_no,
            'item_id' => $item->id,
            'product_id' => $item->product_id,
            'location_id' => $item->location_id,
            'quantity' => $item->quantity,
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $inventory->quantity,
            'order_id' => $inventory->order_id,
            'order_no' => $inventory->order_no
        ]);
    }

    /**
     * 回滚库存交易
     *
     * @param TecH3cWarehouseExitItemModel $item 出库明细项
     * @param int $productId 产品ID
     * @param int $locationId 货位ID
     * @param float $quantity 数量
     * @return void
     */
    protected function rollbackInventoryTransaction(TecH3cWarehouseExitItemModel $item, $productId, $locationId, $quantity)
    {
        // 更新库存记录
        $inventory = TecH3cWarehouseInventoryModel::where([
            'product_id' => $productId,
            'location_id' => $locationId
        ])->first();
        
        if (!$inventory) {
            Log::warning("回滚库存失败: 找不到产品 {$productId} 在货位 {$locationId} 的库存记录");
            return;
        }
        
        // 增加可用库存
        $inventory->available_quantity += $quantity;
        $inventory->save();
        
        // 删除之前的出库交易
        TecH3cWarehouseTransactionModel::where([
            'product_id' => $productId,
            'location_id' => $locationId,
            'type' => 'out',
            'reference_type' => 'exit',
            'reference_id' => $item->exit_id
        ])->delete();
    }

    /**
     * 获取客户订单对应的产品库存信息
     * 根据客户订单ID和仓库ID，获取特定客户订单对应的产品库存信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomOrderInventoryProducts(Request $request)
    {
        try {
            // 获取参数
            $warehouseId = $request->input('warehouse_id');
            $orderItemId = $request->input('order_item_id');
            $productId = $request->input('product_id');
            
            // 记录请求信息
            Log::info('获取客户订单产品库存信息请求', [
                'warehouse_id' => $warehouseId,
                'order_item_id' => $orderItemId,
                'product_id' => $productId,
                'all_params' => $request->all()
            ]);
            
            // 参数验证
            if (empty($orderItemId)) {
                return response()->json([
                    'status' => false,
                    'message' => '客户订单明细ID不能为空',
                    'data' => ['results' => []]
                ], 400);
            }
            
            // 获取客户订单明细信息
            $orderItem = TecH3cCustomerPurchaseOrderItemModel::with(['order', 'productInfo'])
                ->find($orderItemId);
            
            if (!$orderItem) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到客户订单明细',
                    'data' => ['results' => []]
                ], 404);
            }
            
            // 获取产品ID
            $productId = $productId ?: $orderItem->product_id;
            
            if (empty($productId)) {
                return response()->json([
                    'status' => false,
                    'message' => '客户订单明细未关联产品',
                    'data' => ['results' => []]
                ], 400);
            }
            
            // 构造查询
            $query = TecH3cWarehouseInventoryModel::query()
                ->select([
                    'id',
                    'product_id',
                    'warehouse_id',
                    'location_id',
                    'quantity',
                    'locked_quantity',
                    'available_quantity',
                    'unit_id'
                ]);
            
            // 筛选产品
            $query->where('product_id', $productId);
            
            // 如果有仓库参数，进一步筛选
            if (!empty($warehouseId)) {
                $query->where('warehouse_id', $warehouseId);
            }
            
            // 只获取有可用库存的记录
            $query->where('available_quantity', '>', 0);
            
            // 执行查询
            $inventory = $query->get();
            
            // 获取产品信息
            $product = TecH3cProductListModel::find($productId);
            
            // 如果没有库存或产品不存在，返回空结果
            if ($inventory->isEmpty() || !$product) {
                Log::warning('客户订单产品无库存', [
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'order_item_id' => $orderItemId
                ]);
                
                return response()->json([
                    'status' => true,
                    'message' => '无库存数据',
                    'data' => ['results' => []]
                ]);
            }
            
            // 获取产品单位 - 使用默认单位
            $unitId = 1; // 默认单位ID
            $unit = TecProductUnitModel::find($unitId);
            $unitName = $unit ? $unit->unit_name : '个';
            
            // 获取仓库和货位名称
            $warehouseIds = $inventory->pluck('warehouse_id')->unique()->toArray();
            $locationIds = $inventory->pluck('location_id')->unique()->toArray();
            
            $warehouses = TecWarehouseModel::whereIn('id', $warehouseIds)->pluck('name', 'id');
            $locations = TecWarehouseLocationModel::whereIn('id', $locationIds)->get()->pluck('location_name', 'id');
            
            // 构造结果
            $results = [];
            
            foreach ($inventory as $item) {
                $warehouseName = $warehouses[$item->warehouse_id] ?? '未知仓库';
                $locationName = $locations[$item->location_id] ?? '未知货位';
                
                // 构建产品显示名称，不包含BOM编号
                $productDisplayName = $product->product ?? '未知产品';
                
                $results[] = [
                    'id' => $productId,
                    'text' => $productDisplayName . ' (' . $locationName . ': ' . $item->available_quantity . $unitName . ')',
                    'warehouse_id' => $item->warehouse_id,
                    'warehouse_name' => $warehouseName,
                    'location_id' => $item->location_id,
                    'location_name' => $locationName,
                    'available_quantity' => $item->available_quantity,
                    'unit_id' => $unitId, // 使用默认单位ID
                    'unit_name' => $unitName
                ];
            }
            
            // 按可用库存降序排序
            usort($results, function($a, $b) {
                return $b['available_quantity'] <=> $a['available_quantity'];
            });
            
            Log::info('获取客户订单产品库存信息成功', [
                'product_id' => $productId,
                'result_count' => count($results)
            ]);
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => ['results' => $results]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取客户订单产品库存信息异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取产品库存时发生错误: ' . $e->getMessage(),
                'data' => ['results' => []]
            ], 500);
        }
    }

    /**
     * 验证库存是否足够
     *
     * @param array $formData 表单数据
     * @return array [bool $valid, string $message] 是否有效,错误信息
     */
    protected function validateInventory(array $formData): array
    {
        $itemsData = $formData['items'] ?? [];
        
        if (empty($itemsData)) {
            return [false, '出库明细不能为空'];
        }
        
        // 处理Dcat Admin表单格式
        if (is_array($itemsData) && (isset($itemsData['new_1']) || isset($itemsData[0]))) {
            $processedItems = [];
            foreach ($itemsData as $key => $item) {
                // 跳过已删除的行
                if (isset($item['_remove_']) && $item['_remove_'] === '1') {
                    continue;
                }
                $processedItems[] = $item;
            }
            $itemsData = $processedItems;
        }

        foreach ($itemsData as $key => $item) {
            // 跳过空行
            if (!isset($item['product_id']) || !isset($item['location_id']) || !isset($item['quantity'])) {
                continue;
            }
            
            // 检查必填字段
            if (empty($item['product_id']) || empty($item['location_id']) || empty($item['quantity'])) {
                return [false, '产品、货位和数量不能为空'];
            }

            // 解析location_id,可能包含采购订单信息如"1__PO-H3CHK-CIP-2025031701"
            $locationId = $item['location_id'];
            $orderNo = null;
            
            if (is_string($locationId) && strpos($locationId, '__') !== false) {
                $parts = explode('__', $locationId);
                $locationId = $parts[0];
                $orderNo = $parts[1] ?? null;
            }
            
            $locationId = (int)$locationId;
            $quantity = (float)$item['quantity'];
            
            // 查询条件
            $conditions = [
                'product_id' => $item['product_id'],
                'location_id' => $locationId,
                'warehouse_id' => $formData['warehouse_id'] ?? null,
            ];
            
            // 如果有指定订单号,优先按订单号筛选
            if ($orderNo) {
                $conditions['order_no'] = $orderNo;
            }
            
            // 查找库存记录
            $query = TecH3cWarehouseInventoryModel::where('product_id', $item['product_id'])
                ->where('location_id', $locationId);
                
            if (isset($formData['warehouse_id'])) {
                $query->where('warehouse_id', $formData['warehouse_id']);
            }
            
            if ($orderNo) {
                $query->where('order_no', $orderNo);
            }
            
            $inventory = $query->first();
            
            if (!$inventory) {
                // 获取产品信息用于提示
                $product = TecH3cProductListModel::find($item['product_id']);
                $productName = $product ? "{$product->product_bom} - {$product->product}" : "ID:{$item['product_id']}";
                
                // 获取货位信息
                $location = TecWarehouseLocationModel::find($locationId);
                $locationName = $location ? $location->location_name : "ID:{$locationId}";
                
                $errorMsg = "创建出库单失败: 找不到产品 {$productName} 在货位 {$locationName} 的库存记录";
                if ($orderNo) {
                    $errorMsg .= "（订单号: {$orderNo}）";
                }
                
                return [false, $errorMsg];
            }
            
            // 检查库存是否足够
            if ($inventory->available_quantity < $quantity) {
                // 获取产品名称
                $product = TecH3cProductListModel::find($item['product_id']);
                $productName = $product ? "{$product->product_bom} - {$product->product}" : "ID:{$item['product_id']}";
                
                // 获取货位信息
                $location = TecWarehouseLocationModel::find($locationId);
                $locationName = $location ? $location->location_name : "ID:{$locationId}";
                
                $errorMsg = "创建出库单失败: 产品库存不足！当前可用库存: {$inventory->available_quantity}, 尝试出库: {$quantity}";
                
                // 添加详细信息
                $errorDetail = "产品: {$productName}, 货位: {$locationName}";
                if ($orderNo) {
                    $errorDetail .= ", 订单号: {$orderNo}";
                }
                
                // 记录详细错误信息到日志
                Log::warning('库存不足', [
                    'product_id' => $item['product_id'],
                    'product_name' => $productName,
                    'location_id' => $locationId,
                    'location_name' => $locationName,
                    'order_no' => $orderNo,
                    'available' => $inventory->available_quantity,
                    'required' => $quantity
                ]);
                
                return [false, $errorMsg . "\n" . $errorDetail];
            }
        }
        
        return [true, ''];
    }

    /**
     * 打印出库单
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\View
     */
    public function print($id)
    {
        try {
            // 获取出库单数据
            $exit = \App\Models\TecH3cWarehouseExitModel::with([
                'items' => function($query) {
                    $query->with([
                        'product',
                        'customerOrderItem.order',
                        'purchaseOrderItem.order',
                        'location',
                        'unit'
                    ]);
                },
                'warehouse',
            ])->findOrFail($id);
            
            // 获取公司信息
            $companyInfo = \App\Models\TecH3cCompanyInfoModel::first();

            // 详细日志记录
            \Illuminate\Support\Facades\Log::info('打印出库单详细信息', [
                'exit_id' => $id,
                'exit_no' => $exit->exit_no,
                'warehouse' => optional($exit->warehouse)->name,
                'remarks' => $exit->remark,
                'items_count' => $exit->items->count(),
                'items' => $exit->items->map(function ($item) {
                    return [
                        'product' => optional($item->product)->product,
                        'product_zokusei' => optional($item->product)->product_zokusei,
                        'unit' => optional($item->unit)->unit_name,
                        'location' => optional($item->location)->location_name,
                    ];
                })->toArray()
            ]);

            // 创建视图
            return view('admin.h3c.tec_h3c_warehouse_exit_confirm', compact('exit', 'companyInfo'));
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('打印出库单时发生错误', [
                'exit_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return admin_error('打印出库单失败', $e->getMessage());
        }
    }

    /**
     * 生成纳品书
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\View
     */
    public function delivery($id)
    {
        try {
            // 获取出库单数据
            $exit = \App\Models\TecH3cWarehouseExitModel::with([
                'items' => function($query) {
                    $query->with([
                        'product',
                        'customerOrderItem.order.sndCompany',
                        'customerOrderItem.order.contact',
                        'purchaseOrderItem.order',
                        'location',
                        'unit'
                    ]);
                },
                'warehouse',
            ])->findOrFail($id);

            // 获取公司信息
            $companyInfo = \App\Models\TecH3cCompanyInfoModel::first();

            // 如果没有公司信息，创建默认值
            if (!$companyInfo) {
                $companyInfo = (object) [
                    'company_name' => '株式会社テクリアス',
                    'postal_code' => '101-0021',
                    'address_line1' => '東京都千代田区外神田2-4-1',
                    'address_line2' => 'ビルディングササゲ・ウエスト5F',
                    'registration_number' => 'T3011101076941',
                    'phone' => '03-3526-2080',
                    'email' => '<EMAIL>',
                    'logo_path' => null,
                    'seal_path' => null
                ];
            }

            // 详细日志记录
            \Illuminate\Support\Facades\Log::info('生成纳品书详细信息', [
                'exit_id' => $id,
                'exit_no' => $exit->exit_no,
                'warehouse' => optional($exit->warehouse)->name,
                'remarks' => $exit->remark,
                'items_count' => $exit->items->count(),
                'company_info' => [
                    'name' => $companyInfo->company_name ?? 'N/A',
                    'has_logo' => !empty($companyInfo->logo_path),
                    'has_seal' => !empty($companyInfo->seal_path)
                ],
                'items' => $exit->items->map(function ($item) {
                    return [
                        'product' => optional($item->product)->product,
                        'product_zokusei' => optional($item->product)->product_zokusei,
                        'unit' => optional($item->unit)->unit_name,
                        'location' => optional($item->location)->location_name,
                    ];
                })->toArray()
            ]);

            // 创建视图
            return view('admin.h3c.tec_h3c_warehouse_exit_delivery', compact('exit', 'companyInfo'));
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('生成纳品书时发生错误', [
                'exit_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return admin_error('生成纳品书失败', $e->getMessage());
        }
    }
}
