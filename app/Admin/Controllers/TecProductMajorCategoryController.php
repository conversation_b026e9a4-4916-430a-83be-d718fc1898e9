<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\TecProductMajorCategoryRepo;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use App\Models\TecProductMajorCategoryModel;
use App\Admin\Actions\Post\Restore;

class TecProductMajorCategoryController extends AdminController
{
    /**
     * Index interface.
     *
     * @param Content $content
     *
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header('商品大分類')
            ->description('商品大分類管理')
            ->body(function (Row $row) {
                // 左侧占据一半宽度
                $row->column(6, function (Column $column) {
                    $column->append($this->grid());
                });

                // 右侧留空
                $row->column(6, function (Column $column) {
                    // 留空，不添加内容
                });
            });
    }


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecProductMajorCategoryRepo(), function (Grid $grid) {

            $grid->column('id')->sortable()->style('width: 100px; text-align: left;');

            $grid->column('major_category')->display(function () {
                // 安全地获取背景色和主要类别
                $backgroundColor = 'success';
                $majorCategory = '未分类';

                // 如果是模型实例，直接获取
                if ($this instanceof TecProductMajorCategoryModel) {
                    $backgroundColor = $this->background_color ?? 'success';
                    $majorCategory = $this->major_category ?? '未分类';
                } 
                // 如果是数组或对象，尝试安全获取
                else {
                    $backgroundColor = $this->background_color ?? $this['background_color'] ?? 'success';
                    $majorCategory = $this->major_category ?? $this['major_category'] ?? '未分类';
                }

                $bgColor = match ($backgroundColor) {
                    'success' => 'var(--success)',   // 青翠欲滴
                    'warning' => 'var(--orange)',    // 千里清秋
                    'primary' => 'var(--primary)',   // 碧海青天
                    'danger' => 'var(--red)',        // 丹霞映日
                    'info' => 'var(--cyan)',         // 青紫缥缈
                    default => 'var(--gray)',
                };

                $textColor = match ($backgroundColor) {
                    'warning' => 'black',
                    default => 'white',
                };

                return "<span style='
                    background-color: {$bgColor}; 
                    color: {$textColor}; 
                    padding: 4px 8px; 
                    margin-right: 3px; 
                    border-radius: 12px; 
                    display: inline-block; 
                    font-size: 12px; 
                    font-weight: 500; 
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    transition: transform 0.2s ease;
                    cursor: default;
                '>{$majorCategory}</span>";
            })->style('text-align: left;');

            $grid->column('short_code')->style('width: 300px; text-align: left;');

            // 设置筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('short_code')->width(2);
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            // 禁止多选
            $grid->disableRowSelector();

            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecProductMajorCategoryModel::class));
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecProductMajorCategoryRepo(), function (Show $show) {
            $show->field('id');
            $show->field('major_category');
            $show->field('short_code');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecProductMajorCategoryRepo(), function (Form $form) {
            $form->display('id');
            $form->text('major_category');
            $form->text('short_code');
            // 添加 radio 选择框来选择背景色
            $form->radio('background_color', '云锦')->options([
                'success' => '<span style="background-color: var(--success); color: white; padding: 5px;">青翠欲滴</span>',
                'warning' => '<span style="background-color: var(--orange); color: black; padding: 5px;">千里清秋</span>',
                'primary' => '<span style="background-color: var(--primary); color: white; padding: 5px;">碧海青天</span>',
                'danger' => '<span style="background-color: var(--red); color: white; padding: 5px;">丹霞映日</span>',
                'info' => '<span style="background-color: var(--cyan); color: white; padding: 5px;">青紫缥缈</span>'
            ])->default('success');

            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮            
        });
    }
}
