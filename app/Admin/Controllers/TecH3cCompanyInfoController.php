<?php

namespace App\Admin\Controllers;

use App\Models\TecH3cCompanyInfoModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use Dcat\Admin\Http\Controllers\AdminController;

/**
 * H3C公司基本信息控制器
 */
class TecH3cCompanyInfoController extends AdminController
{


    /**
     * Make a grid builder.
     */
    protected function grid()
    {
        return Grid::make(new TecH3cCompanyInfoModel(), function (Grid $grid) {
            $grid->column('company_name_jp', '会社名（日本語）')->width('200px');
            $grid->column('company_name_en', '会社名（英語）')->width('200px');
            $grid->column('phone', '電話番号')->width('120px');
            $grid->column('email', 'E-mail')->width('180px');
            $grid->column('website', 'ウェブサイト')->width('200px')->display(function ($website) {
                if (empty($website)) {
                    return '-';
                }

                // 确保URL有协议前缀
                $url = $website;
                if (!preg_match('/^https?:\/\//', $url)) {
                    $url = 'http://' . $url;
                }

                return "<a href='{$url}' target='_blank' rel='noopener noreferrer' style='color: #007bff; text-decoration: none;'>{$website}</a>";
            });

            // 禁用批量操作
            $grid->disableBatchActions();

            // 禁用筛选器
            $grid->disableFilter();

            // 设置每页显示数量
            $grid->paginate(15);

            // 工具栏设置
            $grid->tools(function (Grid\Tools $tools) {
                $tools->batch(function (Grid\Tools\BatchActions $actions) {
                    $actions->disableDelete();
                });
            });
        });
    }

    /**
     * Make a show builder.
     */
    protected function detail($id)
    {
        return Show::make($id, new TecH3cCompanyInfoModel(), function (Show $show) {
            
            // 基本信息
            $show->divider('基本情報');
            $show->field('company_name_jp', '会社名（日本語）');
            $show->field('company_name_en', '会社名（英語）');
            $show->field('postal_code', '郵便番号');
            $show->field('address_line1', '住所1');
            $show->field('address_line2', '住所2');
            $show->field('address_line3', '住所3');
            $show->field('registration_number', '登録番号');
            
            // 联系信息
            $show->divider('連絡先情報');
            $show->field('phone', '電話番号');
            $show->field('fax', 'FAX番号');
            $show->field('email', 'E-mail');
            $show->field('website', 'ウェブサイト');
            
            // 银行信息1
            $show->divider('銀行情報1');
            $show->field('bank1_name', '銀行名1');
            $show->field('bank1_branch', '支店名1');
            $show->field('bank1_branch_code', '支店番号1');
            $show->field('bank1_account_type', '口座種別1');
            $show->field('bank1_account_number', '口座番号1');
            $show->field('bank1_account_name_jp', '口座名義1（日本語）');
            $show->field('bank1_account_name_en', '口座名義1（英語）');
            $show->field('bank1_is_default', 'デフォルト銀行1')->using([0 => 'いいえ', 1 => 'はい']);
            
            // 银行信息2
            $show->divider('銀行情報2');
            $show->field('bank2_name', '銀行名2');
            $show->field('bank2_branch', '支店名2');
            $show->field('bank2_branch_code', '支店番号2');
            $show->field('bank2_account_type', '口座種別2');
            $show->field('bank2_account_number', '口座番号2');
            $show->field('bank2_account_name_jp', '口座名義2（日本語）');
            $show->field('bank2_account_name_en', '口座名義2（英語）');
            $show->field('bank2_is_default', 'デフォルト銀行2')->using([0 => 'いいえ', 1 => 'はい']);
            
            // 银行信息3
            $show->divider('銀行情報3');
            $show->field('bank3_name', '銀行名3');
            $show->field('bank3_branch', '支店名3');
            $show->field('bank3_branch_code', '支店番号3');
            $show->field('bank3_account_type', '口座種別3');
            $show->field('bank3_account_number', '口座番号3');
            $show->field('bank3_account_name_jp', '口座名義3（日本語）');
            $show->field('bank3_account_name_en', '口座名義3（英語）');
            $show->field('bank3_is_default', 'デフォルト銀行3')->using([0 => 'いいえ', 1 => 'はい']);
            
            // 海外銀行情報
            $show->divider('海外銀行情報');
            $show->field('international_bank_name', '海外送金銀行名');
            $show->field('international_bank_code', '海外送金銀行コード');
            $show->field('international_branch_name', '海外送金支店名');
            $show->field('international_branch_code', '海外送金支店コード');
            $show->field('international_account_name', '海外送金口座名義');
            $show->field('international_account_number', '海外送金口座番号');
            $show->field('swift_code', 'SWIFT Code');
            $show->field('bank_address_en', '銀行住所（英語）');
            
            // 文件信息
            $show->divider('ファイル情報');
            $show->field('logo_path', 'ロゴファイル')->image();
            $show->field('seal_path', '印章ファイル')->image();

        });
    }

    /**
     * Make a form builder.
     */
    protected function form()
    {
        return Form::make(new TecH3cCompanyInfoModel(), function (Form $form) {

            // Tab 1: 基本信息
            $form->tab('基本情報', function (Form $form) {
                // 基本信息
                $form->row(function (Form\Row $row) {
                    $row->width(4)->text('company_name_jp', '会社名（日本語）')->required();
                    $row->width(4)->text('company_name_en', '会社名（英語）');
                    $row->width(4)->text('registration_number', '登録番号')->required();

                });

                $form->row(function (Form\Row $row) {
                    $row->width(2)->text('postal_code', '郵便番号')->required();
                    $row->width(4)->text('address_line1', '住所1')->required();
                    $row->width(3)->text('address_line2', '住所2');
                    $row->width(3)->text('address_line3', '住所3');
                });


                // 联系信息
                $form->divider('連絡先情報');
                $form->row(function (Form\Row $row) {
                    $row->width(3)->text('phone', '電話番号')->required();
                    $row->width(3)->text('fax', 'FAX番号');
                    $row->width(3)->email('email', 'E-mail')->required();
                    $row->width(3)->url('website', 'ウェブサイト');
                });

                // 文件路径 - 使用标准字段+CSS实现一行布局
                $form->divider('ファイル情報');
                $form->row(function (Form\Row $row) {
                    $row->width(6)->text('logo_path', 'ロゴファイルパス')
                        ->placeholder('/static/images/logo.png');
                    $row->width(6)->text('seal_path', '印章ファイルパス')
                        ->placeholder('/static/images/seal.png');
                });

                // 使用CSS将预览框定位到输入框右侧
                $form->html('<style>
                    .logo-path-container, .seal-path-container {
                        position: relative;
                    }
                    .logo-path-container .form-control, .seal-path-container .form-control {
                        padding-right: 70px !important;
                    }
                    .image-preview-box {
                        position: absolute;
                        right: 5px;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 60px;
                        height: 32px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background: #f8f9fa;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10;
                    }
                </style>

                <script>
                    $(document).ready(function() {
                        // 为输入框容器添加类名并插入预览框
                        setTimeout(function() {
                            $("input[name=\'logo_path\']").closest(".form-group").addClass("logo-path-container");
                            $("input[name=\'seal_path\']").closest(".form-group").addClass("seal-path-container");

                            // 在logo输入框后添加预览框
                            if (!$("#logo-preview").length) {
                                $("input[name=\'logo_path\']").after(\'<div id="logo-preview" class="image-preview-box"><i class="fa fa-image" style="color: #d1d3e2; font-size: 16px;"></i></div>\');
                            }

                            // 在seal输入框后添加预览框
                            if (!$("#seal-preview").length) {
                                $("input[name=\'seal_path\']").after(\'<div id="seal-preview" class="image-preview-box"><i class="fa fa-image" style="color: #d1d3e2; font-size: 16px;"></i></div>\');
                            }
                        }, 500);
                    });
                </script>');

                // 输入框右侧预览功能
                $form->html('<script>
                    $(document).ready(function() {
                        console.log("Input-group image preview loaded");

                        function createInlinePreview(imageSrc) {
                            return `<img src="${imageSrc}" style="max-height: 32px; max-width: 50px; border-radius: 2px; object-fit: contain;" title="クリックして拡大">`;
                        }

                        function createInlineError() {
                            return `<i class="fa fa-exclamation-triangle" style="color: #dc3545; font-size: 14px;" title="画像が見つかりません"></i>`;
                        }

                        function createInlinePlaceholder() {
                            return `<i class="fa fa-image" style="color: #d1d3e2; font-size: 16px;" title="画像プレビュー"></i>`;
                        }

                        function createInlineLoading() {
                            return `<i class="fa fa-spinner fa-spin" style="color: #6c757d; font-size: 14px;" title="読み込み中"></i>`;
                        }

                        function normalizeImagePath(path) {
                            if (!path) return "";
                            path = path.trim();
                            if (path.startsWith("http://") || path.startsWith("https://")) {
                                return path;
                            }
                            if (!path.startsWith("/")) {
                                path = "/" + path;
                            }
                            return path;
                        }

                        function updateInlinePreview(inputName, previewId) {
                            // 尝试多种选择器来找到输入框
                            var $input = $(`input[name="${inputName}"]`);
                            if (!$input.length) {
                                $input = $(`input[name*="${inputName}"]`);
                            }
                            if (!$input.length) {
                                $input = $(`.field_${inputName}_`);
                            }

                            var rawPath = $input.val();
                            var preview = $(`#${previewId}`);

                            console.log("更新预览:", inputName, "值:", rawPath, "输入框数量:", $input.length);

                            if (!rawPath || rawPath.trim() === "") {
                                preview.html(createInlinePlaceholder());
                                return;
                            }

                            var normalizedPath = normalizeImagePath(rawPath);
                            console.log("Loading image:", normalizedPath);

                            // 显示加载状态
                            preview.html(createInlineLoading());

                            // 测试图片加载
                            var testImg = new Image();
                            testImg.onload = function() {
                                console.log("Image loaded successfully:", normalizedPath);
                                var imgHtml = createInlinePreview(normalizedPath);
                                preview.html(imgHtml);

                                // 添加点击放大功能 - 参照临时商品按钮的实现
                                preview.find("img").on("click", function() {
                                    // 创建遮罩层
                                    var overlay = $("<div>").css({
                                        position: "fixed",
                                        top: 0,
                                        left: 0,
                                        width: "100%",
                                        height: "100%",
                                        background: "rgba(0,0,0,0.5)",
                                        zIndex: 99999998
                                    }).appendTo("body");

                                    // 创建模态框容器
                                    var container = $("<div>").css({
                                        position: "fixed",
                                        top: "50%",
                                        left: "50%",
                                        transform: "translate(-50%, -50%)",
                                        width: "90%",
                                        maxWidth: "800px",
                                        height: "auto",
                                        maxHeight: "80%",
                                        background: "#fff",
                                        borderRadius: "8px",
                                        boxShadow: "0 10px 30px rgba(0,0,0,0.3)",
                                        zIndex: 99999999,
                                        overflow: "hidden"
                                    }).appendTo("body");

                                    // 创建头部
                                    var header = $("<div>").css({
                                        padding: "15px 20px",
                                        borderBottom: "1px solid #dee2e6",
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        background: "#f8f9fa"
                                    }).appendTo(container);

                                    $("<h5>").text("画像プレビュー").css({
                                        margin: 0,
                                        fontSize: "18px",
                                        fontWeight: "500"
                                    }).appendTo(header);

                                    var closeBtn = $("<button>").html("&times;").css({
                                        background: "none",
                                        border: "none",
                                        fontSize: "24px",
                                        cursor: "pointer",
                                        padding: "0",
                                        width: "30px",
                                        height: "30px",
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center"
                                    }).appendTo(header);

                                    // 创建内容区域
                                    var body = $("<div>").css({
                                        padding: "20px",
                                        textAlign: "center",
                                        maxHeight: "calc(80vh - 80px)",
                                        overflow: "auto"
                                    }).appendTo(container);

                                    $("<img>").attr("src", normalizedPath).css({
                                        maxWidth: "100%",
                                        maxHeight: "60vh",
                                        borderRadius: "4px"
                                    }).appendTo(body);

                                    // 关闭功能
                                    function closeModal() {
                                        overlay.remove();
                                        container.remove();
                                    }

                                    closeBtn.on("click", closeModal);
                                    overlay.on("click", closeModal);

                                    // ESC键关闭
                                    $(document).on("keydown.imagePreview", function(e) {
                                        if (e.keyCode === 27) {
                                            closeModal();
                                            $(document).off("keydown.imagePreview");
                                        }
                                    });
                                });
                            };
                            testImg.onerror = function() {
                                console.log("Image failed to load:", normalizedPath);
                                preview.html(createInlineError());
                            };

                            // 超时处理
                            setTimeout(function() {
                                if (preview.find(".fa-spinner").length > 0) {
                                    preview.html(createInlineError());
                                }
                            }, 3000);

                            testImg.src = normalizedPath;
                        }

                        // 监听输入变化 - 使用更广泛的选择器
                        var logoTimeout, sealTimeout;

                        // Logo路径监听
                        $(document).on("input change", "input[name=\'logo_path\'], input[name*=\'logo_path\'], .field_logo_path_", function() {
                            clearTimeout(logoTimeout);
                            logoTimeout = setTimeout(function() {
                                updateInlinePreview("logo_path", "logo-preview");
                            }, 300);
                        });

                        // 印章路径监听
                        $(document).on("input change", "input[name=\'seal_path\'], input[name*=\'seal_path\'], .field_seal_path_", function() {
                            clearTimeout(sealTimeout);
                            sealTimeout = setTimeout(function() {
                                updateInlinePreview("seal_path", "seal-preview");
                            }, 300);
                        });

                        // 初始化预览
                        setTimeout(function() {
                            updateInlinePreview("logo_path", "logo-preview");
                            updateInlinePreview("seal_path", "seal-preview");
                        }, 1000);
                    });
                </script>');
            });

            // Tab 2: 银行信息
            $form->tab('銀行情報', function (Form $form) {
                // 移除测试代码，添加银行开关逻辑
                $form->html('<script>
                    console.log("Bank switches script loaded in tab");

                    // 延迟执行，确保DOM元素已渲染
                    setTimeout(function() {
                        console.log("Setting up bank switches...");

                        var switches = document.querySelectorAll("input[name$=\'_is_default\']");
                        console.log("Found switches:", switches.length);

                        // 为每个开关添加事件监听
                        switches.forEach(function(switchEl, index) {
                            console.log("Setting up switch", index + 1, ":", switchEl.name);

                            switchEl.addEventListener("change", function() {
                                console.log("Switch changed:", this.name, "checked:", this.checked);

                                if (this.checked) {
                                    console.log("This switch is ON, turning off others...");

                                    // 关闭其他开关
                                    switches.forEach(function(otherSwitch) {
                                        if (otherSwitch !== switchEl && otherSwitch.checked) {
                                            console.log("Turning off:", otherSwitch.name);
                                            otherSwitch.checked = false;

                                            // 触发change事件以更新UI
                                            otherSwitch.dispatchEvent(new Event("change"));
                                        }
                                    });

                                    console.log("Finished updating other switches");
                                }
                            });
                        });

                        console.log("All bank switches setup completed");
                    }, 1000);
                </script>');
                // 银行信息1
                $form->divider('銀行情報1');
                $form->row(function (Form\Row $row) {
                    $row->width(3)->switch('bank1_is_default', 'デフォルト銀行');
                    $row->width(3)->text('bank1_name', '銀行名');
                    $row->width(3)->text('bank1_branch', '支店名');
                    $row->width(3)->text('bank1_branch_code', '支店番号');

                });

                $form->row(function (Form\Row $row) {
                    $row->width(3)->select('bank1_account_type', '口座種別')->options(['普通' => '普通', '当座' => '当座']);
                    $row->width(3)->text('bank1_account_number', '口座番号');
                    $row->width(3)->text('bank1_account_name_jp', '口座名義（日本語）');
                    $row->width(3)->text('bank1_account_name_en', '口座名義（英語）');
                });


                // 银行信息2
                $form->divider('銀行情報2');
                $form->row(function (Form\Row $row) {
                    $row->width(3)->switch('bank2_is_default', 'デフォルト銀行');
                    $row->width(3)->text('bank2_name', '銀行名');
                    $row->width(3)->text('bank2_branch', '支店名');
                    $row->width(3)->text('bank2_branch_code', '支店番号');
                });

                $form->row(function (Form\Row $row) {
                    $row->width(3)->select('bank2_account_type', '口座種別')->options(['普通' => '普通', '当座' => '当座']);
                    $row->width(3)->text('bank2_account_number', '口座番号');
                    $row->width(3)->text('bank2_account_name_jp', '口座名義（日本語）');
                    $row->width(3)->text('bank2_account_name_en', '口座名義（英語）');
                });

                // 银行信息3
                $form->divider('銀行情報3');
                $form->row(function (Form\Row $row) {
                    $row->width(3)->switch('bank3_is_default', 'デフォルト銀行');
                    $row->width(3)->text('bank3_name', '銀行名');
                    $row->width(3)->text('bank3_branch', '支店名');
                    $row->width(3)->text('bank3_branch_code', '支店番号');
                });

                $form->row(function (Form\Row $row) {
                    $row->width(3)->select('bank3_account_type', '口座種別')->options(['普通' => '普通', '当座' => '当座']);
                    $row->width(3)->text('bank3_account_number', '口座番号');
                    $row->width(3)->text('bank3_account_name_jp', '口座名義（日本語）');
                    $row->width(3)->text('bank3_account_name_en', '口座名義（英語）');
                });
            });

            // Tab 3: 海外银行信息
            $form->tab('海外銀行情報', function (Form $form) {
                $form->row(function (Form\Row $row) {
                    $row->width(3)->text('international_bank_name', '銀行名');
                    $row->width(2)->text('swift_code', 'SWIFT Code');
                    $row->width(2)->text('international_bank_code', '銀行コード');
                    $row->width(3)->text('international_branch_name', '支店名');
                    $row->width(2)->text('international_branch_code', '支店コード');
                });


                $form->row(function (Form\Row $row) {
                    $row->width(5)->text('international_account_name', '口座名義');
                    $row->width(5)->text('international_account_number', '口座番号');

                }); 
                $form->row(function (Form\Row $row) {
                    $row->width(10)->textarea('bank_address_en', '銀行住所（英語）');
                });
               
            });



            // 添加表单保存前的验证
            $form->saving(function (Form $form) {
                $data = $form->input();

                // 确保只有一个默认银行
                $defaultCount = 0;
                $lastDefault = null;

                if (!empty($data['bank1_is_default'])) {
                    $defaultCount++;
                    $lastDefault = 1;
                }
                if (!empty($data['bank2_is_default'])) {
                    $defaultCount++;
                    $lastDefault = 2;
                }
                if (!empty($data['bank3_is_default'])) {
                    $defaultCount++;
                    $lastDefault = 3;
                }

                // 如果有多个默认银行，只保留最后一个
                if ($defaultCount > 1) {
                    $form->bank1_is_default = ($lastDefault == 1) ? 1 : 0;
                    $form->bank2_is_default = ($lastDefault == 2) ? 1 : 0;
                    $form->bank3_is_default = ($lastDefault == 3) ? 1 : 0;
                }
            });
        });



        return $form;
    }


}
