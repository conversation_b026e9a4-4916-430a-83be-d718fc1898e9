<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\TecTagRepo;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use App\Models\TecTagModel;
use App\Admin\Actions\Post\Restore;


class TecTagController extends AdminController
{
    public function getTags()
    {
        return TecTagRepo::all(['id', 'tag']);
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecTagRepo(), function (Grid $grid) {
            $grid->column('id')->style('width: 100px; text-align: left;')->sortable();

            // 处理 tag 列的显示
            $grid->column('tag')->display(function ($tag) {
                // 使用当前行的背景颜色
                $backgroundColor = $this->background_color ?? '#FFFFFF';
                // 返回带有背景颜色的 HTML 内容
                return "<span style='background-color: {$backgroundColor}; padding: 5px; display: inline-block;'>{$tag}</span>";
            })->sortable()->style('width: 300px; text-align: left;');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed();
        
            });
            $grid->quickSearch('tag');
            // 禁止多选
            $grid->disableRowSelector();
            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecTagModel::class));
                }
            });
        });
    }

    /**
     * Index interface.
     *
     * @param Content $content
     *
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header('Product Categories')
            ->description('Manage your product categories')
            ->body(function (Row $row) {
                // 左侧占据一半宽度
                $row->column(4, function (Column $column) {
                    $column->append($this->grid());
                });

                // 右侧留空
                $row->column(8, function (Column $column) {
                    // 留空，不添加内容
                });
            });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecTagRepo(), function (Show $show) {
            $show->field('id');
            $show->field('tag');
            $show->field('background_color');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecTagRepo(), function (Form $form) {
            $form->display('id');
            $form->text('tag');
            $form->color('background_color')->default('#FFFFFF'); // 使用 color 控件来选择颜色

            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮      
            // 隐藏查看按钮
            $form->tools(function (Form\Tools $tools) {
                $tools->disableDelete(); // 隐藏删除按钮
                $tools->disableView();
            });
        });
    }
}
