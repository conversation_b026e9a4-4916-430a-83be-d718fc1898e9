<?php

/*
 * // +----------------------------------------------------------------------
 * // | erp
 * // +----------------------------------------------------------------------
 * // | Copyright (c) 2006~2020 erp All rights reserved.
 * // +----------------------------------------------------------------------
 * // | Licensed ( LICENSE-1.0.0 )
 * // +----------------------------------------------------------------------
 * // | Author: yxx <<EMAIL>>
 * // +----------------------------------------------------------------------
 */

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Admin;
use App\Models\ProductModel;
use Illuminate\Support\Fluent;
use App\Models\TecSndCompanyModel;
use App\Admin\Actions\Grid\EditOrder;
use App\Models\TecPurchaseOrderModel;
use App\Admin\Actions\Grid\BatchOrderPrint;
use App\Models\TecProductMajorCategoryModel;
use App\Admin\Repositories\TecPurchaseOrderRepo;
use App\Admin\Actions\Grid\BatchCreatePurInOrderSave;
use App\Admin\Extensions\Form\Order\TecOrderController;
use Dcat\Admin\Http\Controllers\AdminController;
class TecPurchaseOrderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecPurchaseOrderRepo(['user', 'supplier']), function (Grid $grid) {
            // $grid->column('id')->sortable();
            // // $grid->column('check_status')->using(TecPurchaseOrderModel::CHECK_STATUS);
            // $grid->column('order_no');
            // $grid->column('other')->emp();
            // $grid->column('status', '状态')->using(TecPurchaseOrderModel::STATUS)->label(TecPurchaseOrderModel::STATUS_COLOR);
            // $grid->column('review_status', '审核状态')->using(TecPurchaseOrderModel::REVIEW_STATUS)->label(TecPurchaseOrderModel::REVIEW_STATUS_COLOR);
            // $grid->column('supplier.name', '供应商名称')->emp();
            // $grid->column('user.username', '创建用户');
            // $grid->column('created_at');
            // $grid->column('finished_at')->emp();
            // $grid->tools(BatchOrderPrint::make());
            // $grid->disableQuickEditButton();
            // $grid->actions(new EditOrder());
            // $grid->filter(function (Grid\Filter $filter) {
            // });

            // 禁止多选
            $grid->disableRowSelector();

            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
            });
        });
    }

    public function iFrameGrid()
    {
        return Grid::make(new TecPurchaseOrderRepo(['user', 'supplier']), function (Grid $grid) {
            $grid->model()->where([
                'status'        => TecPurchaseOrderModel::STATUS_WAIT,
                'review_status' => TecPurchaseOrderModel::REVIEW_STATUS_OK
            ])->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            //            $grid->column('check_status')->using(PurchaseOrderModel::CHECK_STATUS);

            $grid->column('order_no');
            $grid->column('other')->emp();
            $grid->column('status', '状态')->using(TecPurchaseOrderModel::STATUS)->label(TecPurchaseOrderModel::STATUS_COLOR);
            $grid->column('review_status', '审核状态')->using(TecPurchaseOrderModel::REVIEW_STATUS)->label(TecPurchaseOrderModel::REVIEW_STATUS_COLOR);
            $grid->column('supplier.name', '供应商名称')->emp();
            $grid->column('user.username', '创建用户');
            $grid->column('created_at');
            $grid->column('finished_at')->emp();
            $grid->disableQuickEditButton();
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->tools(BatchCreatePurInOrderSave::make());
            $grid->filter(function (Grid\Filter $filter) {});
        });
    }

    protected function setForm(Form &$form): void
    {
        // 第一行
        $form->row(function (Form\Row $row) {
            // 业务类别
            $row->width(2)->select('business_categories', '业务类别')
                ->options(TecProductMajorCategoryModel::all()->pluck('major_category', 'id'))
                ->required()
                ->loadSndCompanyAndContactPerson(
                    route('dcat.admin.api.sndCompaniesAndContactPersonByBusinessCategory')
                );

            // 供应商
            $row->width(4)->select('sndCompany_id', '供应商')
                ->options([]) // 初始为空，待动态加载
                ->required();

            // 担当者
            $row->width(3)->select('contact_person', '担当者')
                ->options([]) // 初始为空，待动态加载
                ->required();
            // 貿易条件
            $row->width(3)->select('incoterms', '貿易条件')
                ->options(config('purchase_config.incoterms'))
                ->required();
        });


        // 第三行
        $form->row(function (Form\Row $row) {
            // 案件名
            $row->width(3)->text('case_name', '案件名')->required();

            // PO单号
            $row->width(3)->text('order_no', 'PO单号')->required();

            // 币种
            $row->width(2)->select('currency', '交易币种')->options(config('purchase_config.currencies'));

            // 当期参照汇率
            $row->width(2)->number('exchange_rate', '当期参照汇率');

            // 单据状态
            $order = $this->order;
            if ($order && $order->review_status === TecPurchaseOrderModel::REVIEW_STATUS_OK) {
                $row->width(2)->select('status', '单据状态')->options(TecPurchaseOrderModel::STATUS)->default($this->oredr_model::STATUS_WAIT)->required();
            } else {
                $row->width(2)->select('status', '单据状态')->options([TecPurchaseOrderModel::STATUS_WAIT => '待收货'])->default(TecPurchaseOrderModel::STATUS_WAIT)->required();
            }
        });

        // 第四行
        $form->row(function (Form\Row $row) {
            // 供应商报价单号
            $row->width(4)->text('quotation_no', '供应商报价单号');


            // 业务日期
            $row->width(2)->text('created_at', '业务日期')->default(now())->required()->readOnly();

            // 备注
            $row->width(6)->text('other', '备注')->saveAsString();
        });
    }





    protected function creating(Form &$form): void
    {
        $businessCategory = $form->business_category;

        $form->row(function (Form\Row $row) {
            $row->hasMany('items', '', function (Form\NestedForm $table) {
                $table->select('product_id', '名称')->options(ProductModel::pluck('name', 'id'))->loadpku(route('dcat.admin.api.product.find'))->required();
                $table->ipt('unit', '单位')->rem(3)->default('-')->disable();
                $table->ipt('type', '类型')->rem(5)->default('-')->disable();
                $table->select('sku_id', '属性选择')->options()->required();
                $table->tableDecimal('percent', '含绒量')->default(0);
                $table->select('standard', '检验标准')->options(TecPurchaseOrderModel::STANDARD)->default(0);
                $table->num('should_num', '采购数量')->required();
                $table->tableDecimal('price', '采购价格')->default(0.00)->required();
            })->useTable()->width(12)->enableHorizontal();
        });
    }

    protected function setItems(Grid &$grid): void
    {
        $order = $this->order;
        $grid->column('sku.product.name', '产品名称');
        $grid->column('sku.product.unit.name', '单位');
        $grid->column('sku.product.type_str', '类型');
        $grid->column('sku_id', '属性')->if(function () use ($order) {
            return $order->review_status === TecPurchaseOrderModel::REVIEW_STATUS_OK;
        })->display(function () {
            return $this->sku['attr_value_ids_str'] ?? '';
        })->else()->selectplus(function (Fluent $fluent) {
            return $fluent->sku['product']['sku_key_value'];
        });
        $grid->column('percent', '含绒量')->if(function () use ($order) {
            return $order->review_status !== TecPurchaseOrderModel::REVIEW_STATUS_OK;
        })->edit();

        $grid->column('standard', '检验标准')->if(function () use ($order) {
            return $order->review_status === TecPurchaseOrderModel::REVIEW_STATUS_OK;
        })->display(function () {
            return TecPurchaseOrderModel::STANDARD[$this->standard];
        })->else()->select(TecPurchaseOrderModel::STANDARD);

        $grid->column('should_num', '采购数量')->if(function () use ($order) {
            return $order->review_status !== TecPurchaseOrderModel::REVIEW_STATUS_OK;
        })->edit();
        $grid->column('price', '采购价格')->if(function () use ($order) {
            return $order->review_status !== TecPurchaseOrderModel::REVIEW_STATUS_OK;
        })->edit();
        $grid->column("_", '合计')->display(function () {
            return bcmul($this->should_num, $this->price, 2);
        });
    }
}
