<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\TecProductAttributeModel;
use App\Models\TecProductCategoryModel;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Widgets\Table;
use App\Admin\Repositories\TecProductAttributeRepo;

class TecProductAttributeController extends AdminController
{
    protected function grid()
    {
        return Grid::make(new TecProductAttributeRepo(), function (Grid $grid) {
            // 设置id列样式
            $grid->column('id')->style('width: 100px; text-align: left;');

            // 设置gallery_id列样式并支持排序
            $grid->column('category_id', '分类')->display(function ($categoryId) {
                $category = TecProductCategoryModel::find($categoryId);
                return $category ? $category->full_path : '未分类';
            })->style('width: 200px; text-align: left;')->sortable()->label();

            // 添加内联编辑功能到属性名称列
            $grid->column('attribute_name', '属性名称')->editable()->expand(function () {
                $attribute = TecProductAttributeModel::with('values')->find($this->id);
                $values = $attribute->values->map(function ($value) {
                    return [$value->name, $value->value];
                })->toArray();
                $table = new Table(['属性名', '属性值'], $values);
                return "<div style='background-color: #008080; padding: 2px; width: 500px; margin: left;margin-left: 200px'>" . $table->render() . "</div>";
            })->style('width: 300px; font-weight: bold; text-align: left;');

            $grid->quickSearch('attribute_name');
            $grid->disableBatchDelete();
            $grid->expandFilter();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('category_id', '分类')->select(TecProductCategoryModel::all()->pluck('full_path', 'id'));
                $filter->like('attribute_name', 'Attribute Name');
            });
            $grid->disableRowSelector();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(false);
                $actions->disableQuickEdit();
                $actions->disableDelete(false);

                // 添加复制按钮
                $copyUrl = route('dcat.admin.t_product_attributes.copy', ['id' => $actions->row->id]);
                $token = csrf_token();
                $actions->append("
                    <form action='{$copyUrl}' method='POST' style='display:inline'>
                        <input type='hidden' name='_token' value='{$token}'>
                        <button type='submit' style='border: none; background: none;' title='复制'>
                            <i class='feather icon-copy'></i>
                        </button>
                    </form>
                ");
            });
            $grid->model()->orderBy('category_id')->orderBy('attribute_name');
        });
    }

    protected function form()
    {
        return Form::make(new TecProductAttributeRepo('values'), function (Form $form) {
            $form->select('category_id', '分类')->options(
                TecProductCategoryModel::all()->pluck('full_path', 'id')
            )->required();
            $form->text('attribute_name', '属性名称')->required();

            $form->hasMany('values', '属性值', function (Form\NestedForm $form) {
                $form->select('name', '属性名')->options(
                    TecProductAttributeModel::getFixedUnits()
                )->required()->width(6);


                $form->text('value', '属性值')->required()->width(6);
            })->useTable();
            $form->disableViewCheck();
            $form->disableEditingCheck();
        });
    }

    public function copy($id)
    {
        $attribute = TecProductAttributeModel::with('values')->findOrFail($id);

        $newAttribute = $attribute->replicate();
        $newAttribute->attribute_name = $attribute->attribute_name . '-copy';
        $newAttribute->save();

        foreach ($attribute->values as $value) {
            $newValue = $value->replicate();
            $newValue->attribute_id = $newAttribute->id;
            $newValue->save();
        }

        return redirect()->route('t_product_attributes.index')->with('successSA2', ['title' => '成功', 'message' => '复制成功']);
    }
}
