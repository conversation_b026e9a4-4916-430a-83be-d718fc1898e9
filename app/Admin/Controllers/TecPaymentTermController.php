<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\TecPaymentTermRepo;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use App\Models\TecPaymentTermModel;
use App\Admin\Actions\Post\Restore;


class TecPaymentTermController extends AdminController
{
    
    /**
     * Index interface.
     *
     * @param Content $content
     *
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header('支払い管理')
            ->description('支払い条件管理')
            ->body(function (Row $row) {
                // 左侧占据一半宽度
                $row->column(6, function (Column $column) {
                    $column->append($this->grid());
                });

                // 右侧留空
                $row->column(6, function (Column $column) {
                    // 留空，不添加内容
                });
            });
    }


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecPaymentTermRepo(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('term_name');
            $grid->column('term_description');
            // $grid->column('months');
            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed();
        
            });
            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecPaymentTermModel::class));
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecPaymentTermRepo(), function (Show $show) {
            $show->field('id');
            $show->field('term_name');
            $show->field('term_description');
            $show->field('months');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecPaymentTermRepo(), function (Form $form) {
            $form->display('id');
            $form->text('term_name');
            $form->text('term_description');
            $form->text('months');

            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮
        });
    }
}
