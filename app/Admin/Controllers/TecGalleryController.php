<?php

namespace App\Admin\Controllers;

use Exception;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Row;
use Illuminate\Http\Request;
use Dcat\Admin\Layout\Column;
use App\Imports\GalleryImport;
use Dcat\Admin\Layout\Content;
use App\Models\TecGalleryModel;
use Illuminate\Support\Facades\Log;

use Maatwebsite\Excel\Facades\Excel;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use App\Admin\Extensions\Expand\galleryFull;
use App\Admin\Actions\Grid\GalleryExcelImport;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\Post\Restore;


class TecGalleryController extends AdminController
{
    /**
     * Index interface.
     *
     * @param Content $content
     *
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header('Product Categories')
            ->description('Manage your product categories')
            ->body(function (Row $row) {
                // 左侧占据一半宽度
                $row->column(6, function (Column $column) {
                    $column->append($this->grid());
                });

                // 右侧留空
                $row->column(6, function (Column $column) {
                    // 留空，不添加内容
                });
            });
    }

    /**
     * 创建表格构建器.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecGalleryModel(), function (Grid $grid) {


            // 设置id列样式
            $grid->column('id')->style('width: 100px; font-weight: bold; text-align: left;');

            // 设置gallery_id列样式并支持排序
            $grid->column('gallery_id')->sortable()->style('width: 200px; font-weight: bold; text-align: left;');

            // 显示图片列
            $grid->column('image_paths')->display(function ($imagePaths) {
                $images = is_string($imagePaths) ? json_decode($imagePaths, true) : $imagePaths;
                if (is_array($images) && !empty($images)) {
                    $url = url(Storage::url($images[0])); // 获取第一张图片的URL
                    return "<img src='{$url}' style='width:50px;height:50px;'>"; // 显示图片
                }
                return '';
            })->expand(GalleryFull::class);

            // $grid->tools(GalleryExcelImport::make());
            // $grid->tools(new GalleryExcelImport());

            // $grid->tools(new GalleryExcelImport());
            $grid->tools(function ($tools) {
                $tools->append(new GalleryExcelImport());
            });
            // 设置筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('gallery_id')->width(2); // 限制检索框的长度
                $filter->scope('trashed', '回收站')->onlyTrashed();

            });

            // 禁止多选
            $grid->disableRowSelector();

            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecGalleryModel::class));
                }
            });

            // 默认按gallery_id升序排列
            $grid->model()->orderBy('gallery_id', 'asc');
        });
    }

    protected function form()
    {
        return Form::make(new TecGalleryModel(), function (Form $form) {
            $form->display('id');
            $form->text('gallery_id')->required()->rules('required|string|max:255');

            // 上传图片image_paths 并对图片进行压缩处理,修改图片名称为gallery_id_原文件名
            $form->multipleImage('image_paths')
                ->uniqueName()
                ->sortable()
                ->maxSize(102400) // 设置最大尺寸
                ->saving(function ($paths) use ($form) {
                    // 确保$paths不为null且是数组
                    if (is_null($paths) || !is_array($paths)) {
                        $paths = [];
                    }
                    // 确保$galleryId不为null
                    $galleryId = $form->gallery_id ?? ($form->model()->attributes['gallery_id'] ?? '');
                    if (empty($galleryId)) {
                        return json_encode($paths); // 如果gallery_id为空，则不处理图片
                    }
                    $newPaths = [];
                    foreach ($paths as $path) {
                        if (!Storage::disk('public')->exists($path)) {
                            continue; // 如果文件不存在，则跳过
                        }
                        $pathInfo = pathinfo($path);
                        // 检查文件名是否已经包含gallery_id，避免重复重命名
                        if (strpos($pathInfo['filename'], $galleryId) === 0) {
                            $newPaths[] = $path; // 如果已经包含，直接使用原路径
                            continue;
                        }
                        $newFileName = "{$galleryId}_{$pathInfo['basename']}";
                        $newFilePath = 'images/' . $newFileName;
                        $absolutePath = storage_path('app/public/' . $path);
                        $image = Image::make($absolutePath);
                        $image->resize(800, 800, function ($constraint) {
                            $constraint->aspectRatio();
                            $constraint->upsize();
                        });
                        // 保存压缩和重命名后的图片
                        $image->save(storage_path('app/public/' . $newFilePath));
                        $newPaths[] = $newFilePath;
                        // 如果新旧路径不同，删除原始图片文件
                        if ($path !== $newFilePath && Storage::disk('public')->exists($path)) {
                            Storage::disk('public')->delete($path);
                        }
                    }
                    return json_encode($newPaths);
                })->required();


            // 在保存之前的自定义逻辑（如果需要）
            $form->saving(function (Form $form) {
                // 自定义逻辑
            });

            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮
            // 隐藏查看按钮
            $form->tools(function (Form\Tools $tools) {
                $tools->disableView();
            });
        });
    }

    public function import(Request $request)
    {
        if ($request->hasFile('excel_file')) {
            $file = $request->file('excel_file');

            Log::info('Starting import process'); // 添加日志记录
            Log::info('Uploaded file path: ' . $file->getPathname());

            // 手动解析 Excel 文件，检查其内容
            $data = Excel::toArray([], $file);
            Log::info('Parsed Excel Data: ', $data);

            try {
                // 创建 GalleryImport 实例
                $import = new GalleryImport($data[0]);
                // 验证所有行的 gallery_id 是否存在
                $import->validateRows();

                // 使用导入类来读取Excel文件
                Excel::import($import, $file);

                Log::info('Import process completed'); // 添加日志记录

                return response()->json([
                    'status' => true,
                    'message' => '文件上传成功,自动刷新页面'
                ]);
            } catch (Exception $e) {
                Log::error('Import Error: ' . $e->getMessage());
                return response()->json([
                    'status' => false,
                    'message' => '上传失败: ' . $e->getMessage()
                ], 400);
            }
        }

        return response()->json([
            'status' => false,
            'message' => '上传失败，请选择文件'
        ], 400);
    }
}
