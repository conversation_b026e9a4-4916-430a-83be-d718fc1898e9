<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Content;
use App\Models\TecSndContactModel;
use App\Models\TecSndCompanyModel;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\Post\Restore;
use App\Support\GridStyleHelper;
use App\Admin\Actions\Grid\TecTecSndContactCopyAction;

class TecSndContactController extends AdminController
{
    // 配置 Grid
    protected function grid()
    {
        $grid = Grid::make(new TecSndContactModel(), function (Grid $grid) {
            $grid->scrollbarX();
            
            $grid->column('id')->sortable();

            // 公司名称
            GridStyleHelper::companyNameColumnConfig('company_id')($grid);

            // 联系人全名
            GridStyleHelper::contactFullNameColumnConfig()($grid);

            $grid->column('department', '部门')->sortable();
            $grid->column('position', '职务')->sortable();
            $grid->column('phone_landline', '座机')->sortable();
            $grid->column('phone', '手机/FAX')->sortable();
            $grid->column('email', '邮箱')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('company_id', '公司名称')->select(function () {
                    // 使用模型方法获取带备注的公司选项
                    return TecSndCompanyModel::getOptionsWithNotes()->toArray();
                })->width(2);
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            $grid->quickSearch(['first_name', 'last_name', 'company.name', 'phone', 'email']);
            
            // 设置操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecSndContactModel::class));
                }
            });

            $grid->batchActions(function ($batch) {
                $batch->add(new TecTecSndContactCopyAction());
            });
        });

        // 设置默认排序（按公司名称分组，然后按联系人姓名排序）
        $grid->model()->orderByCompanyName();

        return $grid;
    }

    // 配置 Index 页面
    public function index(Content $content)
    {
        return $content
            ->header('联系人管理')
            ->description('管理联系人信息')
            ->body($this->grid());
    }

    // 配置 Form 页面
    protected function form()
    {
        return Form::make(new TecSndContactModel(), function (Form $form) {
            // 获取公司选项
            $companyOptions = TecSndCompanyModel::getOptionsWithNotes()->toArray();
            
            // 设置布局为两列
            $form->column(6, function (Form $form) use ($companyOptions) {
                // 修复：如果是编辑模式，先从数据库重新获取联系人的公司ID，确保使用正确的值
                if ($form->isEditing()) {
                    // 获取当前正在编辑的联系人ID
                    $contactId = $form->model()->id;
                    if ($contactId) {
                        // 直接从数据库查询最新的联系人数据
                        $contact = TecSndContactModel::find($contactId);
                        if ($contact) {
                            $correctCompanyId = $contact->company_id;
                            \Illuminate\Support\Facades\Log::info('强制设置公司ID', [
                                'contact_id' => $contactId,
                                'correct_company_id' => $correctCompanyId
                            ]);
                            
                            // 强制设置表单模型的company_id为正确的值
                            $form->model()->company_id = $correctCompanyId;
                        }
                    }
                }
                
                // 设置公司选择器
                $select = $form->select('company_id', '公司')
                    ->options($companyOptions)
                    ->required();
                
                // 如果是编辑模式且有公司ID，则设置默认值
                if ($form->isEditing() && $form->model()->company_id) {
                    $select->default($form->model()->company_id);
                }
                
                $form->text('last_name', '姓')->required();
                $form->text('first_name', '名');
                $form->email('email', '邮箱')->required();
                $form->email('email_cc', '抄送邮箱');
                $form->email('email_billing', '账单邮箱');
            });
            $form->column(6, function (Form $form) {
                $form->text('department', '部门');
                $form->text('position', '职务');
                $form->text('phone_landline', '座机');
                $form->text('phone', '手机/FAX');
                $form->textarea('notes', '备注');
            });
            
            // 表单保存前记录日志
            $form->saving(function (Form $form) {
                \Illuminate\Support\Facades\Log::info('保存联系人', [
                    'id' => $form->model()->id,
                    'company_id' => $form->company_id
                ]);
            });
        });
    }

    // 配置编辑和新增页面
    public function edit($id, Content $content)
    {
        // 记录编辑前的联系人信息
        $contact = TecSndContactModel::find($id);
        if ($contact) {
            \Illuminate\Support\Facades\Log::info('编辑联系人', [
                'id' => $contact->id,
                'company_id' => $contact->company_id
            ]);
            
            // 检查数据库中的公司是否存在
            $companyCheck = TecSndCompanyModel::find($contact->company_id);
            if (!$companyCheck) {
                \Illuminate\Support\Facades\Log::warning('数据库中未找到对应公司', [
                    'company_id' => $contact->company_id
                ]);
            }
        }
        
        return $content
            ->header('担当者管理')
            ->description('担当者編集')
            ->body($this->form()->edit($id));
    }

    public function create(Content $content)
    {
        return $content
            ->header('担当者管理')
            ->description('担当者追加')
            ->body($this->form());
    }
}
