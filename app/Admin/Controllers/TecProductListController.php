<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Admin;
use App\Models\TecH3cProductListModel;
use App\Models\TecProductUnitModel;
use Illuminate\Support\Facades\Log;
use App\Models\TecSkuAttributeModel;
use App\Models\TecProductCategoryModel;
use App\Models\TecProductMajorCategoryModel;
use App\Admin\Repositories\TecProductListRepo;
use Illuminate\Validation\ValidationException;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use App\Models\TecProductListModel;
use App\Admin\Actions\Post\Restore;


class TecProductListController extends AdminController
{
    public function index(Content $content)
    {
        return $content
            ->header('Product Lists')
            ->description('Manage your product lists')
            ->body(function (Row $row) {
                // 左侧占据一半宽度
                $row->column(8, function (Column $column) {
                    $column->append($this->grid());
                });

                // 右侧留空
                $row->column(4, function (Column $column) {
                    // 留空，不添加内容
                });
            });
    }
    // 配置表格显示
    protected function grid()
    {
        return Grid::make(new TecH3cProductListModel(), function (Grid $grid) {
            $grid->column('id')->style('width: 100px; text-align: left;');
            // 显示业务分类列
            $grid->column('major_category_id', '业务分类')->display(function ($majorCategoryId) {
                // 从 TecProductMajorCategoryModel 中获取业务分类模型实例
                $major_category = TecProductMajorCategoryModel::find($majorCategoryId);

                // 获取背景色，默认值为 'success'
                $backgroundColor = $major_category ? $major_category->background_color : 'success';

                // 返回带有背景色的 HTML 字符串
                return '<span style="background-color: var(--' . $backgroundColor . '); color: white; padding: 2px;">' . ($major_category ? $major_category->major_category : '未知') . '</span>';
            })->style('width: 300px; text-align: left;');

            // 显示产品名称列
            $grid->column('name')->label()->style('width: 200px; text-align: left; ');

            // 显示单位列
            $grid->column('unit.unit_name', '单位')->display(function ($unitName) {
                // 如果 unitName 为空，则显示 '无'
                return $unitName ?: '无';
            })->style('width: 100px; text-align: left; ');

            // 显示产品分类列并设置背景色
            $grid->column('category_id', '产品分类')->display(function ($productCategoryId) {
                // 查找单个模型实例
                $category = TecProductCategoryModel::find($productCategoryId);

                // 如果找到模型
                if ($category) {
                    $title = $category->full_path ?? '-';
                    $backgroundColor = $category->background_color ?? '';

                    // 如果有背景颜色，则设置背景色
                    if ($backgroundColor) {
                        return "<span style='background-color: {$backgroundColor}; padding: 2px 4px; border-radius: 3px;'>{$title}</span>";
                    }

                    return $title;
                }
                // 如果未找到模型，返回默认值
                return '-';
            })->style('width: 300px; text-align: left;');

            // 禁用查看按钮
            $grid->disableViewButton();

            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecProductListModel::class));
                }
            });

            // 配置筛选条件
            $grid->filter(function (Grid\Filter $filter) {
                // 筛选业务分类
                $filter->equal('major_category_id', '业务')->select(TecProductMajorCategoryModel::pluck('major_category', 'id')->toArray())->width(4);

                // 筛选产品分类
                $filter->equal('category_id', '分类')->select(TecProductCategoryModel::all()->pluck('full_path', 'id')->toArray())->width(4);
                $filter->scope('trashed', '回收站')->onlyTrashed();

            });
        });
    }

    // 配置表单
    protected function form()
    {
        return Form::make(new TecProductListRepo(['t_product_attr']), function (Form $form) {
            // 业务分类选择字段
            $form->select('major_category_id', '业务')
                ->options(TecProductMajorCategoryModel::pluck('major_category', 'id'))
                ->required();

            // 商品名称字段
            $form->text('name', '商品名称')->required();

            // 单位选择字段
            $form->select('unit_id', '单位')
                ->options(TecProductUnitModel::all()->pluck('unit_name', 'id'))
                ->required();

            // 分类选择字段
            $form->select('category_id', '商品分类SKU')
                ->options(TecProductCategoryModel::all()->pluck('full_path', 'id'))
                ->required()
                ->load('t_product_attr.attr_id', route('dcat.admin.api.attributes.byCategory'))
                ->help('先增加属性再选择分类');

            // 在下拉列表后面添加一个链接，点击时打开新的页面
            $form->html(function () {
                return <<<HTML
                    <div style="margin-top: 5px;">
                        <a href="javascript:void(0);" id="custom-link">商品分类SKU追加画面</a>
                    </div>
                    <script>
                        document.getElementById('custom-link').addEventListener('click', function () {
                            window.open('/admin/r_sku', '_blank');
                        });
                    </script>
            HTML;
            });

            // 属性字段
            $form->hasMany('t_product_attr', '', function (Form\NestedForm $table) {
                $table->select('attr_id', '属性')->options(TecSkuAttributeModel::pluck('attr_name', 'id'))->required()
                    ->load('attr_value_ids', route('dcat.admin.api.attributeCombineValue.find'));
                $table->multipleSelect('attr_value_ids', '属性值')->options()->required();
            })->width(12)->enableHorizontal()->useTable()->required();

            // 表单保存时验证
            $form->saving(function (Form $form) {
                // 获取表单提交的数据
                $data = $form->input(); //->t_product_attr;
                Log::info($data);

                // 提取 t_product_attr 部分的数据
                $productAttrs = $data['t_product_attr'] ?? [];

                // 收集所有 attr_id
                $attrIds = collect($productAttrs)->pluck('attr_id');

                // 检查是否有重复的 attr_id
                if ($attrIds->count() !== $attrIds->unique()->count()) {
                    // 如果存在重复的 attr_id，抛出 ValidationException
                    throw ValidationException::withMessages([
                        't_product_attr' => ['属性SKU不能重复。'],
                    ]);
                }
            });

            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮

            $form->tools(function (Form\Tools $tools) {
                //$tools->disableDelete();
                $tools->disableView();
                //$tools->disableList();
            });

            // 添加自定义 JavaScript
            Admin::script($this->script());
        });
    }

    // 返回自定义 JavaScript 代码
    protected function script()
    {
        return <<<JS
            $(document).ready(function() {
                // 初始化 select2 元素
                $('select').select2({
                    placeholder: '请选择属性值',
                    allowClear: true,
                    width: '100%'
                });
                var categorySelect = $('select[name="category_id"]');
                
                // 监听分类选择变化事件
                categorySelect.on('change', function () {
                    var categoryId = $(this).val();
                    console.log("选择的分类 ID:", categoryId);
                    
                    // 更新属性下拉框
                    $('select[name$="[attr_id]"]').each(function () {
                        var attrSelect = $(this);
                        console.log("更新属性选择框，属性选择框名称:", attrSelect.attr('name'));
                        loadAttributes(attrSelect, categoryId);
                    });
                    
                    // 清空所有属性值下拉框
                    $('select[name$="[attr_value_ids][]"]').each(function () {
                        console.log("清空属性值选择框，属性值选择框名称:", $(this).attr('name'));
                        $(this).empty().append('<option value="">请选择属性值</option>').trigger('change.select2');
                    });
                });
                
                // 加载属性函数
                function loadAttributes(attrSelect, categoryId) {
                    if (!categoryId) {
                        attrSelect.empty().append('<option value="">请选择属性</option>').trigger('change.select2');
                        return;
                    }
                    console.log("加载属性，分类 ID:", categoryId);
                    $.ajax({
                        url: '/admin/api/attributes/byCategory',
                        type: 'GET',
                        data: { q: categoryId },
                        success: function(response) {
                            console.log("属性 API 响应数据:", response);
                            // 处理响应数据
                            if (Array.isArray(response) && response.length === 0) {
                                // 如果返回的是空数组
                                attrSelect.empty().append('<option value="">请选择属性</option>').trigger('change.select2');
                            } else if (Array.isArray(response) && response.length > 0) {
                                // 如果返回的是数组，并且数组中有数据
                                var options = '<option value="">请选择属性</option>';
                                $.each(response, function(index, item) {
                                    options += '<option value="' + item.id + '">' + item.text + '</option>';
                                });
                                attrSelect.html(options).trigger('change.select2');
                            } else {
                                console.error("属性 API 响应数据格式不正确:", response);
                                // 处理格式不正确的情况
                                attrSelect.empty().append('<option value="">请选择属性</option>').trigger('change.select2');
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.error("属性 API 请求失败:", textStatus, errorThrown);
                            // 处理请求失败的情况
                            attrSelect.empty().append('<option value="">请选择属性</option>').trigger('change.select2');
                        }
                    });
                }
            });
        JS;
    }
}
