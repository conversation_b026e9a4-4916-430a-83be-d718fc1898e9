<?php

namespace App\Admin\Controllers\Banhua;

use App\Http\Controllers\Controller;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\TecWarehouseModel;
use App\Models\Banhua\TecBanhuaLocationModel;
use App\Models\Banhua\BanhuaConstants;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Dcat\Admin\Admin;
use Maatwebsite\Excel\Facades\Excel;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use App\Models\Banhua\BanhuaStockImportBatchModel;
use App\Models\Banhua\BanhuaStockImportBatchDataModel;
use App\Services\TempFileService;
use App\Models\Banhua\BanhuaSkuStockModel;

class TecBanhuaOrderBatchImportController extends Controller
{
    /**
     * 显示上传表单
     */
    public function index(Content $content)
    {
        return $content
            ->title('订单批量导入')
            ->description('上传订单Excel文件进行批量导入')
            ->body(view('admin.banhua.order-import-form'));
    }
    
    /**
     * 创建导入表单
     */
    protected function form()
    {
        // 预设变量
        $uploadUrl = $this->getUploadUrl();
        $importUrl = $this->getImportUrl();
        $previewEditUrl = $this->getPreviewEditUrl();
        $checkSessionUrl = $this->getCheckSessionUrl();
        $loadSheetDataUrl = $this->getLoadSheetDataUrl();
        $csrf = csrf_token();
        $adminUrl = admin_url();
        
        // 创建HTML元素
        $html = [];
        
        // 卡片容器
        $html[] = '<div class="card">';
        $html[] = '<div class="card-header"><h3 class="card-title">Excel批量导入订单</h3></div>';
        $html[] = '<div class="card-body">';
        
        // 表单
        $html[] = '<form id="order-import-form" method="post" enctype="multipart/form-data">';
        $html[] = '<input type="hidden" name="_token" value="'.$csrf.'">';
        $html[] = '<div class="alert alert-info">';
        $html[] = '<p>请上传包含订单数据的Excel文件。文件应包含以下信息：</p>';
        $html[] = '<ul>';
        $html[] = '<li>订单编号</li>';
        $html[] = '<li>订单日期</li>';
        $html[] = '<li>客户信息</li>';
        $html[] = '<li>商品SKU及数量</li>';
        $html[] = '</ul>';
        $html[] = '<p>系统将预读Excel文件，让您选择要导入的工作表。</p>';
        $html[] = '</div>';
        
        $html[] = '<div class="form-group">';
        $html[] = '<label for="excel_file">选择Excel文件</label>';
        $html[] = '<input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx,.xls">';
        $html[] = '<small class="form-text text-muted">支持.xlsx和.xls格式，文件大小不超过40MB</small>';
        $html[] = '</div>';
        
        $html[] = '<div class="form-group">';
        $html[] = '<button type="button" id="upload-btn" class="btn btn-primary">上传并预览</button>';
        $html[] = '</div>';
        
        $html[] = '</form>';
        
        // 预览区域
        $html[] = '<div id="preview-area" style="display:none;">';
        $html[] = '<h4>Excel预览</h4>';
        $html[] = '<div id="sheet-selector" class="form-group">';
        $html[] = '<label>选择工作表</label>';
        $html[] = '<div id="sheet-buttons" class="btn-group" role="group"></div>';
        $html[] = '</div>';
        
        $html[] = '<div id="preview-tables"></div>';
        
        $html[] = '<div class="form-group mt-4">';
        $html[] = '<button type="button" id="continue-btn" class="btn btn-success">继续导入</button>';
        $html[] = '</div>';
        $html[] = '</div>';
        
        $html[] = '</div>'; // card-body
        $html[] = '</div>'; // card
        
        // 添加JavaScript
        $html[] = '<script>';
        $html[] = 'var sessionKey = "";';
        $html[] = 'var selectedSheets = [];';
        $html[] = 'var sheetData = {};';
        
        // 上传按钮点击事件
        $html[] = '$(document).ready(function() {';
        $html[] = '  $("#upload-btn").click(function() {';
        $html[] = '    var formData = new FormData($("#order-import-form")[0]);';
        $html[] = '    $.ajax({';
        $html[] = '      url: "' . $uploadUrl . '",';
        $html[] = '      type: "POST",';
        $html[] = '      data: formData,';
        $html[] = '      contentType: false,';
        $html[] = '      processData: false,';
        $html[] = '      success: function(response) {';
        $html[] = '        if (response.status) {';
        $html[] = '          sessionKey = response.data.session_key;';
        $html[] = '          showSheetSelector(response.data.sheets);';
        $html[] = '          showPreviews(response.data.previews);';
        $html[] = '        } else {';
        $html[] = '          Dcat.error(response.message);';
        $html[] = '        }';
        $html[] = '      },';
        $html[] = '      error: function(xhr) {';
        $html[] = '        Dcat.error("上传失败：" + xhr.responseText);';
        $html[] = '      }';
        $html[] = '    });';
        $html[] = '  });';
        
        // 显示工作表选择器
        $html[] = '  function showSheetSelector(sheets) {';
        $html[] = '    $("#sheet-buttons").empty();';
        $html[] = '    sheets.forEach(function(sheet, index) {';
        $html[] = '      var btn = $("<button type=\'button\' class=\'btn btn-outline-primary sheet-btn\' data-sheet=\'" + sheet.name + "\'>" + sheet.name + "</button>");';
        $html[] = '      $("#sheet-buttons").append(btn);';
        $html[] = '      if (index === 0) {';
        $html[] = '        btn.addClass("active");';
        $html[] = '        selectedSheets.push(sheet.name);';
        $html[] = '      }';
        $html[] = '    });';
        $html[] = '    $("#preview-area").show();';
        
        $html[] = '    $(".sheet-btn").click(function() {';
        $html[] = '      var sheetName = $(this).data("sheet");';
        $html[] = '      if ($(this).hasClass("active")) {';
        $html[] = '        $(this).removeClass("active");';
        $html[] = '        selectedSheets = selectedSheets.filter(function(name) { return name !== sheetName; });';
        $html[] = '      } else {';
        $html[] = '        $(this).addClass("active");';
        $html[] = '        if (!selectedSheets.includes(sheetName)) {';
        $html[] = '          selectedSheets.push(sheetName);';
        $html[] = '        }';
        $html[] = '      }';
        $html[] = '      // 更新预览表格的显示状态';
        $html[] = '      $(".preview-table-container").each(function() {';
        $html[] = '        var tableSheetName = $(this).data("sheet");';
        $html[] = '        if (selectedSheets.includes(tableSheetName)) {';
        $html[] = '          $(this).show();';
        $html[] = '        } else {';
        $html[] = '          $(this).hide();';
        $html[] = '        }';
        $html[] = '      });';
        $html[] = '    });';
        $html[] = '  }';
        
        // 显示预览表格
        $html[] = '  function showPreviews(previews) {';
        $html[] = '    $("#preview-tables").empty();';
        $html[] = '    Object.keys(previews).forEach(function(sheetName) {';
        $html[] = '      var preview = previews[sheetName];';
        $html[] = '      var tableHtml = "<div class=\'preview-table-container mb-4\' data-sheet=\'" + sheetName + "\'>";';
        $html[] = '      tableHtml += "<h5>" + sheetName + "</h5>";';
        $html[] = '      tableHtml += "<div class=\'table-responsive\'>";';
        $html[] = '      tableHtml += "<table class=\'table table-bordered table-striped\'>";';
        
        $html[] = '      // 表头';
        $html[] = '      tableHtml += "<thead><tr>";';
        $html[] = '      if (preview.headers && preview.headers.length > 0) {';
        $html[] = '        preview.headers.forEach(function(header) {';
        $html[] = '          tableHtml += "<th>" + header + "</th>";';
        $html[] = '        });';
        $html[] = '      }';
        $html[] = '      tableHtml += "</tr></thead>";';
        
        $html[] = '      // 表体';
        $html[] = '      tableHtml += "<tbody>";';
        $html[] = '      if (preview.data && preview.data.length > 0) {';
        $html[] = '        preview.data.forEach(function(row) {';
        $html[] = '          tableHtml += "<tr>";';
        $html[] = '          row.forEach(function(cell) {';
        $html[] = '            tableHtml += "<td>" + (cell !== null ? cell : "") + "</td>";';
        $html[] = '          });';
        $html[] = '          tableHtml += "</tr>";';
        $html[] = '        });';
        $html[] = '      }';
        $html[] = '      tableHtml += "</tbody>";';
        
        $html[] = '      tableHtml += "</table>";';
        $html[] = '      tableHtml += "</div>";';
        $html[] = '      tableHtml += "</div>";';
        
        $html[] = '      $("#preview-tables").append(tableHtml);';
        $html[] = '    });';
        $html[] = '  }';
        
        // 继续按钮点击事件
        $html[] = '  $("#continue-btn").click(function() {';
        $html[] = '    if (selectedSheets.length === 0) {';
        $html[] = '      Dcat.warning("请至少选择一个工作表");';
        $html[] = '      return;';
        $html[] = '    }';
        $html[] = '    window.location.href = "' . $previewEditUrl . '?session_key=" + sessionKey + "&selected_sheets=" + JSON.stringify(selectedSheets);';
        $html[] = '  });';
        
        $html[] = '});';
        $html[] = '</script>';
        
        // 组合所有HTML元素
        return implode("\n", $html);
    }
    
    /**
     * 获取上传URL
     */
    protected function getUploadUrl()
    {
        return admin_url('banhua/order-batch-import/upload');
    }
    
    /**
     * 获取导入URL
     */
    protected function getImportUrl()
    {
        return admin_url('banhua/order-batch-import/import');
    }
    
    /**
     * 获取预览编辑URL
     */
    protected function getPreviewEditUrl()
    {
        return admin_url('banhua/order/preview-edit');
    }
    
    /**
     * 获取检查会话URL
     */
    protected function getCheckSessionUrl()
    {
        return admin_url('banhua/order-batch-import/check-session');
    }
    
    /**
     * 获取加载工作表数据URL
     */
    protected function getLoadSheetDataUrl()
    {
        return admin_url('banhua/order-batch-import/load-sheet-data');
    }
    
    /**
     * 获取最终导入URL
     */
    protected function getFinalImportUrl()
    {
        return admin_url('banhua/order/final-import');
    }
    
    /**
     * 获取清理数据URL
     */
    protected function getCleanupDataUrl()
    {
        return admin_url('banhua/order-batch-import/cleanup-import-data');
    }
    
    /**
     * 处理Excel上传
     */
    public function upload(Request $request)
    {
        try {
            // 验证请求
            $request->validate([
                'excel_file' => 'required|file|mimes:xlsx,xls|max:40960',
            ]);
            
            // 获取上传的文件
            $file = $request->file('excel_file');
            
            // 使用TempFileService保存文件
            $filePath = TempFileService::saveUploadedFile($file, 'order_import');
            
            // 使用PhpSpreadsheet读取Excel文件
            $reader = IOFactory::createReaderForFile($filePath);
            $reader->setReadDataOnly(true);
            
            // 获取所有工作表
            $spreadsheet = $reader->load($filePath);
            $worksheetNames = $spreadsheet->getSheetNames();
            
            // 准备工作表信息
            $sheets = [];
            $previewData = [];
            
            foreach ($worksheetNames as $index => $sheetName) {
                $worksheet = $spreadsheet->getSheet($index);
                $highestRow = $worksheet->getHighestRow();
                $highestColumn = $worksheet->getHighestColumn();
                
                // 只有当工作表有数据时才添加
                if ($highestRow > 1) {
                    $sheets[] = [
                        'name' => $sheetName,
                        'rows' => $highestRow,
                        'columns' => $highestColumn
                    ];
                    
                    // 获取表头（使用第一行作为表头）
                    $headers = [];
                    for ($col = 'A'; $col <= $highestColumn; $col++) {
                        $cellValue = $col; // 使用列标识作为表头
                        $headers[] = $cellValue;
                    }
                    
                    // 获取预览数据（最多10行）
                    $data = [];
                    $previewRows = min($highestRow, 10);
                    for ($row = 1; $row <= $previewRows; $row++) {
                        $rowData = [];
                        for ($col = 'A'; $col <= $highestColumn; $col++) {
                            $cellValue = $worksheet->getCell($col . $row)->getValue();
                            $rowData[] = $cellValue;
                        }
                        $data[] = $rowData;
                    }
                    
                    $previewData[$sheetName] = [
                        'headers' => $headers,
                        'data' => $data
                    ];
                }
            }
            
            // 如果没有有效的工作表，返回错误
            if (empty($sheets)) {
                return response()->json([
                    'status' => false,
                    'message' => '上传的Excel文件没有包含有效数据'
                ]);
            }
            
            // 获取默认仓库
            $warehouses = TecWarehouseModel::all();
            $defaultWarehouse = null;
            
            foreach ($warehouses as $warehouse) {
                if (strpos($warehouse->name, '主') !== false || strpos($warehouse->name, '中心') !== false) {
                    $defaultWarehouse = $warehouse->id;
                    break;
                }
            }
            if (!$defaultWarehouse && $warehouses->isNotEmpty()) {
                $defaultWarehouse = $warehouses->first()->id;
            }
            
            // 生成会话密钥
            $sessionKey = 'order_import_' . md5(uniqid('', true));
            
            // 将基本信息保存到数据库中
            DB::beginTransaction();
            try {
                // 创建导入批次
                $importBatch = BanhuaStockImportBatchModel::createBatch($sessionKey, 'order');
                
                // 添加工作表信息和文件路径
                $importBatch->update([
                    'sheet_info' => $sheets,
                    'file_path' => $filePath
                ]);
                
                DB::commit();
                
                // 返回预览数据
                return response()->json([
                    'status' => true,
                    'message' => '文件上传成功',
                    'data' => [
                        'session_key' => $sessionKey,
                        'sheets' => $sheets,
                        'previews' => $previewData,
                        'warehouses' => $warehouses->pluck('name', 'id')->toArray(),
                        'default_warehouse' => $defaultWarehouse
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('上传Excel文件时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '上传失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 显示预览和编辑页面
     */
    public function previewEdit(Content $content, Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $selectedSheets = $request->input('selected_sheets');
            
            if (is_string($selectedSheets)) {
                $selectedSheets = json_decode($selectedSheets, true);
            }
            
            // 获取导入批次数据
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            
            if (!$importBatch) {
                admin_error('错误', '找不到导入批次数据');
                return redirect(admin_url('banhua/order-batch-import'));
            }
            
            // 更新选中的工作表
            $importBatch->update([
                'selected_sheets' => $selectedSheets
            ]);
            
            // 检查文件是否存在
            $filePath = $importBatch->file_path;
            if (!file_exists($filePath)) {
                admin_error('错误', '导入文件不存在或已被删除');
                return redirect(admin_url('banhua/order-batch-import'));
            }
            
            // 读取Excel文件
            $reader = IOFactory::createReaderForFile($filePath);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($filePath);
            
            // 获取所有仓库
            $warehouses = TecWarehouseModel::orderBy('name')->get();
            
            // 准备视图数据
            $viewData = [
                'title' => '订单导入预览',
                'sessionKey' => $sessionKey,
                'importBatchId' => $importBatch->id,
                'selectedSheets' => $selectedSheets,
                'warehouses' => $warehouses,
                'defaultWarehouseId' => $warehouses->first()->id ?? null,
            ];
            
            // 构建页面布局
            return $content
                ->title('订单导入预览')
                ->description('确认导入数据并进行必要的编辑')
                ->body(view('admin.banhua.order-import-preview', $viewData));
            
        } catch (\Exception $e) {
            Log::error('显示预览编辑页面时出错: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
            admin_error('错误', '加载预览失败: ' . $e->getMessage());
            return redirect(admin_url('banhua/order-batch-import'));
        }
    }
    
    /**
     * 执行最终导入
     */
    public function finalImport(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $importBatchId = $request->input('import_batch_id');
            $selectedRows = json_decode($request->input('selected_rows', '{}'), true);
            $warehouseId = $request->input('warehouse_id');
            
            // 获取前端提交的完整表格数据
            $tableData = json_decode($request->input('table_data', '{}'), true);
            
            if (!$sessionKey || !$importBatchId) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的请求参数'
                ]);
            }

            // 获取导入批次
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            if (!$importBatch || $importBatch->id != $importBatchId) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到导入批次数据'
                ]);
            }
            
            // 检查文件是否存在
            $filePath = $importBatch->file_path;
            if (!empty($filePath) && !file_exists($filePath)) {
                Log::warning('导入文件不存在: ' . $filePath);
                // 继续执行，因为我们已经有了批次数据
            }
            
            // 获取批次数据
            $batchData = $importBatch->batchData()->get();
            if ($batchData->isEmpty()) {
                return response()->json([
                    'status' => false,
                    'message' => '没有找到导入数据，请重新上传'
                ]);
            }
            
            // 统计数据
            $stats = [
                'total' => 0,
                'success' => 0,
                'failed' => 0,
                'skipped' => 0,
                'errors' => []
            ];
            
            // 处理数据
            DB::beginTransaction();
            try {
                // 处理第一个工作表（SKU数据）
                $sheet1Data = $batchData->where('sheet_name', '2025-06-23')->first();
                $skuData = [];
                
                if ($sheet1Data) {
                    $rowData = $sheet1Data->row_data;
                    
                    // 提取B列和E列的JAN码（SKU）
                    foreach ($rowData as $row) {
                        if (isset($row[1]) && !empty($row[1])) { // B列
                            $skuData[] = $row[1];
                        }
                        if (isset($row[4]) && !empty($row[4])) { // E列
                            $skuData[] = $row[4];
                        }
                    }
                    
                    // 去重
                    $skuData = array_unique($skuData);
                }
                
                // 处理第二个工作表（订单数据）
                $sheet2Data = $batchData->where('sheet_name', '订单信息概览')->first();
                $orderData = [];
                
                if ($sheet2Data) {
                    $rowData = $sheet2Data->row_data;
                    
                    // 从第二行开始处理（跳过表头）
                    for ($i = 1; $i < count($rowData); $i++) {
                        $row = $rowData[$i];
                        
                        // 检查是否是选中的行
                        $sheetName = '订单信息概览';
                        if (isset($selectedRows[$sheetName]) && !in_array($i, $selectedRows[$sheetName])) {
                            $stats['skipped']++;
                            continue;
                        }
                        
                        // 检查必要字段是否存在
                        if (!isset($row[1]) || empty($row[1]) || // 订单日期
                            !isset($row[2]) || empty($row[2]) || // 单号
                            !isset($row[3]) || empty($row[3]) || // 买家名称
                            !isset($row[4]) || empty($row[4]) || // 数量
                            !isset($row[6]) || empty($row[6])) { // SKU
                            $stats['failed']++;
                            $stats['errors'][] = "第 " . ($i + 1) . " 行数据不完整";
                            continue;
                        }
                        
                        $orderData[] = [
                            'order_date' => $row[1], // B列：订单日期
                            'order_number' => $row[2], // C列：单号
                            'buyer_name' => $row[3], // D列：买家名称
                            'quantity' => $row[4], // E列：数量
                            'sku' => $row[6], // G列：SKU
                            'remark' => isset($row[7]) ? $row[7] : '', // H列：订单备注
                            'warehouse_id' => $warehouseId,
                            'created_at' => now(),
                            'updated_at' => now()
                        ];
                        
                        $stats['success']++;
                    }
                    
                    $stats['total'] = $stats['success'] + $stats['failed'] + $stats['skipped'];
                }
                
                // 在这里实现订单数据的实际导入逻辑
                // 例如：将订单数据保存到数据库中
                // 这部分需要根据实际的订单处理流程来实现
                
                // 示例：更新批次状态为已完成
                $importBatch->update([
                    'status' => 'completed',
                    'completed_at' => now()
                ]);
                
                DB::commit();
                
                // 返回处理结果
                return response()->json([
                    'status' => true,
                    'message' => sprintf(
                        '订单导入完成，总计：%d 条，成功：%d 条，失败：%d 条，跳过：%d 条',
                        $stats['total'],
                        $stats['success'],
                        $stats['failed'],
                        $stats['skipped']
                    ),
                    'data' => [
                        'stats' => $stats,
                        'sku_count' => count($skuData),
                        'order_count' => count($orderData),
                        'clear_preview' => true
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('导入订单数据时出错: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
            return response()->json([
                'status' => false,
                'message' => '导入失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清理导入数据
     */
    public function cleanupImportData(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            
            if (!$sessionKey) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的会话标识'
                ]);
            }
            
            // 删除导入批次及相关数据
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            
            if (!$importBatch) {
                return response()->json([
                    'status' => true,
                    'message' => '没有找到需要清理的数据'
                ]);
            }
            
            // 开始事务
            DB::beginTransaction();
            try {
                // 首先删除批次数据
                BanhuaStockImportBatchDataModel::where('batch_id', $importBatch->id)->delete();
                
                // 删除临时文件
                $filePath = $importBatch->file_path;
                if (!empty($filePath) && file_exists($filePath)) {
                    try {
                        unlink($filePath);
                    } catch (\Exception $e) {
                        Log::warning('删除临时文件失败: ' . $e->getMessage());
                        // 继续执行，不中断流程
                    }
                }
                
                // 删除批次记录
                $importBatch->delete();
                
                DB::commit();
                
                return response()->json([
                    'status' => true,
                    'message' => '临时数据已清理'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('清理临时数据失败: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
            return response()->json([
                'status' => false,
                'message' => '清理临时数据失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 加载工作表数据
     */
    public function loadSheetData(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $selectedSheets = json_decode($request->input('selected_sheets'), true);
            
            if (!$sessionKey || !$selectedSheets) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的请求参数'
                ]);
            }
            
            // 获取导入批次
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            if (!$importBatch) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到导入批次数据'
                ]);
            }
            
            // 检查文件是否存在
            $filePath = $importBatch->file_path;
            if (!file_exists($filePath)) {
                return response()->json([
                    'status' => false,
                    'message' => '导入文件不存在或已被删除，请重新上传'
                ]);
            }
            
            // 读取Excel文件
            $reader = IOFactory::createReaderForFile($filePath);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($filePath);
            
            $data = [];
            
            foreach ($selectedSheets as $sheetName) {
                $worksheet = $spreadsheet->getSheetByName($sheetName);
                if (!$worksheet) continue;
                
                $highestRow = $worksheet->getHighestRow();
                $highestColumn = $worksheet->getHighestColumn();
                
                // 获取表头
                $headers = [];
                for ($col = 'A'; $col <= $highestColumn; $col++) {
                    $headers[] = $col; // 使用列标识作为表头
                }
                
                // 获取所有行数据
                $rows = [];
                for ($row = 1; $row <= $highestRow; $row++) {
                    $rowData = [];
                    for ($col = 'A'; $col <= $highestColumn; $col++) {
                        $cellValue = $worksheet->getCell($col . $row)->getValue();
                        $rowData[] = $cellValue;
                    }
                    $rows[] = $rowData;
                }
                
                $data[$sheetName] = [
                    'headers' => $headers,
                    'rows' => $rows
                ];
                
                // 保存工作表数据到数据库
                $batchData = BanhuaStockImportBatchDataModel::where('batch_id', $importBatch->id)
                    ->where('sheet_name', $sheetName)
                    ->first();
                
                if (!$batchData) {
                    BanhuaStockImportBatchDataModel::saveSheetData(
                        $importBatch->id,
                        $sheetName,
                        $headers,
                        $rows,
                        [], // locationPairs
                        [], // possibleSizes
                        [] // headerMappings
                    );
                }
            }
            
            return response()->json([
                'status' => true,
                'message' => '加载数据成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('加载工作表数据时出错: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
            return response()->json([
                'status' => false,
                'message' => '加载数据失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 保存编辑数据
     */
    public function saveEdit(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $importBatchId = $request->input('import_batch_id');
            $modifiedCells = json_decode($request->input('modified_cells'), true);

            if (!$sessionKey || !$importBatchId || !$modifiedCells) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的请求参数'
                ]);
            }

            // 获取导入批次
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            if (!$importBatch || $importBatch->id != $importBatchId) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到导入批次数据'
                ]);
            }

            DB::beginTransaction();
            try {
                // 更新每个工作表的修改数据
                foreach ($modifiedCells as $sheetName => $rowUpdates) {
                    BanhuaStockImportBatchDataModel::updateCells(
                        $importBatchId,
                        $sheetName,
                        $rowUpdates
                    );
                }
                
                DB::commit();
                return response()->json([
                    'status' => true,
                    'message' => '保存成功'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('保存编辑数据时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '保存失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查会话
     */
    public function checkSession(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            
            if (!$sessionKey) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的会话标识'
                ]);
            }
            
            // 获取导入批次
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            
            if (!$importBatch) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到导入批次数据'
                ]);
            }
            
            // 检查文件是否存在
            if (!file_exists($importBatch->file_path)) {
                return response()->json([
                    'status' => false,
                    'message' => '导入文件不存在或已被删除'
                ]);
            }
            
            return response()->json([
                'status' => true,
                'message' => '会话有效',
                'data' => [
                    'batch_id' => $importBatch->id,
                    'sheet_info' => $importBatch->sheet_info,
                    'selected_sheets' => $importBatch->selected_sheets
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('检查会话时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '检查会话失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 预览导入数据
     */
    public function preview($sessionKey)
    {
        // 查找导入批次
        $importBatch = BanhuaStockImportBatchModel::where('session_key', $sessionKey)->first();
        
        if (!$importBatch) {
            admin_error('错误', '导入批次不存在');
            return redirect()->back();
        }
        
        // 检查文件是否存在
        if (!TempFileService::fileExists($importBatch->file_path)) {
            admin_error('错误', '导入文件不存在或已被删除');
            return redirect()->back();
        }
        
        // 从会话中获取处理后的订单数据
        $processedOrders = session("order_import_{$sessionKey}", []);
        
        // 返回预览视图
        return view('admin.banhua.order-import-preview', [
            'sessionKey' => $sessionKey,
            'importBatch' => $importBatch,
            'processedOrders' => $processedOrders
        ]);
    }

    /**
     * 处理导入数据
     */
    public function processData(Request $request)
    {
        try {
            // 验证请求
            $validated = $request->validate([
                'session_key' => 'required|string',
                'sheet_name_sku' => 'required|string',
                'sheet_name_order' => 'required|string',
            ]);
            
            $sessionKey = $validated['session_key'];
            $sheetNameSku = $validated['sheet_name_sku'];
            $sheetNameOrder = $validated['sheet_name_order'];
            
            // 设置默认值
            $warehouseId = $request->input('warehouse_id', null);
            $locationId = $request->input('location_id', null);
            
            // 查找导入批次
            $importBatch = BanhuaStockImportBatchModel::where('session_key', $sessionKey)->first();
            
            if (!$importBatch) {
                return response()->json([
                    'status' => false,
                    'message' => '导入批次不存在'
                ]);
            }
            
            // 检查文件是否存在
            if (!TempFileService::fileExists($importBatch->file_path)) {
                return response()->json([
                    'status' => false,
                    'message' => '导入文件不存在或已被删除'
                ]);
            }
            
            // 使用PhpSpreadsheet读取Excel文件
            $reader = IOFactory::createReaderForFile($importBatch->file_path);
            $reader->setReadDataOnly(true);
            
            // 加载工作簿
            $spreadsheet = $reader->load($importBatch->file_path);
            
            // 处理SKU工作表
            $skuWorksheet = $spreadsheet->getSheetByName($sheetNameSku);
            if (!$skuWorksheet) {
                return response()->json([
                    'status' => false,
                    'message' => 'SKU工作表不存在'
                ]);
            }
            
            // 处理订单工作表
            $orderWorksheet = $spreadsheet->getSheetByName($sheetNameOrder);
            if (!$orderWorksheet) {
                return response()->json([
                    'status' => false,
                    'message' => '订单工作表不存在'
                ]);
            }
            
            // 开始处理数据
            DB::beginTransaction();
            try {
                // 解析SKU工作表，提取JAN码
                $janCodes = [];
                $highestRow = $skuWorksheet->getHighestRow();
                
                // 从第2行开始（跳过表头）
                for ($row = 2; $row <= $highestRow; $row++) {
                    $janCode = $skuWorksheet->getCell('B' . $row)->getValue(); // JAN码在B列
                    $sku = $skuWorksheet->getCell('E' . $row)->getValue(); // SKU在E列
                    
                    if ($janCode && $sku) {
                        $janCodes[$sku] = $janCode;
                    }
                }
                
                // 解析订单工作表
                $orders = [];
                $highestRow = $orderWorksheet->getHighestRow();
                
                // 从第2行开始（跳过表头）
                for ($row = 2; $row <= $highestRow; $row++) {
                    $orderDate = $orderWorksheet->getCell('A' . $row)->getValue(); // 订单日期
                    $orderNumber = $orderWorksheet->getCell('B' . $row)->getValue(); // 订单号
                    $buyerName = $orderWorksheet->getCell('C' . $row)->getValue(); // 买家名称
                    $quantity = $orderWorksheet->getCell('D' . $row)->getValue(); // 数量
                    $sku = $orderWorksheet->getCell('E' . $row)->getValue(); // SKU
                    $remark = $orderWorksheet->getCell('F' . $row)->getValue(); // 备注
                    
                    if ($orderNumber && $sku && $quantity) {
                        $janCode = $janCodes[$sku] ?? null;
                        
                        $orders[] = [
                            'order_date' => $orderDate,
                            'order_number' => $orderNumber,
                            'buyer_name' => $buyerName,
                            'quantity' => $quantity,
                            'sku' => $sku,
                            'jan_code' => $janCode,
                            'remark' => $remark
                        ];
                    }
                }
                
                // 处理订单数据，但不保存到数据库
                $processedOrders = [];
                foreach ($orders as $orderData) {
                    // 查找SKU
                    $sku = TecBanhuaProductSkuModel::where('sku', $orderData['sku'])
                                         ->orWhere('jan', $orderData['jan_code'])
                                         ->first();
                    
                    if ($sku) {
                        // 准备订单数据
                        $orderItem = [
                            'sku_id' => $sku->id,
                            'sku_code' => $sku->sku,
                            'sku_name' => $sku->name,
                            'quantity' => $orderData['quantity'],
                            'order_date' => $orderData['order_date'],
                            'order_number' => $orderData['order_number'],
                            'buyer_name' => $orderData['buyer_name'],
                            'remark' => $orderData['remark']
                        ];
                        
                        // 仅当仓库ID和货位ID不为null时才添加
                        if ($warehouseId) {
                            $orderItem['warehouse_id'] = $warehouseId;
                        }
                        
                        if ($locationId) {
                            $orderItem['location_id'] = $locationId;
                        }
                        
                        $processedOrders[] = $orderItem;
                    }
                }
                
                // 将处理后的订单数据保存到会话中，而不是数据库
                session(["order_import_{$sessionKey}" => $processedOrders]);
                
                // 更新导入批次状态
                $importBatch->update([
                    'status' => 'processed',
                    'processed_at' => now()
                ]);
                
                DB::commit();
                
                // 生成预览页面的URL
                $previewUrl = admin_url('banhua/order-batch-import/preview/' . $sessionKey);
                
                return response()->json([
                    'status' => true,
                    'message' => '数据处理成功',
                    'data' => [
                        'total_orders' => count($orders),
                        'processed_orders' => count($processedOrders),
                        'preview_url' => $previewUrl
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('处理订单导入数据时出错: ' . $e->getMessage(), [
                    'session_key' => $sessionKey,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                return response()->json([
                    'status' => false,
                    'message' => '处理数据失败: ' . $e->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('处理订单导入数据请求时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '处理失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 执行导入操作
     */
    public function import(Request $request)
    {
        try {
            // 验证请求
            $validated = $request->validate([
                'session_key' => 'required|string',
            ]);
            
            $sessionKey = $validated['session_key'];
            
            // 查找导入批次
            $importBatch = BanhuaStockImportBatchModel::where('session_key', $sessionKey)
                                                      ->where('status', 'processed')
                                                      ->first();
            
            if (!$importBatch) {
                return response()->json([
                    'status' => false,
                    'message' => '导入批次不存在或未处理'
                ]);
            }
            
            // 从会话中获取处理后的订单数据
            $processedOrders = session("order_import_{$sessionKey}", []);
            
            if (empty($processedOrders)) {
                return response()->json([
                    'status' => false,
                    'message' => '没有待导入的数据'
                ]);
            }
            
            // 开始导入数据
            DB::beginTransaction();
            try {
                $successCount = 0;
                $failCount = 0;
                
                foreach ($processedOrders as $orderData) {
                    try {
                        // 创建导入批次数据记录
                        $batchData = [
                            'batch_id' => $importBatch->id,
                            'sheet_name' => 'orders', // 设置一个默认的工作表名
                            'sku_id' => $orderData['sku_id'],
                            'quantity' => $orderData['quantity'],
                            'status' => 'pending',
                            'extra_data' => [
                                'order_date' => $orderData['order_date'],
                                'order_number' => $orderData['order_number'],
                                'buyer_name' => $orderData['buyer_name'],
                                'remark' => $orderData['remark'] ?? ''
                            ]
                        ];
                        
                        // 仅当仓库ID和货位ID不为null时才添加
                        if (isset($orderData['warehouse_id'])) {
                            $batchData['warehouse_id'] = $orderData['warehouse_id'];
                        }
                        
                        if (isset($orderData['location_id'])) {
                            $batchData['location_id'] = $orderData['location_id'];
                        }
                        
                        $batchDataModel = BanhuaStockImportBatchDataModel::create($batchData);
                        
                        // 查找SKU库存
                        $query = BanhuaSkuStockModel::where('sku_id', $orderData['sku_id']);
                        
                        // 仅当仓库ID和货位ID不为null时才添加到查询条件
                        if (isset($orderData['warehouse_id'])) {
                            $query->where('warehouse_id', $orderData['warehouse_id']);
                        }
                        
                        if (isset($orderData['location_id'])) {
                            $query->where('location_id', $orderData['location_id']);
                        }
                        
                        $skuStock = $query->first();
                        
                        if ($skuStock) {
                            // 更新库存
                            $skuStock->update([
                                'quantity' => $skuStock->quantity - $orderData['quantity'],
                                'updated_at' => now()
                            ]);
                        } else {
                            // 创建库存记录
                            $stockData = [
                                'sku_id' => $orderData['sku_id'],
                                'quantity' => -$orderData['quantity'],
                                'created_at' => now(),
                                'updated_at' => now()
                            ];
                            
                            // 仅当仓库ID和货位ID不为null时才添加
                            if (isset($orderData['warehouse_id'])) {
                                $stockData['warehouse_id'] = $orderData['warehouse_id'];
                            }
                            
                            if (isset($orderData['location_id'])) {
                                $stockData['location_id'] = $orderData['location_id'];
                            }
                            
                            BanhuaSkuStockModel::create($stockData);
                        }
                        
                        // 更新批次数据状态
                        $batchDataModel->update([
                            'status' => 'completed',
                            'completed_at' => now()
                        ]);
                        
                        $successCount++;
                    } catch (\Exception $e) {
                        // 记录失败
                        Log::error('处理订单项时出错: ' . $e->getMessage(), [
                            'order_data' => $orderData,
                            'error' => $e->getMessage()
                        ]);
                        
                        $failCount++;
                    }
                }
                
                // 更新导入批次状态
                $importBatch->update([
                    'status' => 'completed',
                    'completed_at' => now(),
                    'success_count' => $successCount,
                    'fail_count' => $failCount
                ]);
                
                // 删除临时文件
                if (TempFileService::fileExists($importBatch->file_path)) {
                    TempFileService::deleteFile($importBatch->file_path);
                }
                
                // 清除会话数据
                session()->forget("order_import_{$sessionKey}");
                
                DB::commit();
                
                return response()->json([
                    'status' => true,
                    'message' => "导入完成，成功：{$successCount}，失败：{$failCount}",
                    'data' => [
                        'success_count' => $successCount,
                        'fail_count' => $failCount
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('执行订单导入操作时出错: ' . $e->getMessage(), [
                    'session_key' => $sessionKey,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                return response()->json([
                    'status' => false,
                    'message' => '导入失败: ' . $e->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('执行订单导入请求时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '导入失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取导入批次的订单数据
     */
    public function getItems(Request $request)
    {
        try {
            $sessionKey = $request->get('session_key');
            if (!$sessionKey) {
                return response()->json([
                    'status' => false,
                    'message' => '会话密钥不能为空'
                ]);
            }
            
            // 从会话中获取处理后的订单数据
            $processedOrders = session("order_import_{$sessionKey}", []);
            
            // 为每个订单项添加一个唯一ID
            foreach ($processedOrders as $index => &$item) {
                $item['id'] = $index + 1; // 从1开始的ID
            }
            
            return response()->json([
                'status' => true,
                'message' => '获取数据成功',
                'data' => $processedOrders
            ]);
        } catch (\Exception $e) {
            Log::error('获取订单数据时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '获取数据失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 更新订单数据
     */
    public function updateItem(Request $request)
    {
        try {
            // 验证请求
            $validated = $request->validate([
                'session_key' => 'required|string',
                'item_id' => 'required|integer',
                'quantity' => 'required|numeric|min:1',
                'order_date' => 'nullable|string',
                'order_number' => 'nullable|string',
                'buyer_name' => 'nullable|string',
                'sku_code' => 'nullable|string',
                'remark' => 'nullable|string'
            ]);
            
            // 获取会话数据
            $sessionKey = $validated['session_key'];
            $processedOrders = session("order_import_{$sessionKey}", []);
            
            // 查找要更新的项目
            $itemId = $validated['item_id'];
            $itemIndex = $itemId - 1; // 因为ID是从1开始的
            
            if (!isset($processedOrders[$itemIndex])) {
                return response()->json([
                    'status' => false,
                    'message' => '订单数据不存在'
                ]);
            }
            
            // 更新数据
            if (isset($validated['quantity'])) {
                $processedOrders[$itemIndex]['quantity'] = $validated['quantity'];
            }
            
            if (isset($validated['order_date'])) {
                $processedOrders[$itemIndex]['order_date'] = $validated['order_date'];
            }
            
            if (isset($validated['order_number'])) {
                $processedOrders[$itemIndex]['order_number'] = $validated['order_number'];
            }
            
            if (isset($validated['buyer_name'])) {
                $processedOrders[$itemIndex]['buyer_name'] = $validated['buyer_name'];
            }
            
            if (isset($validated['remark'])) {
                $processedOrders[$itemIndex]['remark'] = $validated['remark'];
            }
            
            // 如果SKU变更，需要重新查找SKU信息
            if (isset($validated['sku_code']) && !empty($validated['sku_code'])) {
                $sku = TecBanhuaProductSkuModel::where('sku', $validated['sku_code'])->first();
                
                if ($sku) {
                    $processedOrders[$itemIndex]['sku_id'] = $sku->id;
                    $processedOrders[$itemIndex]['sku_code'] = $sku->sku;
                    $processedOrders[$itemIndex]['sku_name'] = $sku->name;
                }
            }
            
            // 保存回会话
            session(["order_import_{$sessionKey}" => $processedOrders]);
            
            return response()->json([
                'status' => true,
                'message' => '更新成功'
            ]);
        } catch (\Exception $e) {
            Log::error('更新订单数据时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '更新失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 删除订单数据
     */
    public function deleteItems(Request $request)
    {
        try {
            // 验证请求
            $validated = $request->validate([
                'session_key' => 'required|string',
                'item_ids' => 'required|array',
                'item_ids.*' => 'integer'
            ]);
            
            // 获取会话数据
            $sessionKey = $validated['session_key'];
            $processedOrders = session("order_import_{$sessionKey}", []);
            
            // 将ID转换为索引（ID是从1开始的，索引是从0开始的）
            $indexesToDelete = array_map(function($id) {
                return $id - 1;
            }, $validated['item_ids']);
            
            // 删除指定的项目
            $newOrders = [];
            foreach ($processedOrders as $index => $order) {
                if (!in_array($index, $indexesToDelete)) {
                    $newOrders[] = $order;
                }
            }
            
            // 保存回会话
            session(["order_import_{$sessionKey}" => $newOrders]);
            
            $deletedCount = count($processedOrders) - count($newOrders);
            
            return response()->json([
                'status' => true,
                'message' => "成功删除 {$deletedCount} 条数据"
            ]);
        } catch (\Exception $e) {
            Log::error('删除订单数据时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '删除失败: ' . $e->getMessage()
            ]);
        }
    }
} 