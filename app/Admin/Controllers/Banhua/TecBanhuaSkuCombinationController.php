<?php

namespace App\Admin\Controllers\Banhua;

use App\Admin\Repositories\Banhua\TecBanhuaSkuCombination;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\Banhua\TecBanhuaSkuCombinationItemModel;
use App\Models\Banhua\TecBanhuaSkuCombinationModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Widgets\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TecBanhuaSkuCombinationController extends AdminController
{
    /**
     * 初始化操作
     */
    protected function init()
    {
        // 注册脚本
        Admin::script($this->script());
    }
    
    /**
     * 初始化脚本
     */
    protected function script()
    {
        // 使用PHP端生成正确的URL
        $skuInfoUrl = admin_url('banhua/sku-combinations/get-sku-info');
        $suggestionsUrl = admin_url('banhua/sku-combinations/suggestions');
        
        // 获取所有SKU选项
        $skuOptions = TecBanhuaProductSkuModel::get(['id', 'name', 'sku', 'jan'])
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'text' => "{$item->name}({$item->sku})",
                    'sku' => $item->sku,
                    'jan' => $item->jan
                ];
            })->toJson();
        
        return <<<JS
        $(function () {
            console.log('初始化SKU组合JS...');
            
            // 添加CSRF令牌到所有AJAX请求
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            
            const compositeSkuSelect = $('select[name="composite_sku_id"]');
            const nameInput = $('input[name="name"]');
            const skuSelect = $('#sku-select');
            const skuQuantity = $('#sku-quantity');
            const addSkuButton = $('#add-sku-item');
            const skuItemsList = $('#sku-items-list');
            const itemsJsonInput = $('input[name="items_data[items_json]"]');
            
            // 初始化SKU下拉框
            const skuOptions = {$skuOptions};
            
            // 初始化Select2
            skuSelect.select2({
                data: skuOptions,
                placeholder: '选择SKU',
                allowClear: true
            });
            
            // 存储所有已添加的组合项
            let skuItems = [];
            
            // 更新组合项JSON
            function updateItemsJson() {
                itemsJsonInput.val(JSON.stringify(skuItems));
            }
            
            // 渲染组合项列表
            function renderSkuItems() {
                skuItemsList.empty();
                
                skuItems.forEach(function(item, index) {
                    const skuOption = skuOptions.find(option => option.id == item.sku_id);
                    const row = $('<tr></tr>');
                    
                    row.append($('<td></td>').text(skuOption ? skuOption.sku : ''));
                    
                    // 创建可编辑的数量输入框
                    const quantityCell = $('<td></td>');
                    const quantityInput = $('<input type="number" class="form-control form-control-sm" min="1" value="' + item.quantity + '">');
                    
                    // 监听数量变化
                    quantityInput.on('change', function() {
                        const newQuantity = parseInt($(this).val()) || 1;
                        if (newQuantity < 1) {
                            $(this).val(1);
                            skuItems[index].quantity = 1;
                        } else {
                            skuItems[index].quantity = newQuantity;
                        }
                        updateItemsJson();
                    });
                    
                    quantityCell.append(quantityInput);
                    row.append(quantityCell);
                    
                    const deleteButton = $('<button type="button" class="btn btn-sm btn-danger"><i class="fa fa-trash"></i></button>');
                    deleteButton.on('click', function() {
                        skuItems.splice(index, 1);
                        renderSkuItems();
                        updateItemsJson();
                    });
                    
                    row.append($('<td></td>').append(deleteButton));
                    skuItemsList.append(row);
                });
            }
            
            // 添加SKU组合项
            addSkuButton.on('click', function() {
                const skuId = skuSelect.val();
                const quantity = parseInt(skuQuantity.val()) || 1;
                
                if (!skuId) {
                    Dcat.warning('请选择SKU');
                    return;
                }
                
                if (quantity < 1) {
                    Dcat.warning('数量必须大于0');
                    return;
                }
                
                // 检查是否已存在相同SKU
                const existingIndex = skuItems.findIndex(item => item.sku_id == skuId);
                if (existingIndex >= 0) {
                    // 更新数量
                    skuItems[existingIndex].quantity += quantity;
                } else {
                    // 添加新项
                    skuItems.push({
                        sku_id: skuId,
                        quantity: quantity
                    });
                }
                
                // 更新视图
                renderSkuItems();
                updateItemsJson();
                
                // 重置输入
                skuSelect.val(null).trigger('change');
                skuQuantity.val(1);
                
                Dcat.success('已添加组合项');
            });
            
            // 监听组合SKU选择变化
            $(document).off('change', 'select[name="composite_sku_id"]');
            
            // 绑定一次性事件处理程序
            $(document).on('change', 'select[name="composite_sku_id"]', function () {
                const skuId = $(this).val();
                if (!skuId) return;
                
                // 获取SKU信息并自动设置名称
                $.ajax({
                    url: '{$skuInfoUrl}',
                    type: 'GET',
                    data: { sku_id: skuId },
                    success: function (response) {
                        if (response.status && response.data) {
                            // 设置组合名称
                            const composName = response.data.sku + '-组合';
                            nameInput.val(composName);
                            
                            // 清空现有组合项
                            skuItems = [];
                            renderSkuItems();
                            updateItemsJson();
                            
                            // 请求获取建议
                            $.ajax({
                                url: '{$suggestionsUrl}',
                                type: 'GET',
                                data: { sku_id: skuId },
                                success: function (response) {
                                    if (response.status && response.data && response.data.length > 0) {
                                        // 添加建议项（去除重复项）
                                        const uniqueSkus = new Set();
                                        response.data.forEach(function(item) {
                                            if (!uniqueSkus.has(item.sku)) {
                                                uniqueSkus.add(item.sku);
                                                skuItems.push({
                                                    sku_id: item.id,
                                                    quantity: 1
                                                });
                                            }
                                        });
                                        
                                        renderSkuItems();
                                        updateItemsJson();
                                        
                                        Dcat.success('已自动添加 ' + response.data.length + ' 个推荐的组合项');
                                    } else {
                                        Dcat.warning('未找到匹配的组合项，请手动选择');
                                    }
                                },
                                error: function() {
                                    Dcat.error('获取推荐组合项失败，请手动选择');
                                }
                            });
                        }
                    },
                    error: function() {
                        Dcat.error('获取SKU信息失败');
                    }
                });
            });
            
            // 加载保存的组合项（编辑模式）
            if ($('form.edit-form').length > 0) {
                // 从后端获取已保存的组合项
                const id = $('input[name="_key"]').val();
                if (id) {
                    $.ajax({
                        url: '{$suggestionsUrl}',
                        type: 'GET',
                        data: { combination_id: id },
                        success: function(response) {
                            if (response.status && response.data) {
                                response.data.forEach(function(item) {
                                    skuItems.push({
                                        sku_id: item.id,
                                        quantity: item.quantity || 1
                                    });
                                });
                                
                                renderSkuItems();
                                updateItemsJson();
                            }
                        }
                    });
                }
            }
        });
        JS;
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        // 初始化
        $this->init();
        
        return Grid::make(new TecBanhuaSkuCombination(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name', '组合名称');
            $grid->column('compositeSku.sku', 'SKU编码')->display(function($value) {
                return $value ?: ($this->compositeSku ? $this->compositeSku->sku : '');
            });
            $grid->column('split_priority', '出库优先级')->display(function ($value) {
                if ($value == TecBanhuaSkuCombinationModel::SPLIT_PRIORITY_COMPOSITE) {
                    return '<span class="badge badge-success">组合出库优先</span>';
                } else {
                    return '<span class="badge badge-info">拆分出库优先</span>';
                }
            });
            
            // 直接显示组合项SKU（最多3个）
            $grid->column('组合项SKU')->display(function () {
                $items = TecBanhuaSkuCombinationItemModel::with('sku')
                    ->where('combination_id', $this->id)
                    ->get();
                
                if ($items->isEmpty()) {
                    return '-';
                }
                
                $skuList = $items->map(function ($item) {
                    return $item->sku->sku . ' <span class="text-muted">(' . $item->quantity . ')</span>';
                })->implode('<br>');
                
                return $skuList;
            });
            
            $grid->column('description', '描述');

            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(true); // 禁用编辑按钮
                $actions->disableQuickEdit(true); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
            });
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '组合名称')->width(2);
                $filter->like('compositeSku.sku', 'SKU编码')->width(2);
                $filter->equal('split_priority', '出库优先级')->select([
                    TecBanhuaSkuCombinationModel::SPLIT_PRIORITY_COMPOSITE => '组合出库优先',
                    TecBanhuaSkuCombinationModel::SPLIT_PRIORITY_SEPARATE => '拆分出库优先'
                ])->width(2);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        // 初始化
        $this->init();
        
        return Show::make($id, new TecBanhuaSkuCombination(), function (Show $show) {
            $show->field('id');
            $show->field('name', '组合名称');
            $show->field('compositeSku.name', '组合SKU名称');
            $show->field('compositeSku.sku', 'SKU编码');
            $show->field('compositeSku.jan', 'JAN编码');
            $show->field('split_priority', '出库优先级')->as(function ($value) {
                if ($value == TecBanhuaSkuCombinationModel::SPLIT_PRIORITY_COMPOSITE) {
                    return '组合出库优先';
                } else {
                    return '拆分出库优先';
                }
            });
            $show->field('description', '描述');
            
            $show->divider('组合项');
            
            // 查询组合项
            $items = TecBanhuaSkuCombinationItemModel::with('sku')
                ->where('combination_id', $this->id)
                ->get();
            
            $show->html(function () use ($items) {
                $headers = ['SKU ID', 'SKU名称', 'SKU编码', 'JAN编码', '数量'];
                
                $rows = $items->map(function ($item) {
                    return [
                        $item->sku_id,
                        $item->sku->name,
                        $item->sku->sku,
                        $item->sku->jan,
                        $item->quantity,
                    ];
                })->toArray();
                
                return Table::make($headers, $rows);
            });
            
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        // 初始化
        $this->init();
        
        return Form::make(new TecBanhuaSkuCombination(), function (Form $form) {
            
            // 选择组合SKU，智能过滤真正的组合SKU
            $form->select('composite_sku_id', '组合SKU')
                ->options(function () {
                    // 获取已经创建过组合的SKU ID列表（编辑模式下需要排除当前记录）
                    $existingCompositeSKUs = TecBanhuaSkuCombinationModel::pluck('composite_sku_id')->toArray();
                    
                    // 在编辑模式下，需要保留当前选中的SKU
                    $currentId = request()->route('sku_combination');
                    if ($currentId) {
                        $currentComboSku = TecBanhuaSkuCombinationModel::find($currentId);
                        if ($currentComboSku) {
                            $key = array_search($currentComboSku->composite_sku_id, $existingCompositeSKUs);
                            if ($key !== false) {
                                unset($existingCompositeSKUs[$key]);
                            }
                        }
                    }
                    
                    // 获取所有包含连字符的SKU
                    $potentialSKUs = TecBanhuaProductSkuModel::where('name', 'like', '%-%')
                        ->whereNotIn('id', $existingCompositeSKUs)
                        ->orderBy('pid') // 首先按pid排序
                        ->orderBy('name') // 然后按名称排序
                        ->get(['id', 'name', 'sku', 'jan', 'pid']);
                    
                    // 对SKU进行去重，保留每个SKU编码的第一个
                    $uniqueSKUs = collect();
                    $seenSkus = [];
                    
                    foreach ($potentialSKUs as $sku) {
                        // 如果这个SKU编码已经出现过，跳过
                        if (isset($seenSkus[$sku->sku])) {
                            continue;
                        }
                        
                        // 记录这个SKU编码已经出现过
                        $seenSkus[$sku->sku] = true;
                        // 添加到结果集合
                        $uniqueSKUs->push($sku);
                    }
                    
                    // 过滤掉简单的后缀形式（如-1、-2、-RGB等），但保留复杂组合
                    return $uniqueSKUs->filter(function ($item) {
                            // 分割名称检查是否是真正的组合
                            $parts = explode('-', $item->name);
                            
                            // 如果只有两部分，检查第二部分是否是简单后缀
                            if (count($parts) == 2) {
                                $suffix = trim($parts[1]);
                                
                                // 检查后缀是否为纯数字，且长度小于等于2（排除-1, -2, -3等）
                                // 但保留长数字（如388821）可能是有意义的编号
                                if (is_numeric($suffix) && strlen($suffix) <= 2) {
                                    return false;
                                }
                                
                                // 排除常见简单后缀
                                $simpleSuffixes = ['RGB', 'LED', 'SET', 'L', 'M', 'S', 'XL', 'XXL'];
                                if (in_array($suffix, $simpleSuffixes)) {
                                    return false;
                                }
                                
                                // 检查是否是单字符或纯数字+单字符
                                if (preg_match('/^\d{1,2}[A-Z]$/', $suffix)) {
                                    return false;
                                }
                            }
                            
                            // 特殊处理：检查SKU编码是否包含两个数字序列，比如388820-388821
                            if (preg_match('/\d{5,6}-\d{5,6}/', $item->sku)) {
                                return true;
                            }
                            
                            // 通过所有检查，认为是真正的组合SKU
                            return true;
                        })
                        ->mapWithKeys(function ($item) {
                            // 为选项添加pid信息，帮助用户理解分组，但采用更简洁的格式
                            return [$item->id => "{$item->name} ({$item->sku})"];
                        });
                })
                ->required()
                ->help('选择组合SKU，系统已智能过滤出真正的组合产品')
                ->when('creating', function (Form $form) {
                    // 当创建表单时自动生成名称
                    return $form;
                });
                
            $form->text('name', '组合名称')
                ->required()
                ->help('组合名称，默认使用SKU编码加-组合后缀，可自定义');
                
            $form->radio('split_priority', '出库优先级')
                ->options([
                    TecBanhuaSkuCombinationModel::SPLIT_PRIORITY_COMPOSITE => '组合出库优先',
                    TecBanhuaSkuCombinationModel::SPLIT_PRIORITY_SEPARATE => '拆分出库优先'
                ])
                ->default(TecBanhuaSkuCombinationModel::SPLIT_PRIORITY_COMPOSITE)
                ->help('选择出库时的优先级：组合出库优先表示优先作为整体出库，拆分出库优先表示优先拆分为子SKU出库');
                
            $form->textarea('description', '描述');
            
            // 使用表格方式处理组合项（改用embeds处理多对多关系）
            $form->embeds('items_data', '组合项', function (Form\EmbeddedForm $form) {
                $form->hidden('items_json')->default('[]');
            });
            
            // 使用HTML生成表格，让用户手动管理组合项
            $form->html(<<<HTML
            <div class="card-header">组合项管理</div>
            <div class="table-responsive" style="margin-top: 10px;">
                <table class="table table-hover" id="sku-items-table">
                    <thead>
                        <tr>
                            <th>SKU编码</th>
                            <th>数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="sku-items-list">
                    </tbody>
                </table>
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-6">
                            <select class="form-control" id="sku-select"></select>
                        </div>
                        <div class="col-md-2">
                            <input type="number" class="form-control" id="sku-quantity" value="1" min="1">
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-success" id="add-sku-item">添加组合项</button>
                        </div>
                    </div>
                </div>
            </div>
            HTML);
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            
            // 处理自动生成名称和组合项
            $form->saving(function (Form $form) {
                $compositeSkuId = $form->composite_sku_id;
                $compositeSku = TecBanhuaProductSkuModel::find($compositeSkuId);
                
                if (!$compositeSku) {
                    return $form->error('组合SKU不存在');
                }
                
                // 处理JSON格式的组合项数据
                if (isset($form->items_data['items_json']) && $form->items_data['items_json']) {
                    try {
                        // 添加日志记录原始数据
                        \Illuminate\Support\Facades\Log::info('组合项原始数据', [
                            'items_json' => $form->items_data['items_json']
                        ]);
                        
                        $items = json_decode($form->items_data['items_json'], true);
                        
                        // 添加日志记录解析后的数据
                        \Illuminate\Support\Facades\Log::info('组合项解析数据', [
                            'items' => $items
                        ]);
                        
                        if (is_array($items) && !empty($items)) {
                            // 确保每个项目都有正确的格式
                            $formattedItems = [];
                            foreach ($items as $item) {
                                if (isset($item['sku_id']) && isset($item['quantity'])) {
                                    $formattedItems[] = [
                                        'sku_id' => (int)$item['sku_id'],
                                        'quantity' => (int)$item['quantity']
                                    ];
                                }
                            }
                            
                            // 添加日志记录格式化后的数据
                            \Illuminate\Support\Facades\Log::info('组合项格式化数据', [
                                'formattedItems' => $formattedItems
                            ]);
                            
                            // 清除不需要的字段
                            unset($form->items_data);
                            
                            // 设置组合项数据
                            $form->items = $formattedItems;
                        } else {
                            // 如果组合项为空，返回错误
                            return $form->error('组合项不能为空，请至少添加一个组合项');
                        }
                    } catch (\Exception $e) {
                        // 记录异常
                        \Illuminate\Support\Facades\Log::error('组合项处理异常', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        return $form->error('组合项数据无效: ' . $e->getMessage());
                    }
                } else {
                    // 如果没有组合项数据，返回错误
                    return $form->error('组合项不能为空，请至少添加一个组合项');
                }
            });

            // 使用正确的方法处理保存错误
            $form->saved(function (Form $form, $result) {
                // 保存成功后的处理
                \Illuminate\Support\Facades\Log::info('SKU组合保存成功', [
                    'id' => $result,
                    'name' => $form->name
                ]);
            });
            
            // 使用表单验证器来处理唯一性约束
            $form->submitted(function (Form $form) {
                // 检查名称是否已存在
                $name = $form->name;
                $id = $form->model()->id;
                
                $exists = TecBanhuaSkuCombinationModel::where('name', $name)
                    ->when($id, function ($query) use ($id) {
                        return $query->where('id', '!=', $id);
                    })
                    ->exists();
                
                if ($exists) {
                    // 如果名称已存在，添加随机后缀
                    $randomSuffix = substr(md5(microtime()), 0, 5);
                    $newName = $name . '-' . $randomSuffix;
                    
                    // 记录修改日志
                    \Illuminate\Support\Facades\Log::info('自动修改组合名称以避免重复', [
                        'original_name' => $name,
                        'new_name' => $newName
                    ]);
                    
                    // 更新表单数据
                    $form->name = $newName;
                }
            });
        });
    }
    
    /**
     * 获取SKU信息
     */
    public function getSkuInfo(Request $request)
    {
        $skuId = $request->input('sku_id');
        $sku = TecBanhuaProductSkuModel::find($skuId);
        
        if (!$sku) {
            return response()->json(['status' => false, 'message' => '未找到SKU']);
        }
        
        return response()->json([
            'status' => true,
            'data' => [
                'id' => $sku->id,
                'name' => $sku->name,
                'sku' => $sku->sku,
                'jan' => $sku->jan
            ]
        ]);
    }
    
    /**
     * 自动生成组合建议
     */
    public function generateSuggestions(Request $request)
    {
        // 如果是编辑模式，获取现有组合项
        if ($request->has('combination_id')) {
            $combinationId = $request->input('combination_id');
            $items = TecBanhuaSkuCombinationItemModel::with('sku')
                ->where('combination_id', $combinationId)
                ->get();
                
            $suggestions = $items->map(function ($item) {
                return [
                    'id' => $item->sku_id,
                    'name' => $item->sku->name,
                    'sku' => $item->sku->sku,
                    'jan' => $item->sku->jan,
                    'quantity' => $item->quantity
                ];
            })->toArray();
            
            return response()->json([
                'status' => true, 
                'data' => $suggestions
            ]);
        }
        
        // 新建模式，根据选择的SKU生成建议
        $skuId = $request->input('sku_id');
        $sku = TecBanhuaProductSkuModel::find($skuId);
        
        if (!$sku) {
            return response()->json(['status' => false, 'message' => '未找到SKU']);
        }
        
        $skuName = $sku->name;
        $suggestions = [];
        
        // 检查SKU名称是否包含"-"，如果是，则尝试拆分并查找对应的SKU
        if (strpos($skuName, '-') !== false) {
            $parts = explode('-', $skuName);
            
            // 获取组合SKU的属性后缀
            $skuSuffix = '';
            if (preg_match('/PSWOOD[0-9]+$/', $sku->sku, $matches)) {
                $skuSuffix = $matches[0];
            }
            
            foreach ($parts as $part) {
                $baseName = trim($part);
                $possibleSkuPrefix = 'bh' . $baseName;
                
                // 查找完全匹配的SKU（带属性后缀）
                $query = TecBanhuaProductSkuModel::where('name', $baseName);
                
                if (!empty($skuSuffix)) {
                    $query->where('sku', 'like', $possibleSkuPrefix . $skuSuffix);
                }
                
                $partSkus = $query->get();
                
                // 如果找不到精确匹配，尝试只匹配名称前缀
                if ($partSkus->isEmpty()) {
                    $partSkus = TecBanhuaProductSkuModel::where('name', $baseName)
                                                        ->where('sku', 'like', $possibleSkuPrefix . '%')
                                                        ->get();
                }
                
                // 最后尝试只匹配名称
                if ($partSkus->isEmpty()) {
                    $partSkus = TecBanhuaProductSkuModel::where('name', $baseName)->get();
                }
                
                if ($partSkus->isNotEmpty()) {
                    foreach ($partSkus as $partSku) {
                        // 检查是否已存在相同SKU编码的建议
                        $existingSku = collect($suggestions)->first(function ($item) use ($partSku) {
                            return $item['sku'] === $partSku->sku;
                        });
                        
                        // 只有在不存在重复时才添加
                        if (!$existingSku) {
                            $suggestions[] = [
                                'id' => $partSku->id,
                                'name' => $partSku->name,
                                'sku' => $partSku->sku,
                                'jan' => $partSku->jan
                            ];
                        }
                    }
                }
            }
        }
        
        return response()->json([
            'status' => true, 
            'data' => $suggestions
        ]);
    }
} 