<?php

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class GalleryThumbController extends AdminController
{
    /**
     * 获取图库缩略图
     * 
     * @param string $id 图库ID
     * @return \Illuminate\Http\Response
     */
    public function getThumb($id)
    {
        try {
            // 记录请求信息
            \Illuminate\Support\Facades\Log::info('获取图库缩略图请求', [
                'id' => $id
            ]);
            
            // 1. 首先尝试从图库表获取
            $gallery = DB::table('t_banhua_image_gallery')
                ->where('id', $id)
                ->orWhere('image_id', $id)
                ->first();
            
            if ($gallery && !empty($gallery->path)) {
                \Illuminate\Support\Facades\Log::info('从图库表找到图片', [
                    'id' => $id,
                    'path' => $gallery->path
                ]);
                
                // 如果是URL直接重定向
                if (filter_var($gallery->path, FILTER_VALIDATE_URL)) {
                    return redirect($gallery->path);
                }
                
                // 否则从存储中获取
                if (Storage::exists($gallery->path)) {
                    return response(Storage::get($gallery->path))
                        ->header('Content-Type', Storage::mimeType($gallery->path));
                }
            }
            
            // 2. 然后尝试从SKU表获取
            $sku = DB::table('t_banhua_product_skus')
                ->where('id', $id)
                ->orWhere('pid', $id)
                ->first();
            
            if ($sku) {
                // 如果SKU表有图片字段
                if (!empty($sku->image)) {
                    \Illuminate\Support\Facades\Log::info('从SKU表找到图片', [
                        'id' => $id,
                        'path' => $sku->image
                    ]);
                    
                    // 如果是URL直接重定向
                    if (filter_var($sku->image, FILTER_VALIDATE_URL)) {
                        return redirect($sku->image);
                    }
                    
                    // 否则从存储中获取
                    if (Storage::exists($sku->image)) {
                        return response(Storage::get($sku->image))
                            ->header('Content-Type', Storage::mimeType($sku->image));
                    }
                }
                
                // 如果SKU有pid，尝试通过pid查找图库
                if (!empty($sku->pid)) {
                    $gallery = DB::table('t_banhua_image_gallery')
                        ->where('id', $sku->pid)
                        ->orWhere('image_id', $sku->pid)
                        ->first();
                    
                    if ($gallery && !empty($gallery->path)) {
                        \Illuminate\Support\Facades\Log::info('通过SKU的pid找到图片', [
                            'id' => $id,
                            'pid' => $sku->pid,
                            'path' => $gallery->path
                        ]);
                        
                        // 如果是URL直接重定向
                        if (filter_var($gallery->path, FILTER_VALIDATE_URL)) {
                            return redirect($gallery->path);
                        }
                        
                        // 否则从存储中获取
                        if (Storage::exists($gallery->path)) {
                            return response(Storage::get($gallery->path))
                                ->header('Content-Type', Storage::mimeType($gallery->path));
                        }
                    }
                }
            }
            
            // 如果没有找到图片或图片不存在，返回默认图片
            \Illuminate\Support\Facades\Log::info('未找到图片，返回默认图片', ['id' => $id]);
            $defaultImagePath = public_path('vendor/dcat-admin/images/no-image.png');
            
            if (file_exists($defaultImagePath)) {
                return response(file_get_contents($defaultImagePath))
                    ->header('Content-Type', 'image/png');
            }
            
            // 如果默认图片也不存在，返回404
            return response()->json(['error' => 'Image not found'], 404);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取图库缩略图失败: ' . $e->getMessage(), [
                'id' => $id,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to get thumbnail: ' . $e->getMessage()], 500);
        }
    }
} 