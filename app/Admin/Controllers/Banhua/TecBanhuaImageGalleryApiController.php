<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use App\Http\Controllers\Controller;
use App\Models\Banhua\TecBanhuaImageGalleryModel;
use Illuminate\Http\Request;

class TecBanhuaImageGalleryApiController extends Controller
{
    public function index(Request $request)
    {
        $q = $request->get('q');
        
        return TecBanhuaImageGalleryModel::when($q, function ($query, $q) {
                return $query->where('image_id', 'like', "%{$q}%")
                    ->orWhere('name', 'like', "%{$q}%");
            })
            ->paginate(null, ['image_id as id', 'name as text']);
    }
} 