<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Banhua\TecBanhuaConsumableCombinationModel;
use App\Models\Banhua\TecBanhuaConsumableStockModel;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Admin;
use App\Models\Banhua\TecBanhuaAttrModel;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use App\Models\Banhua\TecBanhuaImageGalleryModel;

/**
 * 消耗品组合管理控制器
 */
class TecBanhuaConsumableCombinationController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '消耗品组合管理';

    /**
     * 列表页面
     */
    public function index(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('消耗品组合列表')
            ->body($this->grid());
    }

    /**
     * 构建列表
     */
    protected function grid(): Grid
    {
        return Grid::make(new TecBanhuaConsumableCombinationModel(), function (Grid $grid) {
            // 设置分页选项
            $grid->paginate(20);
            $grid->perPages([20, 50, 100]);
            
            // 启用横向滚动条
            $grid->scrollbarX();
            
            $grid->model()->orderBy('id', 'desc');
            
            // 重新设置表格列的顺序
            $grid->column('id', 'ID')->sortable()->width('60px');
            $grid->column('name', '组合名称');

            // 将属性列设置为固定显示，并调整样式
            $grid->column('attribute_selection', '属性')
                ->width('400px')
                ->display(function($value) {
                $result = [];
                
                // 直接使用DB查询获取属性信息
                if (!empty($value) && is_string($value) && preg_match('/attr:(\d+):(\d+)/', $value, $matches)) {
                    $attrId = (int)$matches[1];
                    $valueId = (int)$matches[2];
                    
                    // 使用DB::table进行简单查询，不再记录日志
                    $attribute = DB::table('t_banhua_attrs')->find($attrId);
                    $attrValue = DB::table('t_banhua_attr_values')->find($valueId);
                    
                    if ($attribute && $attrValue) {
                        // 添加属性值后的顿号分隔符
                        $result[] = "<span style='color:rgb(0, 0, 255);'>{$attrValue->name}</span>";
                        
                        // 在属性和SKU备注之间添加分隔
                        $result[] = "<span style='margin:0 3px;'> | </span>";
                    }
                }
                
                // 直接从数据库查询SKU对应的图库记录
                // 获取selected_skus字段值（可能是数组或JSON字符串）
                $rawSelectedSkus = $this->raw_selected_skus ?? $this->selected_skus;
                $skuIds = [];
                
                // 处理不同类型的输入值
                if (is_string($rawSelectedSkus)) {
                    // 如果是JSON字符串，需要解码
                    $skuIds = json_decode($rawSelectedSkus, true) ?: [];
                } elseif (is_array($rawSelectedSkus)) {
                    $skuIds = $rawSelectedSkus;
                }
                
                // 过滤无效值
                $skuIds = array_filter($skuIds, function($id) {
                    return !is_null($id) && $id > 0;
                });
                
                if (!empty($skuIds)) {
                    // 直接查询SKU和图库信息
                    $skuData = DB::table('t_banhua_product_skus as s')
                        ->leftJoin('t_banhua_image_gallery as g', 's.pid', '=', 'g.image_id')
                        ->whereIn('s.id', $skuIds)
                        ->whereNotNull('g.remark')
                        ->where('g.remark', '!=', '')
                        ->select('s.id', 's.sku', 's.name', 's.pid as image_id', 'g.remark')
                        ->get();
                    
                    // 使用集合的unique方法去重
                    $uniqueRemarks = $skuData->pluck('remark')->unique()->values();

                     // 处理每个唯一备注
                     $remarkCount = count($uniqueRemarks);
                     foreach ($uniqueRemarks as $index => $remark) {
                         if (!empty($remark)) {
                             // 如果不是最后一个元素，添加顿号分隔符
                             $separator = ($index < $remarkCount - 1) ? "、" : "";
                             $result[] = "<span style='color:rgb(37, 120, 88);'>{$remark}{$separator}</span>";
                         }
                     }
                }
                
                // 即使结果为空，也保证返回视觉上的占位符
                if (empty($result)) {
                    return '<span style="color:#999;">无属性</span>';
                }
                
                // 将所有标签拼接成HTML字符串返回
                return implode('', $result);
            });
            
            $grid->column('description', '组合描述')->display(function($description) {
                // 移除选择类型信息，只显示其他描述
                if (!empty($description)) {
                    $parts = explode("\n\n选择类型:", $description);
                    $desc = $parts[0];
                    return !empty($desc) ? $desc : '无描述';
                }
                return '无描述';
            });
            
            // 添加组合内SKU数量列
            $grid->column('sku_count', 'SKU数量')->display(function () {
                // 获取组合中的SKU数量
                $skuIds = $this->raw_selected_skus;
                \Illuminate\Support\Facades\Log::info('计算SKU数量', [
                    'id' => $this->id,
                    'skuIds' => $skuIds,
                    'raw_selected_skus_type' => gettype($this->raw_selected_skus),
                    'raw_selected_skus_count' => is_array($this->raw_selected_skus) ? count($this->raw_selected_skus) : 0,
                ]);
                
                if (empty($skuIds)) {
                    return 0;
                }
                
                return count($skuIds);
            });
            
            // 添加组合内SKU列表
            $grid->column('sku_list', 'SKU列表')->display(function () {
                try {
                    // 获取原始数据
                    $rawSelectedSkus = $this->selected_skus;
                    
                    // 处理可能的JSON字符串
                    $skuIds = [];
                    if (is_string($rawSelectedSkus)) {
                        $skuIds = json_decode($rawSelectedSkus, true) ?: [];
                    } elseif (is_array($rawSelectedSkus)) {
                        $skuIds = $rawSelectedSkus;
                    }
                    
                    // 过滤无效ID
                    $skuIds = array_filter($skuIds, function($id) {
                        return !is_null($id) && $id > 0;
                    });
                    
                    \Illuminate\Support\Facades\Log::info('显示SKU列表', [
                        'id' => $this->id,
                        'raw_selected_skus' => $rawSelectedSkus,
                        'skuIds' => $skuIds,
                        'skuIds_type' => gettype($skuIds),
                    ]);
                    
                    // 如果没有选择SKU，显示无SKU
                    if (empty($skuIds)) {
                        return '无SKU';
                    }
                    
                    // 直接查询SKU和关联的图库
                    $skus = TecBanhuaProductSkuModel::whereIn('id', $skuIds)->get();
                    
                    if ($skus->isEmpty()) {
                        return '无SKU';
                    }
                    
                    // 获取所有SKU编码
                    $skuCodes = $skus->pluck('sku')->toArray();
                    
                    // 只显示前5个SKU
                    $displaySkus = array_slice($skuCodes, 0, 5);
                    $result = implode('，', $displaySkus);
                    
                    // 如果SKU数量超过5个，显示"更多"提示
                    $totalCount = count($skuCodes);
                    if ($totalCount > 5) {
                        $result .= " ...等{$totalCount}个";
                    }
                    
                    return $result ?: '无SKU';
                    
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('显示SKU列表时出错', [
                        'id' => $this->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);
                    return '获取SKU信息出错';
                }
            });
            
            $grid->column('status', '状态')->using([
                TecBanhuaConsumableCombinationModel::STATUS_DISABLED => '禁用',
                TecBanhuaConsumableCombinationModel::STATUS_ENABLED => '启用',
            ])->dot([
                TecBanhuaConsumableCombinationModel::STATUS_DISABLED => 'danger',
                TecBanhuaConsumableCombinationModel::STATUS_ENABLED => 'success',
            ]);
            
            
            // 添加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete(false); // 启用删除按钮
            });
            
            // 筛选器
            $grid->filter(function ($filter) {
                $filter->like('name', '组合名称')->width(2);
                $filter->equal('status', '状态')->select([
                    TecBanhuaConsumableCombinationModel::STATUS_DISABLED => '禁用',
                    TecBanhuaConsumableCombinationModel::STATUS_ENABLED => '启用',
                ])->width(2);
                
                // 添加按SKU筛选
                $filter->where('sku_filter', function ($query) {
                    $input = $this->input;
                    $query->whereHasSelectedSku($input);
                }, 'SKU编码')->width(2);
            });
            
        });
    }

    /**
     * 创建页面
     */
    public function create(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('创建消耗品组合')
            ->body($this->form());
    }

    /**
     * 编辑页面
     */
    public function edit($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('编辑消耗品组合')
            ->body($this->form()->edit($id));
    }

    /**
     * 构建表单
     */
    protected function form(): Form
    {
        return Form::make(new TecBanhuaConsumableCombinationModel(), function (Form $form) {
            $form->display('id');
            
            // 获取SKU选项
            $skuOptions = $this->getUniqueSkus();
            
            // 添加SKU多选组件（改为checkbox形式）
            if (!empty($skuOptions)) 
            {
                $form->checkbox('selected_skus', '消耗品组合')
                    ->options($skuOptions)
                    ->required();
            } else {
                $form->html('<div class="alert alert-warning">没有可用的消耗品SKU</div>');
            }
            
            // 添加属性选择
            $this->addCombinedAttributeSelector($form);
            
            // 基本信息 - 只保留组合名称
            $form->text('name', '组合名称')
                ->required()
                ->rules('required|max:100')
                ->default($this->generateDefaultName()) // 设置默认名称
                ->help('组合名称必须唯一');
            
            $form->textarea('description', '组合描述')
                ->rows(3);
            
            $form->radio('status', '状态')
                ->options([
                    TecBanhuaConsumableCombinationModel::STATUS_ENABLED => '启用',
                    TecBanhuaConsumableCombinationModel::STATUS_DISABLED => '禁用',
                ])
                ->default(TecBanhuaConsumableCombinationModel::STATUS_ENABLED);

            // 保存前处理
            $form->saving(function (Form $form) {
                // 设置创建人
                if ($form->isCreating()) {
                    $form->created_by = Admin::user()->id;
                }
                
                // 记录原始 selected_skus 值
                \Illuminate\Support\Facades\Log::info('保存前的 selected_skus 值', [
                    'request_method' => request()->method(),
                    'is_creating' => $form->isCreating(),
                    'is_editing' => $form->isEditing(),
                    'selected_skus' => $form->selected_skus,
                    'selected_skus_type' => gettype($form->selected_skus),
                    'form_input' => request()->input('selected_skus'),
                    'all_inputs' => request()->all(),
                ]);
                
                // 处理checkbox的选中值
                $selectedSkus = request()->input('selected_skus');
                if (is_null($selectedSkus)) {
                    $form->selected_skus = [];
                } else if (is_array($selectedSkus)) {
                    // 过滤无效值并转换为整数
                    $validSkus = array_filter($selectedSkus, function($value) {
                        return !is_null($value) && $value !== '';
                    });
                    
                    $form->selected_skus = array_map('intval', $validSkus);
                    
                    \Illuminate\Support\Facades\Log::info('处理后的 selected_skus 值', [
                        'selected_skus' => $form->selected_skus,
                        'selected_skus_json' => json_encode($form->selected_skus),
                    ]);
                }
                
                // 处理合并的属性选择
                $this->processCombinedAttributeSelection($form);
            });

            // 保存后处理
            $form->saved(function (Form $form) {
                $model = $form->model();
                
                \Illuminate\Support\Facades\Log::info('保存后的模型数据', [
                    'id' => $model->id,
                    'selected_skus' => $model->selected_skus,
                    'raw_selected_skus' => $model->raw_selected_skus,
                    'selected_skus_attribute' => $model->getAttributeValue('selected_skus'),
                ]);
                
                // 确保数据库中保存了正确的 JSON 格式
                if (!empty($form->selected_skus)) {
                    // 直接更新数据库，确保格式正确
                    \Illuminate\Support\Facades\DB::table('t_banhua_consumable_combinations')
                        ->where('id', $model->id)
                        ->update([
                            'selected_skus' => json_encode($form->selected_skus)
                        ]);
                        
                    \Illuminate\Support\Facades\Log::info('更新后的数据库值', [
                        'id' => $model->id,
                        'selected_skus_json' => json_encode($form->selected_skus),
                    ]);
                }
            });

            // 删除编辑提交按钮文本
            $form->disableEditingCheck();
            $form->disableCreatingCheck();
            $form->disableViewCheck();
        });
    }

    /**
     * 添加合并后的属性选择器到表单
     * 
     * @param Form $form
     * @return void
     */
    protected function addCombinedAttributeSelector(Form $form): void
    {
        // 检查form对象是否有效
        if (!$form) {
            return;
        }

        try {
            // 获取所有属性和属性值
            $attributes = TecBanhuaAttrModel::with('values')
                ->orderBy('sort')
                ->get();
            
            // 如果没有属性，直接返回
            if ($attributes->isEmpty()) {
                // 添加一个提示信息
                $form->html('<div class="alert alert-warning">没有可用的属性</div>', '属性选择');
                return;
            }
            
            // 构建合并后的选项列表
            $combinedOptions = [];
            $combinedOptions[''] = '请选择'; // 添加一个空选项
            
            foreach ($attributes as $attribute) {
                // 确保属性对象有效
                if (!$attribute || !$attribute->id || !$attribute->name) {
                    continue;
                }
                
                // 获取属性值列表
                $values = $attribute->values()
                    ->orderBy('sort')
                    ->get();
                
                // 如果没有属性值，跳过
                if ($values->isEmpty()) {
                    continue;
                }
                
                // 为每个属性值创建一个选项
                foreach ($values as $value) {
                    $key = "attr:{$attribute->id}:{$value->id}";
                    $display = "{$attribute->name}-{$value->name}";
                    $combinedOptions[$key] = $display;
                }
            }
            
            // 如果没有选项，直接返回
            if (count($combinedOptions) <= 1) {
                // 添加一个提示信息
                $form->html('<div class="alert alert-warning">没有可用的属性值</div>', '属性选择');
                return;
            }
            
            // 如果是编辑模式，尝试获取已选择的值
            $defaultValue = null;
            if ($form->isEditing()) {
                $model = $form->model();
                if ($model && !empty($model->attribute_selection)) {
                    $defaultValue = $model->attribute_selection;
                }
            }
            
            try {
                // 使用表单的select方法添加字段
                $selectField = $form->select('attribute_selection', '属性选择');
                if ($selectField) {
                    $selectField->options($combinedOptions);
                    if ($defaultValue) {
                        $selectField->default($defaultValue);
                    }
                    // 使用try-catch包裹help方法调用
                    try {
                        $selectField->help('请选择一个属性组合');
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::warning('无法设置select字段的help文本', [
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            } catch (\Exception $e) {
                // 如果select方法失败，使用html方法添加一个简单的下拉列表
                $form->html($this->buildSimpleSelect($combinedOptions, $defaultValue), '属性选择');
            }
            
        } catch (\Exception $e) {
            // 记录错误但不中断程序
            \Illuminate\Support\Facades\Log::error('添加属性选择器过程中发生错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 添加一个错误提示
            $form->html('<div class="alert alert-danger">加载属性选择器时出错</div>', '属性选择');
        }
    }

    /**
     * 构建简单的下拉列表HTML
     * 
     * @param array $options 选项列表
     * @param string|null $defaultValue 默认选中值
     * @return string HTML代码
     */
    protected function buildSimpleSelect(array $options, ?string $defaultValue = null): string
    {
        $html = '<div class="form-group row">';
        $html .= '<div class="col-sm-8">';
        $html .= '<select class="form-control" name="attribute_selection">';
        
        foreach ($options as $value => $label) {
            $selected = ($value === $defaultValue) ? 'selected' : '';
            $html .= "<option value=\"{$value}\" {$selected}>{$label}</option>";
        }
        
        $html .= '</select>';
        $html .= '<span class="help-block"><i class="fa fa-info-circle"></i>&nbsp;请选择一个属性组合</span>';
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * 处理合并属性选择
     * 
     * @param Form $form
     * @return void
     */
    protected function processCombinedAttributeSelection(Form $form): void
    {
        try {
            // 从请求中获取属性选择值
            $selection = $form->attribute_selection;
            
            // 如果没有选择属性，则返回
            if (empty($selection)) {
                return;
            }
            
            // 解析选择的属性值
            // 格式: attr:{attr_id}:{value_id}
            if (preg_match('/attr:(\d+):(\d+)/', $selection, $matches)) {
                $attrId = $matches[1];
                $valueId = $matches[2];
                
                // 获取属性和属性值信息
                $attribute = TecBanhuaAttrModel::find($attrId);
                $value = TecBanhuaAttrValueModel::find($valueId);
                
                if ($attribute && $value) {
                    // 构建描述
                    $attrDesc = "{$attribute->name}:{$valueId}({$value->name})";
                    
                    // 如果已有描述，则添加到描述末尾
                    $currentDesc = $form->description ?? '';
                    if (!empty($currentDesc) && !str_contains($currentDesc, $attrDesc)) {
                        $form->description = $currentDesc . "\n\n" . $attrDesc;
                    } else if (empty($currentDesc)) {
                        $form->description = $attrDesc;
                    }
                }
            }
        } catch (\Exception $e) {
            // 记录错误但不中断程序
            \Illuminate\Support\Facades\Log::error('处理属性选择过程中发生错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 生成默认的组合名称
     * 
     * @return string
     */
    protected function generateDefaultName(): string
    {
        // 获取当前最大的组合序号
        $maxNumber = TecBanhuaConsumableCombinationModel::where('name', 'like', '组合%')
            ->get()
            ->map(function ($item) {
                // 提取名称中的数字部分
                if (preg_match('/组合(\d+)/', $item->name, $matches)) {
                    return (int)$matches[1];
                }
                return 0;
            })
            ->max();
        
        // 生成新的序号
        $newNumber = $maxNumber ? $maxNumber + 1 : 1;
        
        return "组合{$newNumber}";
    }

    /**
     * 获取唯一的SKU列表（去重）
     * 
     * @return array
     */
    protected function getUniqueSkus(): array
    {
        try {
            // 获取所有有库存的消耗品库存记录
            $stocks = TecBanhuaConsumableStockModel::with(['sku.gallery'])
                ->where('stock', '>', 0)
                ->get();
            
            // 如果没有库存记录，返回空数组
            if ($stocks->isEmpty()) {
                return [];
            }
            
            // 使用集合的方法对SKU进行去重
            $uniqueSkus = $stocks->map(function ($stock) {
                    if (!$stock->sku) return null;
                    
                    // 获取SKU的备注（从图库中）
                    $remark = '';
                    $skuCode = $stock->sku->sku;
                    
                    if ($stock->sku->gallery && !empty($stock->sku->gallery->remark)) {
                        $remark = $stock->sku->gallery->remark;
                    } else if (!empty($stock->sku->name)) {
                        $remark = $stock->sku->name;
                    }
                    
                    return [
                        'id' => $stock->sku->id,
                        'sku' => $skuCode,
                        'name' => $stock->sku->name,
                        'remark' => $remark,
                    ];
                })
                ->filter() // 过滤掉null值
                ->unique('id') // 按SKU ID去重
                ->mapWithKeys(function ($item) {
                    // 显示格式：备注(SKU编码) - 例如：包装箱(GOODS-BOX)
                    $display = $item['remark'];
                    if (!empty($item['sku'])) {
                        $display .= "({$item['sku']})";
                    }
                    
                    return [$item['id'] => $display];
                })
                ->toArray();
            
            return $uniqueSkus;
        } catch (\Exception $e) {
            // 记录错误但返回空数组
            \Illuminate\Support\Facades\Log::error('获取SKU列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [];
        }
    }

    // 这些API已不再需要，因为组合明细是自动创建的

    /**
     * 详情页面
     */
    public function show($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('消耗品组合详情')
            ->body($this->detail($id));
    }

    

    /**
     * 添加属性筛选器
     * 
     * @param \Dcat\Admin\Grid\Filter $filter
     * @return void
     */
    protected function addAttributeFilters($filter): void
    {
        try {
            // 获取所有属性
            $attributes = TecBanhuaAttrModel::with('values')
                ->orderBy('sort')
                ->get();
            
            // 如果没有属性，直接返回
            if ($attributes->isEmpty()) {
                return;
            }
            
            // 构建合并的属性值选项
            $combinedOptions = [];
            
            foreach ($attributes as $attribute) {
                // 获取属性值列表
                $values = $attribute->values()
                    ->orderBy('sort')
                    ->get();
                
                // 如果没有属性值，跳过
                if ($values->isEmpty()) {
                    continue;
                }
                
                // 为每个属性值创建一个选项
                foreach ($values as $value) {
                    $key = "{$attribute->id}:{$value->id}";
                    $display = "{$attribute->name}-{$value->name}";
                    $combinedOptions[$key] = $display;
                }
            }
            
            // 如果没有选项，直接返回
            if (empty($combinedOptions)) {
                return;
            }
            
            // 添加单个属性筛选器 - 使用select方法而不是where
            $filter->select('attribute_filter', '属性筛选')
                ->options($combinedOptions)
                ->when(function ($query, $value) {
                    if (strpos($value, ':') !== false) {
                        list($attrId, $valueId) = explode(':', $value);
                        
                        $query->where(function($q) use ($attrId, $valueId) {
                            // 首先查找attribute_selection字段
                            $q->orWhere('attribute_selection', 'like', "%attr:{$attrId}:{$valueId}%");
                            
                            // 然后查找description字段中的格式
                            // 查找旧格式
                            $attribute = TecBanhuaAttrModel::find($attrId);
                            if ($attribute) {
                                $q->orWhere('description', 'like', "%{$attribute->name}:{$valueId}(%");
                            }
                            
                            // 查找新格式
                            $q->orWhere('description', 'like', "%attr:{$attrId}:{$valueId}%");
                        });
                    }
                });
        } catch (\Exception $e) {
            // 记录错误但不中断程序
            \Illuminate\Support\Facades\Log::error('添加属性筛选器失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

  

   

    /**
     * 获取SKU关联的图库备注
     *
     * @param array $skuIds SKU ID数组
     * @return array
     */
    public function getGalleryRemarks(array $skuIds): array
    {
        // 过滤无效值
        $skuIds = array_filter($skuIds, function($id) {
            return !is_null($id) && $id > 0;
        });
        
        \Illuminate\Support\Facades\Log::info('获取图库备注前过滤SKU ID', [
            'original_count' => count($skuIds),
            'filtered_skuIds' => $skuIds,
        ]);
        
        if (empty($skuIds)) {
            return [];
        }
        
        // 获取SKU关联的图库ID
        $productIds = TecBanhuaProductSkuModel::whereIn('id', $skuIds)
            ->pluck('pid')
            ->toArray();
        
        \Illuminate\Support\Facades\Log::info('获取到的图库ID', [
            'skuIds' => $skuIds,
            'productIds' => $productIds,
        ]);
        
        if (empty($productIds)) {
            return [];
        }
        
        // 获取图库备注
        $remarks = TecBanhuaImageGalleryModel::whereIn('image_id', $productIds)
            ->whereNotNull('remark')
            ->where('remark', '!=', '')
            ->pluck('remark')
            ->toArray();
        
        \Illuminate\Support\Facades\Log::info('获取到的图库备注', [
            'productIds' => $productIds,
            'remarks_count' => count($remarks),
            'remarks' => $remarks,
        ]);
        
        return $remarks;
    }
    
} 