<?php

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Banhua\TecBanhuaProductCategoryModel;
use App\Admin\Repositories\Banhua\TecBanhuaProductCategoryRepo;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Tree;
use Dcat\Admin\Form;
use App\Admin\Actions\Post\Restore;


class TecBanhuaProductCategoryController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '阪画产品分类';
    
    /**
     * 模型树显示分类
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        return $content->header($this->title)->description('可拖动编辑')->body(function (Row $row) {
            $tree = new Tree(new TecBanhuaProductCategoryModel());

            // 定制节点显示内容
            $tree->branch(function ($branch) {
                // 获取 background_color 字段的值
                $backgroundColor = isset($branch['background_color']) && $branch['background_color'] ? $branch['background_color'] : null;

                // 设置初始内联样式
                $inlineStyle = 'style="border-radius: 4px; padding: 8px 12px; font-size: 12px;';

                // 如果设置了背景颜色，则添加背景颜色和白色字体样式
                if ($backgroundColor) {
                    $inlineStyle .= ' background-color: ' . $backgroundColor . '; color: white;"';
                } else {
                    $inlineStyle .= '"';
                }

                // 返回带有（可能有背景颜色和白色字体）HTML 内容
                return '<span ' . $inlineStyle . '>' . $branch['title'] . '</span>';
            });

            $tree->disableCreateButton(); // 关闭快速创建
            $tree->disableEditButton(); // 关闭编辑

            // 将现有内容放置在一列中
            $row->column(4, function (Column $column) use ($tree) {
                $column->append($tree);
            });

            // 将另一列留空
            $row->column(8, function (Column $column) {
                // 留空，不添加内容
            });
        });
    }

    /**
     * 构建表单
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecBanhuaProductCategoryRepo(), function (Form $form) {
            $categories = TecBanhuaProductCategoryModel::orderBy('order')->get()->pluck('full_path', 'id')->prepend('顶级', 0);

            $form->select('parent_id', trans('admin.parent_id'))
                ->options($categories)
                ->saving(function ($v) {
                    return $v ? (int) $v : 0; // 如果没有选择，返回0
                })
                ->default(0) // 确保默认值为"顶级"
                ->required(); // 确保此字段为必填

            $form->text('title')->required(); // 确保标题字段为必填

            // 添加 background_color 字段
            $form->color('background_color')
                ->default('#000000'); // 设置默认值
        });
    }
} 