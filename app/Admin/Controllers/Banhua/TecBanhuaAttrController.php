<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Repositories\Banhua\TecBanhuaAttrRepo;
use App\Models\Banhua\TecBanhuaAttrModel;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 阪画属性控制器
 */
class TecBanhuaAttrController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '阪画属性管理';

    /**
     * 列表页面
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content): Content
    {
        return $content
            ->title($this->title)
            ->description('阪画属性列表')
            ->body($this->grid());
    }

    /**
     * 构建列表
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new TecBanhuaAttrRepo(), function (Grid $grid) {
            $grid->scrollbarX();
            $grid->column('id', 'ID')->sortable();
            $grid->column('name', '属性名称');
            $grid->column('sort', '排序')->sortable()->editable();
            $grid->column('values', '属性值')->display(function ($_, $column) {
                // 在Dcat Admin中，第一个参数是当前字段值，第二个参数是列对象
                // 通过$this获取当前行的模型实例
                $color = $this->color ?? 'success'; // 获取颜色，默认为success
                
                return $this->values()
                    ->orderBy('sort')
                    ->get()
                    ->pluck('name')
                    ->map(function ($name) use ($color) {
                        // 使用模型中设置的颜色
                        $colorMap = [
                            'primary' => ['color' => 'white', 'bg' => 'var(--primary)'],
                            'success' => ['color' => 'white', 'bg' => 'var(--success)'],
                            'info' => ['color' => 'white', 'bg' => 'var(--cyan)'],
                            'warning' => ['color' => 'white', 'bg' => 'var(--orange)'],
                            'danger' => ['color' => 'white', 'bg' => 'var(--red)']
                        ];
                        
                        $colorInfo = $colorMap[$color] ?? ['color' => 'white', 'bg' => 'var(--gray)'];
                        
                        return "<span style='
                            background-color: {$colorInfo['bg']}; 
                            color: {$colorInfo['color']}; 
                            padding: 4px 8px; 
                            margin-right: 4px;
                            margin-bottom: 4px;
                            border-radius: 12px; 
                            display: inline-block; 
                            font-size: 12px; 
                            font-weight: 500; 
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        '>".e($name)."</span>";
                    })->implode('');
            });
            
            // 默认按排序字段排序
            $grid->model()->orderBy('sort');
        });
    }

    /**
     * 创建页面
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content): Content
    {
        return $content
            ->title($this->title)
            ->description('创建阪画属性')
            ->body($this->form());
    }

    /**
     * 编辑页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function edit($id, Content $content): Content
    {
        // 记录ID参数
        Log::info('编辑方法接收到的ID: ' . $id);
        
        // 确保加载关联数据
        $form = $this->form();
        
        return $content
            ->title($this->title)
            ->description('编辑阪画属性')
            ->body($form->edit($id));
    }

    /**
     * 构建表单
     *
     * @return \Dcat\Admin\Form
     */
    protected function form(): \Dcat\Admin\Form
    {
        return \Dcat\Admin\Form::make(new TecBanhuaAttrRepo(), function (\Dcat\Admin\Form $form) {
            $form->text('name')->help('例如：颜色，尺寸')->required();
            $form->number('sort')->default(0)->help('数值越小越靠前');
            $form->radio('color', '徽章颜色')->options([
                'primary' => '<span style="background-color: var(--primary); color: white; padding: 5px 10px; border-radius: 12px;">蓝色</span>',
                'success' => '<span style="background-color: var(--success); color: white; padding: 5px 10px; border-radius: 12px;">绿色</span>',
                'info' => '<span style="background-color: var(--cyan); color: white; padding: 5px 10px; border-radius: 12px;">青色</span>',
                'warning' => '<span style="background-color: var(--orange); color: white; padding: 5px 10px; border-radius: 12px;">黄色</span>',
                'danger' => '<span style="background-color: var(--red); color: white; padding: 5px 10px; border-radius: 12px;">红色</span>'
            ])->default('success');
            
            $form->divider('');
            
            // 添加自定义HTML用于显示和编辑属性值
            $form->html($this->renderAttributeValuesEditor());
            
            // 禁用默认的提交按钮，使用自定义提交
            $form->disableSubmitButton();
            $form->disableResetButton();
        });
    }

    /**
     * 渲染属性值编辑器
     *
     * @return string
     */
    protected function renderAttributeValuesEditor(): string
    {
        // 判断当前是否为新建模式
        $isCreate = request()->is('*/create');
        
        // 获取当前编辑的属性ID
        $attrId = null;
        
        // 只有在编辑模式下才获取属性ID和属性值
        if (!$isCreate) {
            // 从URL路径中获取ID
            $segments = request()->segments();
            Log::info('URL段: ' . json_encode($segments));
            
            // 查找形如 .../banhua/attrs/123/edit 的路径
            foreach ($segments as $index => $segment) {
                if ($segment === 'attrs' && isset($segments[$index + 1]) && is_numeric($segments[$index + 1])) {
                    $attrId = $segments[$index + 1];
                    Log::info('从URL段提取的ID: ' . $attrId);
                    break;
                }
            }
            
            // 记录日志
            Log::info('最终确定的编辑属性ID: ' . $attrId);
            Log::info('请求URL: ' . request()->url());
            Log::info('请求路径: ' . request()->path());
        }
        
        // 获取属性值数据
        $values = [];
        if (!$isCreate && $attrId) {
            $attr = TecBanhuaAttrModel::with(['values' => function ($query) {
                $query->orderBy('sort');
            }])->find($attrId);
            
            Log::info('查询属性结果: ' . ($attr ? 'found' : 'not found'));
            if ($attr) {
                Log::info('属性名称: ' . $attr->name);
                Log::info('属性值数量: ' . $attr->values->count());
            }
            
            if ($attr && $attr->values) {
                $values = $attr->values->map(function ($item, $index) {
                    return [
                        'id' => $item->id,
                        'name' => $item->name,
                        'sort' => $index // 确保sort值是连续的索引
                    ];
                })->toArray();
                Log::info('属性值数据: ' . json_encode($values, JSON_UNESCAPED_UNICODE));
            }
        }
        
        // 添加调试信息
        $debugInfo = "当前模式: " . ($isCreate ? '新增' : '编辑');
        $debugInfo .= ", 属性ID: " . ($attrId ?: '无');
        
        // 生成唯一ID，确保每个页面有独立的ID
        $uniqueId = 'attr_editor_' . uniqid();
        $pageMode = $isCreate ? 'create' : 'edit';
        
        return <<<HTML
<div class="card">
    <div class="card-header">
        <h3 class="card-title">属性值列表</h3>
        <div class="card-tools">
            <button type="button" class="btn btn-sm btn-success" id="{$uniqueId}_add_btn">
                <i class="feather icon-plus"></i> 添加属性值
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- <div class="alert alert-info">
            {$debugInfo}
        </div> -->
        <table class="table table-hover" id="{$uniqueId}_table">
            <thead>
                <tr>
                    <th width="80">排序</th>
                    <th>名称</th>
                    <th width="100">操作</th>
                </tr>
            </thead>
            <tbody id="{$uniqueId}_tbody"></tbody>
        </table>
        
        <!-- 使用标准表单字段，确保可以正确提交 -->
        <input type="hidden" name="attr_values_json" id="{$uniqueId}_values_json">
    </div>
</div>

<script>
// 使用立即执行函数创建独立作用域
(function() {
    // 页面标识
    var pageMode = '{$pageMode}';
    var uniqueId = '{$uniqueId}';
    console.log('初始化属性编辑器:', uniqueId, '当前模式:', pageMode);
    
    // 强制加载SortableJS
    if (typeof Sortable === 'undefined') {
        var script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js';
        document.head.appendChild(script);
    }
    
    // 存储属性值数据
    var attrValues = JSON.parse('{$this->escapeJson(json_encode($values))}');
    console.log(uniqueId + ' 初始化属性值数据:', attrValues);
    
    // 获取DOM元素
    function getElement(id) {
        return document.getElementById(uniqueId + '_' + id);
    }
    
    // 更新隐藏字段的值
    function updateValuesJson() {
        var input = getElement('values_json');
        if (input) {
            input.value = JSON.stringify(attrValues);
            console.log(uniqueId + ' 更新隐藏字段值:', input.value);
        } else {
            console.error(uniqueId + ' 找不到隐藏字段');
        }
    }
    
    // 渲染属性值表格
    function renderAttrValues() {
        console.log(uniqueId + ' 渲染属性值表格, 数据:', attrValues);
        const tbody = getElement('tbody');
        if (!tbody) {
            console.error(uniqueId + ' 找不到表格体元素');
            return;
        }
        
        // 清空表格
        tbody.innerHTML = '';
        
        // 检查数据
        if (!attrValues || attrValues.length === 0) {
            console.log(uniqueId + ' 没有属性值数据');
            var tr = document.createElement('tr');
            tr.innerHTML = '<td colspan="3" class="text-center">没有数据</td>';
            tbody.appendChild(tr);
        } else {
            // 添加行
            attrValues.forEach(function(value, index) {
                console.log(uniqueId + ' 添加行:', index, value);
                const tr = document.createElement('tr');
                tr.dataset.id = value.id || '';
                tr.dataset.index = index;
                
                tr.innerHTML = 
                    '<td>' +
                    '    <span class="btn-move handle" style="cursor:move;color:#409EFF;font-size:18px;">' +
                    '        <i class="fa fa-arrows-alt"></i>' +
                    '    </span>' +
                    '</td>' +
                    '<td>' +
                    '    <input type="text" class="form-control attr-value-name" value="' + (value.name || '') + '" data-index="' + index + '" required>' +
                    '</td>' +
                    '<td>' +
                    '    <button type="button" class="btn btn-sm btn-danger delete-btn" data-index="' + index + '">' +
                    '        <i class="feather icon-trash"></i>' +
                    '    </button>' +
                    '</td>';
                
                tbody.appendChild(tr);
            });
            
            // 绑定事件
            tbody.querySelectorAll('.attr-value-name').forEach(function(input) {
                input.addEventListener('change', function() {
                    var index = parseInt(this.dataset.index);
                    console.log(uniqueId + ' 名称变更:', index, this.value);
                    if (attrValues[index]) {
                        attrValues[index].name = this.value;
                        updateValuesJson();
                    }
                });
            });
            
            tbody.querySelectorAll('.delete-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    var index = parseInt(this.dataset.index);
                    console.log(uniqueId + ' 删除按钮点击:', index);
                    deleteAttrValue(index);
                });
            });
        }
        
        // 更新隐藏字段
        updateValuesJson();
    }
    
    // 初始化排序功能
    function initSortable() {
        console.log(uniqueId + ' 初始化排序功能');
        const tbody = getElement('tbody');
        if (!tbody) {
            console.error(uniqueId + ' 无法初始化排序：tbody不存在');
            return;
        }
        
        if (typeof Sortable === 'undefined') {
            console.error(uniqueId + ' Sortable库未加载，延迟初始化');
            setTimeout(initSortable, 500);
            return;
        }
        
        // 销毁之前的实例
        if (tbody.sortableInstance) {
            tbody.sortableInstance.destroy();
        }
        
        // 创建新的实例
        tbody.sortableInstance = Sortable.create(tbody, {
            handle: '.handle',
            animation: 150,
            onEnd: function() {
                console.log(uniqueId + ' 排序结束，重新排序数据');
                // 重新排序数据
                const newValues = [];
                tbody.querySelectorAll('tr').forEach(function(row, index) {
                    const oldIndex = parseInt(row.dataset.index);
                    if (!isNaN(oldIndex) && attrValues[oldIndex]) {
                        const value = Object.assign({}, attrValues[oldIndex], {sort: index});
                        newValues.push(value);
                    }
                });
                
                attrValues = newValues;
                updateValuesJson();
            }
        });
    }
    
    // 添加属性值
    function addAttrValue() {
        console.log(uniqueId + ' 添加属性值');
        attrValues.push({
            id: '',
            name: '',
            sort: attrValues.length
        });
        
        renderAttrValues();
        initSortable();
    }
    
    // 删除属性值
    function deleteAttrValue(index) {
        console.log(uniqueId + ' 删除属性值:', index);
        if (index >= 0 && index < attrValues.length) {
            attrValues.splice(index, 1);
            // 更新所有项的sort值
            attrValues.forEach(function(value, idx) {
                value.sort = idx;
            });
            renderAttrValues();
            initSortable();
        }
    }
    
    // 初始化函数
    function init() {
        console.log(uniqueId + ' 开始初始化');
        
        // 绑定添加按钮事件
        var addBtn = getElement('add_btn');
        if (addBtn) {
            addBtn.addEventListener('click', addAttrValue);
            console.log(uniqueId + ' 已绑定添加按钮事件');
        } else {
            console.error(uniqueId + ' 找不到添加按钮');
        }
        
        // 初始渲染
        renderAttrValues();
        
        // 初始化排序功能
        setTimeout(initSortable, 500);
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        // 如果DOM已经加载完成，立即初始化
        setTimeout(init, 100);
    }
    
    // 在表单提交前确保数据已更新
    document.addEventListener('submit', function(e) {
        if (e.target.tagName === 'FORM') {
            console.log(uniqueId + ' 表单提交前检查');
            updateValuesJson();
        }
    }, true);
})();
</script>
HTML;
    }
    
    /**
     * 转义JSON字符串，确保在JavaScript中正确解析
     *
     * @param string $json
     * @return string
     */
    protected function escapeJson(string $json): string
    {
        return str_replace(
            ["\\", "'", "\r", "\n", "<script>", "</script>"],
            ["\\\\", "\'", "\\r", "\\n", "<' + 'script>", "</' + 'script>"],
            $json
        );
    }

    /**
     * 上移属性值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function moveUp(Request $request)
    {
        $id = $request->input('id');
        $value = TecBanhuaAttrValueModel::find($id);
        
        if (!$value) {
            return response()->json(['status' => false, 'message' => '属性值不存在']);
        }

        $prevValue = TecBanhuaAttrValueModel::where('attr_id', $value->attr_id)
            ->where('sort', '<', $value->sort)
            ->orderBy('sort', 'desc')
            ->first();

        if ($prevValue) {
            $tempSort = $value->sort;
            $value->sort = $prevValue->sort;
            $prevValue->sort = $tempSort;
            
            $value->save();
            $prevValue->save();
        }

        return response()->json(['status' => true]);
    }

    /**
     * 下移属性值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function moveDown(Request $request)
    {
        $id = $request->input('id');
        $value = TecBanhuaAttrValueModel::find($id);
        
        if (!$value) {
            return response()->json(['status' => false, 'message' => '属性值不存在']);
        }

        $nextValue = TecBanhuaAttrValueModel::where('attr_id', $value->attr_id)
            ->where('sort', '>', $value->sort)
            ->orderBy('sort', 'asc')
            ->first();

        if ($nextValue) {
            $tempSort = $value->sort;
            $value->sort = $nextValue->sort;
            $nextValue->sort = $tempSort;
            
            $value->save();
            $nextValue->save();
        }

        return response()->json(['status' => true]);
    }

    /**
     * 获取属性值列表（用于级联选择）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAttrValues(Request $request)
    {
        $attrId = $request->get('q');
        
        // 添加更详细的调试日志
        \Illuminate\Support\Facades\Log::info('属性值API调用', [
            'attr_id' => $attrId,
            'path' => $request->path(),
            'method' => $request->method(),
            'full_url' => $request->fullUrl(),
            'all_params' => $request->all()
        ]);
        
        if (!$attrId) {
            \Illuminate\Support\Facades\Log::warning('属性值API调用：没有提供attr_id参数');
            return response()->json([]);
        }
        
        $values = TecBanhuaAttrValueModel::where('attr_id', $attrId)
            ->orderBy('sort')
            ->get(['id', 'name'])
            ->map(function ($item) {
                return [
                    'id' => (string)$item->id, // 确保ID是字符串类型
                    'text' => $item->name,
                ];
            });
        
        // 记录返回的属性值
        \Illuminate\Support\Facades\Log::info('属性值API返回数据', [
            'attr_id' => $attrId,
            'count' => $values->count(),
            'values' => $values->toArray()
        ]);
        
        return response()->json($values);
    }

    /**
     * 处理表单提交
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update($id)
    {
        try {
            $request = request();
            
            // 记录请求数据
            Log::info('接收到的请求数据: ' . json_encode($request->all(), JSON_UNESCAPED_UNICODE));
            
            // 如果是行内编辑，则只更新对应字段
            if ($request->has('_inline_edit_')) {
                try {
                    $attr = TecBanhuaAttrModel::find($id);
                    if (!$attr) {
                        return response()->json([
                            'status' => false,
                            'data' => [
                                'message' => '找不到ID为 ' . $id . ' 的属性记录'
                            ]
                        ]);
                    }

                    // 兼容 Dcat Admin editable 组件的多种请求格式
                    // 1. 直接从请求体获取 name 和 value
                    $field = $request->input('name');
                    $newValue = $request->input('value');

                    // 2. 如果没有 name 参数，则尝试从请求中获取第一个非系统参数
                    if (empty($field)) {
                        foreach ($request->except(['_token', '_method', '_inline_edit_']) as $key => $val) {
                            $field = $key;
                            $newValue = $val;
                            break;
                        }
                    }

                    // 3. 如果仍然没有字段名，则记录错误并返回友好提示
                    if (empty($field)) {
                        Log::error('行内编辑请求异常: 无法确定要更新的字段', ['request' => $request->all()]);
                        return response()->json([
                            'status' => false,
                            'data' => [
                                'message' => '无法确定要更新的字段，请刷新页面重试'
                            ]
                        ]);
                    }

                    // 获取旧值
                    $oldValue = $attr->{$field};
                    
                    // 根据字段类型进行数据验证和转换
                    if ($field === 'sort') {
                        // 排序字段必须是整数
                        if (!is_numeric($newValue) || strpos($newValue, '.') !== false || preg_match('/[a-zA-Z]/', $newValue)) {
                            return response()->json([
                                'status' => false,
                                'data' => [
                                    'message' => '排序必须是整数'
                                ]
                            ]);
                        }
                        // 转换为整数
                        $newValue = (int)$newValue;
                    }

                    // 更新字段值
                    $attr->{$field} = $newValue;
                    $attr->save();

                    // 字段名称映射，使显示更友好
                    $fieldLabels = [
                        'name' => '属性名称',
                        'sort' => '排序',
                        'color' => '颜色'
                    ];
                    $readableFieldName = $fieldLabels[$field] ?? $field;

                    // 返回成功消息，确保 message 在 data 里面
                    return response()->json([
                        'status' => true,
                        'data' => [
                            'message' => "更新成功: [{$newValue}]"
                        ]
                    ]);
                } catch (\Exception $e) {
                    // 记录错误日志
                    Log::error('行内编辑更新失败: ' . $e->getMessage(), [
                        'id' => $id,
                        'request' => $request->all(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    
                    // 返回友好的错误消息
                    return response()->json([
                        'status' => false,
                        'data' => [
                            'message' => '更新失败: ' . $e->getMessage()
                        ]
                    ]);
                }
            }
            
            // 获取属性值数据
            $valuesJson = $request->input('attr_values_json');
            Log::info('原始属性值JSON数据: ' . $valuesJson);
            
            $values = [];
            
            if (!empty($valuesJson)) {
                try {
                    // 尝试直接解析JSON
                    $values = json_decode($valuesJson, true);
                    
                    // 如果解析失败，检查是否有转义问题
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        Log::warning('JSON直接解析失败，尝试处理转义字符: ' . json_last_error_msg());
                        
                        // 尝试去除转义字符
                        $cleanJson = stripslashes($valuesJson);
                        Log::info('处理转义后的JSON: ' . $cleanJson);
                        
                        $values = json_decode($cleanJson, true);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            Log::error('处理转义后JSON解析仍然失败: ' . json_last_error_msg());
                            throw new \Exception('JSON解析错误: ' . json_last_error_msg());
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('解析JSON时出错: ' . $e->getMessage());
                    Log::error('原始JSON: ' . $valuesJson);
                    throw $e;
                }
            }
            
            Log::info('解析后的属性值数据: ' . json_encode($values, JSON_UNESCAPED_UNICODE));
            
            // 更新属性基本信息
            $attr = TecBanhuaAttrModel::find($id);
            if (!$attr) {
                Log::error('找不到属性记录: ID=' . $id);
                return response()->json([
                    'status' => false,
                    'data' => [],
                    'message' => '找不到ID为 ' . $id . ' 的属性记录'
                ]);
            }
            
            Log::info('更新属性基本信息: ID=' . $id . ', 名称=' . $request->input('name') . ', 排序=' . $request->input('sort', 0) . ', 颜色=' . $request->input('color', 'success'));
            $attr->name = $request->input('name');
            $attr->sort = $request->input('sort', 0);
            $attr->color = $request->input('color', 'success');
            $attr->save();
            
            // 处理属性值
            if (!empty($values)) {
                // 获取当前所有属性值的ID
                $existingIds = $attr->values()->pluck('id')->toArray();
                Log::info('现有属性值IDs: ' . json_encode($existingIds));
                $updatedIds = [];
                
                foreach ($values as $index => $value) {
                    if (!empty($value['name'])) {
                        if (!empty($value['id'])) {
                            // 更新现有属性值
                            Log::info('尝试更新属性值: ID=' . $value['id'] . ', 名称=' . $value['name'] . ', 排序=' . $index);
                            TecBanhuaAttrValueModel::where('id', $value['id'])
                                ->update([
                                    'name' => $value['name'],
                                    'sort' => $index,
                                ]);
                            $updatedIds[] = $value['id'];
                            
                            // 记录日志
                            Log::info('更新属性值成功: ID=' . $value['id'] . ', 名称=' . $value['name'] . ', 排序=' . $index);
                        } else {
                            // 添加新属性值
                            Log::info('尝试添加新属性值: 名称=' . $value['name'] . ', 排序=' . $index);
                            $newValue = $attr->values()->create([
                                'name' => $value['name'],
                                'sort' => $index,
                            ]);
                            
                            // 记录日志
                            Log::info('添加新属性值成功: ID=' . $newValue->id . ', 名称=' . $value['name'] . ', 排序=' . $index);
                        }
                    }
                }
                
                // 删除不再存在的属性值
                $deleteIds = array_diff($existingIds, $updatedIds);
                if (!empty($deleteIds)) {
                    Log::info('尝试删除属性值: IDs=' . json_encode($deleteIds));
                    TecBanhuaAttrValueModel::whereIn('id', $deleteIds)->delete();
                    
                    // 记录日志
                    Log::info('删除属性值成功: IDs=' . implode(',', $deleteIds));
                }
            } else {
                // 如果没有属性值数据，删除所有现有属性值
                Log::info('没有属性值数据，删除所有现有属性值');
                $attr->values()->delete();
            }
            
            return response()->json([
                'status' => true,
                'data' => ['attr_id' => $attr->id],
                'message' => '属性 [' . $attr->name . '] 及其 ' . count($values) . ' 个属性值已成功保存'
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('更新属性时发生错误: ' . $e->getMessage());
            Log::error('错误堆栈: ' . $e->getTraceAsString());
            
            return response()->json([
                'status' => false,
                'data' => ['error' => $e->getMessage()],
                'message' => '保存失败: ' . $e->getMessage() . '，请查看日志获取更多信息'
            ]);
        }
    }

    /**
     * 处理创建表单提交
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store()
    {
        try {
            $request = request();
            
            // 记录请求数据
            Log::info('接收到的创建请求数据: ' . json_encode($request->all(), JSON_UNESCAPED_UNICODE));
            
            // 获取属性值数据
            $valuesJson = $request->input('attr_values_json');
            Log::info('原始属性值JSON数据: ' . $valuesJson);
            
            $values = [];
            
            if (!empty($valuesJson)) {
                try {
                    // 尝试直接解析JSON
                    $values = json_decode($valuesJson, true);
                    
                    // 如果解析失败，检查是否有转义问题
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        Log::warning('JSON直接解析失败，尝试处理转义字符: ' . json_last_error_msg());
                        
                        // 尝试去除转义字符
                        $cleanJson = stripslashes($valuesJson);
                        Log::info('处理转义后的JSON: ' . $cleanJson);
                        
                        $values = json_decode($cleanJson, true);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            Log::error('处理转义后JSON解析仍然失败: ' . json_last_error_msg());
                            throw new \Exception('JSON解析错误: ' . json_last_error_msg());
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('解析JSON时出错: ' . $e->getMessage());
                    Log::error('原始JSON: ' . $valuesJson);
                    throw $e;
                }
            }
            
            Log::info('解析后的属性值数据: ' . json_encode($values, JSON_UNESCAPED_UNICODE));
            
            // 创建属性
            $attr = new TecBanhuaAttrModel();
            $attr->name = $request->input('name');
            $attr->sort = $request->input('sort', 0);
            $attr->color = $request->input('color', 'success');
            $attr->save();
            
            Log::info('创建属性成功: ID=' . $attr->id . ', 名称=' . $attr->name . ', 排序=' . $attr->sort . ', 颜色=' . $attr->color);
            
            // 处理属性值
            if (!empty($values)) {
                foreach ($values as $index => $value) {
                    if (!empty($value['name'])) {
                        // 添加新属性值
                        Log::info('尝试添加新属性值: 名称=' . $value['name'] . ', 排序=' . $index);
                        $newValue = $attr->values()->create([
                            'name' => $value['name'],
                            'sort' => $index,
                        ]);
                        
                        // 记录日志
                        Log::info('创建属性值成功: ID=' . $newValue->id . ', 名称=' . $value['name'] . ', 排序=' . $index);
                    }
                }
            }
            
            return response()->json([
                'status' => true,
                'data' => ['attr_id' => $attr->id],
                'message' => '属性 [' . $attr->name . '] 及其 ' . count($values) . ' 个属性值已成功创建'
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('创建属性时发生错误: ' . $e->getMessage());
            Log::error('错误堆栈: ' . $e->getTraceAsString());
            
            return response()->json([
                'status' => false,
                'data' => ['error' => $e->getMessage()],
                'message' => '创建失败: ' . $e->getMessage() . '，请查看日志获取更多信息'
            ]);
        }
    }
} 