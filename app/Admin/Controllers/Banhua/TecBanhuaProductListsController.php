<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Banhua\TecBanhuaProductListsModel;
use App\Models\Banhua\TecBanhuaProductCategoryModel;
use App\Models\Banhua\TecBanhuaProductUnitModel;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * 阪画产品列表管理控制器
 */
class TecBanhuaProductListsController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '阪画产品列表';

    /**
     * 列表页面
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content): Content
    {
        try {
            \Illuminate\Support\Facades\Log::info('访问产品列表页面');
            
            return $content
                ->header($this->title)
                ->description('阪画产品列表')
                ->body($this->grid());
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('构建产品列表页面出错', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回一个简单的错误页面
            return $content
                ->header($this->title)
                ->description('阪画产品列表')
                ->body('<div class="alert alert-danger">加载列表时出错：' . $e->getMessage() . '</div>');
        }
    }

    /**
     * 构建列表
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        try {
            
        return Grid::make(new TecBanhuaProductListsModel(), function (Grid $grid) {
                try {
            // 启用横向滚动条
            $grid->scrollbarX();                    
            // 关联分类和单位
            $grid->model()->with(['category', 'unit']);
            
            // 基本列
            $grid->column('id')->sortable();
            $grid->column('category.title', '分类');
            
            // 编码列
            $grid->column('code', '编码');
            $grid->column('name', '名称');
            $grid->column('pid', '图库ID');
            $grid->column('jan', 'JAN');
            $grid->column('sku', 'SKU');
            $grid->column('size', '尺寸');
            
            // 单位和价格
            $grid->column('unit.unit_name', '单位');
            $grid->column('price', '价格');
            $grid->column('cost', '成本');
            $grid->column('stock', '库存');
            $grid->column('status', '状态')->display(function($status) {
                return $status == 1 ? '<span class="badge badge-success">上架</span>' : '<span class="badge badge-secondary">下架</span>';
            });
            
            // 图片
            $grid->column('image', '图片')->display(function ($value) {
                if (empty($value)) {
                    return '';
                }
                
                // 确保URL是有效的
                $value = trim($value);
                if (!filter_var($value, FILTER_VALIDATE_URL)) {
                    return "<span class='text-muted'>图片URL无效</span>";
                }
                
                // 生成缩略图和预览图
                $thumbUrl = $value;
                
                // 使用Dcat Admin的图片预览功能
                return "<img 
                    src='data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7' 
                    data-src='{$thumbUrl}' 
                    style='max-width:50px;max-height:50px;cursor:pointer;' 
                    class='img-thumbnail lazy img-preview' 
                    data-original='{$value}'
                    title='点击预览' />";
            });
            
            // 添加图片懒加载和预览JavaScript
            Admin::script('
            $(function() {
                // 图片懒加载初始化
                var lazyLoadInstance = new LazyLoad({
                    elements_selector: ".lazy",
                    threshold: 50,
                    callback_error: function(element) {
                        $(element).attr("src", "/vendor/dcat-admin/images/picture.png");
                    }
                });
                
                // 监听表格刷新事件，重新初始化懒加载
                $(document).on("pjax:complete", function() {
                    if (lazyLoadInstance) {
                        lazyLoadInstance.update();
                    }
                });
                
                // 图片预览功能
                $(document).off("click", ".img-preview").on("click", ".img-preview", function() {
                    var src = $(this).data("original");
                    if (!src) return;
                    
                    // 创建预览层
                    var imgHtml = "<div style=\'text-align:center;\'><img src=\'" + src + "\' style=\'max-width:100%;max-height:600px;\' /></div>";
                    layer.open({
                        type: 1,
                        title: "图片预览",
                        shadeClose: true,
                        shade: 0.3,
                        area: ["40%", "60%"],
                        content: imgHtml
                    });
                });
            });
            ');               
           
            // 筛选器
            $grid->filter(function ($filter) {
                // 按分类筛选 - 只显示有数据的分类
                $filter->equal('category_id', '分类')->select(function () {
                    // 获取所有有产品数据的分类ID
                    $categoryIds = TecBanhuaProductListsModel::distinct()
                        ->pluck('category_id')
                        ->filter() // 过滤掉null和空值
                        ->toArray();
                    
                    // 获取这些分类的选项
                    return TecBanhuaProductCategoryModel::whereIn('id', $categoryIds)
                        ->pluck('title', 'id');
                })->width(2);
                
                
                // 按编码筛选
                $filter->like('code', '编码')->width(2);
                $filter->like('sku', 'SKU')->width(2);
                $filter->like('pid', '图库ID')->width(2);
                $filter->like('jan', 'JAN')->width(2);
                $filter->like('name', '名称')->width(2);
                $filter->like('size', '尺寸')->width(2);
                
                // 状态筛选
                $filter->equal('status', '状态')->select([
                    0 => '下架',
                    1 => '上架',
                ])->width(2);
            });
            
            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(false); // 启用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
            });
            
            // 排序
            $grid->model()->orderBy('id', 'desc');
                    
            // 分页设置 - 优化大数据量下的性能
            $grid->paginate(20); // 每页显示20条记录
            $grid->disablePerPages(); // 禁用每页显示数量的选择器，避免用户选择过大的数值
            
            // 关闭详情按钮，减少不必要的请求
            $grid->disableViewButton();
            
            // 批量操作优化
            $grid->batchActions(function ($batch) {
                // 禁用批量删除，避免误操作
                $batch->disableDelete();
            });
            
            // 添加响应式表格
            $grid->fixColumns(0, 0); // 固定左侧和右侧的列，防止表格过宽

            \Illuminate\Support\Facades\Log::info('Grid构建完成');
                } catch (\Exception $innerEx) {
                    \Illuminate\Support\Facades\Log::error('Grid回调构建失败', [
                        'error' => $innerEx->getMessage(),
                        'trace' => $innerEx->getTraceAsString()
                    ]);
                    // 创建一个简化版的Grid以避免错误
                    $grid->column('id')->sortable();
                    $grid->column('code', '编码');
                    $grid->column('name', '名称');
                }
                
                // 添加导出工具
                $grid->tools(function ($tools) {
                    // 添加导出按钮
                    $tools->append(new \App\Admin\Actions\Grid\Banhua\TecBanhuaProductListsExportAction());
                    
                    // 添加缓存清除按钮
                    $tools->append('<a class="btn btn-success btn-mini btn-outline" href="' . admin_url('banhua/clear-product-lists-cache') . '" style="margin-right:3px">
                        <i class="fa fa-motorcycle"></i>
                    </a>');
                });
                
                return $grid;
            });
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Grid构建失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // 返回一个空白的Grid
            return Grid::make(new TecBanhuaProductListsModel());
        }
    }

    /**
     * 创建页面
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('创建产品')
            ->body($this->form());
    }

    /**
     * 编辑页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function edit($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('编辑产品')
            ->body($this->form()->edit($id));
    }

    /**
     * 构建表单
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new TecBanhuaProductListsModel(), function (Form $form) {
            $form->display('id');
            
            $form->row(function ($row) {
                // 分类
                $row->width(4)->select('category_id', '分类')
                ->options(TecBanhuaProductCategoryModel::selectOptions())
                ->required();
                
                // 单位
                $row->width(3)->select('unit_id', '单位')
                ->options(TecBanhuaProductUnitModel::pluck('unit_name', 'id'))
                ->required();
                
                // 状态
                $row->width(3)->switch('status', '状态')
                    ->states([
                        'on' => ['value' => 1, 'text' => '上架', 'color' => 'success'],
                        'off' => ['value' => 0, 'text' => '下架', 'color' => 'default'],
                    ])
                    ->default(1);
            });

            $form->row(function ($row) {
                // 编码
                $row->width(3)->text('code', '编码')->required();
                // 名称
                $row->width(3)->text('name', '名称')->required();
                // SKU
                $row->width(3)->text('sku', 'SKU');
                // 图库ID
                $row->width(3)->text('pid', '图库ID');
            });
            
            $form->row(function ($row) {
                // JAN
                $row->width(3)->text('jan', 'JAN');
                // 尺寸
                $row->width(3)->text('size', '尺寸');
                // 价格
                $row->width(3)->currency('price', '价格')->symbol('￥');
                // 成本
                $row->width(3)->currency('cost', '成本')->symbol('￥');
            });
            
            $form->row(function ($row) {
                // 库存
                $row->width(3)->number('stock', '库存')->min(0)->default(0);
                // 排序
                $row->width(3)->number('sort', '排序')->min(0)->default(0);
            });
            
            $form->row(function ($row) {
                // 图片路径
                $row->width(8)->text('image', '图片路径')->help('图片的URL')
                    ->attribute(['id' => 'image_url_input']);
                    
                // 添加图片预览容器
                $row->width(4)->html('<div id="image_preview_container" style="margin-top:10px;display:none;">
                    <img id="image_preview" src="" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;">
                </div>', '图片预览');
            });
            
            // 描述
            $form->textarea('description', '描述')->rows(3);
            
            // 添加图片预览JavaScript
            Admin::script('
            $(function() {
                // 输入框变化时自动更新预览
                $("#image_url_input").on("change blur", function() {
                    var imageUrl = $(this).val();
                    if (imageUrl) {
                        // 验证URL是否有效
                        if (!isValidUrl(imageUrl)) {
                            $("#image_preview_container").html("<span class=\'text-muted\'>图片URL无效</span>");
                            $("#image_preview_container").show();
                            return;
                        }
                        
                        // 重置预览容器
                        $("#image_preview_container").html(\'<img id="image_preview" src="" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;">\');
                        $("#image_preview_container").show();
                        $("#image_preview").after(\'<div id="image_loading">加载中...</div>\');
                        
                        // 创建新图片对象测试加载
                        var img = new Image();
                        img.onload = function() {
                            $("#image_loading").remove();
                            $("#image_preview").attr("src", imageUrl);
                        };
                        img.onerror = function() {
                            $("#image_loading").remove();
                            $("#image_preview_container").html("<span class=\'text-muted\'>图片URL无效</span>");
                        };
                        img.src = imageUrl;
                    } else {
                        $("#image_preview_container").hide();
                    }
                });
                
                // 页面加载时检查是否有图片URL
                var initialImageUrl = $("#image_url_input").val();
                if (initialImageUrl) {
                    $("#image_url_input").trigger("change");
                }
                
                // URL验证函数
                function isValidUrl(url) {
                    try {
                        return /^(http|https):\/\/[^ "]+$/.test(url);
                    } catch (e) {
                        return false;
                    }
                }
            });
            ');
        });
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('产品详情')
            ->body($this->detail($id));
    }

    /**
     * 构建详情
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id): Show
    {
        return Show::make($id, new TecBanhuaProductListsModel(), function (Show $show) {
            $show->field('id');
            $show->field('category.title', '分类');
            $show->field('unit.unit_name', '单位');
            
            $show->field('code', '编码');
            $show->field('name', '名称');
            $show->field('pid', '图库ID');
            $show->field('jan', 'JAN');
            $show->field('sku', 'SKU');
            $show->field('size', '尺寸');
            $show->field('price', '价格');
            $show->field('cost', '成本');
            $show->field('stock', '库存');
            $show->field('status_text', '状态');
            $show->field('image', '图片路径');
            $show->field('description', '描述');
            $show->field('sort', '排序');
            
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * 清除产品列表缓存
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearProductListsCache()
    {
        try {
            // 这里可以添加清除缓存的代码
            \Illuminate\Support\Facades\Log::info('手动刷新产品列表');
            
            admin_toastr('刷新成功', 'success');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('刷新操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            admin_toastr('刷新失败: ' . $e->getMessage(), 'error');
        }
        
        return redirect()->back();
    }
    
    /**
     * 导出产品数据
     *
     * @param Request $request
     * @return mixed
     */
    public function exportProductLists(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaProductListsExportAction();
        return $action->handle($request);
    }
} 