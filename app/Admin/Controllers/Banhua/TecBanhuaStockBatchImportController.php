<?php

namespace App\Admin\Controllers\Banhua;

use App\Http\Controllers\Controller;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\Banhua\TecBanhuaSkuStockModel;
use App\Models\TecWarehouseModel;
use App\Models\Banhua\TecBanhuaLocationModel;
use App\Models\Banhua\BanhuaConstants;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Dcat\Admin\Admin;
use Maatwebsite\Excel\Facades\Excel;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use App\Models\Banhua\BanhuaStockImportBatchModel;
use App\Models\Banhua\BanhuaStockImportBatchDataModel;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use App\Admin\Repositories\Banhua\BanhuaStockImportBatchRepository;

class TecBanhuaStockBatchImportController extends Controller
{
    /**
     * 显示导入页面
     */
    public function index(Content $content)
    {
        return $content
            ->title('批量导入库存')
            ->description('从Excel导入多位置库存数据')
            ->body($this->form());
    }
    
    /**
     * 创建导入表单
     */
    protected function form()
    {
        // 预设变量
        $uploadUrl = $this->getUploadUrl();
        $importUrl = $this->getImportUrl();
        $previewEditUrl = $this->getPreviewEditUrl();
        $checkSessionUrl = $this->getCheckSessionUrl();
        $loadSheetDataUrl = $this->getLoadSheetDataUrl();
        $csrf = csrf_token();
        $adminUrl = admin_url();
        
        // 创建HTML元素
        $html = [];
        
        // 卡片容器
        $html[] = '<div class="card">';
        $html[] = '<div class="card-header"><h3 class="card-title">Excel批量导入库存</h3></div>';
        $html[] = '<div class="card-body">';
        
        // 表单
        $html[] = '<form id="stock-import-form" method="post" enctype="multipart/form-data">';
        $html[] = '<input type="hidden" name="_token" value="'.$csrf.'">';
        $html[] = '<div class="alert alert-info">';
        $html[] = '<p>请上传包含库存数据的Excel文件。文件应包含以下信息：</p>';
        $html[] = '<ul>';
        $html[] = '<li>SKU ID或编码</li>';
        $html[] = '<li>各个位置的库存数量</li>';
        $html[] = '</ul>';
        $html[] = '<p>系统将预读Excel文件，让您选择要导入的工作表。</p>';
        $html[] = '</div>';
        
        $html[] = '<div class="form-group">';
        $html[] = '<label for="excel_file">选择Excel文件</label>';
        $html[] = '<input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx,.xls">';
        $html[] = '<small class="form-text text-muted">支持.xlsx和.xls格式，文件大小不超过40MB</small>';
        $html[] = '</div>';
        
        $html[] = '<button type="submit" class="btn btn-primary">上传并预览</button>';
        $html[] = '</form>';
        
        // 预览容器
        $html[] = '<div id="preview-container" style="display:none; margin-top:20px;">';
        $html[] = '<div class="card">';
        $html[] = '<div class="card-header"><h4 class="card-title">Excel工作表预览</h4></div>';
        $html[] = '<div class="card-body">';
        $html[] = '<div id="sheet-selection"></div>';
        $html[] = '<div id="preview-data" style="margin-top:20px;"></div>';
        $html[] = '<button id="start-import" class="btn btn-success" style="margin-top:20px; display:none;">开始导入</button>';
        $html[] = '</div>';
        $html[] = '</div>';
        $html[] = '</div>';
        
        // 导入结果容器
        $html[] = '<div id="import-result" style="display:none; margin-top:20px;">';
        $html[] = '<div class="card">';
        $html[] = '<div class="card-header"><h4 class="card-title">导入结果</h4></div>';
        $html[] = '<div class="card-body">';
        $html[] = '<div id="result-content"></div>';
        $html[] = '</div>';
        $html[] = '</div>';
        $html[] = '</div>';
        
        $html[] = '</div>';
        $html[] = '</div>';
        
        // 将HTML元素连接成字符串
        $htmlContent = implode("\n", $html);
        
        // 添加脚本
        Admin::script(<<<SCRIPT
$(function() {
    // 定义URL和CSRF变量
    var uploadUrl = '{$uploadUrl}';
    var importUrl = '{$importUrl}';
    var previewEditUrl = '{$previewEditUrl}';
    var checkSessionUrl = '{$checkSessionUrl}';
    var loadSheetDataUrl = '{$loadSheetDataUrl}';
    var csrfToken = '{$csrf}';
    var adminUrl = '{$adminUrl}';
    
    function handleError(message) {
        Dcat.error(message || '操作失败');
        console.error('操作失败:', message);
    }
    
    $('#stock-import-form').on('submit', function(e) {
        e.preventDefault();
        try {
            var formData = new FormData(this);
            formData.append('_token', csrfToken);
            
            if ($('#excel_file').val() === '') {
                Dcat.error('请选择要上传的Excel文件');
                return;
            }
            
            // 检查文件大小
            var fileInput = document.getElementById('excel_file');
            if (fileInput.files.length > 0) {
                var fileSize = fileInput.files[0].size;
                var maxSize = 40 * 1024 * 1024; // 40MB
                if (fileSize > maxSize) {
                    Dcat.error('文件大小超过40MB限制，请选择更小的文件或拆分为多个文件');
                    return;
                }
            }
            
            Dcat.loading();
            $.ajax({
                url: uploadUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    Dcat.loading(false);
                    try {
                        if (response.status) {
                            $('#preview-container').show();
                            var sheets = response.data.sheets;
                            // 保存sheets数据到全局变量
                            window.uploadedSheets = sheets;
                            var sheetHtml = '<div class="form-group"><label>选择要导入的工作表</label><div class="sheet-radio-buttons">';
                            sheets.forEach(function(sheet, index) {
                                sheetHtml += '<div class="custom-control custom-radio">' +
                                    '<input type="radio" class="custom-control-input sheet-radio" id="sheet-' + index + '" name="sheet-selection" data-sheet="' + sheet.name + '" ' + (index === 0 ? 'checked' : '') + '>' +
                                    '<label class="custom-control-label" for="sheet-' + index + '">' + sheet.name + ' (' + sheet.rows + '行 x ' + sheet.cols + '列)</label>' +
                                '</div>';
                            });
                            sheetHtml += '</div></div>';
                            
                            // 添加仓库选择下拉框
                            if (response.data.warehouses) {
                                var warehousesHtml = '<div class="form-group">';
                                warehousesHtml += '<label for="preview_default_warehouse">默认仓库</label>';
                                warehousesHtml += '<select class="form-control" id="preview_default_warehouse">';
                                
                                var defaultWarehouseId = response.data.default_warehouse;
                                $.each(response.data.warehouses, function(id, name) {
                                    var selected = id == defaultWarehouseId ? 'selected' : '';
                                    warehousesHtml += '<option value="' + id + '" ' + selected + '>' + name + '</option>';
                                });
                                
                                warehousesHtml += '</select>';
                                warehousesHtml += '<small class="form-text text-muted">当Excel中的仓库无法识别时，将使用此仓库</small>';
                                warehousesHtml += '</div>';
                                
                                sheetHtml += warehousesHtml;
                            }
                            
                            $('#sheet-selection').html(sheetHtml);
                            window.sessionKey = response.data.session_key;
                            
                            // 如果有工作表，加载第一个工作表的数据
                            if (sheets.length > 0) {
                                loadSheetData(sheets[0].name);
                            }
                            
                            // 点击单选按钮时加载对应工作表数据
                            $('.sheet-radio').on('change', function() {
                                if ($(this).is(':checked')) {
                                    var sheetName = $(this).data('sheet');
                                    loadSheetData(sheetName);
                                }
                            });
                        } else {
                            handleError(response.message || '上传失败');
                        }
                    } catch (err) {
                        handleError('处理响应数据时出错: ' + err.message);
                    }
                },
                error: function(xhr) {
                    Dcat.loading(false);
                    var message = '上传失败';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    handleError(message);
                }
            });
        } catch (err) {
            handleError('提交表单时出错: ' + err.message);
        }
    });
    
    // 加载工作表数据的函数
    function loadSheetData(sheetName) {
        Dcat.loading();
        $.ajax({
            url: loadSheetDataUrl,
            type: 'GET',
            data: {
                session_key: window.sessionKey,
                sheet_name: sheetName
            },
            success: function(response) {
                Dcat.loading(false);
                if (response.status) {
                    var data = response.data;
                    showSheetPreview(sheetName, data);
                    $('#start-import').show();
                } else {
                    handleError(response.message || '加载工作表数据失败');
                }
            },
            error: function(xhr) {
                Dcat.loading(false);
                var message = '加载工作表数据失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                handleError(message);
            }
        });
    }
    
    function showSheetPreview(sheetName, data) {
        try {
            if (!data) {
                $('#preview-data').html('<div class="alert alert-warning">无法预览此工作表</div>');
                return;
            }
            
            var filteredHeaders1 = data.headers1 || [];
            var filteredHeaders2 = data.headers2 || [];
            var filteredRows = data.rows || [];
            var totalRows = filteredRows.length;
            
            // 先构建警告信息
            var infoHtml = '<div class="mt-3">';
            
            // 数据统计信息
            infoHtml += '<div class="alert alert-info">';
            infoHtml += '<strong>数据统计：</strong> 共 ' + totalRows + ' 行数据';
            infoHtml += '</div>';
            
            // 显示产品尺寸信息
            if (data.possible_sizes && data.possible_sizes.length > 0) {
                infoHtml += '<div class="alert alert-success">';
                infoHtml += '<strong>识别到的产品尺寸：</strong> ';
                
                // 使用对象键值进行尺寸去重
                var uniqueSizes = {};
                data.possible_sizes.forEach(function(size) {
                    uniqueSizes[size.size] = size;
                });
                
                // 将去重后的尺寸显示出来
                var isFirst = true;
                var sizeArray = [];
                
                for (var sizeKey in uniqueSizes) {
                    sizeArray.push(sizeKey);
                }
                
                // 按字母顺序排序尺寸，便于查看
                sizeArray.sort();
                
                // 显示排序后的尺寸
                sizeArray.forEach(function(sizeKey, index) {
                    if (index > 0) infoHtml += ', ';
                    infoHtml += sizeKey;
                });
                
                infoHtml += '</div>';
            } else {
                infoHtml += '<div class="alert alert-warning">';
                infoHtml += '<strong>警告：</strong> 无法识别产品尺寸，请确认工作表名称是否为有效的尺寸编号';
                infoHtml += '</div>';
            }
            
            // 显示产品-位置配对信息
            if (data.location_pairs && data.location_pairs.length > 0) {
                infoHtml += '<div class="alert alert-success">';
                infoHtml += '<strong>识别到的产品-位置配对：</strong> ';
                data.location_pairs.forEach(function(pair, index) {
                    if (index > 0) infoHtml += ', ';
                    infoHtml += pair.product_header + ' - ' + (filteredHeaders1[pair.location_col] || '位置列' + (pair.location_col + 1));
                });
                infoHtml += '</div>';
            } else {
                infoHtml += '<div class="alert alert-warning">';
                infoHtml += '<strong>警告：</strong> 无法识别产品-位置配对，请检查表头格式';
                infoHtml += '</div>';
            }
            
            infoHtml += '</div>';
            
            // 构建表格HTML - 不显示第一行表头，只显示第二行表头
            var tableHtml = '<div class="table-responsive"><table class="table table-bordered table-striped">';
                tableHtml += '<thead>';
            
            // 只显示第二行表头
                if (filteredHeaders2 && filteredHeaders2.length > 0) {
                    tableHtml += '<tr>';
                    filteredHeaders2.forEach(function(header2, index) {
                        tableHtml += '<th>' + (header2 || '') + '</th>';
                    });
                    tableHtml += '</tr>';
                }
                tableHtml += '</thead>';
            
            if (filteredRows.length > 0) {
                tableHtml += '<tbody>';
                filteredRows.forEach(function(row) {
                    tableHtml += '<tr>';
                    filteredHeaders1.forEach(function(header1, index) {
                        var key = header1 || 'col_' + (index + 1);
                        var value = row[key];
                        tableHtml += '<td>' + (value !== undefined && value !== null ? value : '') + '</td>';
                    });
                    tableHtml += '</tr>';
                });
                tableHtml += '</tbody>';
            }
            tableHtml += '</table></div>';
            
            // 将信息HTML和表格HTML结合，先显示信息，再显示表格
            $('#preview-data').html(infoHtml + tableHtml);
        } catch (err) {
            console.error('预览数据时出错:', err);
            $('#preview-data').html('<div class="alert alert-danger">预览数据时出错: ' + err.message + '</div>');
        }
    }
    
    // 点击开始导入按钮的处理
    $('#start-import').on('click', function() {
        try {
            var defaultWarehouse = $('#preview_default_warehouse').val();
            
            // 检查是否有sessionKey
            if (!window.sessionKey) {
                Dcat.error('会话密钥不存在，请重新上传文件');
                console.error('会话密钥不存在', window);
                return;
            }
            
            // 获取选中的工作表
            var selectedSheet = $('.sheet-radio:checked').data('sheet');
            
            if (!selectedSheet) {
                Dcat.error('请选择一个工作表进行导入');
                return;
            }
            
            // 构建URL，包含选中的工作表信息
            var url = previewEditUrl + '?session_key=' + window.sessionKey + '&selected_sheets=' + JSON.stringify([selectedSheet]);
            
            // 显示加载中
            Dcat.loading();
            
            // 验证是否成功保存了批次数据
            $.ajax({
                url: checkSessionUrl,
                type: 'GET',
                data: {
                    session_key: window.sessionKey,
                    selected_sheets: JSON.stringify([selectedSheet])
                },
                success: function(response) {
                    Dcat.loading(false);
                    if (response.status) {
                        // 成功保存了批次数据，跳转到预览编辑页面
                        window.location.href = url;
                    } else {
                        // 批次数据保存失败，提示错误
                        Dcat.error(response.message || '导入批次保存失败，请重新上传文件');
                    }
                },
                error: function(xhr) {
                    Dcat.loading(false);
                    var message = '验证会话失败';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    Dcat.error(message);
                    console.error('验证会话失败', xhr);
                }
            });
        } catch (err) {
            Dcat.loading(false);
            Dcat.error('处理导入请求时出错');
            console.error('处理导入请求时出错', err);
        }
    });
});
SCRIPT);
        
        // 创建Card对象并返回
        return new Card(null, $htmlContent);
    }
    
    /**
     * 处理文件上传并预览
     */
    public function upload(Request $request)
    {
        try {
            // 验证请求
            $request->validate([
                'excel_file' => 'required|file|mimes:xlsx,xls|max:40960', // 最大40MB
            ], [
                'excel_file.required' => '请选择要上传的Excel文件',
                'excel_file.file' => '上传的文件无效',
                'excel_file.mimes' => '请上传Excel格式的文件(.xlsx或.xls)',
                'excel_file.max' => '文件大小不能超过40MB',
            ]);
            
            // 设置更长的执行时间限制和内存限制，确保能处理超大Excel
            ini_set('memory_limit', '4G');  // 增加到4GB
            set_time_limit(1800);  // 增加到30分钟
            
            // 保存上传的文件
            $file = $request->file('excel_file');
            $path = $file->getRealPath();
            $extension = $file->getClientOriginalExtension();
            
            // 保存文件到临时目录
            $fileName = 'stock_import_' . md5(uniqid('', true)) . '.' . $extension;
            $tempPath = storage_path('app/temp');
            if (!file_exists($tempPath)) {
                mkdir($tempPath, 0755, true);
            }
            $filePath = $tempPath . '/' . $fileName;
            copy($path, $filePath);
            
            // 读取Excel文件
            $reader = IOFactory::createReader(ucfirst($extension));
            $reader->setReadDataOnly(true);
            
            // 添加读取大文件的优化
            if (method_exists($reader, 'setReadChunkSize')) {
                $reader->setReadChunkSize(1000);  // 设置每次读取的块大小
            }
            
            $spreadsheet = $reader->load($path);
            
            // 获取所有工作表的基本信息
            $sheets = [];
            $previewData = [];
            
            foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
                $sheetName = $worksheet->getTitle();
                $highestRow = $worksheet->getHighestRow();
                $highestColumn = $worksheet->getHighestColumn();
                $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
                
                // 只读取表头信息
                $headers1 = [];
                $headers2 = [];
                
                // 读取第一行和第二行表头
                for ($col = 1; $col <= $highestColumnIndex; $col++) {
                    $header1 = trim($worksheet->getCellByColumnAndRow($col, 1)->getValue() ?? '');
                    $header2 = trim($worksheet->getCellByColumnAndRow($col, 2)->getValue() ?? '');
                    
                    // 保存所有表头，即使第一行是空的
                    $headers1[] = $header1;
                    $headers2[] = $header2;
                }
                
                // 更新sheets数组
                $sheets[] = [
                    'name' => $sheetName,
                    'rows' => $highestRow - 2, // 减去表头的2行
                    'cols' => $highestColumnIndex // 使用实际的列数
                ];
                
                // 只保存表头信息到previewData
                $previewData[$sheetName] = [
                    'headers1' => $headers1,
                    'headers2' => $headers2,
                    'total_columns' => $highestColumnIndex, // 添加总列数信息
                    'rows' => [] // 不加载行数据
                ];
            }
            
            // 获取所有仓库
            $warehouses = TecWarehouseModel::select('id', 'name')->get();
            
            // 选择默认仓库（优先选择带"主"或"中心"字样的仓库）
            $defaultWarehouse = null;
            foreach ($warehouses as $warehouse) {
                if (strpos($warehouse->name, '主') !== false || strpos($warehouse->name, '中心') !== false) {
                    $defaultWarehouse = $warehouse->id;
                    break;
                }
            }
            if (!$defaultWarehouse && $warehouses->isNotEmpty()) {
                $defaultWarehouse = $warehouses->first()->id;
            }
            
            // 生成会话密钥
            $sessionKey = 'stock_import_' . md5(uniqid('', true));
            
            // 将基本信息保存到数据库中
            DB::beginTransaction();
            try {
                // 创建导入批次
                $importBatch = BanhuaStockImportBatchModel::createBatch($sessionKey);
                
                // 添加工作表信息和文件路径
                $importBatch->update([
                    'sheet_info' => $sheets,
                    'file_path' => $filePath
                ]);
                
                DB::commit();
                
                // 返回预览数据
                return response()->json([
                    'status' => true,
                    'message' => '文件上传成功',
                    'data' => [
                        'session_key' => $sessionKey,
                        'sheets' => $sheets,
                        'previews' => $previewData,
                        'warehouses' => $warehouses->pluck('name', 'id')->toArray(),
                        'default_warehouse' => $defaultWarehouse
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
            
        } catch (\Exception $e) {
            Log::error('处理Excel文件失败: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            return response()->json([
                'status' => false,
                'message' => '处理Excel文件失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 加载单个工作表数据
     */
    public function loadSheetData(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $sheetName = $request->input('sheet_name');
            
            if (!$sessionKey || !$sheetName) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的请求参数'
                ]);
            }
            
            // 获取导入批次
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            if (!$importBatch) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到导入批次数据'
                ]);
            }
            
            // 获取文件路径
            $filePath = $importBatch->file_path;
            if (!file_exists($filePath)) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到上传的文件'
                ]);
            }
            
            // 读取Excel文件
            $extension = pathinfo($filePath, PATHINFO_EXTENSION);
            $reader = IOFactory::createReader(ucfirst($extension));
            $reader->setReadDataOnly(true);
            
            // 优化读取大文件
            if (method_exists($reader, 'setReadChunkSize')) {
                $reader->setReadChunkSize(1000);
            }
            
            // 只加载指定的工作表
            if (method_exists($reader, 'setLoadSheetsOnly')) {
                $reader->setLoadSheetsOnly($sheetName);
            }
            
            $spreadsheet = $reader->load($filePath);
            $worksheet = $spreadsheet->getSheetByName($sheetName);
            
            if (!$worksheet) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到指定的工作表'
                ]);
            }
            
            // 读取表头和数据
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
            
            $headers1 = [];
            $headers2 = [];
            $data = [];
            
            // 读取第一行和第二行表头
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $header1 = trim($worksheet->getCellByColumnAndRow($col, 1)->getValue() ?? '');
                $header2 = trim($worksheet->getCellByColumnAndRow($col, 2)->getValue() ?? '');
                
                $headers1[] = $header1;
                $headers2[] = $header2;
            }
            
            // 读取数据行
            for ($row = 3; $row <= $highestRow; $row++) {
                $rowData = [];
                $hasData = false;
                
                for ($col = 1; $col <= $highestColumnIndex; $col++) {
                    $colValue = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                    
                    $headerKey = !empty($headers1[$col-1]) ? $headers1[$col-1] : 'col_' . $col;
                    $rowData[$headerKey] = $colValue;
                    
                    if ($colValue !== null && $colValue !== '') {
                        $hasData = true;
                    }
                }
                
                if ($hasData) {
                    $data[] = $rowData;
                }
            }
            
            // 识别可能的尺寸
            $possibleSizes = $this->identifyProductSizes($sheetName, null);
            
            // 分析表头映射
            $headerMappings = $this->analyzeHeaders($headers1);
            
            // 识别产品列和位置列的配对
            $locationPairs = $this->identifyProductLocationPairs($headers1, $headers2);
            
            // 获取需要忽略的列
            $columnsToIgnore = BanhuaConstants::getColumnsToIgnore();
            
            // 记录过滤前的表头数据
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('过滤前的表头数据', [
                'sheet_name' => $sheetName,
                'headers1' => $headers1,
                'headers2' => $headers2
            ]);
            
            // 创建过滤后的表头
            $filteredHeaders1 = [];
            $filteredHeaders2 = [];
            $columnIndices = [];
            
            // 过滤表头，排除忽略的列
            foreach ($headers1 as $index => $header) {
                // 获取第一行和第二行的表头
                $header1 = trim($header);
                $header2 = isset($headers2[$index]) ? trim($headers2[$index]) : '';
                
                // 移除空格并转换为小写进行比较，提高匹配精度
                $normalizedHeader1 = strtolower($header1);
                $normalizedHeader2 = strtolower($header2);
                
                $shouldIgnore = false;
                
                // 记录每一列的检查过程
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('检查列', [
                    'index' => $index,
                    'header1' => $header1,
                    'header2' => $header2,
                    'normalized_header1' => $normalizedHeader1,
                    'normalized_header2' => $normalizedHeader2
                ]);
                
                foreach ($columnsToIgnore as $ignoreHeader) {
                    $normalizedIgnoreHeader = trim(strtolower($ignoreHeader));
                    // 使用严格匹配，检查第一行和第二行表头
                    if ($normalizedHeader1 === $normalizedIgnoreHeader || 
                        $normalizedHeader2 === $normalizedIgnoreHeader ||
                        strpos($normalizedHeader1, $normalizedIgnoreHeader) !== false ||
                        strpos($normalizedHeader2, $normalizedIgnoreHeader) !== false) {
                        $shouldIgnore = true;
                        // 记录忽略决策
                        \Illuminate\Support\Facades\Log::channel("sku_import")->info('忽略列 - 精确匹配', [
                            'index' => $index,
                            'header1' => $header1,
                            'header2' => $header2,
                            'matched_with' => $ignoreHeader
                        ]);
                    break;
                }
            }
            
                if (!$shouldIgnore) {
                    $filteredHeaders1[] = $header;
                    $filteredHeaders2[] = isset($headers2[$index]) ? $headers2[$index] : '';
                    $columnIndices[] = $index;
                }
            }
            
            // 记录过滤后的表头数据
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('过滤后的表头数据', [
                'sheet_name' => $sheetName,
                'filtered_headers1' => $filteredHeaders1,
                'filtered_headers2' => $filteredHeaders2,
                'column_indices' => $columnIndices
            ]);
            
            // 过滤数据行
            $filteredRowData = [];
            foreach ($data as $rowIndex => $row) {
                $filteredRow = [];
                foreach ($columnIndices as $i) {
                    $key = !empty($headers1[$i]) ? $headers1[$i] : 'col_' . ($i + 1);
                    $filteredRow[$key] = $row[$key] ?? '';
                }
                $filteredRowData[$rowIndex] = $filteredRow;
            }
            
            return response()->json([
                'status' => true,
                'message' => '加载工作表数据成功',
                'data' => [
                    'headers1' => $filteredHeaders1,
                    'headers2' => $filteredHeaders2,
                    'rows' => $filteredRowData,
                    'location_pairs' => $locationPairs,
                    'possible_sizes' => $possibleSizes,
                    'header_mappings' => $headerMappings
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('加载工作表数据失败: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            return response()->json([
                'status' => false,
                'message' => '加载工作表数据失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 检查会话是否有效
     */
    public function checkSession(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $selectedSheets = json_decode($request->input('selected_sheets', '[]'), true);
            
            if (!$sessionKey) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的会话标识'
                ]);
            }
            
            // 记录日志
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('检查会话', [
                'session_key' => $sessionKey,
                'selected_sheets' => $selectedSheets
            ]);
            
            // 检查是否存在批次数据
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            
            if (!$importBatch) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到导入批次数据'
                ]);
            }
            
            // 保存选中的工作表信息到批次
            if (!empty($selectedSheets)) {
                $importBatch->update([
                    'selected_sheets' => json_encode($selectedSheets)
                ]);
                
                // 在这里处理选中的工作表数据
                $sheetName = $selectedSheets[0]; // 假设只选择了一个工作表
                
                // 加载并处理工作表数据
                $filePath = $importBatch->file_path;
                if (file_exists($filePath)) {
                    $extension = pathinfo($filePath, PATHINFO_EXTENSION);
                    $reader = IOFactory::createReader(ucfirst($extension));
                    $reader->setReadDataOnly(true);
                    
                    if (method_exists($reader, 'setLoadSheetsOnly')) {
                        $reader->setLoadSheetsOnly($sheetName);
                    }
                    
                    $spreadsheet = $reader->load($filePath);
                    $worksheet = $spreadsheet->getSheetByName($sheetName);
                    
                    if ($worksheet) {
                        // 读取表头和数据
                        $highestRow = $worksheet->getHighestRow();
                        $highestColumn = $worksheet->getHighestColumn();
                        $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
                        
                        $headers1 = [];
                        $headers2 = [];
                        $data = [];
                        
                        // 读取第一行和第二行表头
                        for ($col = 1; $col <= $highestColumnIndex; $col++) {
                            $header1 = trim($worksheet->getCellByColumnAndRow($col, 1)->getValue() ?? '');
                            $header2 = trim($worksheet->getCellByColumnAndRow($col, 2)->getValue() ?? '');
                            
                            $headers1[] = $header1;
                            $headers2[] = $header2;
                        }
                        
                        // 读取数据行
                        for ($row = 3; $row <= $highestRow; $row++) {
                            $rowData = [];
                            $hasData = false;
                            
                            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                                $colValue = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                                
                                $headerKey = !empty($headers1[$col-1]) ? $headers1[$col-1] : 'col_' . $col;
                                $rowData[$headerKey] = $colValue;
                                
                                if ($colValue !== null && $colValue !== '') {
                                    $hasData = true;
                                }
                            }
                            
                            if ($hasData) {
                                $data[] = $rowData;
                            }
                        }
                        
                        // 识别可能的尺寸
                        $possibleSizes = $this->identifyProductSizes($sheetName, null);
                        
                        // 如果无法识别出尺寸，说明是不相关的sheet，跳过
                        if (!empty($possibleSizes)) {
                            // 分析表头映射
                            $headerMappings = $this->analyzeHeaders($headers1);
                            
                            // 识别产品列和位置列的配对
                            $locationPairs = $this->identifyProductLocationPairs($headers1, $headers2);
                            
                            // 如果无法识别产品-位置配对，也认为是不相关的sheet，跳过
                            if (!empty($locationPairs)) {
                                // 保存工作表数据到数据库
                                BanhuaStockImportBatchDataModel::saveSheetData(
                                    $importBatch->id,
                                    $sheetName,
                                    [
                                        'headers1' => $headers1,
                                        'headers2' => $headers2
                                    ],
                                    $data,
                                    $locationPairs,
                                    $possibleSizes,
                                    $headerMappings
                                );
                            }
                        }
                    }
                }
            }
            
            // 检查是否有批次数据行
            $query = $importBatch->batchData();
            
            // 如果指定了要加载的工作表，则只检查这些工作表的数据
            if (!empty($selectedSheets)) {
                $query->whereIn('sheet_name', $selectedSheets);
            }
            
            $hasBatchData = $query->exists();
            
            return response()->json([
                'status' => true,
                'message' => '会话有效',
                'data' => [
                    'import_batch_id' => $importBatch->id,
                    'batch_data_count' => $query->count(),
                    'selected_sheets' => $selectedSheets,
                    'has_batch_data' => $hasBatchData
                ]
            ]);
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('检查会话失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '检查会话失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取检查会话URL
     */
    protected function getCheckSessionUrl()
    {
        return admin_url('banhua/stock-batch-import/check-session');
    }
    
    /**
     * 获取加载工作表数据URL
     */
    protected function getLoadSheetDataUrl()
    {
        return admin_url('banhua/stock-batch-import/load-sheet-data');
    }
    
    /**
     * 获取上传URL
     */
    protected function getUploadUrl()
    {
        return admin_url('banhua/stock-batch-import/upload');
    }
    
    /**
     * 获取导入URL
     */
    protected function getImportUrl()
    {
        return admin_url('banhua/stock-batch-import/import');
    }
    
    /**
     * 获取导入预览URL
     */
    protected function getImportPreviewUrl()
    {
        return admin_url('banhua/stock/import-preview');
    }
    
    /**
     * 获取预览编辑URL
     */
    protected function getPreviewEditUrl()
    {
        return admin_url('banhua/stock/preview-edit');
    }
    
    /**
     * 获取保存编辑URL
     */
    protected function getSaveEditUrl()
    {
        return admin_url('banhua/stock/save-edit');
    }
    
    /**
     * 获取最终导入URL
     */
    protected function getFinalImportUrl()
    {
        return admin_url('banhua/stock/final-import');
    }
    
    /**
     * 获取清理数据URL
     */
    protected function getCleanupDataUrl()
    {
        return admin_url('banhua/stock-batch-import/cleanup-import-data');
    }
    
    /**
     * 显示预览和编辑页面
     */
    public function previewEdit(Content $content, Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $selectedSheets = $request->input('selected_sheets');
            
            if (is_string($selectedSheets)) {
                $selectedSheets = json_decode($selectedSheets, true);
            }
            
            // 获取导入批次数据
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            
            if (!$importBatch) {
                admin_error('错误', '找不到导入批次数据');
                return redirect(admin_url('banhua/stock-batch-import'));
            }
            
            // 加载批次数据
            $batchDataQuery = $importBatch->batchData();
            if (!empty($selectedSheets)) {
                $batchDataQuery->whereIn('sheet_name', $selectedSheets);
            }
            $batchData = $batchDataQuery->get();
            
            // 准备预览数据
            $previewData = [];
            $missingLocations = [];
            $filteredSheetInfo = [];
            
            // 获取需要忽略的列
            $columnsToIgnore = BanhuaConstants::getColumnsToIgnore();
            
            // 用于收集所有位置名称
            $allLocationNames = [];
            $locationNameToPartsMap = [];
            
            // 第一次遍历：收集所有位置名称
            foreach ($batchData as $data) {
                $sheetName = $data->sheet_name;
                $headers = $data->headers;
                $rowData = $data->row_data;
                
                // 识别产品列和位置列的配对
                $locationPairs = $this->identifyProductLocationPairs($headers['headers1'], $headers['headers2']);
                
                // 收集所有位置名称
                foreach ($rowData as $row) {
                    foreach ($locationPairs as $pair) {
                        $locationColIndex = $pair['location_col'];
                        if (isset($headers['headers1'][$locationColIndex])) {
                            $locationHeader = $headers['headers1'][$locationColIndex];
                            $locationKey = !empty($locationHeader) ? $locationHeader : 'col_' . ($locationColIndex + 1);
                            
                            if (!empty($row[$locationKey])) {
                                $locationName = $row[$locationKey];
                                if (!in_array($locationName, $allLocationNames)) {
                                    $allLocationNames[] = $locationName;
                                    // 解析位置名称并存储结果
                                    $locationNameToPartsMap[$locationName] = $this->parseLocationName($locationName);
                                }
                            }
                        }
                    }
                }
            }
            
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('收集到的所有位置名称', [
                'count' => count($allLocationNames),
                'names' => $allLocationNames
            ]);
            
                            // 批量查询位置信息 - 优化版
                $existingLocationsMap = [];
                
                if (!empty($allLocationNames)) {
                    // 收集所有位置的区域-架位-层级组合用于批量查询
                    $locationKeyConditions = [];
                    $locationNamesByKey = []; // 用于记录每个键对应的原始位置名称
                    
                    foreach ($locationNameToPartsMap as $locationName => $parts) {
                        $key = $parts['area'] . '-' . ($parts['shelf'] ?: '') . '-' . ($parts['level'] ?: '');
                        
                        $locationKeyConditions[$key] = [
                            'area_number' => $parts['area'],
                            'shelf_number' => $parts['shelf'] ?: '',
                            'level_number' => $parts['level'] ?: ''
                        ];
                        
                        // 记录这个键对应的原始位置名称
                        if (!isset($locationNamesByKey[$key])) {
                            $locationNamesByKey[$key] = [];
                        }
                        $locationNamesByKey[$key][] = $locationName;
                    }
                    
                    // 记录查询前的日志
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('位置查询条件', [
                        'total_locations' => count($allLocationNames),
                        'unique_locations' => count($locationKeyConditions),
                        'conditions' => $locationKeyConditions
                    ]);
                    
                    // 一次性查询所有可能匹配的位置
                    $existingLocations = TecBanhuaLocationModel::where(function($query) use ($locationKeyConditions) {
                        $first = true;
                        foreach ($locationKeyConditions as $condition) {
                            $method = $first ? 'where' : 'orWhere';
                            $first = false;
                            
                            $query->$method(function($q) use ($condition) {
                                $q->where('area_number', $condition['area_number'])
                                  ->where('shelf_number', $condition['shelf_number'])
                                  ->where('level_number', $condition['level_number']);
                            });
                        }
                    })->get();
                    
                    // 构建优化的位置映射 - 使用组合键直接索引
                    foreach ($existingLocations as $location) {
                        $key = $location->area_number . '-' . $location->shelf_number . '-' . $location->level_number;
                        $existingLocationsMap[$key] = $location;
                        
                        // 记录找到的位置
                        \Illuminate\Support\Facades\Log::channel("sku_import")->info('找到位置', [
                            'key' => $key,
                            'location_id' => $location->id,
                            'warehouse_id' => $location->warehouse_id,
                            'original_names' => $locationNamesByKey[$key] ?? []
                        ]);
                    }
                    
                    // 记录未找到的位置
                    $missingKeys = array_diff(array_keys($locationKeyConditions), array_keys($existingLocationsMap));
                    foreach ($missingKeys as $missingKey) {
                        \Illuminate\Support\Facades\Log::channel("sku_import")->warning('未找到位置', [
                            'key' => $missingKey,
                            'condition' => $locationKeyConditions[$missingKey],
                            'original_names' => $locationNamesByKey[$missingKey] ?? []
                        ]);
                    }
                    
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('优化批量查询位置信息', [
                        'total_locations' => count($allLocationNames),
                        'unique_locations' => count($locationKeyConditions),
                        'existing_locations' => count($existingLocations),
                        'missing_locations' => count($missingKeys),
                        'memory_usage' => round(memory_get_usage()/1024/1024, 2) . 'MB'
                    ]);
                }
            
            // 第二次遍历：处理预览数据
            foreach ($batchData as $data) {
                $sheetName = $data->sheet_name;
                $headers = $data->headers;
                
                // 创建过滤后的表头
                $filteredHeaders1 = [];
                $filteredHeaders2 = [];
                $columnIndices = [];
                
                // 过滤表头，排除忽略的列
                foreach ($headers['headers1'] as $index => $header) {
                    // 获取第一行和第二行的表头
                    $header1 = trim($header);
                    $header2 = isset($headers['headers2'][$index]) ? trim($headers['headers2'][$index]) : '';
                    
                    // 移除空格并转换为小写进行比较，提高匹配精度
                    $normalizedHeader1 = strtolower($header1);
                    $normalizedHeader2 = strtolower($header2);
                    
                    $shouldIgnore = false;
                    
                    foreach ($columnsToIgnore as $ignoreHeader) {
                        $normalizedIgnoreHeader = trim(strtolower($ignoreHeader));
                        // 使用严格匹配，检查第一行和第二行表头
                        if ($normalizedHeader1 === $normalizedIgnoreHeader || 
                            $normalizedHeader2 === $normalizedIgnoreHeader ||
                            strpos($normalizedHeader1, $normalizedIgnoreHeader) !== false ||
                            strpos($normalizedHeader2, $normalizedIgnoreHeader) !== false) {
                            $shouldIgnore = true;
                        break;
                    }
                }
                    
                    if (!$shouldIgnore) {
                        $filteredHeaders1[] = $header;
                        $filteredHeaders2[] = isset($headers['headers2'][$index]) ? $headers['headers2'][$index] : '';
                        $columnIndices[] = $index;
                    }
                }
                
                // 过滤数据行
                $filteredRowData = [];
                foreach ($data->row_data as $rowIndex => $row) {
                    $filteredRow = [];
                    foreach ($columnIndices as $i) {
                        $key = !empty($headers['headers1'][$i]) ? $headers['headers1'][$i] : 'col_' . ($i + 1);
                        $filteredRow[$key] = $row[$key] ?? '';
                    }
                    $filteredRowData[$rowIndex] = $filteredRow;
                }
                
                // 修改后的headers
                $headers['headers1'] = $filteredHeaders1;
                $headers['headers2'] = $filteredHeaders2;
                
                // 识别可能的尺寸
                $possibleSizes = $this->identifyProductSizes($sheetName, null);
                
                // 分析表头映射
                $headerMappings = $this->analyzeHeaders($filteredHeaders1);
                
                // 识别产品列和位置列的配对
                $locationPairs = $this->identifyProductLocationPairs($filteredHeaders1, $filteredHeaders2);
                
                // 检查库位是否存在
                foreach ($filteredRowData as $row) {
                    // 针对识别出的产品-位置配对检查库位
                    foreach ($locationPairs as $pair) {
                        $locationColIndex = $pair['location_col'];
                        if (isset($filteredHeaders1[$locationColIndex])) {
                            $locationHeader = $filteredHeaders1[$locationColIndex];
                            $locationKey = !empty($locationHeader) ? $locationHeader : 'col_' . ($locationColIndex + 1);
                            
                            if (!empty($row[$locationKey])) {
                                $locationName = $row[$locationKey];
                                // 使用缓存的解析结果
                                $parts = $locationNameToPartsMap[$locationName] ?? $this->parseLocationName($locationName);
                                // 构建查询键
                                $locationKey = $parts['area'] . '-' . $parts['shelf'] . '-' . $parts['level'];
                                
                                // 检查这个位置是否存在
                                $exists = isset($existingLocationsMap[$locationKey]);
                                
                                if (!$exists) {
                                    if (!isset($missingLocations[$locationName])) {
                                        $missingLocations[$locationName] = [
                                            'occurrences' => 1,
                                            'parts' => $parts
                                        ];
                                    } else {
                                        $missingLocations[$locationName]['occurrences']++;
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 准备工作表信息
                $sheetDetails = [
                    'name' => $sheetName,
                    'display_name' => $sheetName  // 确保传递正确的工作表名称用于显示
                ];
                
                // 添加尺寸信息
                if (!empty($possibleSizes)) {
                    $sheetDetails['possible_sizes'] = $possibleSizes;
                }
                
                // 添加表头映射信息
                if (!empty($headerMappings)) {
                    $sheetDetails['header_mappings'] = $headerMappings;
                }
                
                // 添加产品-位置配对信息
                if (!empty($locationPairs)) {
                    $sheetDetails['location_pairs'] = $locationPairs;
                }
                
                $filteredSheetInfo[] = $sheetDetails;
                
                $previewData[$sheetName] = [
                    'headers1' => $filteredHeaders1,
                    'headers2' => $filteredHeaders2,
                    'rows' => $filteredRowData,
                    'location_pairs' => $locationPairs,
                    'possible_sizes' => $possibleSizes,
                    'header_mappings' => $headerMappings
                ];
            }

            // 获取所有仓库
            $warehouses = TecWarehouseModel::select('id', 'name')->get();
            
            // 选择默认仓库（优先选择带"主"或"中心"字样的仓库）
            $defaultWarehouse = null;
            foreach ($warehouses as $warehouse) {
                if (strpos($warehouse->name, '主') !== false || strpos($warehouse->name, '中心') !== false) {
                    $defaultWarehouse = $warehouse->id;
                    break;
                }
            }
            if (!$defaultWarehouse && $warehouses->isNotEmpty()) {
                $defaultWarehouse = $warehouses->first()->id;
            }

            // 记录缺失位置统计
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('缺失位置统计', [
                'missing_count' => count($missingLocations),
                'missing_locations' => array_keys($missingLocations)
            ]);

            // 准备视图数据
            $viewData = [
                'sessionKey' => $sessionKey,
                'importBatchId' => $importBatch->id,
                'previewData' => $previewData,
                'missingLocations' => $missingLocations,
                'warehouses' => $warehouses,
                'defaultWarehouse' => $defaultWarehouse,
                'sheetInfo' => $filteredSheetInfo,
                'selectedSheets' => $selectedSheets,
                'saveEditUrl' => $this->getSaveEditUrl(),
                'finalImportUrl' => $this->getFinalImportUrl(),
                'returnUrl' => $this->getCleanupDataUrl()
            ];

            // 记录最终传递给视图的数据
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('最终传递给视图的数据', [
                'sheet_count' => count($previewData),
                'sheet_names' => array_keys($previewData)
            ]);

            return $content
                ->title('预览和编辑导入数据')
                ->description('检查并编辑要导入的数据')
                ->body(view('admin.banhua.stock_import_preview_edit', $viewData));

        } catch (\Exception $e) {
            Log::error('显示预览页面失败: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            admin_error('错误', '显示预览页面失败: ' . $e->getMessage());
            return redirect(admin_url('banhua/stock-batch-import'));
        }
    }
    
    /**
     * 保存编辑后的数据
     */
    public function saveEdit(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $importBatchId = $request->input('import_batch_id');
            $modifiedCells = json_decode($request->input('modified_cells'), true);

            if (!$sessionKey || !$importBatchId || !$modifiedCells) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的请求参数'
                ]);
            }
            
            // 记录前端提交的修改数据
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('保存编辑数据', [
                'session_key' => $sessionKey,
                'import_batch_id' => $importBatchId,
                'modified_cells_count' => count($modifiedCells),
                'modified_cells_sample' => array_slice($modifiedCells, 0, 1, true) // 记录第一个工作表的修改样本
            ]);

            // 获取导入批次
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            if (!$importBatch || $importBatch->id != $importBatchId) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到导入批次数据'
                ]);
            }

            DB::beginTransaction();
            try {
                // 更新每个工作表的修改数据
                foreach ($modifiedCells as $sheetName => $rowUpdates) {
                    BanhuaStockImportBatchDataModel::updateCells(
                        $importBatchId,
                        $sheetName,
                        $rowUpdates
                    );
                }
                
                DB::commit();
                return response()->json([
                    'status' => true,
                    'message' => '保存成功'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('保存编辑数据时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '保存失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 执行最终导入
     */
    public function finalImport(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $importBatchId = $request->input('import_batch_id');
            $duplicateStrategy = $request->input('duplicate_strategy', 'skip');
            $defaultWarehouse = $request->input('default_warehouse');
            $selectedRows = json_decode($request->input('selected_rows', '{}'), true);
            
            // 获取前端提交的完整表格数据
            $tableData = json_decode($request->input('table_data', '{}'), true);
            
            // 记录前端提交的数据
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('前端提交的表格数据', [
                'session_key' => $sessionKey,
                'import_batch_id' => $importBatchId,
                'selected_rows_count' => count($selectedRows),
                'table_data_sheets_count' => count($tableData),
                'table_data_sample' => array_slice($tableData, 0, 1, true) // 记录第一个工作表的数据样本
            ]);

            if (!$sessionKey || !$importBatchId) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的请求参数'
                ]);
            }

            // 获取导入批次
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            if (!$importBatch || $importBatch->id != $importBatchId) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到导入批次数据'
                ]);
            }
            
            // 加载批次数据（仅用于获取表头信息和产品位置配对）
            $batchData = $importBatch->batchData()->get();

            // 用于收集所有位置名称和预处理数据
            $allLocationNames = [];
            $locationNameToPartsMap = [];
            $allSheetData = [];
            
            // 第一步：从批次数据中获取表头信息和产品位置配对
            $headersBySheet = [];
            $locationPairsBySheet = [];
            $possibleSizesBySheet = [];
            
            foreach ($batchData as $data) {
                $sheetName = $data->sheet_name;
                $headers = $data->headers;
                
                // 如果没有选择这个工作表的任何行，跳过
                if (!isset($selectedRows[$sheetName]) || empty($selectedRows[$sheetName])) {
                    continue;
                }
                
                // 保存表头信息
                $headersBySheet[$sheetName] = $headers;
                
                // 识别产品列和位置列的配对
                $locationPairs = $this->identifyProductLocationPairs($headers['headers1'], $headers['headers2']);
                $locationPairsBySheet[$sheetName] = $locationPairs;
                
                // 识别可能的尺寸
                $possibleSizes = $this->identifyProductSizes($sheetName, null);
                $possibleSizesBySheet[$sheetName] = $possibleSizes;
            }
            
            // 第二步：处理前端提交的表格数据 - 确保使用前端编辑过的数据
            foreach ($tableData as $sheetName => $sheetRows) {
                // 如果没有这个工作表的表头信息，跳过
                if (!isset($headersBySheet[$sheetName])) {
                    continue;
                }
                
                $headers = $headersBySheet[$sheetName];
                $locationPairs = $locationPairsBySheet[$sheetName] ?? [];
                $possibleSizes = $possibleSizesBySheet[$sheetName] ?? [];
                
                // 收集所有位置名称
                foreach ($sheetRows as $rowIndex => $rowData) {
                    // 跳过未选中的行
                    if (!isset($selectedRows[$sheetName]) || !in_array($rowIndex, $selectedRows[$sheetName])) {
                        continue;
                    }
                    
                    foreach ($locationPairs as $pair) {
                        $locationColIndex = $pair['location_col'];
                        if (isset($headers['headers1'][$locationColIndex])) {
                            $locationHeader = $headers['headers1'][$locationColIndex];
                            $locationKey = !empty($locationHeader) ? $locationHeader : 'col_' . ($locationColIndex + 1);
                            
                            if (!empty($rowData[$locationKey])) {
                                $locationName = $rowData[$locationKey];
                                if (!in_array($locationName, $allLocationNames)) {
                                    $allLocationNames[] = $locationName;
                                    // 解析位置名称并存储结果
                                    $locationNameToPartsMap[$locationName] = $this->parseLocationName($locationName);
                                }
                            }
                        }
                    }
                }
                
                // 存储预处理数据
                $allSheetData[$sheetName] = [
                    'headers1' => $headers['headers1'],
                    'headers2' => $headers['headers2'],
                    'rowData' => $sheetRows, // 使用前端提交的表格数据
                    'locationPairs' => $locationPairs,
                    'possibleSizes' => $possibleSizes
                ];
            }
            
            // 批量查询位置信息 - 优化版
            $locationsMap = [];
            $locationKeyMap = [];
            
            if (!empty($allLocationNames)) {
                // 构建查询条件并准备键映射，避免重复查询
                $locationKeyConditions = [];
                foreach ($locationNameToPartsMap as $locationName => $parts) {
                    $key = $parts['area'] . '-' . ($parts['shelf'] ?: '') . '-' . ($parts['level'] ?: '');
                    $locationKeyMap[$locationName] = $key;
                    $locationKeyConditions[$key] = [
                        'area_number' => $parts['area'],
                        'shelf_number' => $parts['shelf'] ?: '',
                        'level_number' => $parts['level'] ?: ''
                    ];
                }
                
                // 一次性查询所有可能匹配的位置
                $existingLocations = TecBanhuaLocationModel::where(function($query) use ($locationKeyConditions) {
                    foreach ($locationKeyConditions as $index => $condition) {
                        $method = $index === 0 ? 'where' : 'orWhere';
                        $query->$method(function($q) use ($condition) {
                            $q->where('area_number', $condition['area_number'])
                              ->where('shelf_number', $condition['shelf_number'])
                              ->where('level_number', $condition['level_number']);
                        });
                    }
                })->get();
                
                // 构建优化的位置映射 - 同时按位置名称和组合键索引
                $locationsByKey = [];
                foreach ($existingLocations as $location) {
                    $key = $location->area_number . '-' . $location->shelf_number . '-' . $location->level_number;
                    $locationsByKey[$key] = $location;
                }
                
                // 将位置映射到原始位置名称
                foreach ($allLocationNames as $locationName) {
                    $key = $locationKeyMap[$locationName];
                    if (isset($locationsByKey[$key])) {
                        $locationsMap[$locationName] = $locationsByKey[$key];
                    }
                }
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('导入预处理 - 优化批量查询位置信息', [
                    'total_locations' => count($allLocationNames),
                    'unique_locations' => count($locationKeyConditions),
                    'found_locations' => count($locationsMap),
                    'missing_locations' => count($allLocationNames) - count($locationsMap),
                    'memory_usage' => round(memory_get_usage()/1024/1024, 2) . 'MB'
                ]);
            }

            DB::beginTransaction();
            try {
                $stats = [];
                $errors = [];
                $totalSuccess = 0;
                $totalFailed = 0;
                $totalSkipped = 0;

                // 处理每个工作表的数据
                foreach ($allSheetData as $sheetName => $sheetData) {
                    $rowData = $sheetData['rowData'];
                    $filteredHeaders1 = $sheetData['headers1'];
                    $filteredHeaders2 = $sheetData['headers2'];
                    $locationPairs = $sheetData['locationPairs'];
                    $possibleSizes = $sheetData['possibleSizes'];
                    
                    // 为每个工作表初始化统计信息
                    $stats[$sheetName] = [
                        'total' => count($rowData),
                        'success' => 0,
                        'failed' => 0,
                        'skipped' => 0
                    ];
                    
                    // 处理每一行数据 - 仅处理选中的行
                    foreach ($rowData as $rowIndex => $row) {
                        // 跳过未选中的行
                        if (!isset($selectedRows[$sheetName]) || !in_array($rowIndex, $selectedRows[$sheetName])) {
                            continue;
                        }
                        
                        try {
                            // 处理导入行
                            $result = $this->processImportRow(
                                $sheetName, 
                                $row, 
                                $filteredHeaders1, 
                                $filteredHeaders2, 
                                $locationPairs, 
                                $possibleSizes, 
                                $duplicateStrategy, 
                                $defaultWarehouse,
                                $locationsMap,
                                $locationNameToPartsMap
                            );
                            
                            // 更新统计信息
                            $stats[$sheetName]['success'] += $result['success'];
                            $stats[$sheetName]['failed'] += $result['failed'];
                            $stats[$sheetName]['skipped'] += $result['skipped'];
                            
                            $totalSuccess += $result['success'];
                            $totalFailed += $result['failed'];
                            $totalSkipped += $result['skipped'];
                            
                            // 记录错误信息
                            foreach ($result['errors'] as $error) {
                                $errors[] = [
                                    'sheet' => $sheetName,
                                    'row' => $rowIndex + 1,
                                    'message' => $error
                                ];
                            }
                        } catch (\Exception $e) {
                            Log::error('处理行数据时出错: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
                            $stats[$sheetName]['failed']++;
                            $totalFailed++;
                            $errors[] = [
                                'sheet' => $sheetName,
                                'row' => $rowIndex + 1,
                                'message' => '处理数据时出错: ' . $e->getMessage()
                            ];
                        }
                    }
                }
                
                // 处理库存队列中的批量操作
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('开始处理库存队列', [
                    'queue_size' => count($this->stockQueue),
                    'memory_usage' => round(memory_get_usage()/1024/1024, 2) . 'MB'
                ]);
                
                $queueResults = $this->processStockQueue();
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('库存队列处理完成', [
                    'created_records' => $queueResults['created'],
                    'updated_records' => $queueResults['updated'],
                    'skipped_records' => $queueResults['skipped'],
                    'memory_usage' => round(memory_get_usage()/1024/1024, 2) . 'MB'
                ]);

                // 更新成功统计，使用实际数据库操作结果
                $totalSuccess = $queueResults['created'] + $queueResults['updated'];
                
                // 计算总计数据
                $totalStats = [
                    'total_success' => $totalSuccess,
                    'total_failed' => $totalFailed,
                    'total_skipped' => $totalSkipped + $queueResults['skipped'] // 包括队列处理时跳过的记录
                ];

                // 更新导入批次状态
                $importBatch->update([
                    'status' => 'completed',
                    'stats' => $stats,
                    'completed_at' => now()
                ]);

                DB::commit();

                // 返回成功信息
                return response()->json([
                    'status' => true,
                    'message' => sprintf(
                        '导入完成，成功：%d 条，失败：%d 条，跳过：%d 条', 
                        $totalSuccess, 
                        $totalFailed, 
                        $totalSkipped + $queueResults['skipped']
                    ),
                    'data' => [
                        'stats' => $stats,
                        'total_stats' => $totalStats, // 添加总体统计数据
                        'errors' => $errors,
                        'clear_preview' => true, // 添加标记，指示前端清空预览数据
                        'db_stats' => [
                            'total_success' => $totalSuccess,
                            'total_failed' => $totalFailed,
                            'total_skipped' => $totalSkipped + $queueResults['skipped'],
                            'created_records' => $queueResults['created'],
                            'updated_records' => $queueResults['updated'],
                            'skipped_records' => $queueResults['skipped']
                        ]
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('导入数据时出错: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
            return response()->json([
                'status' => false,
                'message' => '导入失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 批量创建库位
     */
    public function batchCreateLocations(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            $importBatchId = $request->input('import_batch_id');
            $locations = $request->input('locations', []);
            $warehouseId = $request->input('warehouse_id');

            if (!$sessionKey || !$importBatchId || empty($locations) || !$warehouseId) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的请求参数'
                ]);
            }

            // 获取导入批次
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            if (!$importBatch || $importBatch->id != $importBatchId) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到导入批次数据'
                ]);
            }

            DB::beginTransaction();
            try {
                $createdCount = 0;
                $errors = [];
                $alreadyProcessed = []; // 记录已经处理过的位置名称

                foreach ($locations as $location) {
                    try {
                        $locationName = $location['name'];
                        
                        // 如果已经处理过这个位置名称，跳过
                        if (in_array($locationName, $alreadyProcessed)) {
                            continue;
                        }
                        
                        $alreadyProcessed[] = $locationName;
                        
                        // 使用位置部件信息
                        $parts = $location['parts'];
                        
                        // 检查部件信息是否完整
                        if (empty($parts['area'])) {
                            $errors[] = "位置 {$locationName} 缺少区域信息";
                            continue;
                        }
                        
                        // 检查库位是否已存在
                        $exists = TecBanhuaLocationModel::where('area_number', $parts['area'])
                            ->where('shelf_number', $parts['shelf'] ?: '')
                            ->where('level_number', $parts['level'] ?: '')
                            ->exists();
                            
                        if (!$exists) {
                            TecBanhuaLocationModel::create([
                                'warehouse_id' => $warehouseId,
                                'area_number' => $parts['area'],
                                'shelf_number' => $parts['shelf'] ?: '',
                                'level_number' => $parts['level'] ?: '',
                                'status' => 'available'
                            ]);
                            $createdCount++;
                        }
                    } catch (\Exception $e) {
                        $errors[] = "创建库位 {$locationName} 失败: " . $e->getMessage();
                        \Illuminate\Support\Facades\Log::error('创建库位失败', [
                            'location' => $locationName,
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                if (!empty($errors) && count($errors) == count($locations)) {
                    throw new \Exception(implode("\n", $errors));
                }

                DB::commit();
                return response()->json([
                    'status' => true,
                    'message' => "成功创建 {$createdCount} 个库位",
                    'errors' => $errors
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('批量创建库位时出错: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '创建库位失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 清理临时导入数据
     */
    public function cleanupImportData(Request $request)
    {
        try {
            $sessionKey = $request->input('session_key');
            
            if (!$sessionKey) {
                return response()->json([
                    'status' => false,
                    'message' => '无效的会话标识'
                ]);
            }
            
            // 删除导入批次及相关数据
            $importBatch = BanhuaStockImportBatchModel::findBySessionKey($sessionKey);
            
            if (!$importBatch) {
                return response()->json([
                    'status' => true,
                    'message' => '没有找到需要清理的数据'
                ]);
            }
            
            // 开始事务
            DB::beginTransaction();
            try {
                // 首先删除批次数据
                BanhuaStockImportBatchDataModel::where('batch_id', $importBatch->id)->delete();
                
                // 删除临时文件
                $filePath = $importBatch->file_path;
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
                
                // 删除批次记录
                $importBatch->delete();
                
                DB::commit();
                
                return response()->json([
                    'status' => true,
                    'message' => '临时数据已清理'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('清理临时数据失败: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '清理临时数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 处理导入行数据
     */
    protected function processImportRow($sheetName, $row, $headers1, $headers2, $locationPairs, $possibleSizes, $duplicateStrategy, $defaultWarehouse, $locationsMap = null, $locationNameToPartsMap = null)
    {
        try {
            $results = [
                'success' => 0,
                'skipped' => 0,
                'failed' => 0,
                'errors' => []
            ];
            
            // 获取产品ID（图库ID）- 从Excel行数据中获取
            $galleryId = isset($row['ID']) ? $row['ID'] : null;
            
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('开始处理导入行', [
                'sheet_name' => $sheetName,
                'gallery_id' => $galleryId,
                'row' => $row
            ]);
            
            // 如果没有图库ID，无法继续
            if (!$galleryId) {
                $results['failed']++;
                $results['errors'][] = "找不到产品ID（图库ID）";
                return $results;
            }
            
            // 使用图库ID和工作表名称（尺寸）查找匹配的SKU
            $matchedSkus = $this->identifyProductSizes($sheetName, $galleryId);
            
            \Illuminate\Support\Facades\Log::channel("sku_import")->info('根据图库ID和尺寸找到的SKU', [
                'gallery_id' => $galleryId,
                'sheet_name' => $sheetName,
                'matched_skus_count' => count($matchedSkus),
                'matched_sku_ids' => array_column($matchedSkus, 'id')
            ]);
            
            // 如果没有找到匹配的SKU，报错
            if (empty($matchedSkus)) {
                $results['failed']++;
                $results['errors'][] = "找不到图库ID为{$galleryId}且尺寸为{$sheetName}的SKU";
                return $results;
            }
            
            // 处理每个产品-位置配对
            foreach ($locationPairs as $pair) {
                $productHeader = $pair['product_header'];
                $locationColIndex = $pair['location_col'];
                
                // 产品类型（如"板"代表画板）
                $productType = $productHeader;
                $attrId = $pair['attr_id'] ?? null;
                $valueId = $pair['value_id'] ?? null;
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('处理产品类型', [
                    'product_type' => $productType,
                    'attr_id' => $attrId,
                    'value_id' => $valueId
                ]);
                
                // 确保索引存在
                if (!isset($headers1[$locationColIndex])) {
                    $results['failed']++;
                    $results['errors'][] = "无法找到位置列，索引: {$locationColIndex}";
                    continue;
                }
                
                $locationHeader = $headers1[$locationColIndex];
                $locationKey = !empty($locationHeader) ? $locationHeader : 'col_' . ($locationColIndex + 1);
                
                // 获取产品数量和位置
                $productIndex = $locationColIndex - 1; // 产品列总是在位置列前面
                $productKey = !empty($headers1[$productIndex]) ? $headers1[$productIndex] : 'col_' . ($productIndex + 1);
                
                // 处理行数据中的键可能是"col_N"格式的情况
                $quantity = 0;
                if (isset($row[$productHeader]) && is_numeric($row[$productHeader])) {
                    $quantity = (int)$row[$productHeader];
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('从productHeader获取数量', [
                        'productHeader' => $productHeader,
                        'quantity' => $quantity
                    ]);
                } else if (isset($row[$productKey]) && is_numeric($row[$productKey])) {
                    $quantity = (int)$row[$productKey];
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('从productKey获取数量', [
                        'productKey' => $productKey,
                        'quantity' => $quantity
                    ]);
                } else {
                    // 最后尝试使用列索引直接获取
                    $directKey = 'col_' . ($productIndex + 1);
                    if (isset($row[$directKey]) && is_numeric($row[$directKey])) {
                        $quantity = (int)$row[$directKey];
                        \Illuminate\Support\Facades\Log::channel("sku_import")->info('从directKey获取数量', [
                            'directKey' => $directKey,
                            'quantity' => $quantity
                        ]);
                    }
                }
                
                $locationName = isset($row[$locationKey]) ? trim($row[$locationKey]) : '';
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('处理位置和数量', [
                    'location_name' => $locationName,
                    'quantity' => $quantity,
                    'product_header' => $productHeader,
                    'product_key' => $productKey,
                    'location_key' => $locationKey,
                    'product_index' => $productIndex,
                    'location_index' => $locationColIndex
                ]);
                
                // 跳过无效数据
                if ($quantity <= 0 || empty($locationName)) {
                    $results['skipped']++;
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('跳过无效数据', [
                        'reason' => $quantity <= 0 ? '数量为0或负数' : '位置名称为空',
                        'quantity' => $quantity,
                        'location_name' => $locationName
                    ]);
                    continue;
                }
                
                // 查找库位ID
                $location = null;
                
                // 使用位置映射，如果存在
                if ($locationsMap !== null && isset($locationsMap[$locationName])) {
                    $location = $locationsMap[$locationName];
                } else {
                    // 回退到单独查询
                    $parts = $locationNameToPartsMap[$locationName] ?? $this->parseLocationName($locationName);
                    $location = TecBanhuaLocationModel::where('area_number', $parts['area'])
                        ->where('shelf_number', $parts['shelf'] ?: '')
                        ->where('level_number', $parts['level'] ?: '')
                        ->first();
                }
                
                if (!$location) {
                    $results['failed']++;
                    $results['errors'][] = "找不到库位: {$locationName}";
                    continue;
                }
                
                // 产品类型特殊处理映射表 - 处理表格中与数据库不完全匹配的产品类型
                $productTypeMapping = $this->getProductTypeNameMappings();
                
                // 获取标准化的产品类型名称
                $normalizedProductType = isset($productTypeMapping[$productType]) ? 
                    $productTypeMapping[$productType] : $productType;
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('标准化后的产品类型', [
                    'original_type' => $productType,
                    'normalized_product_type' => $normalizedProductType
                ]);
                
                // 根据不同的产品类型属性ID值来匹配
                $productTypeValueIds = $this->getProductTypeIdMappings();
                
                // 获取目标产品类型对应的ID
                $targetTypeId = isset($productTypeValueIds[$normalizedProductType]) ? 
                    $productTypeValueIds[$normalizedProductType] : ($valueId ?: null);
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('目标产品类型ID', [
                    'target_type_id' => $targetTypeId,
                    'original_value_id' => $valueId
                ]);
                
                // 查找匹配产品类型的SKU - 多级匹配策略
                $typeMatchedSkus = [];
                
                // 第1级匹配：使用产品类型ID和名称精确匹配
                if ($targetTypeId) {
                    foreach ($matchedSkus as $sku) {
                        $attrValueIds = is_array($sku['attr_value_ids']) ? $sku['attr_value_ids'] : json_decode($sku['attr_value_ids'], true);
                        if (!is_array($attrValueIds)) continue;
                        
                        // 必须包含目标产品类型ID
                        if (in_array($targetTypeId, $attrValueIds)) {
                            // 通过attr_value_ids判断产品类型
                            $dbProductType = isset($sku['product_type']) ? strtolower($sku['product_type']) : '';
                            $excelProductType = strtolower($normalizedProductType);
                            
                            // 获取产品类型特殊匹配
                            $specialMatchings = BanhuaConstants::getTypeSpecialMatchMappings();
                            $matched = false;
                            
                            // 精确匹配
                            if ($dbProductType === $excelProductType || $dbProductType === '') {
                                $matched = true;
                            }
                            // 特殊匹配检查
                            else if (isset($specialMatchings[$normalizedProductType])) {
                                foreach ($specialMatchings[$normalizedProductType] as $matchName) {
                                    if (strpos($dbProductType, $matchName) !== false) {
                                        $matched = true;
                                        break;
                                    }
                                }
                            }
                            
                            if ($matched) {
                                
                                $typeMatchedSkus[] = $sku;
                                \Illuminate\Support\Facades\Log::channel("sku_import")->info('第1级匹配成功：精确匹配', [
                                    'sku_id' => $sku['id'],
                                    'db_product_type' => isset($sku['product_type']) ? $sku['product_type'] : '未知',
                                    'excel_product_type' => $normalizedProductType,
                                    'matched_by_type_id' => $dbProductType === ''
                                ]);
                            }
                        }
                    }
                }
                
                // 第2级匹配：使用名称包含关系匹配
                if (empty($typeMatchedSkus) && $normalizedProductType) {
                    foreach ($matchedSkus as $sku) {
                        // 如果SKU有产品类型信息，使用它
                        if (isset($sku['product_type']) && !empty($sku['product_type'])) {
                            $dbProductType = strtolower($sku['product_type']);
                            $excelProductType = strtolower($normalizedProductType);
                            
                            // 使用包含关系匹配
                            if (strpos($dbProductType, $excelProductType) !== false ||
                                strpos($excelProductType, $dbProductType) !== false) {
                                
                                $typeMatchedSkus[] = $sku;
                                \Illuminate\Support\Facades\Log::channel("sku_import")->info('第2级匹配成功：名称包含匹配', [
                                    'sku_id' => $sku['id'],
                                    'db_product_type' => $sku['product_type'],
                                    'excel_product_type' => $normalizedProductType
                                ]);
                            }
                        }
                        // 如果没有产品类型信息，尝试通过属性ID匹配
                        else {
                            $attrValueIds = is_array($sku['attr_value_ids']) ? $sku['attr_value_ids'] : json_decode($sku['attr_value_ids'], true);
                            if (!is_array($attrValueIds)) continue;
                            
                            // 从常量类获取产品类型映射
                            $allMappings = $this->getProductTypeIdMappings();
                            $productTypeMapping = [
                                '板' => [isset($allMappings['画板']) ? $allMappings['画板'] : 19],
                                '画板' => [isset($allMappings['画板']) ? $allMappings['画板'] : 19],
                                '画布' => [isset($allMappings['画布']) ? $allMappings['画布'] : 18],
                                '画芯' => [isset($allMappings['画芯']) ? $allMappings['画芯'] : 18],
                                '画' => [isset($allMappings['画']) ? $allMappings['画'] : 18],
                                '成品' => [isset($allMappings['成品']) ? $allMappings['成品'] : 20]
                            ];
                            
                            $expectedTypeIds = isset($productTypeMapping[$normalizedProductType]) ? $productTypeMapping[$normalizedProductType] : [];
                            foreach ($expectedTypeIds as $typeId) {
                                if (in_array($typeId, $attrValueIds)) {
                                    $typeMatchedSkus[] = $sku;
                                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('第2级匹配成功：通过属性ID匹配', [
                                        'sku_id' => $sku['id'],
                                        'attr_value_id' => $typeId,
                                        'excel_product_type' => $normalizedProductType
                                    ]);
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // 第3级匹配：仅使用产品类型ID匹配
                if (empty($typeMatchedSkus) && $targetTypeId) {
                    foreach ($matchedSkus as $sku) {
                        $attrValueIds = is_array($sku['attr_value_ids']) ? $sku['attr_value_ids'] : json_decode($sku['attr_value_ids'], true);
                        if (!is_array($attrValueIds)) continue;
                        
                        if (in_array($targetTypeId, $attrValueIds)) {
                            $typeMatchedSkus[] = $sku;
                            \Illuminate\Support\Facades\Log::channel("sku_import")->info('第3级匹配成功：仅使用产品类型ID匹配', [
                                'sku_id' => $sku['id'],
                                'db_product_type' => isset($sku['product_type']) ? $sku['product_type'] : '未知',
                                'excel_product_type' => $normalizedProductType,
                                'target_type_id' => $targetTypeId
                            ]);
                        }
                    }
                }
                
                // 第4级匹配：智能匹配画板/画布/成品
                if (empty($typeMatchedSkus)) {
                    // 从常量类获取特殊匹配映射
                    $specialMatching = BanhuaConstants::getTypeSpecialMatchMappings();
                    
                    // 从常量类获取产品类型ID映射
                    $allMappings = $this->getProductTypeIdMappings();
                    $productTypeIdMapping = [
                        '板' => [isset($allMappings['画板']) ? $allMappings['画板'] : 19],
                        '画板' => [isset($allMappings['画板']) ? $allMappings['画板'] : 19],
                        '画芯' => [isset($allMappings['画布']) ? $allMappings['画布'] : 18],
                        '画布' => [isset($allMappings['画布']) ? $allMappings['画布'] : 18],
                        '画' => [isset($allMappings['画芯']) ? $allMappings['画芯'] : 18],
                        '成品' => [isset($allMappings['成品']) ? $allMappings['成品'] : 20]
                    ];
                    
                    $targetTypes = $specialMatching[$normalizedProductType] ?? [];
                    $targetTypeIds = $productTypeIdMapping[$normalizedProductType] ?? [];
                    
                    if (!empty($targetTypes) || !empty($targetTypeIds)) {
                        foreach ($matchedSkus as $sku) {
                            $matched = false;
                            
                            // 方法1: 通过产品类型名称匹配
                            if (isset($sku['product_type']) && !empty($sku['product_type'])) {
                                $skuType = strtolower($sku['product_type']);
                                foreach ($targetTypes as $targetType) {
                                    if (strpos($skuType, strtolower($targetType)) !== false) {
                                        $matched = true;
                                        \Illuminate\Support\Facades\Log::channel("sku_import")->info('第4级匹配成功：智能匹配特殊类型(名称)', [
                                            'sku_id' => $sku['id'],
                                            'db_product_type' => $sku['product_type'],
                                            'excel_product_type' => $normalizedProductType,
                                            'matched_with' => $targetType
                                        ]);
                                        break;
                                    }
                                }
                            }
                            
                            // 方法2: 通过属性ID匹配
                            if (!$matched && !empty($targetTypeIds)) {
                                $attrValueIds = is_array($sku['attr_value_ids']) ? $sku['attr_value_ids'] : json_decode($sku['attr_value_ids'], true);
                                if (is_array($attrValueIds)) {
                                    foreach ($targetTypeIds as $targetTypeId) {
                                        if (in_array($targetTypeId, $attrValueIds)) {
                                            $matched = true;
                                            \Illuminate\Support\Facades\Log::channel("sku_import")->info('第4级匹配成功：智能匹配特殊类型(ID)', [
                                                'sku_id' => $sku['id'],
                                                'excel_product_type' => $normalizedProductType,
                                                'matched_with_id' => $targetTypeId
                                            ]);
                                            break;
                                        }
                                    }
                                }
                            }
                            
                            if ($matched) {
                                $typeMatchedSkus[] = $sku;
                            }
                        }
                    }
                }
                
                // 第5级匹配：使用Excel中的特定类型匹配数据库中任何类型(最后回退)
                // 获取特殊处理类型
                $specialTypes = BanhuaConstants::getSpecialProcessingTypes();
                
                // 修改：对于"板"类型，无论前面是否已经匹配到SKU，都进行特殊处理
                // 这样可以确保"板"类型的数据一定能被正确处理
                $isSpecialType = in_array($productType, $specialTypes) || in_array($normalizedProductType, $specialTypes);
                
                // 如果是特殊类型（如"板"），或者前面的匹配没有找到任何SKU
                if ($isSpecialType || empty($typeMatchedSkus)) {
                    // 获取对应属性ID
                    $allMappings = $this->getProductTypeIdMappings();
                    $boardTypeId = isset($allMappings['画板']) ? $allMappings['画板'] : 19;
                    
                    // 如果是特殊类型且已经有匹配结果，记录日志
                    if ($isSpecialType && !empty($typeMatchedSkus)) {
                        \Illuminate\Support\Facades\Log::channel("sku_import")->info('特殊类型强制重新匹配', [
                            'product_type' => $productType,
                            'normalized_type' => $normalizedProductType,
                            'previous_matches' => count($typeMatchedSkus)
                        ]);
                        
                        // 清空之前的匹配结果，重新匹配
                        $typeMatchedSkus = [];
                    }
                    
                    // 新增：用于存储匹配结果和匹配优先级
                    $matchCandidates = [];
                    
                    foreach ($matchedSkus as $sku) {
                        $attrValueIds = is_array($sku['attr_value_ids']) ? $sku['attr_value_ids'] : json_decode($sku['attr_value_ids'], true);
                        
                        // 优先匹配拥有画板属性ID的SKU
                        if (is_array($attrValueIds) && in_array($boardTypeId, $attrValueIds)) {
                            // 记录匹配度为高优先级（100）
                            $matchCandidates[] = [
                                'sku' => $sku,
                                'priority' => 100,
                                'match_type' => '画板属性ID匹配',
                                'db_product_type' => isset($sku['product_type']) ? $sku['product_type'] : '未知'
                            ];
                            
                            \Illuminate\Support\Facades\Log::channel("sku_import")->info('第5级匹配成功：画板属性ID匹配', [
                                'sku_id' => $sku['id'],
                                'db_product_type' => isset($sku['product_type']) ? $sku['product_type'] : '未知',
                                'excel_product_type' => $normalizedProductType,
                                'matched_by_attr_id' => true
                            ]);
                        }
                        // 退而求其次，匹配任何SKU（作为最后的兼容处理）
                        else if ($isSpecialType) {
                            // 记录匹配度为低优先级（50）
                            $matchCandidates[] = [
                                'sku' => $sku,
                                'priority' => 50,
                                'match_type' => '板特殊兼容匹配',
                                'db_product_type' => isset($sku['product_type']) ? $sku['product_type'] : '未知'
                            ];
                            
                            \Illuminate\Support\Facades\Log::channel("sku_import")->info('第5级匹配成功：板特殊兼容匹配', [
                                'sku_id' => $sku['id'],
                                'db_product_type' => isset($sku['product_type']) ? $sku['product_type'] : '未知',
                                'excel_product_type' => $normalizedProductType,
                                'matched_by_fallback' => true
                            ]);
                        }
                    }
                    
                    // 如果有匹配候选，根据优先级选择最佳匹配
                    if (!empty($matchCandidates)) {
                        // 按优先级降序排序
                        usort($matchCandidates, function($a, $b) {
                            // 优先级高的排在前面
                            if ($a['priority'] != $b['priority']) {
                                return $b['priority'] - $a['priority'];
                            }
                            
                            // 如果优先级相同，数据库中产品类型为"画板"的优先
                            $aType = strtolower($a['db_product_type']);
                            $bType = strtolower($b['db_product_type']);
                            
                            if ($aType == '画板' && $bType != '画板') {
                                return -1;
                            } else if ($aType != '画板' && $bType == '画板') {
                                return 1;
                            }
                            
                            // 都相同时保持原顺序
                            return 0;
                        });
                        
                        // 只选择最佳匹配的SKU
                        $bestMatch = $matchCandidates[0];
                        $typeMatchedSkus[] = $bestMatch['sku'];
                        
                        \Illuminate\Support\Facades\Log::channel("sku_import")->info('选择最佳匹配的SKU', [
                            'sku_id' => $bestMatch['sku']['id'],
                            'db_product_type' => $bestMatch['db_product_type'],
                            'match_type' => $bestMatch['match_type'],
                            'priority' => $bestMatch['priority'],
                            'candidates_count' => count($matchCandidates)
                        ]);
                    }
                }
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('符合产品类型的SKU', [
                    'product_type' => $productType,
                    'normalized_type' => $normalizedProductType,
                    'target_type_id' => $targetTypeId,
                    'type_matched_skus_count' => count($typeMatchedSkus),
                    'type_matched_sku_ids' => array_column($typeMatchedSkus, 'id'),
                ]);
                
                // 如果没有找到匹配产品类型的SKU，记录错误并继续
                if (empty($typeMatchedSkus)) {
                    $results['skipped']++;
                    $errorMsg = "在图库 {$galleryId} 的尺寸 {$sheetName} 中找不到匹配产品类型 {$productType} 的SKU";
                    $results['errors'][] = $errorMsg;
                    
                    \Illuminate\Support\Facades\Log::channel("sku_import")->warning('跳过产品类型', [
                        'product_type' => $productType,
                        'gallery_id' => $galleryId,
                        'sheet_name' => $sheetName,
                        'reason' => $errorMsg,
                        'available_skus' => array_map(function($sku) {
                            return [
                                'id' => $sku['id'],
                                'product_type' => isset($sku['product_type']) ? $sku['product_type'] : '未知',
                                'attr_value_ids' => $sku['attr_value_ids']
                            ];
                        }, $matchedSkus)
                    ]);
                    continue;
                }
                
                // 为每个匹配的SKU添加库存记录到批量处理队列
                foreach ($typeMatchedSkus as $sku) {
                    $skuId = $sku['id'];
                    
                    try {
                        $skuProductType = isset($sku['product_type']) ? $sku['product_type'] : '未知';
                        
                        \Illuminate\Support\Facades\Log::channel("sku_import")->info('准备添加库存记录到队列', [
                            'sku_id' => $skuId,
                            'sku_product_type' => $skuProductType,
                            'excel_product_type' => $normalizedProductType,
                            'quantity' => $quantity,
                            'location_id' => $location->id,
                            'location_name' => $locationName
                        ]);
                        
                        // 添加到库存处理队列
                        $success = $this->createOrUpdateStock($skuId, null, null, $location->id, $quantity, $duplicateStrategy);
                        
                        if ($success) {
                            // 这里只是添加到队列成功，不计入成功统计
                            // 实际的成功统计将在processStockQueue后根据实际数据库操作结果计算
                            // results['success']++;
                        } else {
                            $results['failed']++;
                            $results['errors'][] = "添加库存记录到队列失败: SKU={$skuId}, 位置={$locationName}";
                        }
                    } catch (\Exception $e) {
                        $results['failed']++;
                        $results['errors'][] = "处理记录出错: " . $e->getMessage();
                        \Illuminate\Support\Facades\Log::error('添加库存记录到队列失败', [
                            'sku_id' => $skuId,
                            'location_id' => $location->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
            
            return $results;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('处理行数据时出错', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => 0,
                'skipped' => 0,
                'failed' => 1,
                'errors' => [$e->getMessage()]
            ];
        }
    }
    
    /**
     * 批量创建库存记录队列
     * 这是一个优化版的库存创建方法，先收集需要创建的记录，然后批量处理
     * @var array $stockQueue 收集的库存记录队列
     * @var array $locationWarehouseMap 库位和仓库的映射关系缓存
     */
    protected $stockQueue = [];
    protected $locationWarehouseMap = [];
    
    /**
     * 创建或更新库存记录
     */
    protected function createOrUpdateStock($skuId, $attrId, $valueId, $locationId, $quantity, $strategy)
    {
        // 获取库位所属的仓库ID - 使用缓存避免重复查询
        if (!isset($this->locationWarehouseMap[$locationId])) {
            $warehouseId = TecBanhuaLocationModel::where('id', $locationId)->value('warehouse_id');
            if (!$warehouseId) {
                Log::error('找不到库位对应的仓库', ['location_id' => $locationId]);
                return false;
            }
            $this->locationWarehouseMap[$locationId] = $warehouseId;
        } else {
            $warehouseId = $this->locationWarehouseMap[$locationId];
        }
        
        // 构建唯一键，用于检查重复
        $stockKey = $skuId . '_' . $locationId;
        
        // 将记录添加到队列中
        if (!isset($this->stockQueue[$stockKey])) {
            // 新记录，直接加入队列
            $this->stockQueue[$stockKey] = [
                'sku_id' => $skuId,
                'warehouse_id' => $warehouseId,
                'location_id' => $locationId,
                'stock' => $quantity,
                'strategy' => $strategy,
                'is_new' => true
            ];
            return true;
        } else {
            // 已存在队列中，根据策略处理
            switch ($strategy) {
                case 'skip':
                    // 已存在时不做任何处理
                    return true;
                    
                case 'overwrite':
                    // 覆盖记录
                    $this->stockQueue[$stockKey]['stock'] = $quantity;
                    return true;
                    
                case 'merge':
                    // 合并数据（累加库存）
                    $this->stockQueue[$stockKey]['stock'] += $quantity;
                    return true;
                    
                default:
                    return false;
            }
        }
    }
    
    /**
     * 批量处理库存队列
     * 在处理完所有数据后调用此方法执行批量操作
     */
    protected function processStockQueue()
    {
        if (empty($this->stockQueue)) {
            return [
                'created' => 0,
                'updated' => 0,
                'skipped' => 0
            ];
        }
        
        // 收集所有需要检查的库存项
        $checkItems = [];
        foreach ($this->stockQueue as $stockKey => $item) {
            $checkItems[] = [
                'sku_id' => $item['sku_id'],
                'location_id' => $item['location_id']
            ];
        }
        
        // 批量查询已存在的库存记录
        $existingStocks = TecBanhuaSkuStockModel::where(function($query) use ($checkItems) {
            foreach ($checkItems as $i => $item) {
                $method = $i === 0 ? 'where' : 'orWhere';
                $query->$method(function($q) use ($item) {
                    $q->where('sku_id', $item['sku_id'])
                      ->where('location_id', $item['location_id']);
                });
            }
        })->get()->keyBy(function($stock) {
            return $stock->sku_id . '_' . $stock->location_id;
        });
        
        // 准备批量插入和更新的数据
        $newRecords = [];
        $updateRecords = [];
        $skipCount = 0;
        $now = now();
        
        foreach ($this->stockQueue as $stockKey => $item) {
            // 检查数据库中是否已存在
            $exists = isset($existingStocks[$stockKey]);
            
            if ($exists) {
                $existingStock = $existingStocks[$stockKey];
                
                // 根据策略处理已存在的记录
                switch ($item['strategy']) {
                    case 'skip':
                        $skipCount++;
                        break;
                        
                    case 'overwrite':
                        $existingStock->stock = $item['stock'];
                        $updateRecords[] = $existingStock;
                        break;
                        
                    case 'merge':
                        $existingStock->stock += $item['stock'];
                        $updateRecords[] = $existingStock;
                        break;
                }
            } else {
                // 新记录
                $newRecord = [
                    'sku_id' => $item['sku_id'],
                    'warehouse_id' => $item['warehouse_id'],
                    'location_id' => $item['location_id'],
                    'stock' => $item['stock'],
                    'created_at' => $now,
                    'updated_at' => $now
                ];
                
                $newRecords[] = $newRecord;
            }
        }
        
        // 执行批量操作
        $createdCount = 0;
        $updatedCount = 0;
        
        if (!empty($newRecords)) {
            try {
                // 批量插入新记录
                TecBanhuaSkuStockModel::insert($newRecords);
                $createdCount = count($newRecords);
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('批量插入库存记录失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // 如果批量插入失败，尝试逐个插入
                foreach ($newRecords as $record) {
                    try {
                        TecBanhuaSkuStockModel::create($record);
                        $createdCount++;
                    } catch (\Exception $innerE) {
                        // 记录但继续处理
                        \Illuminate\Support\Facades\Log::error('单条插入库存记录失败', [
                            'sku_id' => $record['sku_id'],
                            'location_id' => $record['location_id'],
                            'error' => $innerE->getMessage()
                        ]);
                    }
                }
            }
        }
        
        // 处理更新记录
        foreach ($updateRecords as $record) {
            try {
                $record->save();
                $updatedCount++;
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('更新库存记录失败', [
                    'sku_id' => $record->sku_id,
                    'location_id' => $record->location_id,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        // 清空队列
        $this->stockQueue = [];
        
        // 返回处理统计
        return [
            'created' => $createdCount,
            'updated' => $updatedCount,
            'skipped' => $skipCount
        ];
    }

    /**
     * 获取表头与产品类型的映射关系（动态加载，支持别名）
     */
    protected function getHeaderAttributeMapping()
    {
        // 使用静态缓存避免重复查询
        static $attributeMappings = null;
        if ($attributeMappings !== null) {
            return $attributeMappings;
        }
        $attributeMappings = [];
        // 特殊列静态映射 - 这些列不是产品类型，而是特殊含义的列
        $attributeMappings['ID'] = ['attr_id' => 'special', 'value_id' => 'id', 'type' => '标识符'];
        $attributeMappings['在庫'] = ['attr_id' => 'special', 'value_id' => 'stock', 'type' => '库存数量'];
        $attributeMappings['在库'] = ['attr_id' => 'special', 'value_id' => 'stock', 'type' => '库存数量'];
        $attributeMappings['库存'] = ['attr_id' => 'special', 'value_id' => 'stock', 'type' => '库存数量'];
        $attributeMappings['数量'] = ['attr_id' => 'special', 'value_id' => 'stock', 'type' => '库存数量'];
        $attributeMappings['数'] = ['attr_id' => 'special', 'value_id' => 'stock', 'type' => '库存数量'];
        $attributeMappings['在庫数'] = ['attr_id' => 'special', 'value_id' => 'stock', 'type' => '库存数量'];
        $attributeMappings['在庫数量'] = ['attr_id' => 'special', 'value_id' => 'stock', 'type' => '库存数量'];

        // 从常量类获取产品类型别名映射
        $typeAlias = BanhuaConstants::getTypeAliasMappings();

        // 静态缓存产品类型属性值
        static $typeValueMap = null;
        if ($typeValueMap === null) {
            $typeValueMap = [];
            $productTypeAttrId = BanhuaConstants::getProductTypeAttrId();
            $typeValues = TecBanhuaAttrValueModel::where('attr_id', $productTypeAttrId)->get();
            foreach ($typeValues as $v) {
                $typeValueMap[$v->name] = [
                    'attr_id' => $productTypeAttrId,
                    'value_id' => $v->id,
                    'type' => $v->name
                ];
            }
        }
        // 主名和别名都加入映射
        foreach ($typeValueMap as $name => $info) {
            $attributeMappings[$name] = $info;
        }
        foreach ($typeAlias as $alias => $real) {
            if (isset($typeValueMap[$real])) {
                $attributeMappings[$alias] = $typeValueMap[$real];
            }
        }
        return $attributeMappings;
    }

    /**
     * 分析表头并匹配属性值
     */
    protected function analyzeHeaders($headers)
    {
        $mappings = [];
        $attributeMappings = $this->getHeaderAttributeMapping();
        
        foreach ($headers as $index => $header) {
            // 忽略空白表头
            if (empty(trim($header))) {
                continue;
            }
            
            // 检查是否匹配已知的表头类型
            if (isset($attributeMappings[$header])) {
                $attrInfo = $attributeMappings[$header];
                $isSpecial = $attrInfo['attr_id'] === 'special';
                
                $mappings[$header] = [
                    'type' => $attrInfo['type'],
                    'attr_id' => $attrInfo['attr_id'],
                    'value_id' => $attrInfo['value_id'],
                    'attr_value' => !$isSpecial,  // 特殊列不是属性值
                    'is_special' => $isSpecial,
                    'special_type' => $isSpecial ? $attrInfo['value_id'] : null
                ];
                
                // 记录特殊列识别日志
                if ($isSpecial) {
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('识别到特殊列', [
                        'header' => $header,
                        'type' => $attrInfo['type'],
                        'special_type' => $attrInfo['value_id']
                    ]);
                }
            } else {
                // 尝试匹配常见的特殊列名
                $specialType = null;
                $type = '未知';
                
                // 检查ID列
                if (stripos($header, 'id') !== false || $header === 'ID') {
                    $specialType = 'id';
                    $type = '标识符';
                }
                // 检查库存列
                elseif (stripos($header, '库存') !== false || 
                        stripos($header, '在庫') !== false || 
                        stripos($header, '数量') !== false) {
                    $specialType = 'stock';
                    $type = '库存数量';
                    
                    // 记录库存列识别日志
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('识别到库存列', [
                        'header' => $header,
                        'type' => $type
                    ]);
                }
                
                $mappings[$header] = [
                    'type' => $type,
                    'attr_value' => false,
                    'is_special' => $specialType !== null,
                    'special_type' => $specialType
                ];
            }
        }
        
        return $mappings;
    }
    
    /**
     * 根据工作表名识别产品尺寸
     * 如果提供了图库ID，则返回该图库下所有匹配工作表尺寸的SKU
     */
    protected function identifyProductSizes($sheetName, $galleryId = null)
    {
        // 使用静态缓存
        static $resultCache = [];
        static $allSkusCache = null;
        static $allSizeAttrs = null;
        static $allProductTypeAttrs = null;
        static $loadedAllData = false;
        
        // 缓存键需要包含图库ID，避免不同图库使用相同的缓存
        $cacheKey = $sheetName . '_' . ($galleryId ?: 'all');
        
        // 如果已经处理过该工作表和图库组合，直接返回缓存结果
        if (isset($resultCache[$cacheKey])) {
            \Illuminate\Support\Facades\Log::channel("sku_import")->debug('使用缓存的产品尺寸结果', ['cache_key' => $cacheKey]);
            return $resultCache[$cacheKey];
        }
        
        // 一次性加载所有需要的数据（仅在首次调用时加载）
        if (!$loadedAllData) {
            \Illuminate\Support\Facades\Log::channel('sku_import')->info('首次加载并优化所有SKU和属性数据');
            
            // 设置更长的执行时间和更大的内存限制
            set_time_limit(300); // 5分钟
            ini_set('memory_limit', '2G');
            
            // 获取尺寸和产品类型属性ID
            $sizeAttrId = BanhuaConstants::getSizeAttrId();
            $productTypeAttrId = BanhuaConstants::getProductTypeAttrId();
            
            // 1. 一次性加载所有尺寸属性
            $allSizeAttrs = TecBanhuaAttrValueModel::where('attr_id', $sizeAttrId)
                ->select(['id', 'name', 'attr_id'])
                ->get()
                ->keyBy('id');
            
            // 2. 一次性加载所有产品类型属性
            $allProductTypeAttrs = TecBanhuaAttrValueModel::where('attr_id', $productTypeAttrId)
                ->select(['id', 'name', 'attr_id'])
                ->get()
                ->keyBy('id');
            
            // 3. 优化SKU查询 - 只获取需要的字段
            // 去掉不存在的product_type字段
            $query = TecBanhuaProductSkuModel::query()
                ->select(['id', 'pid', 'attr_value_ids'])
                ->whereNotNull('attr_value_ids');
                
            // 如果批量导入只涉及特定图库ID，可以添加条件限制
            if (is_array($galleryId) && !empty($galleryId)) {
                $query->whereIn('pid', $galleryId);
            } elseif (is_numeric($galleryId)) {
                $query->where('pid', $galleryId);
            }
            
            // 分批获取SKU数据以优化内存使用
            $allSkusCache = [];
            $skuChunks = $query->orderBy('id')->chunk(1000, function($skus) use (&$allSkusCache) {
                foreach ($skus as $sku) {
                    $pid = $sku->pid;
                    if (!isset($allSkusCache[$pid])) {
                        $allSkusCache[$pid] = [];
                    }
                    // 预处理JSON字段，减少后续重复解析
                    if (is_string($sku->attr_value_ids)) {
                        $sku->attr_value_ids = json_decode($sku->attr_value_ids, true);
                    }
                    $allSkusCache[$pid][] = $sku;
                }
                return true; // 继续获取下一批
            });
            
            $loadedAllData = true;
            
            // 统计加载的数据
            $totalSkus = 0;
            foreach ($allSkusCache as $pid => $skus) {
                $totalSkus += count($skus);
            }
            
            \Illuminate\Support\Facades\Log::channel('sku_import')->info('数据库优化加载完成', [
                'size_attrs_count' => $allSizeAttrs->count(),
                'product_type_attrs_count' => $allProductTypeAttrs->count(), 
                'gallery_count' => count($allSkusCache),
                'total_skus' => $totalSkus,
                'memory_usage' => round(memory_get_usage()/1024/1024, 2) . 'MB'
            ]);
        }
        
        $possibleSizes = [];
        
        // 修改：从工作表名中提取数字部分作为尺寸
        // 例如从"二联3040"中提取"3040"
        $extractedSize = preg_replace('/\D/', '', $sheetName);
        
        // 确保提取的数字是有效的尺寸（通常为4位数）
        if (empty($extractedSize) || strlen($extractedSize) < 4) {
            \Illuminate\Support\Facades\Log::channel('sku_import')->info('无法从工作表名中提取有效的尺寸', [
                'sheet_name' => $sheetName,
                'extracted_size' => $extractedSize
            ]);
            $resultCache[$cacheKey] = [];
            return [];
        }
        
        // 优先使用提取的4位数字
        $size1 = substr($extractedSize, 0, 4);
        $size2 = null;
        
        // 处理例如"3040"的情况，也考虑反向"4030"
        if (strlen($size1) == 4) {
            $size2 = substr($size1, 2, 2) . substr($size1, 0, 2);
        }
        
        \Illuminate\Support\Facades\Log::channel('sku_import')->info('从工作表名中提取尺寸', [
            'sheet_name' => $sheetName,
            'extracted_size' => $extractedSize,
            'size1' => $size1,
            'size2' => $size2
        ]);
        
        // 找到匹配的尺寸属性值ID
        $matchingSizeIds = [];
        foreach ($allSizeAttrs as $attrId => $attr) {
            // 移除所有非数字字符后比较
            $attrDigits = preg_replace('/\D/', '', $attr->name);
            if ($attrDigits == $size1 || ($size2 && $attrDigits == $size2)) {
                $matchingSizeIds[] = $attrId;
            }
        }
        
        // 如果没有找到匹配的尺寸ID，直接返回空数组
        if (empty($matchingSizeIds)) {
            \Illuminate\Support\Facades\Log::channel('sku_import')->info('未找到匹配的尺寸ID', [
                'sheet_name' => $sheetName
            ]);
            $resultCache[$cacheKey] = [];
            return [];
        }
        
        // 从内存缓存中获取指定图库的SKU
        $skusToCheck = [];
        if ($galleryId && isset($allSkusCache[$galleryId])) {
            $skusToCheck = $allSkusCache[$galleryId];
        } elseif (!$galleryId) {
            // 如果没有指定图库ID，则检查所有SKU
            foreach ($allSkusCache as $gallerySkus) {
                $skusToCheck = array_merge($skusToCheck, $gallerySkus);
            }
        }
        
        \Illuminate\Support\Facades\Log::channel('sku_import')->info('查询到的SKU数量', [
            'gallery_id' => $galleryId,
            'skus_count' => count($skusToCheck)
        ]);
        
        // 遍历所有SKU，查找匹配尺寸的SKU
        foreach ($skusToCheck as $sku) {
            // 解析属性值ID数组
            $attrValueIds = is_array($sku->attr_value_ids) ? $sku->attr_value_ids : json_decode($sku->attr_value_ids, true);
            
            if (!is_array($attrValueIds)) {
                    continue;
                }
                
            // 查找SKU的产品类型
            $productTypeName = '未知类型';
            $productTypeId = null;
            foreach ($attrValueIds as $valueId) {
                if (isset($allProductTypeAttrs[$valueId])) {
                    $typeAttr = $allProductTypeAttrs[$valueId];
                    $productTypeName = $typeAttr->name;
                    $productTypeId = $valueId;
                    break;
                }
            }
            
            // 检查SKU是否包含任一匹配的尺寸ID
            foreach ($matchingSizeIds as $sizeId) {
                if (in_array($sizeId, $attrValueIds)) {
                    $attrValue = isset($allSizeAttrs[$sizeId]) ? $allSizeAttrs[$sizeId] : null;
                    $matchedSizeName = $attrValue ? $attrValue->name : '未知尺寸';
                    
                    // 注意：我们手动添加product_type，因为数据库中没有此字段
                    $sizeInfo = [
                        'id' => $sku->id,
                        'size' => $matchedSizeName,
                        'gallery_id' => $sku->pid,
                        'attr_value_ids' => $attrValueIds,
                        'size_id' => $sizeId,
                        'product_type' => $productTypeName  // 从属性值中派生出的产品类型
                    ];
                    
                    $possibleSizes[] = $sizeInfo;
                    
                    // 找到一个匹配的尺寸ID后，不再检查此SKU的其他尺寸ID
                        break;
                }
            }
        }
        
        // 缓存结果
        $resultCache[$cacheKey] = $possibleSizes;
        
        return $possibleSizes;
    }
    
    /**
     * 识别产品列和位置列的配对
     */
    protected function identifyProductLocationPairs($headers1, $headers2)
    {
        $pairs = [];
        $attributeMappings = $this->getHeaderAttributeMapping();
        
        // 记录调试信息
        \Illuminate\Support\Facades\Log::channel('sku_import')->info('开始识别产品-位置配对', [
            'headers1' => $headers1,
            'headers2' => $headers2
        ]);
        
        // 获取产品类型名称映射
        $productTypeNameMappings = $this->getProductTypeNameMappings();
        
        // 创建产品类型关键词列表，包括"画布"、"画板"、"成品"等
        $productTypeKeywords = array_keys($productTypeNameMappings);
        // 移除"在庫"关键词，不再将其视为产品类型
        // $productTypeKeywords[] = '在庫';  // 添加"在庫"关键词
        $productTypeKeywords[] = '板';    // 添加"板"关键词
        $productTypeKeywords[] = '画布';  // 确保"画布"关键词存在
        $productTypeKeywords[] = '画芯';  // 添加"画芯"关键词
        $productTypeKeywords[] = '画';    // 添加"画"关键词
        $productTypeKeywords[] = '画板';  // 确保"画板"关键词存在
        $productTypeKeywords[] = '成品';  // 确保"成品"关键词存在
        // 添加数字成品关键词
        for ($i = 1; $i <= 10; $i++) {
            $productTypeKeywords[] = '成品'.$i;  // 添加"成品1"到"成品10"关键词
        }
        $productTypeKeywords = array_unique($productTypeKeywords);
        
        \Illuminate\Support\Facades\Log::channel('sku_import')->info('产品类型关键词列表', [
            'keywords' => $productTypeKeywords
        ]);
        
        // 获取需要排除的特殊列关键词
        $specialColumnKeywords = ['ID', '在庫', '在库', '库存', '数量', '数', '在庫数', '在庫数量'];
        
        // 第一步：扫描所有表头，找出所有可能的产品列
        $possibleProductCols = [];
        $specialColumns = []; // 记录特殊列的索引
        
        for ($i = 0; $i < count($headers1); $i++) {
            $header1 = trim($headers1[$i]);
            $header2 = isset($headers2[$i]) ? trim($headers2[$i]) : '';
            $isSpecialColumn = false;
            $specialType = '';
            
            // 首先检查是否为特殊列（如ID、库存等）
            if (isset($attributeMappings[$header1]) && 
                isset($attributeMappings[$header1]['attr_id']) && 
                $attributeMappings[$header1]['attr_id'] === 'special') {
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('识别到特殊列', [
                    'index' => $i,
                    'header1' => $header1,
                    'type' => $attributeMappings[$header1]['type'],
                    'special_type' => $attributeMappings[$header1]['value_id']
                ]);
                
                $isSpecialColumn = true;
                $specialType = $attributeMappings[$header1]['type'];
                $specialColumns[$i] = [
                    'type' => $specialType,
                    'special_type' => $attributeMappings[$header1]['value_id']
                ];
                
                // 特殊列也会检查第二行产品类型
                if (!empty($header2)) {
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('特殊列也会检查第二行产品类型', [
                        'index' => $i,
                        'header1' => $header1,
                        'header2' => $header2,
                        'type' => $specialType
                    ]);
                    
                    // 检查第二行是否包含产品类型关键词
                    foreach ($productTypeKeywords as $keyword) {
                        if (stripos($header2, $keyword) !== false) {
                            $possibleProductCols[] = $i;
                            \Illuminate\Support\Facades\Log::channel("sku_import")->info('找到可能的产品类型列(第二行)', [
                                'index' => $i,
                                'header1' => $header1,
                                'header2' => $header2,
                                'matched_keyword' => $keyword
                            ]);
                            break;
                        }
                    }
                }
            }
            
            if (isset($attributeMappings[$header2]) && 
                isset($attributeMappings[$header2]['attr_id']) && 
                $attributeMappings[$header2]['attr_id'] === 'special') {
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('识别到特殊列', [
                    'index' => $i,
                    'header2' => $header2,
                    'type' => $attributeMappings[$header2]['type'],
                    'special_type' => $attributeMappings[$header2]['value_id']
                ]);
                
                $isSpecialColumn = true;
                $specialType = $attributeMappings[$header2]['type'];
                $specialColumns[$i] = [
                    'type' => $specialType,
                    'special_type' => $attributeMappings[$header2]['value_id']
                ];
            }
            
            // 如果是特殊列，我们已经处理了其可能的产品类型，继续下一列
            if ($isSpecialColumn) {
                continue;
            }
            
            // 检查是否包含特殊列关键词，如果包含则跳过（但也检查第二行是否有产品类型）
            $containsSpecialKeyword = false;
            foreach ($specialColumnKeywords as $keyword) {
                if (stripos($header1, $keyword) !== false || stripos($header2, $keyword) !== false) {
                    $containsSpecialKeyword = true;
                    
                    // 检查第二行是否有产品类型
                    if (!empty($header2)) {
                        foreach ($productTypeKeywords as $pKeyword) {
                            if (stripos($header2, $pKeyword) !== false) {
                                $possibleProductCols[] = $i;
                                \Illuminate\Support\Facades\Log::channel("sku_import")->info('特殊关键词列但含产品类型', [
                                    'index' => $i,
                                    'header1' => $header1,
                                    'header2' => $header2,
                                    'keyword' => $keyword,
                                    'matched_product_keyword' => $pKeyword
                                ]);
                                break 2; // 跳出两层循环
                            }
                        }
                    }
                    
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('跳过包含特殊关键词的列', [
                        'index' => $i,
                        'header1' => $header1,
                        'header2' => $header2,
                        'keyword' => $keyword
                    ]);
                    break;
                }
            }
            
            if ($containsSpecialKeyword) {
                continue;
            }
            
            // 检查是否为已知的产品类型
            if (isset($attributeMappings[$header1]) || isset($attributeMappings[$header2])) {
                $possibleProductCols[] = $i;
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('找到已知产品类型列', [
                    'index' => $i,
                    'header1' => $header1,
                    'header2' => $header2
                ]);
                continue;
            }
            
            // 检查是否包含产品类型关键词
            foreach ($productTypeKeywords as $keyword) {
                if ((stripos($header1, $keyword) !== false) || (stripos($header2, $keyword) !== false)) {
                    $possibleProductCols[] = $i;
                    \Illuminate\Support\Facades\Log::channel("sku_import")->info('找到可能的产品类型列(关键词匹配)', [
                        'index' => $i,
                        'header1' => $header1,
                        'header2' => $header2,
                        'matched_keyword' => $keyword
                    ]);
                    break;
                }
            }
            
            // 检查是否为数字列（如"成品1"、"成品2"等）
            if (preg_match('/^(成品|画布|画板|板|画芯|画)\s*\d+$/', $header1) || 
                preg_match('/^(成品|画布|画板|板|画芯|画)\s*\d+$/', $header2)) {
                $possibleProductCols[] = $i;
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('找到可能的产品类型列(数字模式)', [
                    'index' => $i,
                    'header1' => $header1,
                    'header2' => $header2
                ]);
            }
        }
        
        // 第二步：对于每个可能的产品列，查找其对应的位置列
        foreach ($possibleProductCols as $productCol) {
            $header1 = trim($headers1[$productCol]);
            $header2 = isset($headers2[$productCol]) ? trim($headers2[$productCol]) : '';
            
            // 确定产品类型
            $productType = '';
            $attrId = null;
            $valueId = null;
            
            // 检查是否为特殊列
            $isSpecialColumn = isset($specialColumns[$productCol]);
            
            // 从已知映射中获取
            if (isset($attributeMappings[$header1])) {
                $productType = $attributeMappings[$header1]['type'];
                $attrId = $attributeMappings[$header1]['attr_id'];
                $valueId = $attributeMappings[$header1]['value_id'];
            } elseif (isset($attributeMappings[$header2])) {
                $productType = $attributeMappings[$header2]['type'];
                $attrId = $attributeMappings[$header2]['attr_id'];
                $valueId = $attributeMappings[$header2]['value_id'];
            } else {
                // 尝试从关键词中推断产品类型
                foreach ($productTypeKeywords as $keyword) {
                    if (stripos($header1, $keyword) !== false || stripos($header2, $keyword) !== false) {
                        $productType = $keyword;
                        break;
                    }
                }
                
                // 处理数字模式（如"成品1"）
                if (empty($productType)) {
                    if (preg_match('/^(成品|画布|画板|板|画芯|画)\s*(\d+)$/', $header1, $matches) || 
                        preg_match('/^(成品|画布|画板|板|画芯|画)\s*(\d+)$/', $header2, $matches)) {
                        $productType = $matches[1];
                    }
                }
            }
            
            // 如果无法确定产品类型，跳过
            if (empty($productType)) {
                continue;
            }
            
            // 查找位置列 - 检查右侧列
            $locationCol = $this->findLocationColumn($headers1, $headers2, $productCol + 1);
            if ($locationCol !== null) {
                $pair = [
                    'product_header' => !empty($header2) ? $header2 : $header1,
                    'product_type' => $productType,
                    'attr_id' => $attrId,
                    'value_id' => $valueId,
                    'location_col' => $locationCol
                ];
                
                // 如果是特殊列，记录特殊列属性
                if ($isSpecialColumn) {
                    $pair['product_type'] = $isSpecialColumn ? $specialColumns[$productCol]['type'] : $productType;
                    $pair['attr_id'] = 'special';
                    $pair['value_id'] = $specialColumns[$productCol]['special_type'];
                }
                
                $pairs[] = $pair;
                
                \Illuminate\Support\Facades\Log::channel("sku_import")->info('识别到产品-位置配对', [
                    'product_header' => !empty($header2) ? $header2 : $header1,
                    'product_type' => $isSpecialColumn ? $specialColumns[$productCol]['type'] : $productType,
                    'product_col' => $productCol,
                    'location_col' => $locationCol
                ]);
            }
        }
        
        \Illuminate\Support\Facades\Log::channel("sku_import")->info('识别产品-位置配对结果', [
            'pairs_count' => count($pairs),
            'pairs' => $pairs
        ]);
        
        return $pairs;
    }
    
    /**
     * 查找位置列
     * 从指定的起始列开始，查找可能的位置列
     */
    protected function findLocationColumn($headers1, $headers2, $startCol)
    {
        // 位置关键词
        $locationKeywords = ['位置', '库位', '仓位', '货位'];
        
        // 检查从起始列开始的几列
        for ($i = $startCol; $i < min($startCol + 3, count($headers1)); $i++) {
            $header1 = isset($headers1[$i]) ? trim($headers1[$i]) : '';
            $header2 = isset($headers2[$i]) ? trim($headers2[$i]) : '';
            
            // 检查是否包含位置关键词
            foreach ($locationKeywords as $keyword) {
                if (stripos($header1, $keyword) !== false || stripos($header2, $keyword) !== false) {
                    return $i;
                }
            }
            
            // 检查是否为空值+非空值的组合（可能是隐式位置列）
            if (empty($header1) && !empty($header2)) {
                return $i;
            }
            
            // 检查是否包含常见的位置格式（如"A1-2"、"1区2架3层"等）
            if (preg_match('/^[A-Za-z]\d+-\d+$/', $header1) || preg_match('/^[A-Za-z]\d+-\d+$/', $header2) ||
                preg_match('/^\d+区\d+架\d+层$/', $header1) || preg_match('/^\d+区\d+架\d+层$/', $header2)) {
                return $i;
            }
        }
        
        // 如果找不到明确的位置列，则假设下一列就是位置列
        return $startCol;
    }

    /**
     * 解析位置名称，将类似 "A1-3层" 或 "1A-3层" 或 "MN740-1区1架1层" 解析为区域、架位和层数
     * 
     * @param string $locationName 位置名称
     * @return array 解析后的位置信息 ['area' => '区域', 'shelf' => '架位', 'level' => '层数']
     */
    protected function parseLocationName(string $locationName): array
    {
        \Illuminate\Support\Facades\Log::channel("sku_import")->info('解析位置名称', [
            'location_name' => $locationName
        ]);
        
        // 默认区域为BH
        $area = 'BH';
        $shelf = '';
        $level = '';
        
        // 检查是否包含"区"、"架"、"层"关键字的格式 (例如: "MN740-1区1架1层")
        if (preg_match('/^(.*?)[-_\s]?(\d+区\d+架\d+层)$/', $locationName, $matches)) {
            $area = $matches[1]; // 提取前缀作为区域 (如"MN740")
            $location = $matches[2]; // 提取位置部分 (如"1区1架1层")
            
            // 进一步解析位置
            if (preg_match('/(\d+)区(\d+)架(\d+)层/', $location, $parts)) {
                $shelf = $parts[1] . $parts[2]; // 区号+架号作为架位
                $level = $parts[3]; // 层号
            }
        }
        // 检查是否包含"区-架-层"或"区 架 层"格式
        else if (preg_match('/^(.*?)[-_\s]?(\d+)区[-_\s]?(\d+)架[-_\s]?(\d+)层$/', $locationName, $matches)) {
            $area = $matches[1] ?: 'BH'; // 提取前缀作为区域，如果为空则使用默认值"BH"
            $shelf = $matches[2] . $matches[3]; // 区号+架号作为架位
            $level = $matches[4]; // 层号
        }
        // 如果是"A1-3层"这种原有格式
        else {
            // 清理名称 - 移除"层"字和空格
            $cleanName = str_replace('层', '', $locationName);
            $cleanName = trim($cleanName);
            
            // 尝试用"-"分割
            if (strpos($cleanName, '-') !== false) {
                $parts = explode('-', $cleanName);
                $shelf = $parts[0] ?: '*';
                $level = $parts[1] ?? '*';
            }
            // 尝试用空格分割
            elseif (strpos($cleanName, ' ') !== false) {
                $parts = explode(' ', $cleanName);
                $shelf = $parts[0] ?: '*';
                $level = $parts[1] ?? '*';
            }
            // 尝试分离数字和字母
            else {
                // 尝试匹配常见的格式，如"A1"（架号A，层号1）
                if (preg_match('/^([A-Za-z]+)(\d+)$/', $cleanName, $matches)) {
                    $shelf = $matches[1];
                    $level = $matches[2];
                }
                // 新增：匹配"1A"格式（数字在前，字母在后）
                else if (preg_match('/^(\d+)([A-Za-z]+)$/', $cleanName, $matches)) {
                    $shelf = $matches[1] . $matches[2]; // 组合为"1A"
                    $level = '*'; // 默认层号
                }
                // 新增：匹配"1A-3"格式（数字+字母-数字）
                else if (preg_match('/^(\d+[A-Za-z]+)-(\d+)$/', $cleanName, $matches)) {
                    $shelf = $matches[1]; // "1A"
                    $level = $matches[2]; // "3"
                }
                // 如果是"A4-3"这样的格式
                else if (preg_match('/^([A-Za-z]\d+)-(\d+)$/', $cleanName, $matches)) {
                    $shelf = $matches[1];
                    $level = $matches[2];
                }
                // 如果是"4号货架-2"这样的格式
                else if (preg_match('/^(.+?)-(\d+)$/', $cleanName, $matches)) {
                    $shelf = $matches[1];
                    $level = $matches[2];
                }
                // 尝试从末尾提取数字作为层号
                else if (preg_match('/^(.+?)(\d+)$/', $cleanName, $matches)) {
                    $shelf = $matches[1];
                    $level = $matches[2];
                } 
                else {
                    // 无法解析时，整个字符串作为架号，层号为*
                    $shelf = $cleanName;
                    $level = '*';
                }
            }
        }
        
        // 确保架号和层号不为空
        $shelf = $shelf ?: '*';
        $level = $level ?: '*';
        
        \Illuminate\Support\Facades\Log::channel("sku_import")->info('解析位置结果', [
            'area' => $area,
            'shelf' => $shelf,
            'level' => $level,
            'original' => $locationName
        ]);
        
        return [
            'area' => $area,
            'shelf' => $shelf,
            'level' => $level
        ];
    }

    /**
     * 获取产品类型ID映射
     * 动态获取产品类型ID映射，替换原有硬编码
     */
    protected function getProductTypeIdMappings()
    {
        // 使用静态缓存避免重复查询
        static $typeMappings = null;
        if ($typeMappings !== null) {
            return $typeMappings;
        }
        
        $typeMappings = [];
        
        // 尝试从数据库获取映射
        try {
            // 检查是否存在映射表和类
            if (class_exists('\\App\\Models\\Banhua\\TecBanhuaProductTypeMapping')) {
                // 如果存在映射表，使用映射表数据
                $typeMappings = \App\Models\Banhua\TecBanhuaProductTypeMapping::getAllMappings();
            } else {
                // 回退到常量类中的默认值
                $typeMappings = [
                    '板' => BanhuaConstants::getProductTypeIdByName('画板'),
                    '画板' => BanhuaConstants::getProductTypeIdByName('画板'),
                    '半成品' => BanhuaConstants::getProductTypeIdByName('画板'), // 半成品映射到画板
                    '画布' => BanhuaConstants::getProductTypeIdByName('画芯'),  // 修改为映射到画芯
                    '画芯' => BanhuaConstants::getProductTypeIdByName('画芯'),  // 修改为映射到画芯
                    '画' => BanhuaConstants::getProductTypeIdByName('画芯'),    // 修改为映射到画芯
                    '布' => BanhuaConstants::getProductTypeIdByName('画芯'),    // 新增布映射到画芯
                    '成品' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品1' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品2' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品3' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品4' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品5' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品6' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品7' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品8' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品9' => BanhuaConstants::getProductTypeIdByName('成品'),
                    '成品10' => BanhuaConstants::getProductTypeIdByName('成品'),
                ];
                
                // 直接从属性系统获取
                try {
                    $productTypeAttrId = BanhuaConstants::getProductTypeAttrId();
                    $values = TecBanhuaAttrValueModel::where('attr_id', $productTypeAttrId)->get();
                    foreach ($values as $v) {
                        $typeMappings[$v->name] = $v->id;
                    }
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('获取产品类型属性值失败', [
                        'error' => $e->getMessage()
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取产品类型ID映射失败，使用默认值', [
                'error' => $e->getMessage()
            ]);
            
            // 发生错误时使用常量类中的默认值
            $typeMappings = [
                '板' => BanhuaConstants::getProductTypeIdByName('画板'),
                '画板' => BanhuaConstants::getProductTypeIdByName('画板'),
                '半成品' => BanhuaConstants::getProductTypeIdByName('画板'), // 半成品映射到画板
                '画布' => BanhuaConstants::getProductTypeIdByName('画芯'),  // 修改为映射到画芯
                '画芯' => BanhuaConstants::getProductTypeIdByName('画芯'),  // 修改为映射到画芯
                '画' => BanhuaConstants::getProductTypeIdByName('画芯'),    // 修改为映射到画芯
                '布' => BanhuaConstants::getProductTypeIdByName('画芯'),    // 新增布映射到画芯
                '成品' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品1' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品2' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品3' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品4' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品5' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品6' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品7' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品8' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品9' => BanhuaConstants::getProductTypeIdByName('成品'),
                '成品10' => BanhuaConstants::getProductTypeIdByName('成品'),
            ];
        }
        
        return $typeMappings;
    }
    
    /**
     * 获取产品类型名称映射
     * 动态获取产品类型名称别名映射，替换原有硬编码
     */
    protected function getProductTypeNameMappings()
    {
        // 使用静态缓存避免重复查询
        static $nameMappings = null;
        if ($nameMappings !== null) {
            return $nameMappings;
        }
        
        $nameMappings = [];
        
        // 尝试从数据库获取映射
        try {
            // 检查是否存在映射表和类
            if (class_exists('\\App\\Models\\Banhua\\TecBanhuaProductTypeMapping')) {
                // 如果存在映射表，使用映射表数据
                $nameMappings = \App\Models\Banhua\TecBanhuaProductTypeMapping::getNameMappings();
            } else {
                // 回退到常量类中的默认映射
                $nameMappings = [];
                foreach (BanhuaConstants::getInitialProductTypeMappings() as $mapping) {
                    $nameMappings[$mapping['alias_name']] = $mapping['primary_name'];
                }
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取产品类型名称映射失败，使用默认值', [
                'error' => $e->getMessage()
            ]);
            
            // 发生错误时使用常量类中的默认映射
            $nameMappings = [];
            foreach (BanhuaConstants::getInitialProductTypeMappings() as $mapping) {
                $nameMappings[$mapping['alias_name']] = $mapping['primary_name'];
            }
        }
        
        return $nameMappings;
    }
    
} 