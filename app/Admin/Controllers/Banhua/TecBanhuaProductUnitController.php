<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use App\Admin\Repositories\Banhua\TecBanhuaProductUnitRepo;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use App\Models\Banhua\TecBanhuaProductUnitModel;
use App\Admin\Actions\Post\Restore;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 阪画产品单位控制器
 */
class TecBanhuaProductUnitController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '阪画产品单位';

    /**
     * 列表页面
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header($this->title)
            ->description('管理产品单位')
            ->body(function (Row $row) {
                // 左侧占据一半宽度
                $row->column(6, function (Column $column) {
                    $column->append($this->grid());
                });

                // 右侧留空
                $row->column(6, function (Column $column) {
                    // 留空，不添加内容
                });
            });
    }

    /**
     * 构建列表
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecBanhuaProductUnitRepo(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('unit_name', '单位名称');
            $grid->column('sort', '排序')->sortable()->editable();
            // $grid->column('created_at', '创建时间');

            // 默认按排序字段排序
            $grid->model()->orderBy('sort');

            // 禁止多选
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('unit_name', '单位名称');
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecBanhuaProductUnitModel::class));
                }
            });
        });
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    protected function detail($id)
    {
        return Show::make($id, new TecBanhuaProductUnitRepo(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('unit_name', '单位名称');
            $show->field('sort', '排序');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 构建表单
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecBanhuaProductUnitRepo(), function (Form $form) {
            $form->display('id', 'ID');
            $form->text('unit_name', '单位名称')->required();
            $form->number('sort', '排序')->default(0)->help('数值越小越靠前');

            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮
            // 隐藏查看按钮
            $form->tools(function (Form\Tools $tools) {
                $tools->disableView();
            });
        });
    }

    /**
     * 处理行内编辑请求
     *
     * @param mixed $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update($id)
    {
        $fieldMap = [
            'sort' => '排序',
            'unit_name' => '单位名称',
        ];

        try {
            // 获取请求数据
            $request = request();
            
            // 记录请求数据
            Log::info('行内编辑请求数据: ', $request->all());
            
            // 兼容不同的请求格式
            $name = $request->input('name');
            $value = $request->input('value');
            
            // 如果没有name/value格式，则尝试直接获取字段
            if (empty($name)) {
                // 遍历可能的字段
                foreach ($fieldMap as $field => $label) {
                    if ($request->has($field)) {
                        $name = $field;
                        $value = $request->input($field);
                        break;
                    }
                }
            }
            
            // 验证字段是否有效
            if (!array_key_exists($name, $fieldMap)) {
                Log::error('无效的字段名: ' . $name);
                return response()->json([
                    'status' => false,
                    'data' => [
                        'message' => '无效的字段名',
                    ]
                ]);
            }
            
            // 根据字段进行特定验证
            if ($name === 'sort') {
                if (!is_numeric($value)) {
                    return response()->json([
                        'status' => false,
                        'data' => [
                            'message' => '排序必须是数字',
                        ]
                    ]);
                }
                $value = (int)$value;
            }
            
            // 更新数据
            $model = TecBanhuaProductUnitModel::findOrFail($id);
            $model->{$name} = $value;
            $model->save();
            
            // 获取可读的字段名称
            $fieldLabel = $fieldMap[$name] ?? $name;
            
            // 返回成功响应
            return response()->json([
                'status' => true,
                'data' => [
                    'message' => $fieldLabel . '已更新为: ' . $value,
                ]
            ]);
        } catch (\Exception $e) {
            // 记录错误
            Log::error('行内编辑错误: ' . $e->getMessage());
            
            // 返回错误响应
            return response()->json([
                'status' => false,
                'data' => [
                    'message' => '更新失败: ' . $e->getMessage(),
                ]
            ]);
        }
    }
} 