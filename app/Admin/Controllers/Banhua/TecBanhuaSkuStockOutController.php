<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Banhua\TecBanhuaSkuStockModel;
use App\Models\Banhua\TecBanhuaSkuStockOutLogModel;
use App\Models\Banhua\TecBanhuaSkuStockOutLogItemModel;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\TecWarehouseModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Validator;

/**
 * SKU库存出库控制器
 */
class TecBanhuaSkuStockOutController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = 'SKU库存出库';

    /**
     * 处理表单提交
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function store()
    {
        // 验证参数
        $request = request();
        
        \Illuminate\Support\Facades\Log::info('SKU出库表单提交', [
            'request_all' => $request->all()
        ]);
        
        // 检查是否有商品
        $hasItems = false;
        
        // 检查items数组
        if (!empty($request->items) && is_array($request->items)) {
            $hasItems = true;
        }
        
        // 检查items.*.sku_stock_id字段
        if (!$hasItems) {
            foreach ($request->all() as $key => $value) {
                if (strpos($key, 'items') === 0 && strpos($key, 'sku_stock_id') !== false && !empty($value)) {
                    $hasItems = true;
                    break;
                }
            }
        }
        
        if (!$hasItems) {
            return response()->json([
                'status' => false,
                'message' => '请至少添加一个出库商品',
                'data' => null
            ]);
        }
        
        // 处理并规范化商品数据
        $fixedItems = [];
        
        // 如果是数组形式
        if (!empty($request->items) && is_array($request->items)) {
            foreach ($request->items as $key => $item) {
                // 检查必填字段
                if (empty($item['sku_stock_id']) || empty($item['quantity']) || empty($item['warehouse_id'])) {
                    continue;
                                    }
                
                // 确保数量是正整数
                $item['quantity'] = max(1, intval($item['quantity']));
                
                // 添加到修复后的数组
                $fixedItems[] = $item;
    }
        }
        // 如果是表单字段形式
        else {
            $itemData = [];
            
            // 从请求中提取items相关字段
            foreach ($request->all() as $key => $value) {
                if (preg_match('/^items\[(\d+)\]\[([a-z_]+)\]$/', $key, $matches)) {
                    $index = $matches[1];
                    $field = $matches[2];
                    
                    if (!isset($itemData[$index])) {
                        $itemData[$index] = [];
                    }
                    
                    $itemData[$index][$field] = $value;
                }
            }
            
            // 处理提取的字段
            foreach ($itemData as $item) {
                // 检查必填字段
                if (empty($item['sku_stock_id']) || empty($item['quantity']) || empty($item['warehouse_id'])) {
                    continue;
                }
                
                // 确保数量是正整数
                $item['quantity'] = max(1, intval($item['quantity']));
                
                // 添加到修复后的数组
                $fixedItems[] = $item;
            }
        }
        
        // 更新请求数据
        $request->merge(['items' => $fixedItems]);
        
        // 检查修复后的商品数组是否为空
        if (empty($fixedItems)) {
            return response()->json([
                'status' => false,
                'message' => '请至少添加一个有效的出库商品',
                'data' => null
            ]);
        }
        
        \Illuminate\Support\Facades\Log::info('SKU出库表单修复后', [
            'fixed_items' => $fixedItems
        ]);

        // 开始数据库事务
        DB::beginTransaction();
        
        try {
            // 创建出库日志记录
            $outLog = new TecBanhuaSkuStockOutLogModel();
            $outLog->fill($request->except('items', '_token', '_previous_'));
            $outLog->out_code = TecBanhuaSkuStockOutLogModel::generateOutCode();
            $outLog->created_by = Admin::user()->id;
            $outLog->save();
            
            // 处理所有商品出库
            foreach ($fixedItems as $item) {
                // 获取库存记录
                $stock = TecBanhuaSkuStockModel::lockForUpdate()->find($item['sku_stock_id']);
                
                if (!$stock) {
                    throw new \Exception("未找到SKU库存记录（ID: {$item['sku_stock_id']}）");
                }
                
                // 确保库存充足
                if ($stock->stock < $item['quantity']) {
                    throw new \Exception("SKU库存不足：当前库存{$stock->stock}，需要出库{$item['quantity']}");
                }
                
                // 创建或更新出库明细
                $outLogItem = new TecBanhuaSkuStockOutLogItemModel();
                $outLogItem->out_log_id = $outLog->id;
                $outLogItem->sku_stock_id = $item['sku_stock_id'];
                $outLogItem->warehouse_id = $item['warehouse_id'];
                $outLogItem->quantity = $item['quantity'];
                $outLogItem->unit_cost = $stock->cost_price ?? 0;
                $outLogItem->unit_price = $stock->sale_price ?? 0;
                $outLogItem->save();
                
                // 减少库存
                $stock->stock -= $item['quantity'];
                $stock->save();
            }
            
            // 提交事务
            DB::commit();
            
            // 添加时间戳，确保不被缓存
            $timestamp = time();
            $url = admin_url('banhua/sku-stock-out/') . '?t=' . $timestamp;
            
            // 确保URL是绝对路径
            if (!str_starts_with($url, 'http')) {
                $url = url($url);
            }
            
            // 返回符合Dcat Admin格式的响应
            return response()->json([
                'status'  => true,
                'message' => "出库成功，出库单号：{$outLog->out_code}",
                'data' => [
                    'out_code' => $outLog->out_code
                ],
                'redirect' => $url
                ]);
                
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            // 记录错误
            \Illuminate\Support\Facades\Log::error('SKU出库失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $request->all()
            ]);
            
            // 返回错误信息
            return response()->json([
                'status' => false,
                'message' => "出库失败：" . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 处理表单提交（通过自定义AJAX）
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitForm()
    {
        try {
            // 验证参数
            $request = request();
            
            \Illuminate\Support\Facades\Log::info('SKU出库表单提交(submitForm)', [
                'request_all' => $request->all()
            ]);
            
            // 检查是否有商品
            $hasItems = false;
            
            // 检查items数组
            if (!empty($request->items) && is_array($request->items)) {
                $hasItems = true;
            }
            
            // 检查items.*.sku_stock_id字段
            if (!$hasItems) {
                foreach ($request->all() as $key => $value) {
                    if (strpos($key, 'items') === 0 && strpos($key, 'sku_stock_id') !== false && !empty($value)) {
                        $hasItems = true;
                        break;
                    }
                }
            }
            
            if (!$hasItems) {
                return response()->json([
                    'status' => false,
                    'message' => '请至少添加一个出库商品',
                    'data' => null
                ]);
            }
            
            // 处理并规范化商品数据
            $fixedItems = [];
            
            // 如果是数组形式
            if (!empty($request->items) && is_array($request->items)) {
                foreach ($request->items as $key => $item) {
                    // 检查必填字段
                    if (empty($item['sku_stock_id']) || empty($item['quantity']) || empty($item['warehouse_id'])) {
                        continue;
                    }
                    
                    // 确保数量是正整数
                    $item['quantity'] = max(1, intval($item['quantity']));
                    
                    // 添加到修复后的数组
                    $fixedItems[] = $item;
                }
            }
            // 如果是表单字段形式
            else {
                $itemData = [];
                
                // 从请求中提取items相关字段
                foreach ($request->all() as $key => $value) {
                    if (preg_match('/^items\[(\d+)\]\[([a-z_]+)\]$/', $key, $matches)) {
                        $index = $matches[1];
                        $field = $matches[2];
                        
                        if (!isset($itemData[$index])) {
                            $itemData[$index] = [];
                        }
                        
                        $itemData[$index][$field] = $value;
                    }
                }
                
                // 处理提取的字段
                foreach ($itemData as $item) {
                    // 检查必填字段
                    if (empty($item['sku_stock_id']) || empty($item['quantity']) || empty($item['warehouse_id'])) {
                        continue;
                    }
                    
                    // 确保数量是正整数
                    $item['quantity'] = max(1, intval($item['quantity']));
                
                    // 添加到修复后的数组
                    $fixedItems[] = $item;
                }
            }
            
            // 更新请求数据
            $request->merge(['items' => $fixedItems]);
            
            // 检查修复后的商品数组是否为空
            if (empty($fixedItems)) {
                return response()->json([
                    'status' => false,
                    'message' => '请至少添加一个有效的出库商品',
                    'data' => null
                ]);
            }
            
            \Illuminate\Support\Facades\Log::info('SKU出库表单修复后(submitForm)', [
                'fixed_items' => $fixedItems
            ]);

            // 开始数据库事务
            DB::beginTransaction();
            
            try {
                // 创建出库日志记录
                $outLog = new TecBanhuaSkuStockOutLogModel();
                $outLog->fill($request->except('items', '_token', '_previous_'));
                $outLog->out_code = TecBanhuaSkuStockOutLogModel::generateOutCode();
                $outLog->created_by = Admin::user()->id;
                $outLog->save();
                
                // 处理所有商品出库
                foreach ($fixedItems as $item) {
                    // 获取库存记录
                    $stock = TecBanhuaSkuStockModel::lockForUpdate()->find($item['sku_stock_id']);
                    
                    if (!$stock) {
                        throw new \Exception("未找到SKU库存记录（ID: {$item['sku_stock_id']}）");
                    }
                    
                    // 确保库存充足
                    if ($stock->stock < $item['quantity']) {
                        throw new \Exception("SKU库存不足：当前库存{$stock->stock}，需要出库{$item['quantity']}");
                    }
                    
                    // 创建或更新出库明细
                    $outLogItem = new TecBanhuaSkuStockOutLogItemModel();
                    $outLogItem->out_log_id = $outLog->id;
                    $outLogItem->sku_stock_id = $item['sku_stock_id'];
                    $outLogItem->warehouse_id = $item['warehouse_id'];
                    $outLogItem->quantity = $item['quantity'];
                    $outLogItem->unit_cost = $stock->cost_price ?? 0;
                    $outLogItem->unit_price = $stock->sale_price ?? 0;
                    $outLogItem->save();
                
                    // 减少库存
                    $stock->stock -= $item['quantity'];
                    $stock->save();
                }
                
                // 提交事务
                DB::commit();
                
                // 添加时间戳，确保不被缓存
                $timestamp = time();
                $url = admin_url('banhua/sku-stock-out/') . '?t=' . $timestamp;
                    
                // 确保URL是绝对路径
                if (!str_starts_with($url, 'http')) {
                    $url = url($url);
                }
                
                // 返回符合Dcat Admin格式的响应
                return response()->json([
                    'status'  => true,
                    'message' => "出库成功，出库单号：{$outLog->out_code}",
                    'data' => [
                        'out_code' => $outLog->out_code
                    ],
                    'redirect' => $url
                ]);
                    
            } catch (\Exception $e) {
                // 回滚事务
                DB::rollBack();
                    
                // 记录错误
                \Illuminate\Support\Facades\Log::error('SKU出库失败(submitForm)', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'data' => $request->all()
                ]);
                    
                // 返回错误信息
                return response()->json([
                    'status' => false,
                    'message' => "出库失败：" . $e->getMessage(),
                    'data' => null
                ]);
            }
        } catch (\Exception $e) {
            // 记录错误
            \Illuminate\Support\Facades\Log::error('SKU出库表单处理异常(submitForm)', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回错误信息
            return response()->json([
                'status' => false,
                'message' => "处理失败：" . $e->getMessage(),
                'data' => null
            ]);
                        }
    }

    /**
     * 新建出库页面
     */
    public function index(Content $content): Content
    {
        // 添加直接修复Ajax.js的脚本
        Admin::script(<<<JS
// 直接修复Ajax.js中的handleJsonResponse方法
$(function() {
    // 确保Dcat对象存在
    if (typeof Dcat !== 'undefined') {
        // 保存原始方法的引用
        var originalHandleJsonResponse = Dcat.handleJsonResponse;
        
        // 重写handleJsonResponse方法
        Dcat.handleJsonResponse = function(response, options) {
            console.log('拦截到Ajax响应:', response);
            
            // 确保response不为null或undefined
            if (!response) {
                console.warn('响应为空，创建默认响应对象');
                response = {
                    status: false,
                    message: '',
                    data: null
                };
            }
            
            // 修复response.message
            if (response.message === undefined || response.message === null) {
                console.warn('响应中message为空，设置默认值');
                response.message = '';
            }
            
            // 修复response.status
            if (response.status === undefined || response.status === null) {
                console.warn('响应中status为空，设置默认值');
                response.status = false;
            }
            
            // 修复response.data
            if (response.data === undefined) {
                console.warn('响应中data为空，设置默认值');
                response.data = null;
            }
            
            // 检查是否需要跳转
            if (response.status === true && response.redirect) {
                console.log('检测到需要跳转到:', response.redirect);
                        
                // 在原始方法处理后执行跳转
                setTimeout(function() {
                    console.log('执行跳转到:', response.redirect);
                    window.location.href = response.redirect;
                }, 1000);
            }
            
            // 调用原始方法
            return originalHandleJsonResponse.call(Dcat, response, options);
        };
                    
        console.log('已成功修复Ajax.js中的handleJsonResponse方法');
    }
                    
    // 添加全局表单提交成功处理
    $(document).on('pjax:success', function() {
        var successMessage = localStorage.getItem('sku_out_success');
        if (successMessage) {
            Dcat.success(successMessage);
            localStorage.removeItem('sku_out_success');
                    }
    });
});
JS
        );
        
        // 自定义处理表单和消息显示的JS
        Admin::script($this->getCustomScript());
        
        // 添加隐藏标准消息的CSS
        Admin::style($this->getCustomStyle());
        
        // 添加自定义初始化脚本
        Admin::script(<<<JS
$(function() {
    // 确保页面加载完成后立即执行初始化
    setTimeout(function() {
        // 修改表单提交地址为自定义路由
        $('form.form-horizontal').attr('action', '/admin/banhua/sku-stock-out/submit');
                            
        // 移除可能的列表按钮
        $('.form-footer a.btn-default').remove();
        
        // 确保提交按钮文本正确
        $('.form-footer button[type="submit"]').text('确认出库');
        
        // 如果没有商品项，自动添加一个
        if ($('.has-many-items-form').length === 0) {
            $('.add-has-many-items').click();
        }
        
        // 修改表单提交事件
        $('form.form-horizontal').off('submit').on('submit', function(e) {
            e.preventDefault();
            
            var form = this;
            var formData = new FormData(form);
            
            // 发送AJAX请求
            $.ajax({
                url: form.action,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                        success: function(response) {
                            if (response.status === true) {
                                // 显示成功消息
                        Dcat.success(response.message);
                        
                        // 存储成功消息
                        localStorage.setItem('sku_out_success', response.message);
                                
                        // 直接跳转
                        console.log('成功提交，准备跳转到:', response.redirect);
                                    setTimeout(function() {
                                        window.location.href = response.redirect;
                        }, 1000);
                                } else {
                                Dcat.error(response.message || '操作失败');
                            }
                        },
                error: function() {
                    Dcat.error('提交失败，请稍后重试');
                }
            });
            
            return false;
                    });
    }, 100);
            });
JS
            );
            
        return $content
            ->header($this->title)
            ->description('创建出库单')
            ->body($this->form());
    }

    /**
     * 构建出库表单
     */
    protected function form(): Form
    {
        $form = Form::make(new TecBanhuaSkuStockOutLogModel(), function (Form $form) {
            // 设置表单提交地址
            $form->action(admin_url('banhua/sku-stock-out'));
            
            // 启用提交按钮，但禁用其他不需要的功能
            $form->disableResetButton();
            $form->disableViewCheck();
            $form->disableEditingCheck();
            $form->disableCreatingCheck();
            
            $form->row(function (Form\Row $row) {
            // 添加电商平台选择
                $row->width(2)->select('ecommerce_platform_id', '电商平台')
                ->options(function() {
                    return \App\Models\Banhua\TecBanhuaEcommercePlatformModel::where('status', 1)
                        ->pluck('platform_name', 'id')
                        ->toArray();
                })
                ->help('选择订单来源的电商平台')
                    ->required()
                    ->default(1);
                
            // 添加订单号
                $row->width(4)->text('order_number', '订单号')
                ->help('电商平台的订单编号');
            });

            $form->row(function (Form\Row $row) {
                // 客户姓名
                $row->width(4)->text('customer_name', '客户姓名');
                
                // 客户电话
                $row->width(4)->text('customer_phone', '客户电话');
                
                // 客户地址
                $row->width(4)->textarea('customer_address', '客户地址')->rows(1);
            
            // 添加快递信息
                $row->width(4)->select('express_company', '快递公司')
                    ->options([
                        'ヤマト運輸' => 'ヤマト運輸',
                        '佐川急便' => '佐川急便',
                        'EMS' => 'EMS',
                        'その他' => 'その他'
                    ])->default('佐川急便');

                $row->width(4)->text('tracking_number', '快递单号');

                // 配送要求
                $row->width(4)->textarea('delivery_requirements', '配送要求')->rows(1);
            });

            $form->row(function (Form\Row $row) {
            // 出库原因
                $row->width(4)->select('out_reason', '出库原因')
                ->options([
                    '客户订单' => '客户订单',
                    '内部使用' => '内部使用',
                    '损耗' => '损耗',
                    '其他' => '其他'
                ])
                ->required()
                ->default('客户订单')
                ->rules('required', [
                    'required' => '出库原因不能为空'
                ]);
            
            // 备注
                $row->width(4)->textarea('remarks', '备注')
                ->rows(3);
            });
                
            
            $form->row(function (Form\Row $row) {
            // 商品选择表格（使用堆叠表单实现多SKU选择）
                $row->hasMany('items', '出库商品列表', function (Form\NestedForm $form) {
                // 选择SKU库存
                // 使用仓库过滤SKU库存
                $form->select('warehouse_id', '选择仓库')
                    ->options(function() {
                            return TecWarehouseModel::pluck('name', 'id')->toArray();
                    })
                    ->load('sku_stock_id', '/api/banhua/sku-stock/find')
                    ->required();

                // 选择SKU库存
                $form->select('sku_stock_id', 'SKU库存')
                    ->options(function() {
                        return [];
                    })
                    ->required();
                    
                // 出库数量
                $form->number('quantity', '出库数量')
                    ->min(1)
                    ->required()
                    ->default(1)
                    ->rules('required|integer|min:1', [
                        'required' => '出库数量不能为空',
                        'integer' => '出库数量必须是整数',
                        'min' => '出库数量不能小于1'
                    ]);
                    
                // 隐藏字段，用于前端验证
                $form->hidden('max_stock');
            })->useTable()->required();
            });
            
            // 表单保存前处理
            $form->saving(function (Form $form) {
                // 检查是否有商品
                if (empty($form->items)) {
                    return $form->response()->error('请至少添加一个出库商品');
                }
                
                // 生成出库单号
                $form->out_code = TecBanhuaSkuStockOutLogModel::generateOutCode();
                
                // 设置创建者ID
                $form->created_by = Admin::user()->id;
            });
            
            // 表单保存后处理
            $form->saved(function (Form $form) {
                // 获取刚刚创建的出库记录
                $outLog = TecBanhuaSkuStockOutLogModel::find($form->getKey());
                
                // 开始数据库事务
                DB::beginTransaction();
                
                try {
                    // 处理所有商品出库
                    foreach ($form->items as $item) {
                        // 获取库存记录
                        $stock = TecBanhuaSkuStockModel::lockForUpdate()->find($item['sku_stock_id']);
                        
                        if (!$stock) {
                            throw new \Exception("未找到SKU库存记录（ID: {$item['sku_stock_id']}）");
                        }
                        
                        // 确保库存充足
                        if ($stock->stock < $item['quantity']) {
                            throw new \Exception("SKU库存不足：当前库存{$stock->stock}，需要出库{$item['quantity']}");
                        }
                        
                        // 创建或更新出库明细
                        $outLogItem = TecBanhuaSkuStockOutLogItemModel::firstOrNew([
                            'out_log_id' => $outLog->id,
                            'sku_stock_id' => $item['sku_stock_id']
                        ]);
                        
                        // 设置数量和价格信息以及仓库ID
                        $outLogItem->warehouse_id = $item['warehouse_id'];
                        $outLogItem->quantity = $item['quantity'];
                        $outLogItem->unit_cost = $stock->cost_price ?? 0;
                        $outLogItem->unit_price = $stock->sale_price ?? 0;
                        $outLogItem->save();
                        
                        // 减少库存
                        $stock->stock -= $item['quantity'];
                        $stock->save();
                    }
                    
                    // 提交事务
                    DB::commit();
                    
                    // 不使用admin_success或success方法，避免重复提示
                    // 直接返回响应
                    return $form->response()
                        ->success('出库成功')
                        ->redirect('banhua/sku-stock-out');
                        
                } catch (\Exception $e) {
                    // 回滚事务
                    DB::rollBack();
                    
                    // 删除创建的出库日志记录
                    if ($outLog) {
                        $outLog->delete();
                    }
                    
                    // 记录错误
                    \Illuminate\Support\Facades\Log::error('SKU出库失败', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'data' => $form->input()
                    ]);
                    
                    // 返回错误信息
                    return $form->response()->error("出库失败：" . $e->getMessage());
                }
            });
            
            // 自定义工具栏
            $form->tools(function (Form\Tools $tools) {
                $tools->disableDelete();
                $tools->disableView();
                // 移除列表按钮
                $tools->disableList();
            });
            
            // 表单提交按钮文本修改
            $form->footer(function ($footer) {
                $footer->disableReset();
                $footer->disableViewCheck();
                $footer->disableEditingCheck();
                $footer->disableCreatingCheck();
                
                // 修改提交按钮文本
                Admin::script('
                    $(function () {
                        $(".form-footer button[type=\'submit\']").text("确认出库");
                    });
                ');
            });

            // 添加JavaScript脚本，增强仓库和SKU库存的联动效果
            Admin::script(<<<JS
    $(function() {
            // 确保表单加载后至少有一个商品项
            if ($('.has-many-items-form').length === 0) {
                $('.add-has-many-items').click();
            }

            // 防抖函数
            function debounce(func, wait) {
                let timeout;
                return function() {
                    const context = this;
                    const args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        func.apply(context, args);
                    }, wait);
                };
            }
            
            // 加载SKU库存选项
            function loadSkuStocks(warehouseId, skuStockSelect) {
                if (!warehouseId) return;
                
                // 每次都从服务器获取最新数据
            $.ajax({
                    url: '/admin/api/banhua/sku-stock/find',
                type: 'GET',
                    data: { q: warehouseId },
                success: function(data) {
                        updateSkuStockSelect(skuStockSelect, data);
                    }
                });
            }
                    
            // 更新SKU库存下拉框
            function updateSkuStockSelect(skuStockSelect, data) {
                    // 检查是否为select2元素
                    var isSelect2 = skuStockSelect.hasClass('select2-hidden-accessible');
                        var currentValue = skuStockSelect.val();
                    
                    if (isSelect2) {
                            // 清空并设置select2数据
                        var select2Data = [];
                            
                        $.each(data, function(id, text) {
                            select2Data.push({
                                id: id,
                                text: text
                            });
                        });
                        
                        skuStockSelect.select2('destroy');
                            skuStockSelect.empty();
                        skuStockSelect.select2({
                            data: select2Data,
                            placeholder: '选择SKU库存',
                            allowClear: true
                        });
                            
                            // 如果有原来的值，恢复它
                            if (currentValue) {
                                skuStockSelect.val(currentValue).trigger('change');
                            }
                    } else {
                            // 常规select处理
                        skuStockSelect.empty();
                        skuStockSelect.append('<option value="">选择SKU库存</option>');
                        $.each(data, function(id, text) {
                            skuStockSelect.append(new Option(text, id));
                        });
                            
                            // 如果有原来的值，恢复它
                            if (currentValue) {
                                skuStockSelect.val(currentValue);
                            }
                        }
            }
                    
            // 监听仓库选择变化，使用防抖
            $(document).on('change', "select[name$='[warehouse_id]']", debounce(function() {
                var warehouseId = $(this).val();
                if (!warehouseId) return;
                
                // 获取当前行的SKU库存下拉框
                var skuStockSelect = $(this).closest('.fields-group').find("select[name$='[sku_stock_id]']");
                if (skuStockSelect.length === 0) return;
                
                loadSkuStocks(warehouseId, skuStockSelect);
            }, 300));

            // 确保初始仓库加载，只处理可见元素
            $("select[name$='[warehouse_id]']:visible").each(function() {
                var warehouseId = $(this).val();
                if (warehouseId) {
                    var row = $(this).closest('.has-many-items-form');
                    var skuStockSelect = row.find("select[name$='[sku_stock_id]']");
                    loadSkuStocks(warehouseId, skuStockSelect);
                }
            });
    });
JS
            );
        });
        
        return $form;
    }
    
    /**
     * 获取自定义的JS脚本
     */
    protected function getCustomScript(): string
    {
        return <<<'JS'
// 等待DOM完全加载
document.addEventListener('DOMContentLoaded', function() {
    // 添加全局Ajax错误处理
    if (window.$ && window.$.ajaxSetup) {
        // 保存原始的ajaxSetup
        const originalAjaxSetup = $.ajaxSetup;
        
        // 重写ajaxSetup
        $.ajaxSetup = function(options) {
            const originalOptions = options || {};
            
            // 保存原始的success和error回调
            const originalSuccess = originalOptions.success;
            const originalError = originalOptions.error;
            
            // 添加全局响应处理
            originalOptions.success = function(response, textStatus, jqXHR) {
                // 确保response是一个对象
                if (response === null || response === undefined) {
                    response = {};
                }
                
                // 确保response.status存在
                if (response.status === undefined) {
                    response.status = false;
                }
                
                // 确保response.message存在
                if (response.message === undefined) {
                    response.message = '';
                }
                
                // 确保response.data存在
                if (response.data === undefined) {
                    response.data = null;
                }
                
                // 调用原始的success回调
                if (typeof originalSuccess === 'function') {
                    originalSuccess.call(this, response, textStatus, jqXHR);
                            }
            };
            
            // 调用原始的ajaxSetup
            return originalAjaxSetup.call($, originalOptions);
        };
    }
    
    // 完全禁用Dcat Admin的通知系统
    if (window.Dcat) {
        // 保存原始方法的引用
        const originalSuccess = Dcat.success;
        const originalInfo = Dcat.info;
        const originalWarning = Dcat.warning;
        const originalError = Dcat.error;
        
        // 重写所有通知方法，拦截包含"出库成功"的消息
        Dcat.success = function(message) {
            if (message && message.includes('出库成功')) {
                return false;
            }
            return originalSuccess.apply(this, arguments);
        };
        
        // 其他通知方法正常工作
        Dcat.info = originalInfo;
        Dcat.warning = originalWarning;
        Dcat.error = originalError;
        
        // 修复Ajax响应处理
        if (Dcat.handleJsonResponse) {
            const originalHandleJsonResponse = Dcat.handleJsonResponse;
            
            Dcat.handleJsonResponse = function(response, options) {
                // 确保response是一个有效的对象
                if (!response) {
                    console.error('Ajax响应为空');
                    return false;
                }
                
                // 确保response.status存在
                if (response.status === undefined) {
                    response.status = false;
                }
                
                // 确保response.message存在
                if (response.message === undefined) {
                    response.message = '';
                }
                
                // 确保response.data存在
                if (response.data === undefined) {
                    response.data = null;
                }
                
                return originalHandleJsonResponse.call(this, response, options);
            };
        }
    }
    
    // 创建自定义表单提交处理
    initCustomForm();

    // PJAX完成后重新初始化
    $(document).on('pjax:complete', function() {
        setTimeout(initCustomForm, 100);
    });
});

// 初始化自定义表单
function initCustomForm() {
    // 获取表单元素
    const form = document.querySelector('form.form-horizontal');
    if (!form) return;
    
    // 如果没有商品项，自动添加一个
    const hasItems = form.querySelector('.has-many-items-form');
    if (!hasItems) {
        const addButton = form.querySelector('.add-has-many-items');
        if (addButton) {
            addButton.click();
                }
    }
    
    // 修复表单按钮
    const formFooter = form.querySelector('.form-footer') || form.querySelector('.box-footer');
    if (formFooter) {
        // 移除列表按钮
        const listBtn = formFooter.querySelector('a.btn-default');
        if (listBtn) {
            listBtn.remove();
        }
        
        // 确保有提交按钮
        let submitBtn = formFooter.querySelector('button[type="submit"]');
        if (!submitBtn) {
            submitBtn = document.createElement('button');
            submitBtn.type = 'submit';
            submitBtn.className = 'btn btn-primary';
            submitBtn.textContent = '确认出库';
            formFooter.appendChild(submitBtn);
        } else {
            submitBtn.textContent = '确认出库';
        }
        
        // 设置按钮点击事件
        submitBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleSubmit(form);
            return false;
        });
                }
    
    // 防止表单默认提交
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        handleSubmit(form);
        return false;
    });
    
    // 清除所有现有的成功消息
    document.querySelectorAll('.alert-success').forEach(el => el.remove());
}

// 处理表单提交
function handleSubmit(form) {
    // 验证表单
    const platform = form.querySelector('select[name="ecommerce_platform_id"]');
    const outReason = form.querySelector('select[name="out_reason"]');
    let hasItems = false;
    
    // 检查商品项
    const skuStockSelects = form.querySelectorAll('select[name$="[sku_stock_id]"]');
    const skuStockInputs = form.querySelectorAll('input[name$="[sku_stock_id]"]');
    
    // 从select元素检查
    skuStockSelects.forEach(select => {
        if (select.value) {
            hasItems = true;
        }
    });
    
    // 从input元素检查
    if (!hasItems) {
        skuStockInputs.forEach(input => {
            if (input.value) {
                hasItems = true;
            }
        });
    }
    
    if (!platform || !platform.value) {
        alert('请选择电商平台');
        return;
    }
    
    if (!outReason || !outReason.value) {
        alert('请选择出库原因');
        return;
    }
    
    if (!hasItems) {
        alert('请至少添加一个出库商品');
        return;
    }
    
    // 禁用按钮
    const submitBtn = form.querySelector('.form-footer button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.textContent = '处理中...';
    }
    
    // 创建FormData对象
    const formData = new FormData(form);

    // 发送AJAX请求
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络响应不正常');
        }
        return response.json().catch(e => {
            console.error('解析JSON响应失败:', e);
            return { status: false, message: '服务器响应格式错误', data: null };
        });
    })
    .then(data => {
        // 确保data是一个对象
        data = data || {};
        
        // 确保必要的字段存在
        if (data.status === undefined) data.status = false;
        if (data.message === undefined) data.message = '';
        if (data.data === undefined) data.data = null;
        
        if (data.status === true || data.status === 1) {
            // 显示成功消息
            showSingleSuccessMessage(data.message || '操作成功');
            
            console.log('准备跳转到:', data.redirect);
            
            // 等待一段时间后重定向
            setTimeout(() => {
                if (data.redirect) {
                    // 强制跳转
                    window.location.href = data.redirect;
                    console.log('执行跳转到:', data.redirect);
                }
            }, 1000);
            
            // 确保跳转执行
            setTimeout(() => {
                // 如果2秒后还没跳转，再次尝试
                console.log('检查是否已跳转');
                if (data.redirect && window.location.href !== data.redirect) {
                    console.log('未成功跳转，再次尝试');
                    window.location.replace(data.redirect);
                }
            }, 2000);
        } else {
            // 显示错误消息
            alert(data.message || '操作失败');
            
            // 恢复按钮状态
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = '确认出库';
            }
        }
    })
    .catch(error => {
        console.error('提交错误:', error);
        alert('提交失败，请稍后重试');
        
        // 恢复按钮状态
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = '确认出库';
        }
    });
}

// 显示单一成功消息
function showSingleSuccessMessage(message) {
    // 移除所有现有成功消息
    document.querySelectorAll('.alert-success').forEach(el => el.remove());
    
    // 创建新的消息元素
    const messageEl = document.createElement('div');
    messageEl.className = 'alert alert-success custom-success-message';
    messageEl.innerHTML = `
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
        <h4><i class="icon fa fa-check"></i> 成功</h4>
        <span>${message}</span>
    `;
        
    // 添加到页面
    const contentHeader = document.querySelector('.content-header');
    if (contentHeader && contentHeader.parentNode) {
        contentHeader.parentNode.insertBefore(messageEl, contentHeader.nextSibling);
    } else {
        document.querySelector('.content').prepend(messageEl);
    }
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
}
JS;
    }
    
    /**
     * 获取自定义的CSS样式
     */
    protected function getCustomStyle(): string
    {
        return <<<'CSS'
/* 隐藏所有系统自动生成的成功消息 */
.alert.alert-success:not(.custom-success-message) {
    display: none !important;
}

/* 自定义成功消息样式 */
.custom-success-message {
    margin-bottom: 20px;
    border-left: 3px solid #00a65a;
}

/* 隐藏列表按钮 */
.form-footer a.btn-default {
    display: none !important;
}

/* 确保提交按钮样式正确 */
.form-footer button[type="submit"] {
    display: inline-block !important;
    background-color: #3c8dbc;
    color: #fff;
    border-color: #367fa9;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 3px;
}

/* 提交按钮悬停效果 */
.form-footer button[type="submit"]:hover {
    background-color: #367fa9;
    border-color: #204d74;
}
CSS;
    }
    
    /**
     * 获取SKU库存信息的API
     */
    public function getSkuStockInfo(Request $request)
    {
        $stockId = $request->input('stock_id');
        
        if (!$stockId) {
            return response()->json(['error' => '缺少库存ID参数'], 400);
        }
        
        // 获取库存信息
        $stock = TecBanhuaSkuStockModel::with(['sku', 'warehouse', 'location'])->find($stockId);
        
        if (!$stock) {
            return response()->json(['error' => '未找到库存记录'], 404);
        }
        
        // 返回信息
        return response()->json([
            'id' => $stock->id,
            'sku_id' => $stock->sku_id,
            'sku_code' => $stock->sku ? $stock->sku->sku : null,
            'sku_name' => $stock->sku ? $stock->sku->name : null,
            'warehouse_id' => $stock->warehouse_id,
            'warehouse_name' => $stock->warehouse ? $stock->warehouse->name : null,
            'location_id' => $stock->location_id,
            'location_name' => $stock->location ? $stock->location->location_name : null,
            'stock' => $stock->stock,
            'cost_price' => $stock->cost_price,
            'sale_price' => $stock->sale_price,
        ]);
    }
    
    /**
     * 查找SKU库存API
     * 根据仓库ID查询有库存的SKU
     */
    public function findSkuStocks(Request $request)
    {
        // 获取仓库ID参数 (同时支持q和warehouse_id参数)
        $warehouseId = $request->get('warehouse_id') ?? $request->get('q');
        
        // 如果仓库ID为空，返回空数组
        if (!$warehouseId) {
            return response()->json([]);
        }
        
        // 记录调试信息
        \Illuminate\Support\Facades\Log::info('SKU库存查询(实时数据)', [
            'warehouse_id' => $warehouseId,
            'request_path' => $request->path(),
            'request_ip' => $request->ip()
        ]);
        
        // 查询该仓库下有库存的SKU
        $query = TecBanhuaSkuStockModel::with(['sku', 'sku.gallery', 'warehouse', 'location'])
            ->where('warehouse_id', $warehouseId)
            ->where('stock', '>', 0)
            ->limit(50);
            
        $stocks = $query->get();
            
        // 记录查询结果
        \Illuminate\Support\Facades\Log::info('SKU库存查询结果', [
            'count' => $stocks->count()
        ]);
        
        $options = [];
        
        foreach ($stocks as $stock) {
            if (!$stock->sku) continue;
            
            // 获取SKU名称
            $skuName = '';
            if ($stock->sku->gallery && !empty($stock->sku->gallery->remark)) {
                $skuName = $stock->sku->gallery->remark;
            } else if (!empty($stock->sku->name)) {
                $skuName = $stock->sku->name;
            }
            
            // 获取属性文本
            $attrText = '';
            if (!empty($stock->sku->simplified_attr_values_text)) {
                $attrText = ' - ' . $stock->sku->simplified_attr_values_text;
            } elseif (!empty($stock->sku->attr_values_text)) {
                // 兼容处理，如果simplified_attr_values_text不存在，则使用原有方式
                $processedAttrText = preg_replace('/([^,]+?):/', '', $stock->sku->attr_values_text);
                $attrText = ' - ' . $processedAttrText;
            }
            
            // 获取库位
            $locationName = $stock->location ? $stock->location->location_name : '未指定库位';
            
            // 构建选项文本: SKU名称(SKU编码) - 属性 - 库位 - 库存:XX
            $optionText = "{$skuName}({$stock->sku->sku}){$attrText} - {$locationName} - 库存:{$stock->stock}";
            $options[$stock->id] = $optionText;
        }
        
        // 记录返回的选项
        \Illuminate\Support\Facades\Log::info('SKU库存选项(返回)', [
            'options_count' => count($options)
        ]);
        
        // 直接返回数组，不使用response()->json()
        return $options;
    }
} 