<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Banhua\TecBanhuaConsumableStockModel;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\Banhua\TecBanhuaAttrModel;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use App\Models\Banhua\TecBanhuaLocationModel;
use App\Models\TecWarehouseModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Admin;

/**
 * 消耗品库存管理控制器
 */
class TecBanhuaConsumableStockController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '消耗品库存管理';

    /**
     * 列表页面
     */
    public function index(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('消耗品库存列表')
            ->body($this->grid());
    }

    /**
     * 构建列表
     */
    protected function grid(): Grid
    {
        return Grid::make(new TecBanhuaConsumableStockModel(), function (Grid $grid) {
            // 设置分页选项（优化版）
            $grid->paginate(50);
            $grid->perPages([50,100, 200, 500, 1000]);
            
            // 启用横向滚动条，避免内容过多导致布局混乱
            $grid->scrollbarX();
            
            // 优化关联加载，显式加载sku.gallery以避免N+1查询
            $grid->model()->with(['sku', 'sku.gallery', 'warehouse', 'location']);
            
            // 默认按SKU ID排序
            $grid->model()->orderBy('sku_id', 'asc');
            $grid->column('id')->sortable();
            $grid->column('sku_info', 'SKU编码')->display(function () {
                if (!$this->sku) {
                    return '';
                }
                
                // 优先获取gallery.remark作为显示名称
                $displayName = '';
                if ($this->sku->gallery && !empty($this->sku->gallery->remark)) {
                    $displayName = $this->sku->gallery->remark;
                } else if (!empty($this->sku->name)) {
                    $displayName = $this->sku->name;
                }
                
                // 返回"商品名称(SKU编码)"的格式
                return $displayName . '(' . $this->sku->sku . ')';
            });
            // $grid->column('sku.name', 'SKU名称')->width('200px');
            $grid->column('attr_text', '属性组合')->display(function($value) {
                // 如果值为空，尝试重新计算
                if (empty($value) && !empty($this->attr_value_ids)) {
                    // 获取缓存的属性值映射
                    $valueMapCache = \Illuminate\Support\Facades\Cache::remember('banhua_attr_value_map', 3600, function () {
                        return \App\Models\Banhua\TecBanhuaAttrValueModel::pluck('name', 'id')->toArray();
                    });
                    
                    // 确保attr_value_ids是数组或有效的逗号分隔字符串
                    $valueIds = is_array($this->attr_value_ids) 
                        ? $this->attr_value_ids 
                        : explode(',', (string)$this->attr_value_ids);
                    
                    $names = [];
                    foreach ($valueIds as $id) {
                        $id = trim($id);
                        if (!empty($id) && isset($valueMapCache[$id])) {
                            $names[] = $valueMapCache[$id];
                        }
                    }
                    
                    $value = implode(', ', $names);
                }
                
                return $value;
            });
            
            // 添加SKU图片预览（优化版）
            $grid->column('sku.image', '图片')->display(function ($value) {
                // 获取关联的SKU
                $sku = $this->sku;
                if (!$sku) {
                    return '';
                }
                
                // 尝试从gallery获取图片
                if (!empty($sku->gallery) && !empty($sku->gallery->path)) {
                    $value = $sku->gallery->path;
                } else {
                    $value = $sku->image;
                }
                
                if (empty($value)) {
                    return '';
                }
                
                $value = trim($value);
                if (!filter_var($value, FILTER_VALIDATE_URL)) {
                    return "<span class='text-muted'>图片URL无效</span>";
                }
                
                // 生成唯一的缓存键
                $cacheKey = md5($value);
                
                // 使用更高效的懒加载方式
                return "<img 
                    src='/vendor/dcat-admin/images/picture.png' 
                    data-src='{$value}' 
                    style='max-width:50px;max-height:50px;cursor:pointer;' 
                    class='img-thumbnail lazyload img-preview' 
                    data-original='{$value}'
                    data-cache-key='{$cacheKey}'
                    title='点击预览' />";
            });
            
            $grid->column('warehouse.name', '仓库')->sortable();
            $grid->column('location.location_name', '库位')->sortable();
            $grid->column('batch_no', '批次号')->sortable();
            $grid->column('stock', '库存数量')->sortable();
            // $grid->column('created_at');
            // $grid->column('updated_at');
            // 增加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete(false); // 启用删除按钮
            });
            // 筛选器
            $grid->filter(function ($filter) {
                // 仓库筛选
                $filter->equal('warehouse_id', '仓库')->select(function () {
                    return TecWarehouseModel::pluck('name', 'id');
                })->width(2);
                
                // 库位筛选
                $filter->equal('location_id', '库位')->select(function ($id) {
                    // 如果选择了仓库，则只显示该仓库的库位
                    $warehouseId = request()->input('warehouse_id');
                    if ($warehouseId) {
                        return TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
                            ->get()
                            ->mapWithKeys(function ($location) {
                                $text = $location->area_number . '区' . $location->shelf_number . '架' . $location->level_number . '层';
                                return [$location->id => $text];
                            })
                            ->toArray();
                    } else {
                        // 如果没有选择仓库，则显示所有库位
                        return TecBanhuaLocationModel::with('warehouse')
                            ->get()
                            ->mapWithKeys(function ($location) {
                                $warehouseName = $location->warehouse ? $location->warehouse->name : '未知仓库';
                                $text = $warehouseName . ' - ' . $location->area_number . '区' . $location->shelf_number . '架' . $location->level_number . '层';
                                return [$location->id => $text];
                            })
                            ->toArray();
                    }
                })->width(2);
                
                // SKU筛选
                $filter->where('sku_filter', function ($query) {
                    $query->whereHas('sku', function ($skuQuery) {
                        $skuQuery->where('sku', 'like', "%{$this->input}%");
                    });
                }, '按SKU筛选')->width(2);
                
                // 批次号筛选
                $filter->like('batch_no', '批次号')->width(2);
                
                // 库存筛选
                $filter->where('stock_filter', function ($query) {
                    if ($this->input == 'zero') {
                        $query->where('stock', 0);
                    } elseif ($this->input == 'positive') {
                        $query->where('stock', '>', 0);
                    }
                }, '库存数量')->select([
                    'zero' => '零库存',
                    'positive' => '有库存',
                ])->width(2);
            });
            
            // 添加优化版图片懒加载和预览JavaScript
            Admin::script('
            $(function() {
                // 图片URL缓存
                var imageCache = {};
                
                // 更高效的图片懒加载初始化
                var lazyLoadOptions = {
                    elements_selector: ".lazyload",
                    threshold: 200, // 增加阈值，提前加载
                    callback_loaded: function(element) {
                        // 图片加载成功后缓存
                        var cacheKey = $(element).data("cache-key");
                        var src = $(element).attr("src");
                        if (cacheKey && src) {
                            imageCache[cacheKey] = src;
                        }
                    },
                    callback_error: function(element) {
                        $(element).attr("src", "/vendor/dcat-admin/images/picture.png");
                    },
                    callback_enter: function(element) {
                        // 检查图片是否已缓存
                        var cacheKey = $(element).data("cache-key");
                        if (cacheKey && imageCache[cacheKey]) {
                            $(element).attr("src", imageCache[cacheKey]);
                            $(element).addClass("loaded");
                        }
                    }
                };
                
                // 初始化懒加载
                var lazyLoadInstance = new LazyLoad(lazyLoadOptions);
                
                // 监听表格刷新事件，重新初始化懒加载
                $(document).on("pjax:complete", function() {
                    if (lazyLoadInstance) {
                        lazyLoadInstance.destroy();
                        lazyLoadInstance = new LazyLoad(lazyLoadOptions);
                    }
                });
                
                // 优化的图片预览功能
                $(document).off("click", ".img-preview").on("click", ".img-preview", function() {
                    var src = $(this).data("original");
                    if (!src) return;
                    
                    // 创建预览层，使用缓存的图片
                    var cacheKey = $(this).data("cache-key");
                    var imgSrc = (cacheKey && imageCache[cacheKey]) ? imageCache[cacheKey] : src;
                    
                    var imgHtml = "<div style=\'text-align:center;\'><img src=\'" + imgSrc + "\' style=\'max-width:100%;max-height:400px;\' /></div>";
                        layer.open({
                            type: 1,
                        title: "图片预览",
                            shadeClose: true,
                        shade: 0.3,
                        area: ["40%", "60%"],
                        content: imgHtml
                    });
                });
            });
            ');
            
            // 添加批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                // 添加批量删除按钮
                $batch->add(new \App\Admin\Actions\Grid\Banhua\TecBanhuaConsumableStockBatchDeleteAction());
            });
            
            // 添加导入、导出和刷新缓存按钮
            $grid->tools(function ($tools) {
                // 添加导出按钮
                $tools->append(new \App\Admin\Actions\Grid\Banhua\TecBanhuaConsumableStockExportAction());
                // 添加导入按钮
                $tools->append(new \App\Admin\Actions\Grid\Banhua\TecBanhuaConsumableStockImportAction());
                // 添加缓存清除按钮
                $tools->append('<a class="btn btn-success btn-mini btn-outline" href="' . admin_url('banhua/clear-consumable-stock-cache') . '" style="margin-right:3px">
                    <i class="fa fa-motorcycle"></i>
                </a>');
            });
        });
    }

    /**
     * 创建页面
     */
    public function create(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('创建消耗品库存')
            ->body($this->form());
    }

    /**
     * 编辑页面
     */
    public function edit($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('编辑消耗品库存')
            ->body($this->form()->edit($id));
    }

    /**
     * 构建表单
     */
    protected function form(): Form
    {
        return Form::make(new TecBanhuaConsumableStockModel(), function (Form $form) {
            // $form->display('id');
                
            // 仓库选择
            $form->select('warehouse_id', '仓库')
                ->options(TecWarehouseModel::pluck('name', 'id'))
                ->required();
                
            // 库位选择，动态加载
            $form->select('location_id', '库位')
                ->options(function ($id) {
                    // 如果是编辑模式，预加载当前仓库的库位
                    if ($id && $warehouseId = $this->warehouse_id) {
                        return TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
                            ->get()
                            ->mapWithKeys(function ($location) {
                                return [$location->id => $location->location_name];
                            });
                    }
                    return [];
                })
                ->required();
                
            // SKU选择
            $form->select('sku_id', 'SKU')
                ->options(function () {
                    // 预加载属性和属性值
                    $attrs = TecBanhuaAttrModel::with('values')->get();
                    $attrValuesMap = [];
                    
                    // 构建属性值映射
                    foreach ($attrs as $attr) {
                        foreach ($attr->values as $value) {
                            $attrValuesMap[$value->id] = [
                                'attr_name' => $attr->name,
                                'value_name' => $value->name
                            ];
                        }
                    }
                    
                    // 获取SKU列表并格式化显示，排除JAN不为空的数据
                    return TecBanhuaProductSkuModel::with('gallery')
                        ->select(['id', 'sku', 'name', 'attr_value_ids', 'jan', 'pid'])
                        ->whereNull('jan')
                        ->orWhere('jan', '')
                        ->get()
                        ->map(function ($sku) use ($attrValuesMap) {
                            // 优先使用gallery.remark作为显示名称
                            $displayName = '';
                            if ($sku->gallery && !empty($sku->gallery->remark)) {
                                $displayName = $sku->gallery->remark;
                            } else if (!empty($sku->name)) {
                                $displayName = $sku->name;
                            }
                            
                            // 构建属性文本
                            $attrText = '';
                            if (!empty($sku->attr_value_ids)) {
                                $attrValues = [];
                                foreach ($sku->attr_value_ids as $valueId) {
                                    if (isset($attrValuesMap[$valueId])) {
                                        $attrValues[] = $attrValuesMap[$valueId]['value_name'];
                                    }
                                }
                                if (!empty($attrValues)) {
                                    $attrText = ' - (' . implode(', ', $attrValues) . ')';
                                }
                            }
                            
                            // 返回格式：包装箱(GOODS-BOX) - (30*40, 5CM)
                            return [
                                'id' => $sku->id,
                                'text' => $displayName . '(' . $sku->sku . ')' . $attrText
                            ];
                        })
                        ->pluck('text', 'id')
                        ->toArray();
                })
                ->required()
                ->help('只显示JAN为空的SKU，适用于消耗品管理');
                
            // 添加SKU图片预览
            $form->html(function ($form) {
                // 如果是编辑模式
                if ($form->model()->sku_id) {
                    $sku = TecBanhuaProductSkuModel::with('gallery')->find($form->model()->sku_id);
                    if ($sku) {
                        // 获取图片URL
                        $imageUrl = '';
                        if (!empty($sku->gallery) && !empty($sku->gallery->path)) {
                            $imageUrl = $sku->gallery->path;
                        } elseif (!empty($sku->image)) {
                            $imageUrl = $sku->image;
                        }
                        
                        if (!empty($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                            return <<<HTML
                            <div class="form-group">
                                <label class="col-sm-2 control-label">SKU图片</label>
                                <div class="col-sm-8">
                                    <img src="{$imageUrl}" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;margin-top:10px;" class="img-preview" data-original="{$imageUrl}">
                                </div>
                            </div>
                            HTML;
                        }
                    }
                }
                
                return '';
            });
            // 批次号
            $form->text('batch_no', '批次号')
                ->help('可输入生产批次、采购批次等信息');
                
            // 库存数量
            $form->number('stock', '库存数量')->min(0)->required();
                
            // $form->display('created_at');
            // $form->display('updated_at');
            
            // 添加JS，处理仓库和库位级联
            $form->submitted(function (Form $form) {
                // 检查是否是编辑已有记录
                $isEditing = $form->isEditing();
                
                // 如果是编辑模式，允许批次号修改，但防止创建完全重复的记录
                if ($isEditing) {
                    $id = $form->model()->id;
                    $currentRecord = TecBanhuaConsumableStockModel::find($id);
                    
                    if (!$currentRecord) {
                        return $form->response()->error('无法找到当前记录');
                    }
                    
                    \Illuminate\Support\Facades\Log::info('编辑模式 - 检查防止完全重复', [
                        'id' => $id,
                        'current_batch_no' => $currentRecord->batch_no,
                        'new_batch_no' => $form->batch_no,
                    ]);
                    
                    // 检查是否会导致完全重复的记录
                    $duplicateCheck = TecBanhuaConsumableStockModel::where('sku_id', $form->sku_id)
                        ->where('warehouse_id', $form->warehouse_id)
                        ->where('location_id', $form->location_id)
                        ->where('batch_no', $form->batch_no)
                        ->where('id', '!=', $id) // 排除当前记录
                        ->exists();
                    
                    if ($duplicateCheck) {
                        return $form->response()->error('该SKU在该仓库库位下已存在相同批次号的记录，不允许创建完全重复的数据');
                    }
                    
                    // 通过了重复检查，允许修改
                    return;
                }
                
                // 只对新建记录进行验证，编辑模式已在前面完全跳过
                if (!$isEditing) {
                    // 基础查询条件：相同SKU、仓库、库位
                    $baseQuery = TecBanhuaConsumableStockModel::where('sku_id', $form->sku_id)
                        ->where('warehouse_id', $form->warehouse_id)
                        ->where('location_id', $form->location_id);
                    
                    // 根据批次号情况进行不同的验证
                    if (!empty($form->batch_no)) {
                        // 如果当前记录有批次号，则只检查是否有相同批次号的记录
                        $exists = (clone $baseQuery)
                            ->where(function($query) use ($form) {
                                $query->where('batch_no', $form->batch_no);
                            })
                            ->exists();
                        
                        if ($exists) {
                            return $form->response()->error('该SKU在该仓库库位下已存在相同批次号的库存记录，请选择其他批次号');
                        }
                    } else {
                        // 如果当前记录没有批次号，则检查是否有没有批次号的记录
                        $exists = (clone $baseQuery)
                            ->where(function($query) {
                                $query->whereNull('batch_no')
                                    ->orWhere('batch_no', '');
                            })
                            ->exists();
                        
                        if ($exists) {
                            return $form->response()->error('该SKU在该仓库库位下已存在无批次号的库存记录，请添加批次号以区分');
                        }
                    }
                }
            });
            
            // 保存前处理
            $form->saving(function (Form $form) {
                // 确保价格字段不为null
                $form->cost_price = $form->cost_price ?? 0;
                $form->sale_price = $form->sale_price ?? 0;
                
                // 记录初始值
                \Illuminate\Support\Facades\Log::info('消耗品库存保存前状态', [
                    'form_id' => $form->model()->id,
                    'sku_id' => $form->sku_id,
                    'is_editing' => $form->isEditing(),
                    'initial_attr_value_ids' => $form->attr_value_ids,
                ]);
                
                // 自动设置attr_value_ids，从SKU中获取
                if ($form->sku_id) {
                    $sku = TecBanhuaProductSkuModel::find($form->sku_id);
                    
                    \Illuminate\Support\Facades\Log::info('获取到SKU信息', [
                        'sku_id' => $form->sku_id,
                        'sku_found' => $sku ? 'yes' : 'no',
                        'sku_attr_value_ids' => $sku ? $sku->attr_value_ids : null,
                        'sku_attr_value_ids_type' => $sku ? gettype($sku->attr_value_ids) : 'N/A',
                    ]);
                    
                    if ($sku) {
                        // 无论SKU的attr_value_ids是否为空，都记录它的值
                        if (is_array($sku->attr_value_ids)) {
                            $form->attr_value_ids = implode(',', $sku->attr_value_ids);
                            \Illuminate\Support\Facades\Log::info('设置属性值IDs (从数组)', [
                                'sku_id' => $sku->id,
                                'attr_value_ids' => $form->attr_value_ids,
                            ]);
                        } elseif (is_string($sku->attr_value_ids) && !empty($sku->attr_value_ids)) {
                            $form->attr_value_ids = $sku->attr_value_ids;
                            \Illuminate\Support\Facades\Log::info('设置属性值IDs (从字符串)', [
                                'sku_id' => $sku->id,
                                'attr_value_ids' => $form->attr_value_ids,
                            ]);
                        } else {
                            // 如果SKU的attr_value_ids为空，尝试从其他字段获取
                            \Illuminate\Support\Facades\Log::info('SKU的attr_value_ids为空，尝试从原始数据获取', [
                                'sku_id' => $sku->id,
                                'sku_data' => $sku->toArray(),
                            ]);
                            
                            // 尝试从attributes字段获取
                            if (!empty($sku->attributes)) {
                                \Illuminate\Support\Facades\Log::info('从SKU的attributes字段获取属性值', [
                                    'attributes' => $sku->attributes,
                                ]);
                            }
                        }
                    }
                }
                
                // 记录最终状态
                \Illuminate\Support\Facades\Log::info('消耗品库存保存后状态', [
                    'form_id' => $form->model()->id,
                    'sku_id' => $form->sku_id,
                    'final_attr_value_ids' => $form->attr_value_ids,
                ]);
            });
            
            // 保存后处理
            $form->saved(function (Form $form) {
                $model = $form->model();
                
                \Illuminate\Support\Facades\Log::info('消耗品库存保存完成', [
                    'id' => $model->id,
                    'sku_id' => $model->sku_id,
                    'attr_value_ids' => $model->attr_value_ids,
                    'attr_value_ids_type' => gettype($model->attr_value_ids),
                    'is_editing' => $form->isEditing(),
                ]);
                
                // 如果是新增记录，通过SKU ID查找最新的记录
                if (!$form->isEditing()) {
                    // 获取刚刚保存的记录，通过SKU ID、仓库ID、库位ID和批次号找到
                    $latestRecord = \App\Models\Banhua\TecBanhuaConsumableStockModel::where('sku_id', $form->sku_id)
                        ->where('warehouse_id', $form->warehouse_id)
                        ->where('location_id', $form->location_id)
                        ->when($form->batch_no, function ($query) use ($form) {
                            return $query->where('batch_no', $form->batch_no);
                        })
                        ->latest('id')
                        ->first();
                        
                    \Illuminate\Support\Facades\Log::info('查找最新记录', [
                        'found' => $latestRecord ? 'yes' : 'no',
                        'record_id' => $latestRecord ? $latestRecord->id : null,
                    ]);
                        
                    if ($latestRecord) {
                        $model = $latestRecord;
                    }
                }
                
                // 如果attr_value_ids仍然为空（无论是新增还是编辑），尝试直接更新数据库
                if ($model->id && $model->sku_id && empty($model->attr_value_ids)) {
                    $sku = TecBanhuaProductSkuModel::find($model->sku_id);
                    if ($sku && !empty($sku->attr_value_ids)) {
                        $attrValueIds = is_array($sku->attr_value_ids) 
                            ? implode(',', $sku->attr_value_ids) 
                            : $sku->attr_value_ids;
                            
                        \Illuminate\Support\Facades\DB::table('t_banhua_consumable_stocks')
                            ->where('id', $model->id)
                            ->update(['attr_value_ids' => $attrValueIds]);
                            
                        \Illuminate\Support\Facades\Log::info('强制更新attr_value_ids', [
                            'id' => $model->id,
                            'sku_id' => $model->sku_id,
                            'attr_value_ids' => $attrValueIds,
                        ]);
                    }
                }
            });
            
            // 在saved钩子之外，添加一个事件监听器
            \Illuminate\Support\Facades\Event::listen('dcat.admin.after_save', function ($form, $modelInstance) {
                // 确保模型实例是消耗品库存模型
                if ($modelInstance instanceof \App\Models\Banhua\TecBanhuaConsumableStockModel) {
                    \Illuminate\Support\Facades\Log::info('after_save事件触发', [
                        'model_id' => $modelInstance->id,
                        'sku_id' => $modelInstance->sku_id,
                    ]);
                    
                    // 如果没有attr_value_ids，从SKU中获取
                    if ($modelInstance->id && $modelInstance->sku_id && empty($modelInstance->attr_value_ids)) {
                        $sku = TecBanhuaProductSkuModel::find($modelInstance->sku_id);
                        if ($sku && !empty($sku->attr_value_ids)) {
                            $attrValueIds = is_array($sku->attr_value_ids) 
                                ? implode(',', $sku->attr_value_ids) 
                                : $sku->attr_value_ids;
                                
                            \Illuminate\Support\Facades\DB::table('t_banhua_consumable_stocks')
                                ->where('id', $modelInstance->id)
                                ->update(['attr_value_ids' => $attrValueIds]);
                                
                            \Illuminate\Support\Facades\Log::info('事件监听器更新attr_value_ids', [
                                'id' => $modelInstance->id,
                                'sku_id' => $modelInstance->sku_id,
                                'attr_value_ids' => $attrValueIds,
                            ]);
                        }
                    }
                }
            });

            // 添加JS，处理仓库和库位级联
            Admin::script('
            $(function() {
                // 仓库选择变化时更新库位下拉框
                $("select[name=\'warehouse_id\']").on("change", function() {
                    var warehouseId = $(this).val();
                    if (!warehouseId) {
                        // 如果没有选择仓库，清空库位下拉框
                        $("select[name=\'location_id\']").empty().append("<option value=\'\'>请选择库位</option>");
                        return;
                    }

                    // 加载库位数据
                    $.ajax({
                        url: "/admin/api/banhua/warehouse-locations",
                        type: "GET",
                        data: {q: warehouseId},
                        dataType: "json",
                        success: function(data) {
                            var select = $("select[name=\'location_id\']");
                            select.empty().append("<option value=\'\'>请选择库位</option>");
                            
                            // 添加库位选项
                            $.each(data, function(id, name) {
                                select.append("<option value=\'" + id + "\'>" + name + "</option>");
                            });
                        },
                        error: function() {
                            console.error("加载库位数据失败");
                        }
                    });
                });

                // 页面加载时如果已选择仓库，触发change事件加载库位
                var initialWarehouseId = $("select[name=\'warehouse_id\']").val();
                if (initialWarehouseId) {
                    $("select[name=\'warehouse_id\']").trigger("change");
                }
            });
            ');
        });
    }

    /**
     * 获取仓库下的库位列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWarehouseLocations(Request $request)
    {
        $warehouseId = $request->get('q');
        
        return TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
            ->get()
            ->mapWithKeys(function ($location) {
                return [$location->id => $location->location_name];
            })
            ->toArray();
    }

    /**
     * 获取SKU图片
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSkuImage(Request $request)
    {
        $skuId = $request->get('sku_id');
        if (!$skuId) {
            return response()->json(['status' => 'error', 'message' => 'SKU ID不能为空']);
        }
        
        $sku = TecBanhuaProductSkuModel::with('gallery')->find($skuId);
        if (!$sku) {
            return response()->json(['status' => 'error', 'message' => '未找到SKU']);
        }
        
        $imageUrl = '';
        if (!empty($sku->gallery) && !empty($sku->gallery->path)) {
            $imageUrl = $sku->gallery->path;
        } elseif (!empty($sku->image)) {
            $imageUrl = $sku->image;
        }
        
        if (empty($imageUrl)) {
            return response()->json(['status' => 'error', 'message' => '未找到图片']);
        }
        
        return response()->json([
            'status' => 'success',
            'image_url' => $imageUrl,
                'sku' => $sku->sku,
            'name' => $sku->name
        ]);
    }

    /**
     * 查看详情页
     */
    public function show($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('消耗品库存详情')
            ->body($this->detail($id));
    }

    /**
     * 构建详情页
     */
    protected function detail($id): Show
    {
        return Show::make($id, new TecBanhuaConsumableStockModel(), function (Show $show) {
            $show->field('id');
            $show->field('sku.sku', 'SKU编码');
            $show->field('sku.name', 'SKU名称');
            
            // 显示SKU属性组合
            $show->field('attr_text', '属性组合');
            
            $show->field('warehouse.name', '仓库');
            $show->field('location.location_name', '库位');
            $show->field('stock', '库存数量');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * 清除SKU库存缓存
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearStockCache()
    {
        try {
            // 清除可能存在的缓存
            \Illuminate\Support\Facades\Cache::forget('banhua_consumable_stocks');
            \Illuminate\Support\Facades\DB::statement('ANALYZE TABLE t_banhua_consumable_stocks');
            
            \Illuminate\Support\Facades\Log::info('手动刷新消耗品库存列表');
            
            admin_toastr('刷新成功', 'success');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('刷新操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            admin_toastr('刷新失败: ' . $e->getMessage(), 'error');
        }
        
        return redirect()->back();
    }

    /**
     * 导出SKU库存数据
     *
     * @param Request $request
     * @return mixed
     */
    public function exportStock(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaConsumableStockExportAction();
        return $action->handle($request);
    }
    
    /**
     * 启动导出任务
     *
     * @param Request $request
     * @return mixed
     */
    public function startExport(Request $request)
    {
            $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaConsumableStockExportAction();
            return $action->start($request);
    }
    
    /**
     * 获取导出进度
     *
     * @param Request $request
     * @return mixed
     */
    public function exportProgress(Request $request)
    {
            $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaConsumableStockExportAction();
            return $action->progress($request);
    }
    
    /**
     * 下载导出文件
     *
     * @param Request $request
     * @return mixed
     */
    public function downloadExport(Request $request)
    {
            $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaConsumableStockExportAction();
            return $action->download($request);
    }
    
    /**
     * 导入SKU库存数据
     *
     * @param Request $request
     * @return mixed
     */
    public function importStock(Request $request)
    {
            $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaConsumableStockImportAction();
            return $action->handle($request);
    }
    
    /**
     * 下载SKU库存导入模板
     *
     * @return \Illuminate\Http\Response
     */
    public function downloadImportTemplate()
    {
        try {
            // 禁用Pjax
            if (request()->pjax()) {
                return response()->json([
                    'status' => false,
                    'message' => '请刷新页面后重试'
                ]);
            }
            
            $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaConsumableStockImportAction();
            return $action->downloadTemplate();
        } catch (\Exception $e) {
            admin_toastr('下载模板失败: ' . $e->getMessage(), 'error');
            return redirect()->back();
        }
    }
} 