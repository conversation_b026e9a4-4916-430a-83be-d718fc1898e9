<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Repositories\Banhua\TecBanhuaImageGalleryRepo;
use App\Models\Banhua\TecBanhuaImageGalleryModel;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;

/**
 * 阪画图库管理控制器
 */
class TecBanhuaImageGalleryController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '阪画图库管理';

    /**
     * 列表页面
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content): Content
    {
        try {
            Log::info('访问图库列表页面');
            
            return $content
                ->header($this->title)
                ->description('图库列表')
                ->body($this->grid());
        } catch (\Exception $e) {
            Log::error('构建图库列表页面出错', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回一个简单的错误页面
            return $content
                ->header($this->title)
                ->description('图库列表')
                ->body('<div class="alert alert-danger">加载列表时出错：' . $e->getMessage() . '</div>');
        }
    }

    /**
     * 构建列表
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        try {
            return Grid::make(new TecBanhuaImageGalleryRepo(), function (Grid $grid) {
                try {
                    // 启用横向滚动条
                    $grid->scrollbarX();                    
                    
                    // 基本列
                    $grid->column('id')->sortable();
                    $grid->column('image_id', '图库ID')->sortable();
                    $grid->column('name', '图片名称');
                    // 图片预览 - 参考SKU控制器
                    $grid->column('path', '图片')->display(function ($value) {
                        if (empty($value)) {
                            return '';
                        }
                        
                        // 确保URL是有效的
                        $value = trim($value);
                        if (!filter_var($value, FILTER_VALIDATE_URL)) {
                            return "<span class='text-muted'>图片URL无效</span>";
                        }
                        
                        // 生成缩略图和预览图
                        $thumbUrl = $value;
                        
                        // 使用Dcat Admin的图片预览功能
                        return "<img 
                            src='data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7' 
                            data-src='{$thumbUrl}' 
                            style='max-width:50px;max-height:50px;cursor:pointer;' 
                            class='img-thumbnail lazy img-preview' 
                            data-original='{$value}'
                            loading='lazy'
                            title='点击预览' />";
                    })->width('100px');
                    
                    // 添加图片懒加载和预览JavaScript
                    Admin::script('
                    $(function() {
                        // 图片懒加载初始化函数
                        function initLazyLoading() {
                            // 检查浏览器是否支持Intersection Observer API
                            if ("IntersectionObserver" in window) {
                                const imageObserver = new IntersectionObserver((entries, observer) => {
                                    entries.forEach(entry => {
                                        if (entry.isIntersecting) {
                                            const img = entry.target;
                                            // 检查是否已经加载过
                                            if (img.dataset.src && !img.src.includes(img.dataset.src)) {
                                                img.src = img.dataset.src;
                                            }
                                            // 图片加载后，取消观察
                                            observer.unobserve(img);
                                        }
                                    });
                                });
                                
                                // 获取所有懒加载图片
                                const lazyImages = document.querySelectorAll("img.lazy");
                                lazyImages.forEach(img => {
                                    imageObserver.observe(img);
                                });
                            } else {
                                // 对于不支持IntersectionObserver的浏览器，使用传统的滚动事件监听
                                let lazyImages = [].slice.call(document.querySelectorAll("img.lazy"));
                                let active = false;
                                
                                function lazyLoad() {
                                    if (active === false) {
                                        active = true;
                                        
                                        setTimeout(function() {
                                            lazyImages.forEach(function(lazyImage) {
                                                const rect = lazyImage.getBoundingClientRect();
                                                if (
                                                    rect.top <= window.innerHeight && 
                                                    rect.bottom >= 0 &&
                                                    getComputedStyle(lazyImage).display !== "none"
                                                ) {
                                                    if (lazyImage.dataset.src) {
                                                        lazyImage.src = lazyImage.dataset.src;
                                                    }
                                                    
                                                    lazyImage.classList.remove("lazy");
                                                    
                                                    lazyImages = lazyImages.filter(function(image) {
                                                        return image !== lazyImage;
                                                    });
                                                    
                                                    if (lazyImages.length === 0) {
                                                        document.removeEventListener("scroll", lazyLoad);
                                                        window.removeEventListener("resize", lazyLoad);
                                                        window.removeEventListener("orientationchange", lazyLoad);
                                                    }
                                                }
                                            });
                                            
                                            active = false;
                                        }, 200);
                                    }
                                }
                                
                                // 添加滚动监听
                                document.addEventListener("scroll", lazyLoad);
                                window.addEventListener("resize", lazyLoad);
                                window.addEventListener("orientationchange", lazyLoad);
                                
                                // 初始加载
                                lazyLoad();
                            }
                        }
                        
                        // 初始化懒加载
                        initLazyLoading();
                        
                        // 监听表格刷新事件，重新初始化懒加载
                        $(document).on("pjax:complete", function() {
                            setTimeout(function() {
                                initLazyLoading();
                            }, 100);
                        });
                        
                        // 图片预览功能
                        $(document).off("click", ".img-preview").on("click", ".img-preview", function() {
                            var src = $(this).data("original");
                            if (!src) return;
                            
                            // 创建预览层
                            var imgHtml = "<div style=\'text-align:center;\'><img src=\'" + src + "\' style=\'max-width:100%;max-height:400px;\' /></div>";
                            layer.open({
                                type: 1,
                                title: "图片预览",
                                shadeClose: true,
                                shade: 0.3,
                                area: ["40%", "60%"],
                                content: imgHtml
                            });
                        });
                    });
                    ');
                    
                    $grid->column('remark', '备注')->limit(30);
                    
                    // 筛选器
                    $grid->filter(function ($filter) {
                        $filter->like('image_id', '图库ID')->width(3);
                        $filter->like('name', '图片名称')->width(3);
                    });
                    
                    // 排序
                    $grid->model()->orderBy('id', 'desc');
                    // 增加操作按钮
                    $grid->actions(function (Grid\Displayers\Actions $actions) {
                        $actions->disableEdit(); // 禁用编辑按钮
                        $actions->disableQuickEdit(false); // 启用快速编辑按钮
                        $actions->disableDelete(false); // 启用删除按钮
                    });                    
                    // 分页设置 - 优化大数据量下的性能
                    $grid->paginate(50); // 每页显示50条记录
                    // 启用每页显示数量的选择器，并自定义可选择的数量
                    $grid->perPages([50, 100, 200, 500, 1000]);
                    
                    // 批量操作
                    $grid->batchActions(function ($batch) {
                        // 添加批量删除按钮
                        $batch->add(new \Dcat\Admin\Grid\Tools\BatchDelete('批量删除'));
                    });
                    
                    // 添加导入导出工具
                    $grid->tools(function ($tools) {
                        // 添加导出按钮
                        $tools->append(new \App\Admin\Actions\Grid\Banhua\TecBanhuaImageGalleryExportAction());
                        
                        // 添加导入按钮
                        $tools->append(new \App\Admin\Actions\Grid\Banhua\TecBanhuaImageGalleryImportAction());
                        
                        // 添加缓存清除按钮
                        $tools->append('<a class="btn btn-success btn-mini btn-outline" href="' . admin_url('banhua/clear-gallery-cache') . '" style="margin-right:3px">
                            <i class="fa fa-motorcycle"></i>
                        </a>');
                    });
                    
                    Log::info('Grid构建完成');
                } catch (\Exception $innerEx) {
                    Log::error('Grid回调构建失败', [
                        'error' => $innerEx->getMessage(),
                        'trace' => $innerEx->getTraceAsString()
                    ]);
                    // 创建一个简化版的Grid以避免错误
                    $grid->column('id')->sortable();
                    $grid->column('image_id', '图库ID');
                }
                
                return $grid;
            });
            
        } catch (\Exception $e) {
            Log::error('Grid构建失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // 返回一个空白的Grid
            return Grid::make(new TecBanhuaImageGalleryRepo());
        }
    }

    /**
     * 创建页面
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('创建图片')
            ->body($this->form());
    }

    /**
     * 编辑页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function edit($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('编辑图片')
            ->body($this->form()->edit($id));
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('图片详情')
            ->body($this->detail($id));
    }

    /**
     * 构建详情
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id): Show
    {
        return Show::make($id, new TecBanhuaImageGalleryRepo(), function (Show $show) {
            $show->field('image_id', '图库ID');
            $show->field('name', '图片名称');
            $show->field('path', '图片路径');
            $show->field('path', '图片')->image();
            $show->field('remark', '备注');
        });
    }

    /**
     * 构建表单
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new TecBanhuaImageGalleryRepo(), function (Form $form) {
            $form->display('id');
            
            $form->text('image_id', '图库ID')
                ->required()
                ->rules(['required', 'unique:t_banhua_image_gallery,image_id,' . $form->getKey()])
                ->help('请输入唯一的图库ID');
                
            $form->text('name', '图片名称')
                ->required();
                
            $form->text('path', '图片路径')
                ->required()
                ->attribute(['id' => 'image_url_input'])
                ->help('请输入图片路径');
                
            // 添加图片预览
            $form->html('<div id="image_preview_container" style="margin-top:10px;display:none;">
                <img id="image_preview" src="" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;">
            </div>', '图片预览');
                
            $form->textarea('remark', '备注')
                ->rows(3);
                
            // 添加图片预览脚本
            Admin::script(<<<'JS'
$(document).ready(function() {
    // 初始化图片预览
    var imageUrl = $('#image_url_input').val();
    if (imageUrl) {
        // 验证URL是否有效
        if (!isValidUrl(imageUrl)) {
            $('#image_preview_container').html("<span class='text-muted'>图片URL无效</span>");
            $('#image_preview_container').show();
        } else {
            // 设置预览图片
            $('#image_preview').attr('src', imageUrl);
            $('#image_preview_container').show();
        }
    }
    
    // 输入框变化时自动更新预览
    $('#image_url_input').on('change blur', function() {
        var imageUrl = $(this).val();
        if (imageUrl) {
            // 验证URL是否有效
            if (!isValidUrl(imageUrl)) {
                $('#image_preview_container').html("<span class='text-muted'>图片URL无效</span>");
                $('#image_preview_container').show();
                return;
            }
            
            // 重置预览容器
            $('#image_preview_container').html('<img id="image_preview" src="" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;">');
            $('#image_preview_container').show();
            $('#image_preview').after('<div id="image_loading">加载中...</div>');
            
            // 创建新图片对象测试加载
            var img = new Image();
            img.onload = function() {
                $('#image_loading').remove();
                $('#image_preview').attr('src', imageUrl);
            };
            img.onerror = function() {
                $('#image_loading').remove();
                $('#image_preview_container').html("<span class='text-muted'>图片URL无效</span>");
            };
            img.src = imageUrl;
        } else {
            $('#image_preview_container').hide();
        }
    });
    
    // URL验证函数
    function isValidUrl(url) {
        try {
            return /^(http|https):\/\/[^ "]+$/.test(url);
        } catch (e) {
            return false;
        }
    }
});
JS
            );
                
        });
    }
    
    /**
     * 清除图库列表缓存
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearGalleryCache()
    {
        try {
            // 这里可以添加清除缓存的代码
            Log::info('手动刷新图库列表');
            
            admin_toastr('刷新成功', 'success');
        } catch (\Exception $e) {
            Log::error('刷新操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            admin_toastr('刷新失败: ' . $e->getMessage(), 'error');
        }
        
        return redirect()->back();
    }
    
    /**
     * 导出图库数据
     *
     * @param Request $request
     * @return mixed
     */
    public function exportGallery(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaImageGalleryExportAction();
        return $action->handle($request);
    }
    
    /**
     * 导入图库数据
     *
     * @param Request $request
     * @return mixed
     */
    public function importGallery(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaImageGalleryImportAction();
        return $action->handle($request);
    }
    
    /**
     * 下载图库导入模板
     *
     * @return \Illuminate\Http\Response
     */
    public function downloadTemplate()
    {
        try {
            // 创建新的Excel文档
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('图库导入模板');
            
            // 添加表头
            $sheet->setCellValue('A1', '图库ID');
            $sheet->setCellValue('B1', '图片名称');
            $sheet->setCellValue('C1', '图片路径');
            $sheet->setCellValue('D1', '备注');
            
            // 设置表头样式
            $sheet->getStyle('A1:D1')->getFont()->setBold(true);
            $sheet->getStyle('A1:D1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
                
            // 添加示例数据
            $sheet->setCellValue('A2', 'BH001');
            $sheet->setCellValue('B2', '油画布');
            $sheet->setCellValue('C2', 'https://example.com/images/oil-canvas.jpg');
            $sheet->setCellValue('D2', '高质量油画布');
            
            $sheet->setCellValue('A3', 'BH002');
            $sheet->setCellValue('B3', '水彩纸');
            $sheet->setCellValue('C3', 'https://example.com/images/watercolor-paper.jpg');
            $sheet->setCellValue('D3', '');
            
            // 调整列宽
            $sheet->getColumnDimension('A')->setAutoSize(true);
            $sheet->getColumnDimension('B')->setAutoSize(true);
            $sheet->getColumnDimension('C')->setAutoSize(true);
            $sheet->getColumnDimension('D')->setAutoSize(true);
            
            // 确保第一个工作表是活动工作表
            $spreadsheet->setActiveSheetIndex(0);
            
            // 创建Excel写入器
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'gallery_import_template');
            $writer->save($tempFile);
            
            // 返回文件下载响应
            return response()->download($tempFile, 'banhua_gallery_import_template.xlsx', [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="banhua_gallery_import_template.xlsx"',
            ])->deleteFileAfterSend(true);
            
        } catch (\Exception $e) {
            Log::error('下载图库导入模板失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '下载模板失败: ' . $e->getMessage()
            ]);
        }
    }
} 