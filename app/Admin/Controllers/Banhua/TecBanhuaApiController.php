<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use App\Http\Controllers\Controller;
use App\Models\Banhua\TecBanhuaAttrModel;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use App\Models\Banhua\TecBanhuaProductCategoryModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Layout\Content;
use App\Admin\Controllers\Banhua\TecBanhuaProductListsController;

/**
 * 阪画API控制器
 */
class TecBanhuaApiController extends Controller
{
    /**
     * 获取属性值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAttrValues(Request $request)
    {
        $attrId = $request->get('q');
        
        if (!$attrId) {
            return response()->json([]);
        }
        
        $values = TecBanhuaAttrValueModel::where('attr_id', $attrId)
            ->orderBy('sort')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'text' => $item->name,
                ];
            });
            
        return response()->json($values);
    }
    
    /**
     * 获取所有属性和属性值
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllAttributes()
    {
        $attributes = TecBanhuaAttrModel::with('values')
            ->orderBy('sort')
            ->get();
            
        $result = [];
        
        foreach ($attributes as $attr) {
            $values = [];
            foreach ($attr->values as $value) {
                $values[] = [
                    'id' => $value->id,
                    'name' => $value->name,
                    'sort' => $value->sort,
                ];
            }
            
            $result[] = [
                'id' => $attr->id,
                'name' => $attr->name,
                'sort' => $attr->sort,
                'values' => $values,
            ];
        }
        
        return response()->json([
            'status' => true,
            'data' => $result,
        ]);
    }
    
    /**
     * 获取分类信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategoryInfo(Request $request)
    {
        $id = $request->input('id');
        
        if (!$id) {
            return response()->json([
                'status' => false,
                'message' => '分类ID不能为空',
            ]);
        }
        
        $category = TecBanhuaProductCategoryModel::find($id);
            
        if (!$category) {
            return response()->json([
                'status' => false,
                'message' => '分类不存在',
            ]);
        }
        
        // 构建分类路径
        $fullPath = TecBanhuaProductListsController::buildCategoryPathStatic($id);
        
        return response()->json([
            'status' => true,
            'data' => [
                'id' => $category->id,
                'title' => $category->title,
                'parent_id' => $category->parent_id,
                'full_path' => $fullPath,
            ],
        ]);
    }
    
    /**
     * 获取所有分类（调试用）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllCategories()
    {
        $categories = TecBanhuaProductCategoryModel::all();
        return response()->json($categories);
        }
        
    /**
     * 获取产品SKU列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductSkus(Request $request)
    {
        $productId = $request->input('product_id');
        
        if (!$productId) {
            return response()->json([
                'status' => false,
                'message' => '产品ID不能为空',
            ]);
        }
        
        $skus = \App\Models\Banhua\TecBanhuaProductSkuModel::where('product_id', $productId)->get();
        
        if ($skus->isEmpty()) {
            return response()->json([
                'status' => true,
                'message' => '该产品暂无SKU数据',
                'data' => [],
            ]);
        }
        
        // 处理SKU数据，获取属性值文本
        $result = [];
        foreach ($skus as $sku) {
            $result[] = [
                'id' => $sku->id,
                'sku' => $sku->sku,
                'pid' => $sku->pid,
                'jan' => $sku->jan,
                'price' => $sku->price,
                'stock' => $sku->stock,
                'image' => $sku->image,
                'attr_values' => $sku->attr_values,
                'attr_values_text' => $sku->attr_values_text,
            ];
        }
        
        return response()->json([
            'status' => true,
            'data' => $result,
        ]);
    }

    /**
     * 批量获取属性值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAttrValuesBatch(Request $request)
    {
        $ids = $request->input('ids');
        
        if (!$ids) {
            return response()->json([
                'status' => false,
                'message' => '属性值ID不能为空',
            ]);
        }
        
        // 分割ID字符串为数组
        $idArray = explode(',', $ids);
        
        // 获取属性值及其关联的属性
        $values = TecBanhuaAttrValueModel::with('attr')
            ->whereIn('id', $idArray)
            ->get();
            
        $result = [];
        foreach ($values as $value) {
            $result[] = [
                'id' => $value->id,
                'name' => $value->name,
                'attr_id' => $value->attr_id,
                'attr_name' => $value->attr ? $value->attr->name : '未知属性',
            ];
        }
        
        return response()->json([
            'status' => true,
            'data' => $result,
        ]);
    }
} 