<?php

namespace App\Admin\Controllers\Banhua;

use App\Models\Banhua\BanhuaConstants;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use App\Models\Banhua\TecBanhuaProductTypeMapping;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;

class TecBanhuaProductTypeMappingController extends AdminController
{
    /**
     * 标题
     *
     * @return string
     */
    protected $title = '产品类型映射管理';

    /**
     * 构建表格
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecBanhuaProductTypeMapping(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('alias_name', '别名');
            $grid->column('primary_name', '主类型名称');
            $grid->column('primary_value_id', '主类型ID');
            $grid->column('is_active', '是否启用')->switch();
            $grid->column('description', '说明');
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('alias_name', '别名');
                $filter->like('primary_name', '主类型名称');
                $filter->equal('is_active', '是否启用')->select([0 => '否', 1 => '是']);
            });
            
            // 添加批量同步按钮
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<div class="btn-group pull-right" style="margin-right: 10px">
                <a href="'.admin_url('banhua/product-type-mappings/sync').'" class="btn btn-sm btn-primary">
                    <i class="fa fa-refresh"></i> 同步属性值
                </a>
            </div>');
            });
        });
    }

    /**
     * 构建详情
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecBanhuaProductTypeMapping(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('alias_name', '别名');
            $show->field('primary_name', '主类型名称');
            $show->field('primary_attr_id', '属性ID');
            $show->field('primary_value_id', '属性值ID');
            $show->field('is_active', '是否启用');
            $show->field('description', '说明');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 构建表单
     *
     * @return Form
     */
    protected function form()
    {
        // 获取产品类型属性ID
        $productTypeAttrId = BanhuaConstants::getProductTypeAttrId();
        
        // 获取所有产品类型属性值
        $attrValues = TecBanhuaAttrValueModel::where('attr_id', $productTypeAttrId)
            ->pluck('name', 'name')
            ->toArray();
            
        return Form::make(new TecBanhuaProductTypeMapping(), function (Form $form) use ($attrValues, $productTypeAttrId) {
            $form->display('id', 'ID');
            $form->text('alias_name', '别名')->required()->rules('required|max:50')->help('例如：板、画芯、成品1');
            $form->select('primary_name', '主类型名称')->options($attrValues)->required()->rules('required|max:50')->help('从属性系统中选择标准的产品类型名称');
            $form->hidden('primary_attr_id')->value($productTypeAttrId); // 产品类型属性ID
            $form->display('primary_value_id', '属性值ID')->help('保存后自动填充');
            $form->switch('is_active', '是否启用')->default(1);
            $form->textarea('description', '说明')->rows(3);
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            
            // 保存前回调
            $form->saving(function (Form $form) use ($attrValues, $productTypeAttrId) {
                // 查找并设置对应的属性值ID
                $primaryName = $form->primary_name;
                $attrValue = TecBanhuaAttrValueModel::where('attr_id', $productTypeAttrId)
                    ->where('name', $primaryName)
                    ->first();
                    
                if ($attrValue) {
                    $form->primary_value_id = $attrValue->id;
                }
            });
        });
    }
    
    /**
     * 同步属性值
     */
    public function sync(Content $content)
    {
        try {
            TecBanhuaProductTypeMapping::rebuildMappings();
            admin_success('成功', '属性值同步成功！');
        } catch (\Exception $e) {
            admin_error('错误', '属性值同步失败: ' . $e->getMessage());
        }
        
        return redirect()->to(admin_url('banhua/product-type-mappings'));
    }
    
    /**
     * 初始化映射数据
     */
    public function initMappings(Request $request)
    {
        try {
            // 从常量类获取初始映射数据
            $initialMappings = BanhuaConstants::getInitialProductTypeMappings();
            
            // 遍历添加初始映射
            foreach ($initialMappings as $mapping) {
                // 检查是否已存在
                $exists = TecBanhuaProductTypeMapping::where('alias_name', $mapping['alias_name'])->exists();
                if (!$exists) {
                    TecBanhuaProductTypeMapping::create($mapping);
                }
            }
            
            // 同步属性值ID
            TecBanhuaProductTypeMapping::rebuildMappings();
            
            return response()->json(['status' => true, 'message' => '初始映射数据创建成功']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => '初始映射数据创建失败: ' . $e->getMessage()]);
        }
    }
} 