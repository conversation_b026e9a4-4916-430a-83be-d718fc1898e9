<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Banhua\TecBanhuaConsumableStockModel;
use App\Models\Banhua\TecBanhuaConsumableOutLogModel;
use App\Models\Banhua\TecBanhuaConsumableOutLogItemModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Validator;

/**
 * 消耗品出库控制器
 */
class TecBanhuaConsumableOutController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '消耗品出库';

    /**
     * 出库列表页面
     */
    public function index(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('消耗品库存列表')
            ->body($this->grid());
    }

    /**
     * 构建库存列表
     */
    protected function grid(): Grid
    {
        return Grid::make(new TecBanhuaConsumableStockModel(), function (Grid $grid) {
            // 只显示有库存的记录
            $grid->model()->where('stock', '>', 0);
            
            // 设置分页选项
            $grid->paginate(20);
            $grid->perPages([20, 50, 100]);
            
            // 启用横向滚动条
            $grid->scrollbarX();
            
            // 关联模型预加载（减少N+1查询）
            $grid->model()->with(['sku', 'sku.gallery', 'warehouse', 'location']);
            
            // 定义表格列
            $grid->column('id', 'ID')->sortable();
            
            // SKU信息
            $grid->column('sku_info', 'SKU编码')->display(function () {
                if (!$this->sku) {
                    return '';
                }
                
                // 优先获取gallery.remark作为显示名称
                $displayName = '';
                if ($this->sku->gallery && !empty($this->sku->gallery->remark)) {
                    $displayName = $this->sku->gallery->remark;
                } else if (!empty($this->sku->name)) {
                    $displayName = $this->sku->name;
                }
                
                // 返回"商品名称(SKU编码)"的格式
                return $displayName . '(' . $this->sku->sku . ')';
            });
            
            $grid->column('attr_text', '属性组合')->display(function($value) {
                // 如果值为空，尝试重新计算
                if (empty($value) && !empty($this->attr_value_ids)) {
                    // 获取缓存的属性值映射
                    $valueMapCache = \Illuminate\Support\Facades\Cache::remember('banhua_attr_value_map', 3600, function () {
                        return \App\Models\Banhua\TecBanhuaAttrValueModel::pluck('name', 'id')->toArray();
                    });
                    
                    // 确保attr_value_ids是数组或有效的逗号分隔字符串
                    $valueIds = is_array($this->attr_value_ids) 
                        ? $this->attr_value_ids 
                        : explode(',', (string)$this->attr_value_ids);
                    
                    $names = [];
                    foreach ($valueIds as $id) {
                        $id = trim($id);
                        if (!empty($id) && isset($valueMapCache[$id])) {
                            $names[] = $valueMapCache[$id];
                        }
                    }
                    
                    $value = implode(', ', $names);
                }
                
                return $value ?: '无属性';
            });
            
            // 库存信息
            $grid->column('warehouse.name', '仓库');
            $grid->column('location.location_name', '库位')->display(function ($value) {
                return $value ?: '未指定';
            });
            $grid->column('stock', '库存数量')->sortable();
            
            // 批次信息
            $grid->column('batch_no', '批次号');
            
            // 操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 移除默认按钮
                $actions->disableDelete();
                $actions->disableEdit();
                $actions->disableQuickEdit();
                $actions->disableView();
                
                // 添加出库按钮
                $actions->append('<a href="'.admin_url('banhua/consumable-out/form/'.$actions->getKey()).'" class="btn btn-sm btn-success">出库</a>');
            });
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();

                // SKU名称筛选
                $filter->where('sku_name', function ($query) {
                    $query->whereHas('sku', function ($q) {
                        $q->where('name', 'like', "%{$this->input}%");
                    })->orWhereHas('sku.gallery', function ($q) {
                        $q->where('remark', 'like', "%{$this->input}%");
                    });
                }, 'SKU名称')->width(2);
                
                // SKU编码筛选
                $filter->where('sku_code', function ($query) {
                    $query->whereHas('sku', function ($q) {
                        $q->where('sku', 'like', "%{$this->input}%");
                    });
                }, 'SKU编码')->width(2);
                
                // 仓库筛选 - 使用下拉列表
                $filter->where('warehouse_id', function ($query) {
                    if ($this->input) {
                        $query->where('warehouse_id', $this->input);
                    }
                }, '仓库')->select(function() {
                    // 获取所有仓库列表
                    return \App\Models\TecWarehouseModel::pluck('name', 'id')->toArray();
                })->width(2);
                
                $filter->like('batch_no', '批次号')->width(2);
                

            });
            
            // 禁用创建按钮
            $grid->disableCreateButton();
            
            // 禁用批量操作
            $grid->disableBatchActions();
        });
    }

    /**
     * 单品出库表单页面
     */
    public function outForm($id, Content $content)
    {
        try {
            // 使用fresh()确保获取最新数据
            $stock = TecBanhuaConsumableStockModel::with(['sku', 'sku.gallery', 'warehouse', 'location'])
                ->where('id', $id)
                ->where('stock', '>', 0) // 只允许有库存的记录进行出库操作
                ->firstOrFail();
            
            return $content
                ->header($this->title)
                ->description('出库操作')
                ->body($this->buildOutForm($stock));
        } catch (\Exception $e) {
            // 记录详细的错误信息
            \Illuminate\Support\Facades\Log::error('加载出库表单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'id' => $id
            ]);
            
            // 显示错误信息
            admin_error('加载出库表单失败', $e->getMessage());
            return redirect('/admin/banhua/consumable-out');
        }
    }

    /**
     * 构建出库表单
     */
    protected function buildOutForm($stock): Form
    {
        // 创建一个虚拟表单，不绑定到任何模型
        $form = new Form(new TecBanhuaConsumableOutLogModel());
        
        // 设置表单提交地址
        $form->action(admin_url('banhua/consumable-out/single'));
        
        // 确保使用POST方法
        $form->hidden('_method')->value('POST');
        
        // HTML表单默认使用POST方法，无需额外设置
        
        // 显示消耗品信息
        $displayName = '未知SKU';
        if ($stock->sku) {
            if ($stock->sku->gallery && !empty($stock->sku->gallery->remark)) {
                $displayName = $stock->sku->gallery->remark;
            } else if (!empty($stock->sku->name)) {
                $displayName = $stock->sku->name;
            }
        }
        
        // SKU名称和编码的显示
        $skuCodeDisplay = $stock->sku ? $stock->sku->sku : '未知编码';
        $form->display('sku_info', 'SKU信息')->value($displayName . '(' . $skuCodeDisplay . ')');
        $form->display('attr_text', '属性组合')->value($stock->attr_text ?: '无');
        $form->display('warehouse', '仓库')->value($stock->warehouse ? $stock->warehouse->name : '未知仓库');
        $form->display('location', '库位')->value($stock->location ? $stock->location->location_name : '未指定');
        $form->display('current_stock', '当前库存')->value($stock->stock);
        $form->display('batch_no', '批次号')->value($stock->batch_no ?: '无');
        
        // 出库信息填写
        $form->divider('出库信息');
        
        // 出库数量
        $form->number('quantity', '出库数量')
            ->min(1)
            ->max($stock->stock)
            ->default(1)
            ->required()
            ->help("最大可出库数量：{$stock->stock}")
            ->rules("required|integer|min:1|max:{$stock->stock}", [
                'required' => '出库数量不能为空',
                'integer' => '出库数量必须是整数',
                'min' => '出库数量不能小于1',
                'max' => "出库数量不能超过{$stock->stock}"
            ]);
        
        // 出库原因
        $form->select('out_reason', '出库原因')
            ->options([
                '客户订单' => '客户订单',
                '内部使用' => '内部使用',
                '损耗' => '损耗',
                '其他' => '其他'
            ])
            ->required()
            ->rules('required', [
                'required' => '出库原因不能为空'
            ]);
        
        // 备注
        $form->textarea('remarks', '备注')
            ->rows(3);
        
        // 隐藏字段 - 库存ID
        $form->hidden('stock_id')->value($stock->id);
        
        // 添加隐藏的 _method 字段确保使用 POST 方法
        $form->hidden('_method')->value('POST');
        
        // 自定义表单按钮
        $form->tools(function (Form\Tools $tools) {
            $tools->disableDelete();
            $tools->disableView();
        });
        
        $form->action(admin_url('banhua/consumable-out/single'));
        
        // 自定义表单底部
        $form->footer(function (Form\Footer $footer) {
            // 禁用额外选项
            $footer->disableCreatingCheck();
            $footer->disableEditingCheck();
            $footer->disableViewCheck();
            $footer->disableReset();
        });
        
        // 使用工具栏替换默认按钮
        $form->tools(function (Form\Tools $tools) {
            // 清除所有工具按钮
            $tools->disableList();
            $tools->disableDelete();
            $tools->disableView();
            
            // 添加返回按钮
            $tools->append('<a href="'.admin_url('banhua/consumable-out').'" class="btn btn-sm btn-default"><i class="feather icon-arrow-left"></i>&nbsp;返回</a>');
        });
        
        // 处理表单提交
        $form->submitted(function (Form $form) {
            // 表单提交后的处理
            return $this->handleOutFormSubmission($form);
        });
        
        // 表单保存成功后的处理
        $form->saved(function (Form $form) {
            // 表单保存成功后的处理
            return $form->response()->success('出库成功！')->redirect('banhua/consumable-out');
        });
        
        // 添加自定义JS确保表单正确提交
        Admin::script(<<<JS
        $(function() {
            // 修改提交按钮文本
            $('.form-footer button[type="submit"]').text('确认出库');
            
            // 监听表单提交，但不阻止默认行为
            $('form').on('submit', function() {
                console.log('表单提交中...');
                // 禁用提交按钮，防止重复提交
                $('.form-footer button[type="submit"]').prop('disabled', true).text('处理中...');
            });
            
            // 添加全局AJAX错误处理
            $(document).ajaxError(function(event, xhr, settings) {
                console.log('AJAX错误', xhr);
                if (xhr.responseJSON) {
                    console.log('响应数据', xhr.responseJSON);
                }
                // 恢复提交按钮状态
                $('.form-footer button[type="submit"]').prop('disabled', false).text('确认出库');
            });
            
            // 添加全局AJAX成功处理
            $(document).ajaxSuccess(function(event, xhr, settings) {
                console.log('AJAX成功', xhr);
                if (xhr.responseJSON) {
                    console.log('响应数据', xhr.responseJSON);
                    // 如果有重定向URL，则跳转
                    if (xhr.responseJSON.redirect) {
                        console.log('准备跳转到:', xhr.responseJSON.redirect);
                        setTimeout(function() {
                            window.location.href = xhr.responseJSON.redirect;
                        }, 1000);
                    }
                }
            });
        });
JS
        );
        
        return $form;
    }
    
    /**
     * 处理出库表单提交
     */
    protected function handleOutFormSubmission(Form $form)
    {
        $data = $form->input();
        
        // 验证请求数据
        $validator = Validator::make($data, [
            'stock_id' => 'required|exists:t_banhua_consumable_stocks,id',
            'quantity' => 'required|integer|min:1',
            'out_reason' => 'required|string|max:50',
        ], [
            'stock_id.required' => '库存ID不能为空',
            'stock_id.exists' => '库存记录不存在',
            'quantity.required' => '出库数量不能为空',
            'quantity.integer' => '出库数量必须是整数',
            'quantity.min' => '出库数量不能小于1',
            'out_reason.required' => '出库原因不能为空',
            'out_reason.string' => '出库原因必须是字符串',
            'out_reason.max' => '出库原因不能超过50个字符',
        ]);
        
        if ($validator->fails()) {
            return $form->responseValidationMessages($validator->errors());
        }
        
        // 获取库存记录
        $stockId = $data['stock_id'];
        $outQuantity = $data['quantity'];
        
        // 开始数据库事务
        DB::beginTransaction();
        
        try {
            // 使用严格查询并锁定行，防止并发问题
            $stock = TecBanhuaConsumableStockModel::lockForUpdate()->find($stockId);
            
            if (!$stock) {
                throw new \Exception('未找到库存记录');
            }
            
            // 确保取得最新库存并再次检查
            $freshStock = TecBanhuaConsumableStockModel::find($stockId);
            $freshStockQuantity = $freshStock ? $freshStock->stock : 0;
            
            // 检查库存是否足够（使用两个数据源进行双重检查）
            if ($stock->stock <= 0 || $freshStockQuantity <= 0) {
                throw new \Exception("库存为零，无法出库");
            }
            
            if ($stock->stock < $outQuantity || $freshStockQuantity < $outQuantity) {
                throw new \Exception("库存不足，当前库存：{$stock->stock}，需要出库：{$outQuantity}");
            }
            
            // 创建出库日志
            $outLog = new TecBanhuaConsumableOutLogModel([
                'out_code' => TecBanhuaConsumableOutLogModel::generateOutCode(),
                'consumable_stock_id' => $stockId,
                'quantity' => $outQuantity,
                'out_reason' => $data['out_reason'],
                'remarks' => $data['remarks'] ?? '',
                'created_by' => Admin::user()->id,
            ]);
            
            $outLog->save();
            
            // 创建出库日志明细
            $outLogItem = new TecBanhuaConsumableOutLogItemModel([
                'out_log_id' => $outLog->id,
                'consumable_stock_id' => $stockId,
                'quantity' => $outQuantity,
                'unit_cost' => $stock->cost_price,
                'unit_price' => $stock->sale_price,
            ]);
            
            $outLogItem->save();
            
            // 减少库存
            $stock->stock -= $outQuantity;
            $stock->save();
            
            // 提交事务
            DB::commit();
            
            // 记录成功信息
            \Illuminate\Support\Facades\Log::info('出库成功', [
                'out_code' => $outLog->out_code,
                'quantity' => $outQuantity,
                'stock_id' => $stockId
            ]);
            
            // 设置成功消息
            $successMessage = "出库成功！出库数量：{$outQuantity}，出库单号：{$outLog->out_code}";
            
            // 中断默认的表单处理流程，直接返回成功响应
            // 使用 script 确保跳转
            $script = "setTimeout(function() { window.location.href = '".admin_url('banhua/consumable-out')."'; }, 1000);";
            return $form->response()
                ->success($successMessage)
                ->script($script)
                ->redirect('banhua/consumable-out');
            
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            // 记录详细的错误信息
            \Illuminate\Support\Facades\Log::error('出库失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'stock_id' => $stockId ?? null,
                'quantity' => $outQuantity ?? null,
                'request' => request()->all()
            ]);
            
            // 返回错误响应
            return $form->response()->error($e->getMessage());
        }
    }

    /**
     * 处理单品出库（直接API调用）
     */
    public function singleOut(Request $request)
    {
        \Illuminate\Support\Facades\Log::info('出库表单提交', [
            'request_data' => $request->all(),
            'method' => $request->method(),
            'path' => $request->path(),
            'headers' => $request->header(),
            'content_type' => $request->header('Content-Type'),
        ]);
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'stock_id' => 'required|exists:t_banhua_consumable_stocks,id',
            'quantity' => 'required|integer|min:1',
            'out_reason' => 'required|string|max:50',
        ], [
            'stock_id.required' => '库存ID不能为空',
            'stock_id.exists' => '库存记录不存在',
            'quantity.required' => '出库数量不能为空',
            'quantity.integer' => '出库数量必须是整数',
            'quantity.min' => '出库数量不能小于1',
            'out_reason.required' => '出库原因不能为空',
            'out_reason.string' => '出库原因必须是字符串',
            'out_reason.max' => '出库原因不能超过50个字符',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'data' => [
                    'message' => '表单验证失败',
                    'errors' => $validator->errors()->toArray()
                ]
            ], 422);
        }
        
        // 获取库存记录
        $stockId = $request->input('stock_id');
        $outQuantity = $request->input('quantity');
        
        // 开始数据库事务
        DB::beginTransaction();
        
        try {
            // 使用严格查询并锁定行，防止并发问题
            $stock = TecBanhuaConsumableStockModel::lockForUpdate()->find($stockId);
            
            if (!$stock) {
                throw new \Exception('未找到库存记录');
            }
            
            // 确保取得最新库存并再次检查
            $freshStock = TecBanhuaConsumableStockModel::find($stockId);
            $freshStockQuantity = $freshStock ? $freshStock->stock : 0;
            
            // 检查库存是否足够（使用两个数据源进行双重检查）
            if ($stock->stock <= 0 || $freshStockQuantity <= 0) {
                throw new \Exception("库存为零，无法出库");
            }
            
            if ($stock->stock < $outQuantity || $freshStockQuantity < $outQuantity) {
                throw new \Exception("库存不足，当前库存：{$stock->stock}，需要出库：{$outQuantity}");
            }
            
            // 创建出库日志
            $outLog = new TecBanhuaConsumableOutLogModel([
                'out_code' => TecBanhuaConsumableOutLogModel::generateOutCode(),
                'consumable_stock_id' => $stockId,
                'quantity' => $outQuantity,
                'out_reason' => $request->input('out_reason'),
                'remarks' => $request->input('remarks'),
                'created_by' => Admin::user()->id,
            ]);
            
            $outLog->save();
            
            // 创建出库日志明细
            $outLogItem = new TecBanhuaConsumableOutLogItemModel([
                'out_log_id' => $outLog->id,
                'consumable_stock_id' => $stockId,
                'quantity' => $outQuantity,
                'unit_cost' => $stock->cost_price,
                'unit_price' => $stock->sale_price,
            ]);
            
            $outLogItem->save();
            
            // 减少库存
            $stock->stock -= $outQuantity;
            $stock->save();
            
            // 提交事务
            DB::commit();
            
            // 记录成功信息
            \Illuminate\Support\Facades\Log::info('出库成功', [
                'out_code' => $outLog->out_code,
                'quantity' => $outQuantity,
                'stock_id' => $stockId
            ]);
            
            // 设置成功消息
            $successMessage = "出库成功！出库数量：{$outQuantity}，出库单号：{$outLog->out_code}";
            
            // 返回成功响应，符合 Dcat Admin 预期的格式
            return response()->json([
                'status' => true,
                'data' => [
                    'message' => $successMessage
                ],
                'redirect' => admin_url('banhua/consumable-out'),
                // 添加script字段，确保页面跳转
                'script' => "setTimeout(function() { window.location.href = '".admin_url('banhua/consumable-out')."'; }, 1000);"
            ]);
            
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            // 记录详细的错误信息
            \Illuminate\Support\Facades\Log::error('出库失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'stock_id' => $stockId ?? null,
                'quantity' => $outQuantity ?? null,
                'request' => request()->all()
            ]);
            
            // 返回错误响应，符合 Dcat Admin 预期的格式
            return response()->json([
                'status' => false,
                'data' => [
                    'message' => $e->getMessage()
                ]
            ], 422);
        }
    }
} 