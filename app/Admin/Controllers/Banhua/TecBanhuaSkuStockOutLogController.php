<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Banhua\TecBanhuaSkuStockOutLogItemModel;
use App\Models\Banhua\TecBanhuaSkuStockOutLogModel;
use App\Models\TecWarehouseModel;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\HasResourceActions;
use Dcat\Admin\Layout\Content;
use Illuminate\Support\Facades\DB;

/**
 * SKU出库日志控制器
 */
class TecBanhuaSkuStockOutLogController extends AdminController
{
    use HasResourceActions;
    
    /**
     * 标题
     *
     * @var string
     */
    protected $title = 'SKU出库日志';
    
    /**
     * 首页
     */
    public function index(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('出库记录一览')
            ->body($this->grid());
    }
    
    /**
     * 构建列表
     */
    protected function grid(): Grid
    {
        // 使用明细项模型替代主表模型
        return Grid::make(new TecBanhuaSkuStockOutLogItemModel(), function (Grid $grid) {
            // 设置分页选项
            $grid->paginate(20);
            $grid->perPages([20, 50, 100]);
            
            // 启用横向滚动条
            $grid->scrollbarX();
            
            // 默认只显示最近一周的记录，并按创建时间倒序排列
            $grid->model()->whereHas('outLog', function ($query) {
                $query->where('created_at', '>=', date('Y-m-d 00:00:00', strtotime('-7 days')));
            })->orderBy('id', 'desc');
            
            // 预加载关联数据
            $grid->model()->with(['outLog', 'skuStock', 'skuStock.sku', 'skuStock.sku.gallery', 'warehouse', 'outLog.creator', 'outLog.ecommercePlatform']);
            
            $grid->column('id')->sortable();
            
            // 给出库单号添加颜色标记，只有多条相同出库单号才添加颜色
            $grid->column('outLog.out_code', '出库单号')->display(function($value) use ($grid) {
                // 为每个出库单分配一个颜色
                $colors = [
                    '#8a2be2', '#ff7f50', '#6495ed', '#ffa500', 
                    '#da70d6', '#20b2aa', '#ff69b4', '#7b68ee'
                ];
                
                if ($value) {
                    // 检查该出库单号是否有多条记录
                    $count = \App\Models\Banhua\TecBanhuaSkuStockOutLogItemModel::whereHas('outLog', function($query) use ($value) {
                        $query->where('out_code', $value);
                    })->count();
                    
                    // 只有当有多条记录时才添加颜色
                    if ($count > 1) {
                        // 使用出库单号的数字部分作为颜色索引
                        $numericPart = preg_replace('/[^0-9]/', '', $value);
                        $colorIndex = intval(substr($numericPart, -2)) % count($colors);
                        $color = $colors[$colorIndex];
                        
                        // 返回带颜色的出库单号
                        return "<span style='color:{$color}; font-weight:bold;'>{$value}</span>";
                    }
                }
                
                return $value;
            })->sortable();
            
            // 显示电商平台和订单号
            $grid->column('outLog.ecommercePlatform.platform_name', '电商平台');
            $grid->column('outLog.order_number', '订单号');
            
            // 显示日期时间（年月日时分秒）
            $grid->column('outLog.created_at', '出库时间')->display(function($value) {
                // 显示完整的日期时间格式
                return $value ? $value->format('Y-m-d H:i:s') : '';
            })->sortable();
            
            // 显示SKU名称和编码 - 直接从当前明细项获取
            $grid->column('sku_code', 'SKU编码')->display(function () {
                if ($this->skuStock && $this->skuStock->sku) {
                    return $this->skuStock->sku->sku;
                }
                return '未知SKU';
            });
            
            // 显示属性组合 - 直接从当前明细项获取
            $grid->column('attr_text', '属性组合')->display(function () {
                if ($this->skuStock && $this->skuStock->sku) {
                    $sku = $this->skuStock->sku;
                    
                    // 优先使用simplified_attr_values_text
                    if (!empty($sku->simplified_attr_values_text)) {
                        return $sku->simplified_attr_values_text;
                    } elseif (!empty($sku->attr_values_text)) {
                        // 如果没有simplified_attr_values_text，则处理attr_values_text
                        $processedAttrText = preg_replace('/([^,]+?):/', '', $sku->attr_values_text);
                        return $processedAttrText;
                    }
                    return '无属性';
                }
                return '';
            });
            
            // 显示仓库 - 直接从当前明细项获取
            $grid->column('warehouse.name', '仓库');
            
            // 显示出库数量 - 直接从当前明细项获取
            $grid->column('quantity', '出库数量');
            
            // 显示客户信息
            $grid->column('outLog.customer_name', '客户姓名');
            $grid->column('outLog.customer_phone', '客户电话');
            
            // 显示快递信息
            $grid->column('outLog.express_company', '快递公司');
            $grid->column('outLog.tracking_number', '快递单号');
            
            $grid->column('outLog.out_reason', '出库原因');
            
            // 显示操作人
            $grid->column('outLog.created_by', '操作人')->display(function() {
                if ($this->outLog && $this->outLog->creator) {
                    return $this->outLog->creator->name;
                } elseif ($this->outLog && $this->outLog->created_by) {
                    // 尝试直接从数据库查询
                    $admin = \Dcat\Admin\Models\Administrator::find($this->outLog->created_by);
                    return $admin ? $admin->name : '未知用户('.$this->outLog->created_by.')';
                }
                return '系统';
            });
            
            // 完全禁用操作列
            $grid->disableActions();
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                
                $filter->where('out_code', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('out_code', $this->input);
                    });
                }, '出库单号')->width(2);
                
                // 电商平台筛选
                $filter->where('ecommerce_platform_id', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('ecommerce_platform_id', $this->input);
                    });
                }, '电商平台')->select(function() {
                    return \App\Models\Banhua\TecBanhuaEcommercePlatformModel::pluck('platform_name', 'id')->toArray();
                })->width(2);
                
                // 订单号筛选
                $filter->where('order_number', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('order_number', 'like', "%{$this->input}%");
                    });
                }, '订单号')->width(2);
                
                // 客户信息筛选
                $filter->where('customer_name', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('customer_name', 'like', "%{$this->input}%");
                    });
                }, '客户姓名')->width(2);
                
                $filter->where('customer_phone', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('customer_phone', 'like', "%{$this->input}%");
                    });
                }, '客户电话')->width(2);
                
                // 快递信息筛选
                $filter->where('express_company', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('express_company', 'like', "%{$this->input}%");
                    });
                }, '快递公司')->width(2);
                
                $filter->where('tracking_number', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('tracking_number', 'like', "%{$this->input}%");
                    });
                }, '快递单号')->width(2);
                
                $filter->where('out_reason', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('out_reason', $this->input);
                    });
                }, '出库原因')->width(2);
                
                // 按SKU编码筛选
                $filter->where('sku_code', function ($query) {
                    $query->whereHas('skuStock.sku', function ($q) {
                        $q->where('sku', 'like', "%{$this->input}%");
                    });
                }, 'SKU编码')->width(2);
                
                // 按SKU名称筛选
                $filter->where('sku_name', function ($query) {
                    $query->whereHas('skuStock.sku', function ($q) {
                        $q->where('name', 'like', "%{$this->input}%");
                    })->orWhereHas('skuStock.sku.gallery', function ($q) {
                        $q->where('remark', 'like', "%{$this->input}%");
                    });
                }, 'SKU名称')->width(2);
                
                // 按属性组合筛选
                $filter->where('attr_text', function ($query) {
                    $query->whereHas('skuStock.sku', function ($q) {
                        $q->where('attr_values_text', 'like', "%{$this->input}%");
                    });
                }, '属性组合')->width(2);
                
                // 按仓库筛选
                $filter->equal('warehouse_id', '仓库')->select(function() {
                    // 获取所有仓库列表
                    return \App\Models\TecWarehouseModel::pluck('name', 'id')->toArray();
                })->width(2);
                
                // 使用两个独立的日期筛选器
                $filter->where('start_date', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('created_at', '>=', $this->input . ' 00:00:00');
                    });
                }, '开始日期')->date()->width(2)->default(date('Y-m-d', strtotime('-7 days')));
                
                $filter->where('end_date', function ($query) {
                    $query->whereHas('outLog', function ($q) {
                        $q->where('created_at', '<=', $this->input . ' 23:59:59');
                    });
                }, '结束日期')->date()->width(2)->default(date('Y-m-d', strtotime('+1 day')));
            });
            
            // 禁用创建按钮
            $grid->disableCreateButton();
            // 禁用批量操作
            $grid->disableBatchActions();
            
        });
    }

    /**
     * 详情页面
     */
    public function show($id, Content $content): Content
    {
        // 获取明细项
        $item = TecBanhuaSkuStockOutLogItemModel::find($id);
        if (!$item) {
            return $content->withError('未找到出库明细记录');
        }
        
        // 显示对应的出库单详情
        return $content
            ->header($this->title)
            ->description('出库详情')
            ->body($this->detail($item->out_log_id));
    }

    /**
     * 构建详情页
     */
    protected function detail($id): Show
    {
        return Show::make($id, new TecBanhuaSkuStockOutLogModel(), function (Show $show) {
            $show->field('id');
            $show->field('out_code', '出库单号');
            
            // 显示电商平台和订单号
            $show->field('ecommercePlatform.platform_name', '电商平台');
            $show->field('order_number', '订单号');
            
            // 显示客户信息
            $show->field('customer_name', '客户姓名');
            $show->field('customer_phone', '客户电话');
            $show->field('customer_address', '客户地址');
            $show->field('delivery_requirements', '配送要求');
            
            // 显示快递信息
            $show->field('express_company', '快递公司');
            $show->field('tracking_number', '快递单号');
            
            // 显示出库原因和备注
            $show->field('out_reason', '出库原因');
            $show->field('remarks', '备注');
            
            // 显示创建时间和操作人
            $show->field('created_at', '出库时间');
            $show->field('creator.name', '操作人');
            
            // 显示出库明细列表
            $show->divider();
            $show->field('items', '出库明细')->as(function ($items) {
                // 构建明细表格
                $html = '<table class="table table-bordered">';
                $html .= '<thead><tr>';
                $html .= '<th>SKU编码</th>';
                $html .= '<th>属性组合</th>';
                $html .= '<th>仓库</th>';
                $html .= '<th>出库数量</th>';
                $html .= '</tr></thead>';
                $html .= '<tbody>';
                
                foreach ($items as $item) {
                    $html .= '<tr>';
                    
                    // SKU编码
                    $skuCode = $item->skuStock && $item->skuStock->sku ? $item->skuStock->sku->sku : '未知SKU';
                    $html .= "<td>{$skuCode}</td>";
                    
                    // 属性组合
                    $attrText = '';
                    if ($item->skuStock && $item->skuStock->sku) {
                        $sku = $item->skuStock->sku;
                        if (!empty($sku->simplified_attr_values_text)) {
                            $attrText = $sku->simplified_attr_values_text;
                        } elseif (!empty($sku->attr_values_text)) {
                            $attrText = preg_replace('/([^,]+?):/', '', $sku->attr_values_text);
                        } else {
                            $attrText = '无属性';
                        }
                    }
                    $html .= "<td>{$attrText}</td>";
                    
                    // 仓库
                    $warehouseName = $item->warehouse ? $item->warehouse->name : 
                        ($item->skuStock && $item->skuStock->warehouse ? $item->skuStock->warehouse->name : '未知仓库');
                    $html .= "<td>{$warehouseName}</td>";
                    
                    // 出库数量
                    $html .= "<td>{$item->quantity}</td>";
                    
                    $html .= '</tr>';
                }
                
                $html .= '</tbody></table>';
                return $html;
            })->unescape();
            
            // 禁用工具栏
            $show->panel()->tools(function ($tools) {
                $tools->disableEdit();
                $tools->disableDelete();
            });
        });
    }
} 