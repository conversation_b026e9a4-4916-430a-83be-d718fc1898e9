<?php

namespace App\Admin\Controllers\Banhua;

use App\Admin\Repositories\Banhua\TecBanhuaEcommercePlatform;
use App\Admin\Actions\Grid\Banhua\TecBanhuaEcommercePlatformExportAction;
use App\Admin\Actions\Grid\Banhua\TecBanhuaEcommercePlatformImportAction;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Banhua\TecBanhuaEcommercePlatformModel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class TecBanhuaEcommercePlatformController extends AdminController
{
    /**
     * 设置页面标题
     *
     * @return string
     */
    protected $title = '电商平台管理';

    /**
     * 列表页面
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->title($this->title)
            ->description('电商平台列表')
            ->body($this->grid());
    }

    /**
     * 显示详情页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content)
    {
        return $content
            ->title($this->title)
            ->description('电商平台详情')
            ->body($this->detail($id));
    }

    /**
     * 创建页面
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content)
    {
        return $content
            ->title($this->title)
            ->description('创建电商平台')
            ->body($this->form());
    }

    /**
     * 编辑页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function edit($id, Content $content)
    {
        return $content
            ->title($this->title)
            ->description('编辑电商平台')
            ->body($this->form()->edit($id));
    }

    /**
     * 列表页面配置
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecBanhuaEcommercePlatform(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('platform_name', '平台名称');
            $grid->column('platform_code', '平台代码');
            $grid->column('short_name', '平台简称');
            $grid->column('shop_name', '店铺名称');
            $grid->column('shop_url', '店铺网址')->link();
            $grid->column('manager_name', '负责人');
            $grid->column('contact_phone', '联系电话');
            $grid->column('status', '状态')->using([1 => '启用', 0 => '禁用'])->label([
                1 => 'success',
                0 => 'danger',
            ]);
            $grid->column('created_at', '创建时间')->sortable();
            $grid->column('updated_at', '更新时间')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('platform_name', '平台名称')->width(2);
                $filter->like('platform_code', '平台代码')->width(2);
                $filter->like('shop_name', '店铺名称')->width(2);
                $filter->like('manager_name', '负责人')->width(2);
                $filter->equal('status', '状态')->select([1 => '启用', 0 => '禁用'])->width(2);
            });
            
            // 禁用批量删除
            $grid->disableBatchDelete();
            // 启用工具栏
            $grid->showCreateButton();
            $grid->enableDialogCreate();
            
            // 批量操作
            $grid->batchActions(function ($batch) {
                // 添加批量删除按钮
                $batch->add(new \Dcat\Admin\Grid\Tools\BatchDelete('批量删除'));
            });
            
            // 添加导入导出工具
            $grid->tools(function ($tools) {
                // 添加导出按钮
                $tools->append(new TecBanhuaEcommercePlatformExportAction());
                
                // 添加导入按钮
                $tools->append(new TecBanhuaEcommercePlatformImportAction());

            });

            // 分页设置 - 优化大数据量下的性能
            $grid->paginate(50); // 每页显示50条记录
            // 启用每页显示数量的选择器，并自定义可选择的数量
            $grid->perPages([50, 100, 200, 500]);
        });
    }

    /**
     * 详情页面配置
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecBanhuaEcommercePlatform(), function (Show $show) {
            $show->field('id');
            $show->field('platform_name', '平台名称');
            $show->field('platform_code', '平台代码');
            $show->field('short_name', '平台简称');
            $show->field('shop_name', '店铺名称');
            $show->field('shop_url', '店铺网址')->link();
            $show->field('manager_name', '负责人');
            $show->field('contact_phone', '联系电话');
            $show->field('contact_email', '联系邮箱');
            $show->field('api_key', 'API密钥');
            $show->field('app_id', '应用ID');
            $show->field('api_config', 'API配置');
            $show->field('status', '状态')->using([1 => '启用', 0 => '禁用'])->label([
                1 => 'success',
                0 => 'danger',
            ]);
            $show->field('remarks', '备注');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单配置
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecBanhuaEcommercePlatform(), function (Form $form) {
            $form->display('id');
            $form->text('platform_name', '平台名称')->required()->rules('required|max:100');
            $form->text('platform_code', '平台代码')->required()->rules('required|max:50');
            $form->text('short_name', '平台简称')->required()->rules('required|max:50');
            $form->text('shop_name', '店铺名称')->required()->rules('required|max:100');
            $form->url('shop_url', '店铺网址')->required()->rules('required|url|max:255');
            $form->text('manager_name', '负责人')->required()->rules('required|max:50');
            $form->mobile('contact_phone', '联系电话')->options(['mask' => '999 9999 9999'])->rules('nullable|max:20');
            $form->email('contact_email', '联系邮箱')->rules('nullable|email|max:100');
            
            $form->divider('API配置');
            
            $form->text('api_key', 'API密钥')->rules('nullable|max:255');
            $form->password('api_secret', 'API密钥密文')->rules('nullable|max:255');
            $form->text('app_id', '应用ID')->rules('nullable|max:100');
            $form->password('app_secret', '应用密钥')->rules('nullable|max:255');
            $form->textarea('api_config', 'API配置（JSON格式）')->rows(3)->rules('nullable|json');
            
            $form->divider();
            
            $form->radio('status', '状态')
                ->options([1 => '启用', 0 => '禁用'])
                ->default(1)
                ->required();
            
            $form->textarea('remarks', '备注')->rows(3);
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');

            // 保存前回调
            $form->saving(function (Form $form) {
                // 如果密码字段为空，则不更新密码字段
                if ($form->isEditing() && empty($form->api_secret)) {
                    $form->deleteInput('api_secret');
                }
                
                if ($form->isEditing() && empty($form->app_secret)) {
                    $form->deleteInput('app_secret');
                }
            });
        });
    }
    
    /**
     * 清除电商平台列表缓存
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearEcommerceCache()
    {
        try {
            // 这里可以添加清除缓存的代码
            Log::info('手动刷新电商平台列表');
            
            admin_toastr('刷新成功', 'success');
        } catch (\Exception $e) {
            Log::error('刷新操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            admin_toastr('刷新失败: ' . $e->getMessage(), 'error');
        }
        
        return redirect()->back();
    }
    
    /**
     * 导出电商平台数据
     *
     * @param Request $request
     * @return mixed
     */
    public function export(Request $request)
    {
        $action = new TecBanhuaEcommercePlatformExportAction();
        return $action->handle($request);
    }
    
    /**
     * 导入电商平台数据
     *
     * @param Request $request
     * @return mixed
     */
    public function import(Request $request)
    {
        $action = new TecBanhuaEcommercePlatformImportAction();
        return $action->handle($request);
    }
    
    /**
     * 下载电商平台导入模板
     *
     * @return \Illuminate\Http\Response
     */
    public function downloadTemplate()
    {
        try {
            // 创建新的Excel文档
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('电商平台导入模板');
            
            // 添加表头
            $headers = [
                '平台名称*', '平台代码*', '平台简称*', '店铺名称*', '店铺网址*', '负责人*',
                '联系电话', '联系邮箱', 'API密钥', 'API密钥密文', '应用ID', '应用密钥',
                '状态(1-启用, 0-禁用)*', '备注'
            ];
            
            foreach ($headers as $index => $header) {
                $sheet->setCellValueByColumnAndRow($index + 1, 1, $header);
            }
            
            // 设置表头样式
            $headerRange = 'A1:' . chr(64 + count($headers)) . '1';
            $sheet->getStyle($headerRange)->getFont()->setBold(true);
            $sheet->getStyle($headerRange)->getFill()
                ->setFillType(Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
                
            // 添加示例数据
            $data = [
                [
                    '淘宝', 'taobao', 'TB', '版画旗舰店', 
                    'https://banhua.taobao.com', '张三', 
                    '13800138000', '<EMAIL>', 
                    'key123456', 'secret123456', 'app123456', 'appsecret123456',
                    '1', '淘宝官方旗舰店'
                ],
                [
                    '京东', 'jd', 'JD', '版画京东店', 
                    'https://banhua.jd.com', '李四', 
                    '13900139000', '<EMAIL>', 
                    'key654321', 'secret654321', 'app654321', 'appsecret654321',
                    '1', '京东自营店'
                ],
            ];
            
            foreach ($data as $rowIndex => $rowData) {
                foreach ($rowData as $colIndex => $value) {
                    $sheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex + 2, $value);
                }
            }
            
            // 调整列宽
            foreach (range('A', chr(64 + count($headers))) as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
            
            // 确保第一个工作表是活动工作表
            $spreadsheet->setActiveSheetIndex(0);
            
            // 创建Excel写入器
            $writer = new Xlsx($spreadsheet);
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'ecommerce_import_template');
            $writer->save($tempFile);
            
            // 避免使用deleteFileAfterSend方法，因为可能会导致兼容性问题
            $filename = '电商平台导入模板.xlsx';
            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                'Cache-Control' => 'max-age=0',
            ];
            
            // 返回文件响应
            return \response()->file($tempFile, $headers);
            
        } catch (\Exception $e) {
            Log::error('生成电商平台导入模板失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '生成导入模板失败: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }
} 