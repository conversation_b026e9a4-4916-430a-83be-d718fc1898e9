<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Banhua\TecBanhuaSkuStockModel;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\Banhua\TecBanhuaAttrModel;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use App\Models\Banhua\TecBanhuaLocationModel;
use App\Models\TecWarehouseModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Admin;

/**
 * SKU库存管理控制器
 */
class TecBanhuaSkuStockController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = 'SKU库存管理';

    /**
     * 列表页面
     */
    public function index(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('SKU库存列表')
            ->body($this->grid());
    }

    /**
     * 构建列表
     */
    protected function grid(): Grid
    {
        return Grid::make(new TecBanhuaSkuStockModel(), function (Grid $grid) {
            // 设置分页选项（优化版）
            $grid->paginate(50);
            $grid->perPages([50,100, 200, 500, 1000]);
            
            // 使用完整分页模式，显示总页数和页码导航
            // $grid->simplePaginate(); // 注释掉简单分页
            
            // 启用横向滚动条，避免内容过多导致布局混乱
            $grid->scrollbarX();
            
            // 优化关联加载，显式加载sku.gallery以避免N+1查询
            $grid->model()->with(['sku.gallery', 'warehouse', 'location']);
            $grid->column('id')->sortable();
            $grid->column('sku.sku', 'SKU编码')->sortable();
            $grid->column('sku.name', 'SKU名称')->width('200px');
            $grid->column('sku.attr_values_text', '属性组合')->display(function($text) {
                if (empty($text)) {
                    return '';
                }
                
                // 获取关联的SKU
                $sku = $this->sku;
                if (!$sku) {
                    return '';
                }
                
                // 预加载属性和属性值
                static $attrValuesMap = null;
                if ($attrValuesMap === null) {
                    $attrs = TecBanhuaAttrModel::with('values')->get();
                    $attrValuesMap = [];
                    
                    // 构建属性值映射
                    foreach ($attrs as $attr) {
                        foreach ($attr->values as $value) {
                            $attrValuesMap[$value->id] = [
                                'attr_name' => $attr->name,
                                'value_name' => $value->name
                            ];
                        }
                    }
                }
                
                // 构建属性文本 - 只显示属性值，不显示属性名称
                $attrValues = [];
                if (!empty($sku->attr_value_ids)) {
                    foreach ($sku->attr_value_ids as $valueId) {
                        if (isset($attrValuesMap[$valueId])) {
                            $attrValues[] = $attrValuesMap[$valueId]['value_name'];
                        }
                    }
                }
                
                return implode(', ', $attrValues);
            });
            
            // 添加SKU图片预览（优化版）
            $grid->column('sku.image', '图片')->display(function ($value) {
                // 获取关联的SKU
                $sku = $this->sku;
                if (!$sku) {
                    return '';
                }
                
                // 尝试从gallery获取图片
                if (!empty($sku->gallery) && !empty($sku->gallery->path)) {
                    $value = $sku->gallery->path;
                } else {
                    $value = $sku->image;
                }
                
                if (empty($value)) {
                    return '';
                }
                
                $value = trim($value);
                if (!filter_var($value, FILTER_VALIDATE_URL)) {
                    return "<span class='text-muted'>图片URL无效</span>";
                }
                
                // 生成唯一的缓存键
                $cacheKey = md5($value);
                
                // 使用更高效的懒加载方式
                return "<img 
                    src='/vendor/dcat-admin/images/picture.png' 
                    data-src='{$value}' 
                    style='max-width:50px;max-height:50px;cursor:pointer;' 
                    class='img-thumbnail lazyload img-preview' 
                    data-original='{$value}'
                    data-cache-key='{$cacheKey}'
                    title='点击预览' />";
            });
            
            $grid->column('warehouse.name', '仓库')->sortable();
            $grid->column('location.location_name', '库位');
            $grid->column('stock', '库存数量')->sortable();
            // $grid->column('cost_price', '成本价');
            // $grid->column('sale_price', '销售价');
            // $grid->column('batch_no', '批次号');
            // $grid->column('expire_date', '有效期');
            // $grid->column('created_at');
            // $grid->column('updated_at');
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete(false); // 启用删除按钮
            });
            // 筛选器
            $grid->filter(function ($filter) {
                // 仓库筛选
                $filter->equal('warehouse_id', '仓库')->select(function () {
                    return TecWarehouseModel::pluck('name', 'id');
                })->width(2);
                
                // 库位筛选
                $filter->equal('location_id', '库位')->select(function ($id) {
                    // 如果选择了仓库，则只显示该仓库的库位
                    $warehouseId = request()->input('warehouse_id');
                    if ($warehouseId) {
                        return TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
                            ->get()
                            ->mapWithKeys(function ($location) {
                                $text = $location->area_number . '区' . $location->shelf_number . '架' . $location->level_number . '层';
                                return [$location->id => $text];
                            })
                            ->toArray();
                    } else {
                        // 如果没有选择仓库，则显示所有库位
                        return TecBanhuaLocationModel::with('warehouse')
                            ->get()
                            ->mapWithKeys(function ($location) {
                                $warehouseName = $location->warehouse ? $location->warehouse->name : '未知仓库';
                                $text = $warehouseName . ' - ' . $location->area_number . '区' . $location->shelf_number . '架' . $location->level_number . '层';
                                return [$location->id => $text];
                            })
                            ->toArray();
                    }
                })->width(2);
                
                // SKU筛选
                $filter->where('sku_filter', function ($query) {
                    $query->whereHas('sku', function ($skuQuery) {
                        $skuQuery->where('sku', 'like', "%{$this->input}%");
                    });
                }, '按SKU筛选')->width(2);
                
                // 库存筛选
                $filter->where('stock_filter', function ($query) {
                    if ($this->input == 'zero') {
                        $query->where('stock', 0);
                    } elseif ($this->input == 'positive') {
                        $query->where('stock', '>', 0);
                    }
                }, '库存数量')->select([
                    'zero' => '零库存',
                    'positive' => '有库存',
                ])->width(2);
            });
            
            // 添加优化版图片懒加载和预览JavaScript
            Admin::script('
            $(function() {
                // 图片URL缓存
                var imageCache = {};
                
                // 更高效的图片懒加载初始化
                var lazyLoadOptions = {
                    elements_selector: ".lazyload",
                    threshold: 200, // 增加阈值，提前加载
                    callback_loaded: function(element) {
                        // 图片加载成功后缓存
                        var cacheKey = $(element).data("cache-key");
                        var src = $(element).attr("src");
                        if (cacheKey && src) {
                            imageCache[cacheKey] = src;
                        }
                    },
                    callback_error: function(element) {
                        $(element).attr("src", "/vendor/dcat-admin/images/picture.png");
                    },
                    callback_enter: function(element) {
                        // 检查图片是否已缓存
                        var cacheKey = $(element).data("cache-key");
                        if (cacheKey && imageCache[cacheKey]) {
                            $(element).attr("src", imageCache[cacheKey]);
                            $(element).addClass("loaded");
                        }
                    }
                };
                
                // 初始化懒加载
                var lazyLoadInstance = new LazyLoad(lazyLoadOptions);
                
                // 监听表格刷新事件，重新初始化懒加载
                $(document).on("pjax:complete", function() {
                    if (lazyLoadInstance) {
                        lazyLoadInstance.destroy();
                        lazyLoadInstance = new LazyLoad(lazyLoadOptions);
                    }
                });
                
                // 优化的图片预览功能
                $(document).off("click", ".img-preview").on("click", ".img-preview", function() {
                    var src = $(this).data("original");
                    if (!src) return;
                    
                    // 创建预览层，使用缓存的图片
                    var cacheKey = $(this).data("cache-key");
                    var imgSrc = (cacheKey && imageCache[cacheKey]) ? imageCache[cacheKey] : src;
                    
                    var imgHtml = "<div style=\'text-align:center;\'><img src=\'" + imgSrc + "\' style=\'max-width:100%;max-height:400px;\' /></div>";
                    layer.open({
                        type: 1,
                        title: "图片预览",
                        shadeClose: true,
                        shade: 0.3,
                        area: ["40%", "60%"],
                        content: imgHtml
                    });
                });
            });
            ');
            
            // 添加批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                // 添加批量删除按钮
                $batch->add(new \App\Admin\Actions\Grid\Banhua\TecBanhuaSkuStockBatchDeleteAction());
            });
            
            // 添加导入、导出和刷新缓存按钮
            $grid->tools(function ($tools) {
                // 添加导出按钮
                $tools->append(new \App\Admin\Actions\Grid\Banhua\TecBanhuaSkuStockExportAction());
                // 添加导入按钮
                $tools->append(new \App\Admin\Actions\Grid\Banhua\TecBanhuaSkuStockImportAction());
                // 添加缓存清除按钮
                $tools->append('<a class="btn btn-success btn-mini btn-outline" href="' . admin_url('banhua/clear-sku-stock-cache') . '" style="margin-right:3px">
                    <i class="fa fa-motorcycle"></i>
                </a>');
            });
        });
    }

    /**
     * 创建页面
     */
    public function create(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('新增SKU库存')
            ->body($this->form());
    }

    /**
     * 编辑页面
     */
    public function edit($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('编辑SKU库存')
            ->body($this->form()->edit($id));
    }

    /**
     * 构建表单
     */
    protected function form(): Form
    {
        return Form::make(new TecBanhuaSkuStockModel(), function (Form $form) {
            // $form->display('id');
            
            // 仓库选择
            $form->select('warehouse_id', '仓库')
                ->options(TecWarehouseModel::pluck('name', 'id'))
                ->required();
            
            // 库位选择，动态加载
            $form->select('location_id', '库位')
                ->options(function ($id) {
                    // 如果是编辑模式，预加载当前仓库的库位
                    if ($id && $warehouseId = $this->warehouse_id) {
                        return TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
                            ->get()
                            ->mapWithKeys(function ($location) {
                                return [$location->id => $location->location_name];
                            });
                    }
                    return [];
                })
                ->required();
            
            // SKU选择
            $form->select('sku_id', 'SKU')
                ->options(function () {
                    // 预加载属性和属性值
                    $attrs = TecBanhuaAttrModel::with('values')->get();
                    $attrValuesMap = [];
                    
                    // 构建属性值映射
                    foreach ($attrs as $attr) {
                        foreach ($attr->values as $value) {
                            $attrValuesMap[$value->id] = [
                                'attr_name' => $attr->name,
                                'value_name' => $value->name
                            ];
                        }
                    }
                    
                    // 获取SKU列表并格式化显示
                    return TecBanhuaProductSkuModel::select(['id', 'sku', 'name', 'attr_value_ids'])
                        ->get()
                        ->map(function ($sku) use ($attrValuesMap) {
                            $attrText = '';
                            
                            // 构建属性文本
                            if (!empty($sku->attr_value_ids)) {
                                $attrValues = [];
                                foreach ($sku->attr_value_ids as $valueId) {
                                    if (isset($attrValuesMap[$valueId])) {
                                        $attrValues[] = $attrValuesMap[$valueId]['value_name'];
                                    }
                                }
                                $attrText = ' (' . implode(', ', $attrValues) . ')';
                            }
                            
                            return [
                                'id' => $sku->id,
                                'text' => $sku->sku . ' - ' . $sku->name . $attrText
                            ];
                        })
                        ->pluck('text', 'id')
                        ->toArray();
                })
                ->required();
                
            // 添加SKU图片预览
            $form->html(function ($form) {
                // 如果是编辑模式
                if ($form->model()->sku_id) {
                    $sku = TecBanhuaProductSkuModel::with('gallery')->find($form->model()->sku_id);
                    if ($sku) {
                        // 获取图片URL
                        $imageUrl = '';
                        if (!empty($sku->gallery) && !empty($sku->gallery->path)) {
                            $imageUrl = $sku->gallery->path;
                        } elseif (!empty($sku->image)) {
                            $imageUrl = $sku->image;
                        }
                        
                        if (!empty($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                            return <<<HTML
                            <div class="form-group">
                                <label class="col-sm-2 control-label">SKU图片</label>
                                <div class="col-sm-8">
                                    <img src="{$imageUrl}" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;margin-top:10px;" class="img-preview" data-original="{$imageUrl}">
                                </div>
                            </div>
                            HTML;
                        }
                    }
                }
                
                return '';
            });
            
            // 添加SKU图片预览的JS
            Admin::script('
            $(function() {
                // SKU选择变化时更新图片预览
                $("select[name=\'sku_id\']").on("change", function() {
                    var skuId = $(this).val();
                    if (!skuId) return;
                    
                    // 加载SKU图片
                    $.get("/api/banhua/sku-image", {sku_id: skuId}, function(data) {
                        if (data && data.image_url) {
                            // 如果已有预览区域，更新图片
                            if ($(".sku-image-preview").length) {
                                $(".sku-image-preview img").attr("src", data.image_url).attr("data-original", data.image_url);
                            } else {
                                // 否则创建预览区域
                                var html = `
                                <div class="form-group sku-image-preview">
                                    <label class="col-sm-2 control-label">SKU图片</label>
                                    <div class="col-sm-8">
                                        <img src="${data.image_url}" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;margin-top:10px;" class="img-preview" data-original="${data.image_url}">
                                    </div>
                                </div>`;
                                
                                // 插入到SKU选择框后面
                                $(html).insertAfter($("select[name=\'sku_id\']").parents(".form-group"));
                            }
                            
                            // 绑定点击预览事件
                            $(".img-preview").off("click").on("click", function() {
                                var src = $(this).attr("data-original");
                                if (!src) return;
                                
                                // 创建预览层
                                var imgHtml = "<div style=\'text-align:center;\'><img src=\'" + src + "\' style=\'max-width:100%;max-height:400px;\' /></div>";
                                layer.open({
                                    type: 1,
                                    title: "图片预览",
                                    shadeClose: true,
                                    shade: 0.3,
                                    area: ["40%", "60%"],
                                    content: imgHtml
                                });
                            });
                        }
                    });
                });
                
                // 初始加载时如果已选择SKU，触发change事件
                var initialSkuId = $("select[name=\'sku_id\']").val();
                if (initialSkuId) {
                    $("select[name=\'sku_id\']").trigger("change");
                }
                
                // 绑定图片预览事件
                $(document).on("click", ".img-preview", function() {
                    var src = $(this).attr("data-original");
                    if (!src) return;
                    
                    // 创建预览层
                    var imgHtml = "<div style=\'text-align:center;\'><img src=\'" + src + "\' style=\'max-width:100%;max-height:400px;\' /></div>";
                    layer.open({
                        type: 1,
                        title: "图片预览",
                        shadeClose: true,
                        shade: 0.3,
                        area: ["40%", "60%"],
                        content: imgHtml
                    });
                });
            });
            ');
            
            // 库存数量
            $form->number('stock', '库存数量')->min(0)->required();
            
            // // 价格信息
            // $form->currency('cost_price', '成本价')->symbol('￥')->default(0);
            // $form->currency('sale_price', '销售价')->symbol('￥')->default(0);
            
            // // 批次信息
            // $form->text('batch_no', '批次号');
            // $form->date('expire_date', '有效期');
            
            // $form->display('created_at');
            // $form->display('updated_at');
            
            // 添加JS，处理仓库和库位级联
            $form->submitted(function (Form $form) {
                // 验证同一个SKU在同一个仓库同一个库位是否已存在记录
                $exists = TecBanhuaSkuStockModel::where('sku_id', $form->sku_id)
                    ->where('warehouse_id', $form->warehouse_id)
                    ->where('location_id', $form->location_id)
                    ->where('id', '<>', $form->model()->id)
                    ->exists();
                
                if ($exists) {
                    return $form->response()->error('该SKU在该仓库库位下已存在库存记录，请编辑已有记录');
                }
            });
            
            // 保存前处理
            $form->saving(function (Form $form) {
                // 确保价格字段不为null
                $form->cost_price = $form->cost_price ?? 0;
                $form->sale_price = $form->sale_price ?? 0;
            });

            // 添加JS，处理仓库和库位级联
            Admin::script('
            $(function() {
                // 仓库选择变化时更新库位下拉框
                $("select[name=\'warehouse_id\']").on("change", function() {
                    var warehouseId = $(this).val();
                    if (!warehouseId) {
                        // 如果没有选择仓库，清空库位下拉框
                        $("select[name=\'location_id\']").empty().append("<option value=\'\'>请选择库位</option>");
                        return;
                    }

                    // 加载库位数据
                    $.ajax({
                        url: "/admin/api/banhua/warehouse-locations",
                        type: "GET",
                        data: {q: warehouseId},
                        dataType: "json",
                        success: function(data) {
                            var select = $("select[name=\'location_id\']");
                            select.empty().append("<option value=\'\'>请选择库位</option>");
                            
                            // 添加库位选项
                            $.each(data, function(id, name) {
                                select.append("<option value=\'" + id + "\'>" + name + "</option>");
                            });
                        },
                        error: function() {
                            console.error("加载库位数据失败");
                        }
                    });
                });

                // 页面加载时如果已选择仓库，触发change事件加载库位
                var initialWarehouseId = $("select[name=\'warehouse_id\']").val();
                if (initialWarehouseId) {
                    $("select[name=\'warehouse_id\']").trigger("change");
                }
            });
            ');
        });
    }
    
    /**
     * 获取仓库下的库位列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWarehouseLocations(Request $request)
    {
        $warehouseId = $request->get('q');
        
        return TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
            ->get()
            ->mapWithKeys(function ($location) {
                return [$location->id => $location->location_name];
            })
            ->toArray();
    }

    /**
     * 获取SKU图片
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSkuImage(Request $request)
    {
        $skuId = $request->get('sku_id');
        if (!$skuId) {
            return response()->json(['status' => 'error', 'message' => 'SKU ID不能为空']);
        }
        
        $sku = TecBanhuaProductSkuModel::with('gallery')->find($skuId);
        if (!$sku) {
            return response()->json(['status' => 'error', 'message' => '未找到SKU']);
        }
        
        $imageUrl = '';
        if (!empty($sku->gallery) && !empty($sku->gallery->path)) {
            $imageUrl = $sku->gallery->path;
        } elseif (!empty($sku->image)) {
            $imageUrl = $sku->image;
        }
        
        if (empty($imageUrl)) {
            return response()->json(['status' => 'error', 'message' => '未找到图片']);
        }
        
        return response()->json([
            'status' => 'success',
            'image_url' => $imageUrl,
            'sku' => $sku->sku,
            'name' => $sku->name
        ]);
    }

    /**
     * 详情页面
     */
    public function show($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('SKU库存详情')
            ->body($this->detail($id));
    }

    /**
     * 构建详情
     */
    protected function detail($id): Show
    {
        return Show::make($id, new TecBanhuaSkuStockModel(), function (Show $show) {
            $show->field('id');
            $show->field('sku.sku', 'SKU编码');
            $show->field('sku.name', 'SKU名称');
            $show->field('sku.attr_values_text', '属性组合')->as(function ($text) {
                // 获取关联的SKU
                $sku = $this->sku;
                if (!$sku) {
                    return '';
                }
                
                // 预加载属性和属性值（优化版 - 使用请求级缓存）
                static $attrValuesMap = null;
                if ($attrValuesMap === null) {
                    // 使用缓存减少数据库查询
                    $cacheKey = 'banhua_attr_values_map';
                    $attrValuesMap = \Illuminate\Support\Facades\Cache::remember($cacheKey, 3600, function() {
                    $attrs = TecBanhuaAttrModel::with('values')->get();
                        $map = [];
                    
                    // 构建属性值映射
                    foreach ($attrs as $attr) {
                        foreach ($attr->values as $value) {
                                $map[$value->id] = [
                                'attr_name' => $attr->name,
                                'value_name' => $value->name
                            ];
                        }
                    }
                        
                        return $map;
                    });
                }
                
                // 构建属性文本 - 只显示属性值，不显示属性名称（优化版）
                $attrValues = [];
                if (!empty($sku->attr_value_ids)) {
                    $valueIds = is_array($sku->attr_value_ids) ? $sku->attr_value_ids : json_decode($sku->attr_value_ids, true);
                    if (is_array($valueIds)) {
                        foreach ($valueIds as $valueId) {
                        if (isset($attrValuesMap[$valueId])) {
                            $attrValues[] = $attrValuesMap[$valueId]['value_name'];
                            }
                        }
                    }
                }
                
                return implode(', ', $attrValues);
            });
            $show->field('warehouse.name', '仓库');
            $show->field('location.location_name', '库位');
            $show->field('stock', '库存数量');
            $show->field('cost_price', '成本价');
            $show->field('sale_price', '销售价');
            $show->field('batch_no', '批次号');
            $show->field('expire_date', '有效期');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * 清除SKU库存缓存
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearStockCache()
    {
        try {
            // 清除可能存在的缓存
            \Illuminate\Support\Facades\Cache::forget('banhua_sku_stocks');
            \Illuminate\Support\Facades\DB::statement('ANALYZE TABLE t_banhua_sku_stocks');
            
            \Illuminate\Support\Facades\Log::info('手动刷新SKU库存列表');
            
            admin_toastr('刷新成功', 'success');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('刷新操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            admin_toastr('刷新失败: ' . $e->getMessage(), 'error');
        }
        
        return redirect()->back();
    }
    
    /**
     * 导出SKU库存数据
     *
     * @param Request $request
     * @return mixed
     */
    public function exportStock(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaSkuStockExportAction();
        return $action->handle($request);
    }
    
    /**
     * 启动导出任务
     *
     * @param Request $request
     * @return mixed
     */
    public function startExport(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaSkuStockExportAction();
        return $action->start($request);
    }
    
    /**
     * 获取导出进度
     *
     * @param Request $request
     * @return mixed
     */
    public function exportProgress(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaSkuStockExportAction();
        return $action->progress($request);
    }
    
    /**
     * 下载导出文件
     *
     * @param Request $request
     * @return mixed
     */
    public function downloadExport(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaSkuStockExportAction();
        return $action->download($request);
    }
    
    /**
     * 导入SKU库存数据
     *
     * @param Request $request
     * @return mixed
     */
    public function importStock(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaSkuStockImportAction();
        return $action->handle($request);
    }
    
    /**
     * 下载SKU库存导入模板
     *
     * @return \Illuminate\Http\Response
     */
    public function downloadImportTemplate()
    {
        try {
            // 禁用Pjax
            if (request()->pjax()) {
                return response()->json([
                    'status' => false,
                    'message' => '请刷新页面后重试'
                ]);
            }
            
            // 创建新的Excel文档
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            
            // ===== 第一个工作表：导入模板 =====
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('导入模板');
            
            // 添加说明文字
            $sheet->setCellValue('A1', '阪画SKU库存导入模板');
            $sheet->mergeCells('A1:G1');
            $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            $sheet->setCellValue('A2', '填写说明：');
            $sheet->mergeCells('A2:G2');
            $sheet->getStyle('A2')->getFont()->setBold(true);
            
            $sheet->setCellValue('A3', '1. SKU编码、仓库、库位、库存数量为必填项');
            $sheet->mergeCells('A3:G3');
            
            $sheet->setCellValue('A4', '2. 当SKU编码对应多个SKU时，属性组合也为必填项，用于确定唯一的SKU');
            $sheet->mergeCells('A4:G4');
            
            $sheet->setCellValue('A5', '3. 仓库必须填写系统中已存在的仓库名称，请参考"仓库列表"工作表');
            $sheet->mergeCells('A5:G5');
            
            $sheet->setCellValue('A6', '4. 库位必须填写系统中已存在的库位名称，请参考"库位列表"工作表');
            $sheet->mergeCells('A6:G6');
            
            $sheet->setCellValue('A7', '5. 同一个SKU在同一个仓库同一个库位只能有一条记录');
            $sheet->mergeCells('A7:G7');
            
            // 设置表头
            $headers = [
                'SKU编码', '属性组合', '仓库', '库位', '库存数量', '成本价', '销售价', '批次号', '有效期'
            ];
            
            $row = 8;
            foreach ($headers as $col => $header) {
                $sheet->setCellValueByColumnAndRow($col + 1, $row, $header);
                $sheet->getStyleByColumnAndRow($col + 1, $row)->getFont()->setBold(true);
                $sheet->getStyleByColumnAndRow($col + 1, $row)->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('CCCCCC');
            }
            
            // 获取仓库列表
            $warehouses = \App\Models\TecWarehouseModel::pluck('name', 'id')->toArray();
            
            // 预加载所有属性和属性值，构建映射
            $attrValuesMap = [];
            $attrs = \App\Models\Banhua\TecBanhuaAttrModel::with('values')->get();
            foreach ($attrs as $attr) {
                foreach ($attr->values as $value) {
                    $attrValuesMap[$value->id] = $value->name;
                }
            }
            
            // 获取示例SKU
            $skus = \App\Models\Banhua\TecBanhuaProductSkuModel::with(['gallery'])->take(3)->get();
            
            // 示例数据
            $exampleData = [];
            $i = 0;
            
            foreach ($warehouses as $warehouseId => $warehouseName) {
                // 获取该仓库的库位
                $locations = \App\Models\Banhua\TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
                    ->take(2)
                    ->get(['id', 'area_number', 'shelf_number', 'level_number'])
                    ->toArray();
                
                if (empty($locations)) {
                    continue;
                }
                
                foreach ($locations as $location) {
                    if (isset($skus[$i])) {
                        $sku = $skus[$i];
                        $locationName = $location['area_number'] . '区' . $location['shelf_number'] . '架' . $location['level_number'] . '层';
                        
                        // 获取属性组合文本
                        $attrValuesText = '';
                        if (!empty($sku->attr_value_ids)) {
                            $attrValues = [];
                            foreach ($sku->attr_value_ids as $valueId) {
                                if (isset($attrValuesMap[$valueId])) {
                                    $attrValues[] = $attrValuesMap[$valueId];
                                }
                            }
                            $attrValuesText = implode(', ', $attrValues);
                        }
                        
                        // 获取图片URL
                        $imageUrl = '';
                        if (!empty($sku->gallery) && !empty($sku->gallery->path)) {
                            $imageUrl = $sku->gallery->path;
                        } elseif (!empty($sku->image)) {
                            $imageUrl = $sku->image;
                        }
                        
                        $exampleData[] = [
                            $sku->sku,
                            $attrValuesText,
                            $warehouseName,
                            $locationName,
                            100 + $i * 10,
                            50 + $i * 5,
                            80 + $i * 8,
                            'BATCH-' . (1000 + $i),
                            date('Y-m-d', strtotime("+{$i} months"))
                        ];
                        $i++;
                    }
                }
            }
            
            // 如果没有足够的示例数据，添加一些默认示例
            while (count($exampleData) < 3) {
                $exampleData[] = [
                    'BH-EXAMPLE-00' . (count($exampleData) + 1),
                    'PS, 油画布',
                    '默认仓库',
                    '1区1架1层',
                    100 + count($exampleData) * 10,
                    50 + count($exampleData) * 5,
                    80 + count($exampleData) * 8,
                    'BATCH-' . (1000 + count($exampleData)),
                    date('Y-m-d', strtotime("+" . count($exampleData) . " months"))
                ];
            }
            
            // 添加示例数据
            $row = 9;
            foreach ($exampleData as $data) {
                foreach ($data as $col => $value) {
                    $sheet->setCellValueByColumnAndRow($col + 1, $row, $value);
                }
                $row++;
            }
            
            // ===== 第二个工作表：仓库列表 =====
            $warehouseSheet = $spreadsheet->createSheet();
            $warehouseSheet->setTitle('仓库列表');
            
            // 设置仓库表头
            $warehouseSheet->setCellValue('A1', 'ID');
            $warehouseSheet->setCellValue('B1', '仓库名称');
            
            $warehouseSheet->getStyle('A1:B1')->getFont()->setBold(true);
            $warehouseSheet->getStyle('A1:B1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            
            // 填充仓库数据
            $row = 2;
            foreach ($warehouses as $id => $name) {
                $warehouseSheet->setCellValue('A' . $row, $id);
                $warehouseSheet->setCellValue('B' . $row, $name);
                $row++;
            }
            
            // ===== 第三个工作表：库位列表 =====
            $locationSheet = $spreadsheet->createSheet();
            $locationSheet->setTitle('库位列表');
            
            // 设置库位表头
            $locationSheet->setCellValue('A1', 'ID');
            $locationSheet->setCellValue('B1', '仓库ID');
            $locationSheet->setCellValue('C1', '仓库名称');
            $locationSheet->setCellValue('D1', '库位名称');
            
            $locationSheet->getStyle('A1:D1')->getFont()->setBold(true);
            $locationSheet->getStyle('A1:D1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            
            // 填充库位数据
            $row = 2;
            foreach ($warehouses as $warehouseId => $warehouseName) {
                $locations = \App\Models\Banhua\TecBanhuaLocationModel::where('warehouse_id', $warehouseId)
                    ->get(['id', 'area_number', 'shelf_number', 'level_number'])
                    ->toArray();
                
                foreach ($locations as $location) {
                    $locationName = $location['area_number'] . '区' . $location['shelf_number'] . '架' . $location['level_number'] . '层';
                    $locationSheet->setCellValue('A' . $row, $location['id']);
                    $locationSheet->setCellValue('B' . $row, $warehouseId);
                    $locationSheet->setCellValue('C' . $row, $warehouseName);
                    $locationSheet->setCellValue('D' . $row, $locationName);
                    $row++;
                }
            }
            
            // 创建第四个工作表：SKU列表
            $skuSheet = $spreadsheet->createSheet();
            $skuSheet->setTitle('SKU列表');
            
            // 设置SKU表头
            $skuSheet->setCellValue('A1', 'ID');
            $skuSheet->setCellValue('B1', 'SKU编码');
            $skuSheet->setCellValue('C1', 'SKU名称');
            $skuSheet->setCellValue('D1', '属性组合');
            $skuSheet->setCellValue('E1', '图片URL');
            
            $skuSheet->getStyle('A1:E1')->getFont()->setBold(true);
            $skuSheet->getStyle('A1:E1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            
            // 填充SKU数据
            $row = 2;
            $allSkus = \App\Models\Banhua\TecBanhuaProductSkuModel::with(['gallery'])->select(['id', 'sku', 'name', 'attr_value_ids', 'image'])->get();
            
            foreach ($allSkus as $sku) {
                // 获取属性组合文本
                $attrValuesText = '';
                if (!empty($sku->attr_value_ids)) {
                    $attrValues = [];
                    foreach ($sku->attr_value_ids as $valueId) {
                        if (isset($attrValuesMap[$valueId])) {
                            $attrValues[] = $attrValuesMap[$valueId];
                        }
                    }
                    $attrValuesText = implode(', ', $attrValues);
                }
                
                // 获取图片URL
                $imageUrl = '';
                if (!empty($sku->gallery) && !empty($sku->gallery->path)) {
                    $imageUrl = $sku->gallery->path;
                } elseif (!empty($sku->image)) {
                    $imageUrl = $sku->image;
                }
                
                $skuSheet->setCellValue('A' . $row, $sku->id);
                $skuSheet->setCellValue('B' . $row, $sku->sku);
                $skuSheet->setCellValue('C' . $row, $sku->name);
                $skuSheet->setCellValue('D' . $row, $attrValuesText);
                $skuSheet->setCellValue('E' . $row, $imageUrl);
                $row++;
            }
            
            // 自动调整列宽
            foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
                foreach (range('A', 'K') as $column) {
                    $worksheet->getColumnDimension($column)->setAutoSize(true);
                }
            }
            
            // 确保第一个工作表是活动工作表
            $spreadsheet->setActiveSheetIndex(0);
            
            // 创建Writer保存文件
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            $filename = 'banhua_sku_stock_import_template_' . date('YmdHis') . '.xlsx';
            $tempFile = storage_path('app/public/' . $filename);
            $writer->save($tempFile);
            
            // 返回文件下载响应
            return response()->download($tempFile, $filename, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ])->deleteFileAfterSend(true);
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('下载SKU库存导入模板失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '下载模板失败: ' . $e->getMessage()
            ]);
        }
    }
} 