<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Banhua\TecBanhuaConsumableOutLogModel;
use App\Models\Banhua\TecBanhuaConsumableOutLogItemModel;
use App\Models\TecWarehouseModel;
use Dcat\Admin\Models\Administrator;

/**
 * 消耗品出库日志控制器
 */
class TecBanhuaConsumableOutLogController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '消耗品出库日志';

    /**
     * 列表页面
     */
    public function index(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('消耗品出库历史记录')
            ->body($this->grid());
    }

    /**
     * 构建列表
     */
    protected function grid(): Grid
    {
        return Grid::make(new TecBanhuaConsumableOutLogModel(), function (Grid $grid) {
            // 设置分页选项
            $grid->paginate(20);
            $grid->perPages([20, 50, 100]);
            
            // 启用横向滚动条
            $grid->scrollbarX();
            
            // 默认只显示最近一周的记录，并按创建时间倒序排列
            $grid->model()->where('created_at', '>=', date('Y-m-d 00:00:00', strtotime('-7 days')))
                ->orderBy('created_at', 'desc');
            
            // 预加载关联数据
            $grid->model()->with(['combination', 'consumableStock', 'consumableStock.sku', 'consumableStock.sku.gallery', 'consumableStock.warehouse', 'creator']);
            
            $grid->column('id')->sortable();
            $grid->column('out_code', '出库单号')->sortable();
            // 显示日期时间（年月日时分秒）
            $grid->column('created_at', '出库时间')->display(function($value) {
                // 显示完整的日期时间格式
                return $value ? $value->format('Y-m-d H:i:s') : '';
            })->sortable();
            
            // 显示出库类型（组合出库或单品出库）
            $grid->column('out_type', '出库类型')->display(function () {
                if ($this->combination_id) {
                    return '<span class="badge badge-primary">组合出库</span>';
                } elseif ($this->consumable_stock_id) {
                    return '<span class="badge badge-info">单品出库</span>';
                } else {
                    return '<span class="badge badge-secondary">未知类型</span>';
                }
            });
            
            // 显示组合名称或消耗品名称
            $grid->column('item_name', '出库项目')->display(function () {
                if ($this->combination_id && $this->combination) {
                    return $this->combination->name . ' (' . $this->combination->combination_code . ')';
                } elseif ($this->consumable_stock_id && $this->consumableStock && $this->consumableStock->sku) {
                    // 优先使用 gallery.remark 作为显示名称
                    $displayName = '';
                    if ($this->consumableStock->sku->gallery && !empty($this->consumableStock->sku->gallery->remark)) {
                        $displayName = $this->consumableStock->sku->gallery->remark;
                    } else if (!empty($this->consumableStock->sku->name)) {
                        $displayName = $this->consumableStock->sku->name;
                    }
                    
                    // 返回"商品名称(SKU编码)"的格式，与消耗品出库页面保持一致
                    return $displayName . '(' . $this->consumableStock->sku->sku . ')';
                } else {
                    return '未知项目';
                }
            });
            
            // 显示属性组合
            $grid->column('attr_text', '属性组合')->display(function () {
                if ($this->consumable_stock_id && $this->consumableStock) {
                    return $this->consumableStock->attr_text ?: '无属性';
                }
                return ''; // 组合出库不显示属性组合
            });
            
            // 显示仓库
            $grid->column('warehouse', '仓库')->display(function () {
                if ($this->consumable_stock_id && $this->consumableStock && $this->consumableStock->warehouse) {
                    return $this->consumableStock->warehouse->name;
                }
                return ''; // 组合出库不显示单一仓库
            });
            
            $grid->column('quantity', '出库数量');
            $grid->column('out_reason', '出库原因');
            // 显示操作人
            $grid->column('created_by', '操作人')->display(function($value) {
                if ($this->creator) {
                    return $this->creator->name;
                } elseif ($value) {
                    // 尝试直接从数据库查询
                    $admin = \Dcat\Admin\Models\Administrator::find($value);
                    return $admin ? $admin->name : '未知用户('.$value.')';
                }
                return '系统';
            });
            
            // 完全禁用操作列
            $grid->disableActions();
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                
                $filter->equal('out_code', '出库单号')->width(2);
                
                $filter->where('out_type', function ($query) {
                    if ($this->input === 'combination') {
                        $query->whereNotNull('combination_id');
                    } elseif ($this->input === 'single') {
                        $query->whereNotNull('consumable_stock_id');
                    }
                }, '出库类型')->select([
                    'combination' => '组合出库',
                    'single' => '单品出库',
                ])->width(2);
                
                $filter->equal('out_reason', '出库原因')->width(2);
                
                // 按属性组合筛选（只对单品出库有效）
                $filter->where('attr_text', function ($query) {
                    $query->whereHas('consumableStock', function ($q) {
                        $q->where('attr_text', 'like', "%{$this->input}%");
                    });
                }, '属性组合')->width(2);
                
                // 按仓库筛选（只对单品出库有效）
                $filter->where('warehouse_id', function ($query) {
                    if ($this->input) {
                        $query->whereHas('consumableStock', function ($q) {
                            $q->where('warehouse_id', $this->input);
                        });
                    }
                }, '仓库')->select(function() {
                    // 获取所有仓库列表
                    return \App\Models\TecWarehouseModel::pluck('name', 'id')->toArray();
                })->width(2);
                
                // 默认显示最近一周的记录
                $filter->between('created_at', '出库时间')
                    ->date() // 使用date()而不是datetime()，只显示日期
                    ->width(2)
                    ->default([
                        'start' => date('Y-m-d', strtotime('-7 days')),
                        'end' => date('Y-m-d')
                    ]);
            });
            
            // 禁用创建按钮
            $grid->disableCreateButton();
            // 禁用批量操作
            $grid->disableBatchActions();
        });
    }

    /**
     * 详情页面
     */
    public function show($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('出库详情')
            ->body($this->detail($id));
    }

    /**
     * 构建详情页
     */
    protected function detail($id): Show
    {
        return Show::make($id, new TecBanhuaConsumableOutLogModel(), function (Show $show) {
            $show->field('id');
            $show->field('out_code', '出库单号');
            
            // 显示出库类型
            $show->field('out_type', '出库类型')->as(function () {
                if ($this->combination_id) {
                    return '组合出库';
                } elseif ($this->consumable_stock_id) {
                    return '单品出库';
                } else {
                    return '未知类型';
                }
            });
            
            // 显示组合信息（如果是组合出库）
            $show->field('combination.name', '组合名称')->when(function ($model) {
                return $model->combination_id !== null;
            });
            
            $show->field('combination.combination_code', '组合编码')->when(function ($model) {
                return $model->combination_id !== null;
            });
            
            // 显示单品信息（如果是单品出库）
            $show->field('consumableStock.sku.name', '消耗品名称')->when(function ($model) {
                return $model->consumable_stock_id !== null;
            });
            
            $show->field('consumableStock.sku.sku', '消耗品编码')->when(function ($model) {
                return $model->consumable_stock_id !== null;
            });
            
            $show->field('quantity', '出库数量');
            $show->field('out_reason', '出库原因');
            $show->field('remarks', '备注');
            $show->field('creator.name', '操作人');
            $show->field('created_at', '出库时间');
            
            // 显示出库明细（如果是组合出库）
            $show->relation('items', '出库明细', function ($model) {
                $grid = new Grid(new TecBanhuaConsumableOutLogItemModel());
                
                $grid->model()->where('out_log_id', $model->id);
                $grid->model()->with(['consumableStock.sku', 'consumableStock.warehouse']);
                
                $grid->column('id')->sortable();
                $grid->column('consumableStock.sku.name', 'SKU名称');
                $grid->column('consumableStock.sku.sku', 'SKU编码');
                $grid->column('consumableStock.attr_text', '属性组合');
                $grid->column('consumableStock.warehouse.name', '仓库');
                $grid->column('quantity', '出库数量');
                $grid->column('unit_cost', '单位成本');
                $grid->column('unit_price', '单位售价');
                $grid->column('subtotal_cost', '小计成本')->display(function () {
                    return $this->unit_cost * $this->quantity;
                });
                $grid->column('subtotal_price', '小计售价')->display(function () {
                    return $this->unit_price * $this->quantity;
                });
                
                $grid->disableCreateButton();
                $grid->disableActions();
                $grid->disableBatchDelete();
                $grid->disableRowSelector();
                
                return $grid;
            })->when(function ($model) {
                return $model->combination_id !== null;
            });
        });
    }
} 