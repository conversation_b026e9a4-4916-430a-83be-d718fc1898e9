<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Banhua;

use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Repositories\Banhua\TecBanhuaProductSkuRepo;
use App\Models\Banhua\TecBanhuaProductSkuModel;
use App\Models\Banhua\TecBanhuaProductCategoryModel;
use App\Models\Banhua\TecBanhuaProductUnitModel;
use App\Models\Banhua\TecBanhuaAttrModel;
use App\Models\Banhua\TecBanhuaAttrValueModel;
use App\Models\Banhua\TecBanhuaImageGalleryModel;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * 阪画产品直接管理控制器（基于SKU）
 */
class TecBanhuaProductSkuController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '阪画产品SKU管理';
    
    /**
     * 覆盖父类的store方法，确保sku字段在保存前被正确设置
     *
     * @return \Illuminate\Http\Response
     */
    public function store()
    {
        \Illuminate\Support\Facades\Log::info('产品保存请求', [
            'all_inputs' => request()->all()
        ]);
        
        // 获取SKU组合数据
        $rawData = request()->input('_sku_combinations_data');
        \Illuminate\Support\Facades\Log::info('原始SKU组合数据', [
            'raw_data' => $rawData
        ]);
        
        if (!empty($rawData)) {
            try {
                // 获取基本信息
                $categoryId = request()->input('category_id');
                $unitId = request()->input('unit_id');
                $imagePath = request()->input('image');
                $attrValueIds = request()->input('attr_value_ids');
                $commonPid = request()->input('common_pid');
                $commonName = request()->input('common_name');
                
                // 确保attr_value_ids是有效数组，过滤掉null值
                if (is_array($attrValueIds)) {
                    $attrValueIds = array_filter($attrValueIds, function($value) {
                        return $value !== null;
                    });
                }
                
                // 创建一个基本产品记录，不包含SKU相关字段
                // 只使用被其他表关联的字段
                
                \Illuminate\Support\Facades\Log::info('产品基本信息', [
                    'category_id' => $categoryId,
                    'unit_id' => $unitId,
                    'image' => $imagePath,
                    'attr_value_ids' => $attrValueIds
                ]);
                
                // 完全禁用父类的store方法中对SKU表的操作
                // 这里不再调用父类的store方法
                
                // 直接使用saveSkuData方法创建SKU记录
                $skuRequest = new \Illuminate\Http\Request([
                    '_sku_combinations_data' => $rawData,
                    'category_id' => $categoryId,
                    'unit_id' => $unitId,
                    'image' => $imagePath,
                    'common_name' => $commonName,
                    'common_pid' => $commonPid,
                    'attr_value_ids' => $attrValueIds
                ]);
                
                // 调用saveSkuData方法保存所有SKU数据
                try {
                    $result = $this->saveSkuData($skuRequest);
                    \Illuminate\Support\Facades\Log::info('SKU保存结果', [
                        'result' => $result
                    ]);
                } catch (\Exception $saveEx) {
                    \Illuminate\Support\Facades\Log::error('保存SKU数据时出现异常', [
                        'error' => $saveEx->getMessage(),
                        'trace' => $saveEx->getTraceAsString()
                    ]);
                    throw $saveEx; // 重新抛出异常，以便外部catch捕获
                }
                
                // 创建成功后重定向到列表页面
                // admin_toastr('创建成功', 'success');
                // return redirect(admin_url('banhua/skus'));
                return \Dcat\Admin\Admin::json([
                    'status'  => true,
                    'message' => '创建成功',
                    'redirect' => admin_url('banhua/skus'),
                ]);
                
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('保存产品数据失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                admin_toastr('保存失败: ' . $e->getMessage(), 'error');
                return redirect()->back()->withErrors($e->getMessage());
            }
        } else {
            // 如果没有SKU组合数据，提示错误
            admin_toastr('没有SKU组合数据', 'error');
            return redirect()->back()->withErrors('请生成SKU组合数据');
        }
    }

    /**
     * 列表页面
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content): Content
    {
        try {
            \Illuminate\Support\Facades\Log::info('访问产品SKU列表页面');
            
        return $content
            ->header($this->title)
            ->description('阪画产品SKU列表')
            ->body($this->grid());
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('构建产品SKU列表页面出错', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回一个简单的错误页面
            return $content
                ->header($this->title)
                ->description('阪画产品SKU列表')
                ->body('<div class="alert alert-danger">加载列表时出错：' . $e->getMessage() . '</div>');
        }
    }

    /**
     * 构建列表
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        // 全局静态缓存属性值和属性，只查一次
        static $allAttrValues = null;
        static $allAttrs = null;
        if ($allAttrValues === null) {
            $allAttrValues = \App\Models\Banhua\TecBanhuaAttrValueModel::with('attr')->get()->keyBy('id');
        }
        if ($allAttrs === null) {
            $allAttrs = \App\Models\Banhua\TecBanhuaAttrModel::get()->keyBy('id');
        }
        return Grid::make(new TecBanhuaProductSkuRepo(), function (Grid $grid) use (&$allAttrValues, &$allAttrs) {
            try {
                $grid->scrollbarX();
                $grid->model()->with(['category', 'unit', 'gallery']);
                // $grid->column('id')->sortable();
                $grid->column('category.title', '分类');
                $grid->column('sku', 'SKU')->sortable();
                $grid->column('name', '名称')->sortable();
                $grid->column('pid', '图库ID')->sortable();
                $grid->column('jan', 'JAN')->sortable();
                // 属性值列 - 直接用静态缓存
                $grid->column('attr_value_ids', '属性组合')->display(function ($value) use (&$allAttrValues) {
                    if (empty($value)) {
                        return '';
                    }
                    if (is_string($value)) {
                        $value = json_decode($value, true);
                    }
                    if (!is_array($value)) {
                        return '';
                    }
                    $formattedValues = [];
                    foreach ($value as $attrValueId) {
                        if (isset($allAttrValues[$attrValueId])) {
                            $formattedValues[] = $allAttrValues[$attrValueId]->name;
                        }
                    }
                    return implode(', ', $formattedValues);
                });
                $grid->column('unit.unit_name', '单位')->sortable();
                $grid->column('price', '价格');
                $grid->column('image', '图片')->display(function ($value) {
                    if (!empty($this->gallery) && !empty($this->gallery->path)) {
                        $value = $this->gallery->path;
                    }
                    if (empty($value)) {
                        return '';
                    }
                    $value = trim($value);
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        return "<span class='text-muted'>图片URL无效</span>";
                    }
                    $thumbUrl = $value;
                    return "<img 
                        src='data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7' 
                        data-src='{$thumbUrl}' 
                        style='max-width:50px;max-height:50px;cursor:pointer;' 
                        class='img-thumbnail lazy img-preview' 
                        data-original='{$value}'
                        loading='lazy'
                        title='点击预览' />";
                });
                
                // 添加图片懒加载和预览JavaScript
                Admin::script('
                $(function() {
                    // 图片懒加载初始化函数
                    function initLazyLoading() {
                        // 检查浏览器是否支持Intersection Observer API
                        if ("IntersectionObserver" in window) {
                            const imageObserver = new IntersectionObserver((entries, observer) => {
                                entries.forEach(entry => {
                                    if (entry.isIntersecting) {
                                        const img = entry.target;
                                        // 检查是否已经加载过
                                        if (img.dataset.src && !img.src.includes(img.dataset.src)) {
                                            img.src = img.dataset.src;
                                        }
                                        // 图片加载后，取消观察
                                        observer.unobserve(img);
                                    }
                                });
                            });
                            
                            // 获取所有懒加载图片
                            const lazyImages = document.querySelectorAll("img.lazy");
                            lazyImages.forEach(img => {
                                imageObserver.observe(img);
                            });
                        } else {
                            // 对于不支持IntersectionObserver的浏览器，使用传统的滚动事件监听
                            let lazyImages = [].slice.call(document.querySelectorAll("img.lazy"));
                            let active = false;
                            
                            function lazyLoad() {
                                if (active === false) {
                                    active = true;
                                    
                                    setTimeout(function() {
                                        lazyImages.forEach(function(lazyImage) {
                                            const rect = lazyImage.getBoundingClientRect();
                                            if (
                                                rect.top <= window.innerHeight && 
                                                rect.bottom >= 0 &&
                                                getComputedStyle(lazyImage).display !== "none"
                                            ) {
                                                if (lazyImage.dataset.src) {
                                                    lazyImage.src = lazyImage.dataset.src;
                                                }
                                                
                                                lazyImage.classList.remove("lazy");
                                                
                                                lazyImages = lazyImages.filter(function(image) {
                                                    return image !== lazyImage;
                                                });
                                                
                                                if (lazyImages.length === 0) {
                                                    document.removeEventListener("scroll", lazyLoad);
                                                    window.removeEventListener("resize", lazyLoad);
                                                    window.removeEventListener("orientationchange", lazyLoad);
                                                }
                                            }
                                        });
                                        
                                        active = false;
                                    }, 200);
                                }
                            }
                            
                            // 添加滚动监听
                            document.addEventListener("scroll", lazyLoad);
                            window.addEventListener("resize", lazyLoad);
                            window.addEventListener("orientationchange", lazyLoad);
                            
                            // 初始加载
                            lazyLoad();
                        }
                    }
                    
                    // 初始化懒加载
                    initLazyLoading();
                    
                    // 监听表格刷新事件，重新初始化懒加载
                    $(document).on("pjax:complete", function() {
                        setTimeout(function() {
                            initLazyLoading();
                        }, 100);
                    });
                    
                    // 图片预览功能
                    $(document).off("click", ".img-preview").on("click", ".img-preview", function() {
                        var src = $(this).data("original");
                        if (!src) return;
                        
                        // 创建预览层
                        var imgHtml = "<div style=\'text-align:center;\'><img src=\'" + src + "\' style=\'max-width:100%;max-height:400px;\' /></div>";
                        layer.open({
                            type: 1,
                            title: "图片预览",
                            shadeClose: true,
                            shade: 0.3,
                            area: ["40%", "60%"],
                            content: imgHtml
                        });
                    });
                });
                ');               
               
                // 筛选器
                $grid->filter(function ($filter) {
                    // 按分类筛选 - 只显示有数据的分类
                    $filter->equal('category_id', '分类')->select(function () {
                        // 获取所有有SKU数据的分类ID
                        $categoryIds = TecBanhuaProductSkuModel::distinct()
                            ->pluck('category_id')
                            ->filter() // 过滤掉null和空值
                            ->toArray();
                        
                        // 获取这些分类的选项
                        return TecBanhuaProductCategoryModel::whereIn('id', $categoryIds)
                            ->pluck('title', 'id');
                    })->width(2);
                    
                    
                    // 按编码筛选
                    $filter->like('sku', 'SKU')->width(2);
                    $filter->like('pid', '图库ID')->width(2);
                    $filter->like('jan', 'JAN')->width(2);

                    // 使用属性值筛选
                    $filter->where('attr_value_filter', function ($query, $value = null) {
                        // 确保我们有正确的值
                        if (empty($value) && request()->has('attr_value_filter')) {
                            $value = request('attr_value_filter');
                        }
                        
                        if (!empty($value)) {
                            // 记录开始应用筛选
                            \Illuminate\Support\Facades\Log::info('应用属性值筛选', ['value' => $value]);
                            
                            // 使用最可能成功的几种JSON查询格式
                            $query->where(function($q) use ($value) {
                                // 作为字符串数组内元素 - 最常见格式
                                $q->whereRaw('JSON_CONTAINS(attr_value_ids, ?)', ['["'.$value.'"]']);
                                // 作为数字数组内元素
                                $q->orWhereRaw('JSON_CONTAINS(attr_value_ids, ?)', ['['.$value.']']);
                                // 作为单独字符串 - 兼容特殊格式
                                $q->orWhereRaw('JSON_CONTAINS(attr_value_ids, ?)', ['"'.$value.'"']);
                            });
                        }
                    }, '属性值')->select(function() {
                        try {
                            // 限制只获取前100个属性值，避免下拉框过大
                            return TecBanhuaAttrValueModel::with('attr')
                                ->limit(100)
                                ->get()
                                ->mapWithKeys(function($item) {
                                    $attrName = $item->attr ? $item->attr->name : '未知';
                                    return [$item->id => $attrName . ': ' . $item->name];
                                });
                        } catch (\Exception $e) {
                            \Log::error('获取属性值选项失败', ['error' => $e->getMessage()]);
                            return [];
                        }
                    })->width(2);

                });
                // 增加操作按钮
                $grid->actions(function (Grid\Displayers\Actions $actions) {
                    $actions->disableEdit(); // 禁用编辑按钮
                    $actions->disableQuickEdit(false); // 启用快速编辑按钮
                    $actions->disableDelete(false); // 启用删除按钮
                });
                // 排序
                $grid->model()->orderBy('pid', 'desc');
                        
                // 分页设置 - 优化大数据量下的性能
                $grid->paginate(50); // 每页显示50条记录
                // 启用每页显示数量的选择器，并自定义可选择的数量
                $grid->perPages([50, 100, 200, 500, 1000]);
                
                // 关闭详情按钮，减少不必要的请求
                $grid->disableViewButton();
                
                // 批量操作优化
                $grid->batchActions(function ($batch) {
                    // 添加自定义批量删除功能
                    $batch->add(new \App\Admin\Actions\Grid\Banhua\TecBanhuaProductSkuBatchDeleteAction());
                });
                
                // 设置行操作按钮
                $grid->actions(function ($actions) {
                    $actions->disableView(); // 禁用查看按钮
                });
                
                // 添加响应式表格
                $grid->fixColumns(0, 0); // 固定左侧和右侧的列，防止表格过宽
                

                
                \Illuminate\Support\Facades\Log::info('Grid构建完成');
            } catch (\Exception $innerEx) {
                \Illuminate\Support\Facades\Log::error('Grid回调构建失败', [
                    'error' => $innerEx->getMessage(),
                    'trace' => $innerEx->getTraceAsString()
                ]);
                // 创建一个简化版的Grid以避免错误
                $grid->column('id')->sortable();
                $grid->column('sku', 'SKU');
            }
            
            // 添加导入导出工具
            $grid->tools(function ($tools) {
                // 添加导出按钮
                $tools->append(new \App\Admin\Actions\Grid\Banhua\TecBanhuaProductSkuExportAction());
                
                // 添加导入按钮
                $tools->append(new \App\Admin\Actions\Grid\Banhua\TecBanhuaProductSkuImportAction());
                
                // 添加缓存清除按钮
                $tools->append('<a class="btn btn-success btn-mini btn-outline" href="' . admin_url('banhua/clear-product-cache') . '" style="margin-right:3px">
                    <i class="fa fa-motorcycle"></i>
                </a>');
            });
            
            return $grid;
        });
    }

    /**
     * 创建页面
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('创建产品')
            ->body($this->form());
    }

    /**
     * 编辑页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function edit($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('编辑产品')
            ->body($this->form()->edit($id));
    }

    /**
     * 构建表单
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new TecBanhuaProductSkuRepo(), function (Form $form) {
            $form->display('id');
            
            // 判断是否为编辑模式
            $isEdit = $form->isEditing();
            $model = $form->model();
            
            // 如果是编辑模式，获取相关SKU数据
            $skuData = null;
            $skuName = '';
            $skuPid = '';
            
            if ($isEdit && $model) {
                $id = $form->getKey();
                if ($id) {
                    // 获取当前SKU
                    $currentSku = TecBanhuaProductSkuModel::find($id);
                    if ($currentSku) {
                        $skuName = $currentSku->name;
                        $skuPid = $currentSku->pid;
                        
                        \Log::info('编辑模式 - 当前SKU', [
                            'id' => $id,
                            'sku' => $currentSku->sku,
                            'name' => $skuName,
                            'pid' => $skuPid
                        ]);
                        
                        // 获取相同名称和PID的所有SKU
                        $relatedSkus = TecBanhuaProductSkuModel::where('name', $skuName)
                            ->where('pid', $skuPid)
                            ->get();
                            
                        \Log::info('找到相关SKU数量', ['count' => $relatedSkus->count()]);
                        
                        // 转换为所需的数据格式
                        if ($relatedSkus->count() > 0) {
                            $skuData = [];
                            foreach ($relatedSkus as $sku) {
                                $attrValues = [];
                                if (!empty($sku->attr_value_ids)) {
                                    // 处理attr_value_ids，确保它是数组
                                    if (is_string($sku->attr_value_ids)) {
                                        $attrValueIds = json_decode($sku->attr_value_ids, true);
                                    } else {
                                        $attrValueIds = $sku->attr_value_ids;
                                    }
                                    
                                    // 确保是数组
                                    if (is_array($attrValueIds)) {
                                        $attrValues = $attrValueIds;
                                    }
                                }
                                
                                // 获取属性值的显示文本
                                $displayText = '';
                                try {
                                    $displayText = $sku->getAttrValuesTextAttribute();
                                    if (empty($displayText) && !empty($attrValues)) {
                                        // 如果getAttrValuesTextAttribute方法未返回文本，手动构建
                                        $attrValueModels = TecBanhuaAttrValueModel::whereIn('id', $attrValues)
                                            ->with('attr')
                                            ->get();
                                        
                                        $formattedValues = [];
                                        foreach ($attrValueModels as $attrValue) {
                                            // 只添加属性值，不添加属性名称
                                            $formattedValues[] = $attrValue->name;
                                        }
                                        $displayText = implode(', ', $formattedValues);
                                    }
                                } catch (\Exception $e) {
                                    \Log::error('获取属性值文本异常', [
                                        'error' => $e->getMessage(),
                                        'sku_id' => $sku->id
                                    ]);
                                }
                                
                                \Log::info('添加SKU数据', [
                                    'sku' => $sku->sku,
                                    'attr_values' => $attrValues,
                                    'display_text' => $displayText
                                ]);
                                
                                $skuData[] = [
                                    'attr_values' => $attrValues,
                                    'display_text' => $displayText,
                                    'sku' => $sku->sku,
                                    'name' => $sku->name,
                                    'pid' => $sku->pid,
                                    'jan' => $sku->jan,
                                    'price' => $sku->price
                                ];
                            }
                            
                            \Log::info('最终SKU数据', [
                                'count' => count($skuData),
                                'sample' => !empty($skuData) ? $skuData[0] : null
                            ]);
                        }
                    }
                }
            }

            $form->row(function ($row) use ($skuName, $skuPid) {
                // 分类
                $row->width(4)->select('category_id', '分类')
                ->options(TecBanhuaProductCategoryModel::selectOptions())
                ->required();
            // 单位
                $row->width(2)->select('unit_id', '单位')
                ->options(TecBanhuaProductUnitModel::pluck('unit_name', 'id'))
                ->required();
            
                // 名称字段
                $row->width(3)->text('common_name', '名称')
                    ->value($skuName) // 使用从数据库获取的名称
                    ->required();
                
                // 共通PID字段 - 改为下拉选择图库ID
                $row->width(3)->select('common_pid', '图库ID')
                    ->options(function () {
                        return \App\Models\Banhua\TecBanhuaImageGalleryModel::orderBy('image_id')
                            ->pluck('name', 'image_id')
                            ->toArray();
                    })
                    ->value($skuPid) // 使用从数据库获取的PID
                    ->help('选择图库ID')
                    ->attribute(['id' => 'gallery_id_select'])
                    ->required();
            });

            $form->row(function ($row) {
                // 隐藏图片路径字段，通过图库ID自动获取
                $row->hidden('image')->attribute(['id' => 'image_url_input']);
                    
                // 添加图片预览容器
                $row->width(12)->html('<div id="image_preview_container" style="margin-top:10px;display:none;">
                    <img id="image_preview" src="" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;">
                </div>', '图片预览');
                
                // 添加JavaScript脚本来处理图库ID选择变更事件
                Admin::script(<<<'JS'
$(function() {
    // 图库ID选择变更事件
    $('#gallery_id_select').on('change', function() {
        var galleryId = $(this).val();
        if (!galleryId) {
            $('#image_preview_container').hide();
            $('#image_url_input').val('');
            return;
        }
        
        // 通过AJAX获取图片信息
        $.ajax({
            url: '/admin/banhua/get-gallery-image',
            type: 'GET',
            data: {
                gallery_id: galleryId
            },
            success: function(response) {
                if (response.status && response.data.path) {
                    // 更新隐藏的图片路径字段
                    $('#image_url_input').val(response.data.path);
                    
                    // 显示图片预览
                    $('#image_preview').attr('src', response.data.path);
                    $('#image_preview_container').show();
                    
                    // 将图库名称赋值给产品名称字段
                    $('input[name="common_name"]').val(response.data.name);
                } else {
                    $('#image_preview_container').html("<span class='text-muted'>图片不存在或路径无效</span>");
                    $('#image_preview_container').show();
                }
            },
            error: function() {
                $('#image_preview_container').html("<span class='text-muted'>获取图片信息失败</span>");
                $('#image_preview_container').show();
            }
        });
    });
    
    // 页面加载时触发一次变更事件，显示初始图片
    $('#gallery_id_select').trigger('change');
});
JS
                );
            });

            $form->row(function ($row) use ($isEdit) {
            // 产品属性部分
                $row->divider('产品属性');
            
                // 在非编辑模式下显示属性值选择
                if (!$isEdit) {
            // 获取所有属性和属性值
                    $attributes = TecBanhuaAttrModel::with(['values' => function($query) {
                        $query->orderBy('sort', 'asc');
                    }])->orderBy('sort', 'asc')->get();
            
            // 构建属性值选项
            $options = [];
            foreach ($attributes as $attr) {
                foreach ($attr->values as $value) {
                    $options[$value->id] = $attr->name . ': ' . $value->name;
                }
            }
            
            // 创建一个多选下拉列表包含所有属性值
            $row->multipleSelect('attr_value_ids', '属性值')
                ->options($options)
                        ->width(12, 2) // 设置字段宽度为12列，标签宽度为2列
                        ->attribute(['id' => 'attr_value_select']) // 添加特定ID
                        ->config('selectAllOptions.text', '全选')
                        ->config('maxItems', 100) // 最多可选择100项
                        ->config('maxItemText', '已达到最大选择数量')
                        ->config('maxOptions', 100) // 最多显示100个选项
                        ->config('maxOptionsText', '已达到最大显示数量')
                ->saving(function ($value) {
                    return array_filter((array) $value);
                });
                } else {
                    // 在编辑模式下显示提示信息
                    $row->html('', '');
                }
            
            // 添加生成SKU组合按钮
                if (!$isEdit) {
                    $row->html('<div class="form-group"><button type="button" class="btn btn-success" id="generate_skus">生成SKU组合</button></div>', '操作');
                }
            
            // 添加用于显示SKU组合的容器
                $row->html('<div id="sku_combinations_container' . ($isEdit ? '_edit' : '') . '" class="table-responsive" style="display:' . ($isEdit ? 'block' : 'none') . ';">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>属性组合</th>
                            <th>SKU</th>
                            <th>JAN</th>
                            <th>价格</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                        <tbody id="sku_combinations_list' . ($isEdit ? '_edit' : '') . '"></tbody>
                </table>
            </div>', 'SKU组合列表');
            
            });            
            
            
            
            // 添加隐藏字段用于存储SKU数据
            $form->hidden('_sku_combinations_data');
            
            
            // 自定义表单动作
            $form->disableSubmitButton();
            $form->disableResetButton();
            
            // 添加自定义提交按钮
            $form->html('<div class="form-group">
                <button type="button" class="btn btn-primary" id="custom_submit_btn">保存</button>
                <a href="' . admin_url('banhua/skus') . '" class="btn btn-default">返回</a>
            </div>', '操作');
            
            // 添加JavaScript
            Admin::script($this->generateSkuScript());
            
            // 如果是编辑模式，添加已有的SKU数据
            if ($isEdit && !empty($skuData)) {
                Admin::script('
                    $(function() {
                        // 标记为编辑模式
                        window.isEditMode = true;
                        // 设置编辑模式下的容器ID
                        window.skuContainerId = "sku_combinations_container_edit";
                        window.skuListId = "sku_combinations_list_edit";
                        window.skuWarningId = "sku_warning_edit";
                        
                        // 将SKU数据传递给JS
                        window.existingSkuData = ' . json_encode($skuData) . ';
                        console.log("初始化编辑模式SKU数据:", window.existingSkuData);
                        // 初始化显示已有的SKU数据
                        if (window.initExistingSkuData && typeof window.initExistingSkuData === "function") {
                            setTimeout(function() {
                                window.initExistingSkuData();
                            }, 500);
                        }
                    });
                ');
            } else {
                Admin::script('
                    $(function() {
                        // 标记为新增模式
                        window.isEditMode = false;
                        // 设置新增模式下的容器ID
                        window.skuContainerId = "sku_combinations_container";
                        window.skuListId = "sku_combinations_list";
                        window.skuWarningId = "sku_warning";
                    });
                ');
            }
            
            // 添加自定义CSS样式
            Admin::style(<<<'CSS'
/* 只针对属性值多选下拉列表 */
#attr_value_select + .select2-container .select2-dropdown {
    max-height: 400px !important;
    overflow-y: auto !important;
}
#attr_value_select + .select2-container .select2-results__options {
    max-height: 350px !important;
    overflow-y: auto !important;
}
#attr_value_select + .select2-container {
    min-width: 400px !important;
}
CSS
            );
        });
    }

    /**
     * 生成SKU组合的JavaScript
     *
     * @return string
     */
    protected function generateSkuScript(): string
    {
        return <<<'JS'
$(document).ready(function() {
    // 存储全局变量
    var skuCombinationsData = []; // 全局存储SKU组合数据
    var commonPid = ''; // 全局存储共通PID
    var commonName = ''; // 全局存储名称
    var isEditMode = false; // 是否为编辑模式
    
    // 如果全局变量已定义，则使用全局变量
    if (typeof window.isEditMode !== 'undefined') {
        isEditMode = window.isEditMode;
        console.log("使用全局模式设置:", isEditMode ? "编辑模式" : "新增模式");
    } else {
        // 检查是否存在已有的SKU数据，如果存在则认为是编辑模式
        if (window.existingSkuData && window.existingSkuData.length > 0) {
            isEditMode = true;
            console.log("根据SKU数据判断为编辑模式");
        }
    }
    
    // 容器ID
    var skuContainerId = window.skuContainerId || (isEditMode ? 'sku_combinations_container_edit' : 'sku_combinations_container');
    var skuListId = window.skuListId || (isEditMode ? 'sku_combinations_list_edit' : 'sku_combinations_list');
    var skuWarningId = window.skuWarningId || (isEditMode ? 'sku_warning_edit' : 'sku_warning');
    
    console.log("使用的容器ID:", skuContainerId);
    console.log("使用的列表ID:", skuListId);
    
    // 初始化名称和PID值
    commonPid = $('input[name="common_pid"]').val();
    commonName = $('input[name="common_name"]').val();
    
    // 初始化图片预览
    var imageUrl = $('#image_url_input').val();
    if (imageUrl) {
        // 验证URL是否有效
        if (!isValidUrl(imageUrl)) {
            $('#image_preview_container').html("<span class='text-muted'>图片URL无效</span>");
            $('#image_preview_container').show();
        } else {
            // 设置预览图片
            $('#image_preview').attr('src', imageUrl);
            $('#image_preview_container').show();
        }
    }
    
    // 初始化已有SKU数据的函数（供外部调用）
    window.initExistingSkuData = function() {
        console.log("调用initExistingSkuData函数");
        if (window.existingSkuData && window.existingSkuData.length > 0) {
            console.log("存在SKU数据，数量:", window.existingSkuData.length);
            // 将已有数据复制到全局变量
            skuCombinationsData = window.existingSkuData;
            
            // 使用全局变量中的容器ID
            var containerId = window.skuContainerId || 'sku_combinations_container_edit';
            var listId = window.skuListId || 'sku_combinations_list_edit';
            var warningId = window.skuWarningId || 'sku_warning_edit';
            
            console.log("使用容器ID:", containerId, "列表ID:", listId);
            
            // 显示SKU组合表格和警告信息
            $('#' + containerId).show();
            $('#' + warningId).show();
            
            // 清空现有内容
            $('#' + listId).empty();
            
            console.log("开始渲染SKU表格，数据条数:", skuCombinationsData.length);
            // 添加每个SKU到表格，编辑模式下不显示删除按钮
            skuCombinationsData.forEach(function(skuData, index) {
                console.log("渲染SKU行:", index, skuData);
                var row = $('<tr>').append(
                    $('<td>').text(skuData.display_text),
                    $('<td>').append($('<input>').addClass('form-control sku-input').attr({
                        'type': 'text',
                        'data-index': index,
                        'value': skuData.sku,
                        'readonly': true
                    })),
                    $('<td>').append($('<input>').addClass('form-control jan-input').attr({
                        'type': 'text',
                        'data-index': index,
                        'value': skuData.jan || ''
                    })),
                    $('<td>').append($('<input>').addClass('form-control price-input').attr({
                        'type': 'number',
                        'data-index': index,
                        'value': skuData.price || 0,
                        'step': '0.01'
                    })),
                    $('<td>').append(
                        $('<div>').addClass('text-muted').text('')
                    )
                );
                
                $('#' + listId).append(row);
            });
            
            // 保存到隐藏字段
        $('input[name="_sku_combinations_data"]').val(JSON.stringify(skuCombinationsData));
            console.log("SKU数据已保存到隐藏字段");
            
            // 绑定事件处理程序（只监听输入框变化，不绑定删除事件）
            $('.sku-input, .jan-input, .price-input').on('change', function() {
            var index = $(this).data('index');
            var value = $(this).val();
                var type = $(this).attr('class').split(' ')[1].split('-')[0];
                
        if (index >= 0 && index < skuCombinationsData.length) {
                    skuCombinationsData[index][type] = type === 'price' ? parseFloat(value) || 0 : value;
            $('input[name="_sku_combinations_data"]').val(JSON.stringify(skuCombinationsData));
                    console.log("更新SKU数据:", index, type, value);
                }
            });
        } else {
            console.log("没有找到SKU数据");
        }
    };
    
    // 图片预览功能
    $('#preview_image_btn').on('click', function() {
        var imageUrl = $('#image_url_input').val();
        if (!imageUrl) {
            Dcat.warning('请输入图片URL');
            return;
        }
        
        // 验证URL是否有效
        if (!isValidUrl(imageUrl)) {
            $('#image_preview_container').html("<span class='text-muted'>图片URL无效</span>");
            $('#image_preview_container').show();
            return;
        }
        
        // 重置预览容器
        $('#image_preview_container').html('<img id="image_preview" src="" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;">');
        $('#image_preview_container').show();
        $('#image_preview').after('<div id="image_loading">加载中...</div>');
        
        // 创建新图片对象测试加载
        var img = new Image();
        img.onload = function() {
            $('#image_loading').remove();
            $('#image_preview').attr('src', imageUrl);
        };
        img.onerror = function() {
            $('#image_loading').remove();
            $('#image_preview_container').html("<span class='text-muted'>图片URL无效</span>");
        };
        img.src = imageUrl;
    });
    
    // 输入框变化时自动更新预览
    $('#image_url_input').on('change blur', function() {
        var imageUrl = $(this).val();
        if (imageUrl) {
            // 验证URL是否有效
            if (!isValidUrl(imageUrl)) {
                $('#image_preview_container').html("<span class='text-muted'>图片URL无效</span>");
                $('#image_preview_container').show();
                return;
            }
            
            // 重置预览容器
            $('#image_preview_container').html('<img id="image_preview" src="" style="max-width:200px;max-height:200px;border:1px solid #ddd;padding:3px;">');
            $('#image_preview_container').show();
            $('#image_preview').after('<div id="image_loading">加载中...</div>');
            
            // 创建新图片对象测试加载
            var img = new Image();
            img.onload = function() {
                $('#image_loading').remove();
                $('#image_preview').attr('src', imageUrl);
            };
            img.onerror = function() {
                $('#image_loading').remove();
                $('#image_preview_container').html("<span class='text-muted'>图片URL无效</span>");
            };
            img.src = imageUrl;
        } else {
            $('#image_preview_container').hide();
        }
    });
    
    // URL验证函数
    function isValidUrl(url) {
        try {
            return /^(http|https):\/\/[^ "]+$/.test(url);
        } catch (e) {
            return false;
        }
    }
    
    // 生成SKU组合按钮点击事件
    $('#generate_skus').on('click', function() {
        // 获取选中的属性值
        var $select = $('select[name="attr_value_ids[]"]');
        var selectedOptions = $select.find('option:selected');
        
        if (selectedOptions.length === 0) {
            Dcat.warning('请至少选择一个属性值');
            return;
        }
        
        // 获取共通PID和名称
        commonPid = $('input[name="common_pid"]').val();
        commonName = $('input[name="common_name"]').val();
        
        if (!commonName) {
            Dcat.warning('请输入名称');
            return;
        }
        
        // 按属性分组选中的值
        var selectedAttrs = {};
        selectedOptions.each(function() {
            var $option = $(this);
            var text = $option.text();
            var parts = text.split(': ');
            var attrName = parts[0];
            var valueName = parts[1];
            
            if (!selectedAttrs[attrName]) {
                selectedAttrs[attrName] = {
                    attr_name: attrName,
                    values: []
                };
            }
            
            // 检查该值是否已经添加，避免重复
            var valueExists = false;
            for (var i = 0; i < selectedAttrs[attrName].values.length; i++) {
                if (selectedAttrs[attrName].values[i].id === $option.val()) {
                    valueExists = true;
                    break;
                }
            }
            
            if (!valueExists) {
            selectedAttrs[attrName].values.push({
                id: $option.val(),
                name: valueName
            });
            }
        });
        
        // 转换为数组格式
        var attributes = Object.values(selectedAttrs);
        
        // 生成笛卡尔积
        var combinations = generateCartesianProduct(attributes);
        
        if (combinations.length === 0) {
            Dcat.warning('无法生成属性组合');
            return;
        }
        
        // 使用 layer.confirm 显示确认对话框
        layer.confirm('将创建 ' + combinations.length + ' 个SKU组合，确认继续？', {
            title: '生成SKU组合',
            btn: ['确定', '取消'],
            icon: 3
        }, function(index) {
            layer.close(index);
            generateSkuTable(combinations);
        });
        });
        
    // 生成笛卡尔积
        function generateCartesianProduct(attributes) {
        if (!attributes || attributes.length === 0) {
            console.log('没有属性可生成组合');
                return [];
            }
            
        console.log('准备生成笛卡尔积，属性数量：', attributes.length);
        attributes.forEach(function(attr, index) {
            console.log('属性' + (index + 1) + '：' + attr.attr_name + '，值数量：' + attr.values.length);
        });

        // 如果只有一个属性，直接返回该属性的所有值组合
        if (attributes.length === 1) {
            var result = attributes[0].values.map(function(value) {
                return [{
                    attr_id: null,
                    attr_name: attributes[0].attr_name,
                    value_id: value.id,
                    value_name: value.name
                }];
            });
            console.log('只有一个属性，直接返回' + result.length + '个组合');
            return result;
        }

        // 从第一个属性开始构建结果
        var result = attributes[0].values.map(function(value) {
            return [{
                attr_id: null,
                attr_name: attributes[0].attr_name,
                value_id: value.id,
                value_name: value.name
            }];
        });
        
        console.log('初始组合数：', result.length);

        // 依次与后续属性计算笛卡尔积
            for (var i = 1; i < attributes.length; i++) {
                var currentAttr = attributes[i];
                var newResult = [];
            
            console.log('处理属性' + (i + 1) + '：' + currentAttr.attr_name + '，当前组合数：' + result.length + '，属性值数：' + currentAttr.values.length);

            // 确保属性有值
            if (!currentAttr.values || currentAttr.values.length === 0) {
                console.log('警告：属性' + currentAttr.attr_name + '没有值，跳过');
                continue;
            }
                
                result.forEach(function(combination) {
                    currentAttr.values.forEach(function(value) {
                        var newCombo = combination.slice();
                        newCombo.push({
                            attr_id: null,
                            attr_name: currentAttr.attr_name,
                            value_id: value.id,
                            value_name: value.name
                        });
                        newResult.push(newCombo);
                    });
                });
                
                result = newResult;
            console.log('添加属性' + currentAttr.attr_name + '后的组合数：' + result.length);
        }
        
        console.log('最终生成组合数：', result.length);
        
        // 如果没有生成任何组合，可能是处理过程有问题
        if (result.length === 0) {
            console.error('警告：未能生成任何组合，请检查属性数据');
            }
            
            return result;
        }
        
        // 生成SKU表格的函数
        function generateSkuTable(combinations) {
        console.log('开始生成SKU表格，组合数量：', combinations.length);
        console.log('使用共通PID:', commonPid);
        console.log('使用名称:', commonName);
        
        // 使用全局变量中的容器ID
        var containerId = window.skuContainerId || 'sku_combinations_container';
        var listId = window.skuListId || 'sku_combinations_list';
        
        console.log("使用容器ID:", containerId, "列表ID:", listId);
            
            // 清空SKU表格
        $('#' + listId).empty();
            
            // 清空SKU组合数据
        skuCombinationsData = [];
            
            // 生成SKU组合
            combinations.forEach(function(combination, index) {
            // 检查组合是否有效
            if (!combination || !Array.isArray(combination) || combination.length === 0) {
                console.error('无效的组合数据', combination);
                return; // 跳过无效组合
            }
            
                var displayText = combination.map(function(item) {
                if (!item || !item.attr_name || !item.value_name) {
                    console.error('组合项缺少必要字段', item);
                    return '未知';
                }
                // 只返回属性值，不包含属性名称
                return item.value_name;
                }).join(', ');
                
                // 生成编码
            var timestamp = new Date().getTime().toString().substring(8);
            var randomChars = Math.random().toString(36).substring(2, 5).toUpperCase();
            
            // var sku = commonName + '-' + (index + 1) + '-' + timestamp + randomChars;
            var sku = commonName;
            var name = commonName; // 使用名称
            var pid = commonPid; // 使用共通PID
            var jan = ''; // 可为空
                var price = 0;
                
                // 存储SKU数据
                var skuData = {
                    attr_values: combination.map(function(item) { return item.value_id; }),
                    display_text: displayText,
                    sku: sku,
                name: name,
                    pid: pid,
                    jan: jan,
                    price: price
                };
                
            console.log('生成SKU数据', index + 1, ':', skuData);
                skuCombinationsData.push(skuData);
                
                // 添加到表格
            var row = $('<tr>').append(
                $('<td>').text(displayText),
                $('<td>').append($('<input>').addClass('form-control sku-input').attr({
                    'type': 'text',
                    'data-index': index,
                    'value': sku
                })),
                $('<td>').append($('<input>').addClass('form-control jan-input').attr({
                    'type': 'text',
                    'data-index': index,
                    'value': jan
                })),
                $('<td>').append($('<input>').addClass('form-control price-input').attr({
                    'type': 'number',
                    'data-index': index,
                    'value': price,
                    'step': '0.01'
                })),
                $('<td>').append(
                    $('<button>').addClass('btn btn-sm btn-danger remove-sku')
                        .attr('data-index', index)
                        .html('<i class="feather icon-trash"></i>')
                )
            );

            $('#' + listId).append(row);
            });
            
            // 保存SKU组合数据到隐藏字段
            $('input[name="_sku_combinations_data"]').val(JSON.stringify(skuCombinationsData));
        console.log('保存的SKU组合数据:', JSON.stringify(skuCombinationsData));
        console.log('SKU组合数据长度:', skuCombinationsData.length);
            
            // 显示SKU组合表格
        $('#' + containerId).show();
        
        // 监听共通PID输入框变化
        $('input[name="common_pid"]').on('change', function() {
            commonPid = $(this).val();
            console.log('共通PID已更新:', commonPid);
        });
        
        // 监听名称输入框变化
        $('input[name="common_name"]').on('change', function() {
            commonName = $(this).val();
            console.log('名称已更新:', commonName);
        });
        
        // 添加事件绑定
        // 监听输入框变化
        $('.sku-input, .jan-input, .price-input').on('change', function() {
            var index = $(this).data('index');
            var value = $(this).val();
            var type = $(this).attr('class').split(' ')[1].split('-')[0];
            
            if (index >= 0 && index < skuCombinationsData.length) {
                skuCombinationsData[index][type] = type === 'price' ? parseFloat(value) || 0 : value;
                $('input[name="_sku_combinations_data"]').val(JSON.stringify(skuCombinationsData));
            }
        });
        
        // 删除SKU按钮事件
        $('.remove-sku').on('click', function() {
            if (isEditMode) {
                Dcat.warning('编辑模式下不允许删除SKU');
            return false;
        }
        
            var index = $(this).data('index');
            if (index >= 0 && index < skuCombinationsData.length) {
                skuCombinationsData.splice(index, 1);
                $(this).closest('tr').remove();
                $('input[name="_sku_combinations_data"]').val(JSON.stringify(skuCombinationsData));
                
                // 更新索引
                $('#' + listId + ' tr').each(function(i) {
                    $(this).find('[data-index]').attr('data-index', i);
                });
            }
        });
    }

    // 自定义提交按钮点击事件
    $('#custom_submit_btn').on('click', function() {
        try {
            // 更新共通PID和名称
            commonPid = $('input[name="common_pid"]').val();
            commonName = $('input[name="common_name"]').val();
            
            // 获取SKU组合数据
            var skuData = $('input[name="_sku_combinations_data"]').val();
            console.log("提交前的SKU数据:", skuData);
            
            // 在编辑模式下，如果没有SKU数据，尝试从页面中重新收集
            if (isEditMode && (!skuData || skuData === '[]')) {
                console.log("编辑模式下没有找到SKU数据，尝试从表格中收集");
                var newSkuData = [];
                
                $('#' + skuListId + ' tr').each(function(index) {
                    var $row = $(this);
                    var sku = $row.find('.sku-input').val();
                    var jan = $row.find('.jan-input').val();
                    var price = parseFloat($row.find('.price-input').val()) || 0;
                    var displayText = $row.find('td:first').text();
                    
                    if (sku) {
                        newSkuData.push({
                            sku: sku,
                            jan: jan,
                            price: price,
                            name: commonName,
                            pid: commonPid,
                            display_text: displayText,
                            attr_values: []  // 在编辑模式下可能无法获取原始属性值
                        });
                    }
                });
                
                if (newSkuData.length > 0) {
                    console.log("从表格收集到 " + newSkuData.length + " 条SKU数据");
                    $('input[name="_sku_combinations_data"]').val(JSON.stringify(newSkuData));
                    skuData = JSON.stringify(newSkuData);
                }
            }
            
            // 如果有SKU组合数据，更新它们的name和pid值
            if (skuData && skuData !== '[]') {
                try {
                    var skuDataArray = JSON.parse(skuData);
                    if (Array.isArray(skuDataArray) && skuDataArray.length > 0) {
                        skuDataArray.forEach(function(item) {
                            item.name = commonName;
                            item.pid = commonPid;
                        });
                        
                        // 更新隐藏字段
                        $('input[name="_sku_combinations_data"]').val(JSON.stringify(skuDataArray));
                        console.log("更新后的SKU数据长度:", skuDataArray.length);
                    }
                } catch (e) {
                    console.error("解析SKU数据失败:", e);
                }
            }
            
            // 获取表单数据
            var formData = {
                'category_id': $('select[name="category_id"]').val(),
                'unit_id': $('select[name="unit_id"]').val(),
                'common_name': commonName,
                'common_pid': commonPid,
                'attr_value_ids': $('select[name="attr_value_ids[]"]').val(),
                '_sku_combinations_data': skuData
            };

            // 验证必填字段
            if (!formData.category_id) {
                Dcat.error('请选择分类');
                return false;
            }
            if (!formData.unit_id) {
                Dcat.error('请选择单位');
                return false;
            }
            if (!formData.common_name) {
                Dcat.error('请输入名称');
                return false;
            }
            
            // 在新增模式下验证属性值和SKU组合
            if (!isEditMode) {
                if (!formData.attr_value_ids || formData.attr_value_ids.length === 0) {
                    Dcat.error('请选择属性值');
                    return false;
                }
                if (!formData._sku_combinations_data || formData._sku_combinations_data === '[]') {
                    Dcat.error('请先生成SKU组合');
                    return false;
                }
            } 
            // 在编辑模式下验证是否有SKU数据
            else {
                if (!formData._sku_combinations_data || formData._sku_combinations_data === '[]') {
                    Dcat.error('找不到SKU数据，请刷新页面重试');
                    return false;
                }
        }
        
        // 显示加载中
        Dcat.loading();
        
            console.log('提交表单数据:', formData);
            
            // 提交表单
            $('form').submit();
            
        } catch (e) {
            console.error('表单提交错误:', e);
            Dcat.error('提交失败，请检查表单数据');
            return false;
        }
    });
    });
JS;
    }
    
    /**
     * 保存SKU数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveSkuData(Request $request)
    {
        // 记录请求数据
        \Illuminate\Support\Facades\Log::info('保存SKU数据请求', [
            'all_inputs' => $request->all()
        ]);
        
        // 获取基本信息
        $categoryId = $request->input('category_id');
        $unitId = $request->input('unit_id');
        $imagePath = $request->input('image');
        $attrValueIds = $request->input('attr_value_ids');
        $commonPid = $request->input('common_pid');
        $commonName = $request->input('common_name');
        
        // 确保attr_value_ids是有效数组，过滤掉null值
        if (is_array($attrValueIds)) {
            $attrValueIds = array_filter($attrValueIds, function($value) {
                return $value !== null;
            });
        }
        
        // 获取SKU组合数据
        $skuCombinationsData = null;
        $rawData = $request->input('_sku_combinations_data');
        
        \Illuminate\Support\Facades\Log::info('原始SKU组合数据', [
            'raw_data' => $rawData,
            'is_empty' => empty($rawData),
            'type' => gettype($rawData)
        ]);
        
        if (!empty($rawData)) {
            try {
                $skuCombinationsData = json_decode($rawData, true);
                \Illuminate\Support\Facades\Log::info('解析后的SKU组合数据', [
                    'data' => $skuCombinationsData,
                    'is_array' => is_array($skuCombinationsData),
                    'count' => is_array($skuCombinationsData) ? count($skuCombinationsData) : 0
                ]);
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('解析SKU组合数据失败', [
                    'error' => $e->getMessage(),
                    'raw_data' => $rawData
                ]);
            }
        }
        
        // 如果主字段为空，尝试使用备份字段
        if (empty($skuCombinationsData)) {
            $backupData = $request->input('_sku_combinations_backup');
            \Illuminate\Support\Facades\Log::info('尝试使用备份数据', [
                'backup_data' => $backupData
            ]);
            
            if (!empty($backupData)) {
                try {
                    $skuCombinationsData = json_decode($backupData, true);
                    \Illuminate\Support\Facades\Log::info('从备份解析的SKU组合数据', [
                        'data' => $skuCombinationsData
                    ]);
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('解析备份SKU组合数据失败', [
                        'error' => $e->getMessage(),
                        'backup_data' => $backupData
                    ]);
                }
            }
        }
        
        // 验证基本信息
        if (empty($categoryId) || empty($unitId)) {
            \Illuminate\Support\Facades\Log::error('分类或单位为空', [
                'category_id' => $categoryId,
                'unit_id' => $unitId
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '分类和单位不能为空'
            ]);
        }
        
        try {
            // 批量创建SKU记录
            $skuRecords = [];
            $errorRecords = [];
            
            if (!empty($skuCombinationsData) && is_array($skuCombinationsData)) {
                \Illuminate\Support\Facades\Log::info('开始处理SKU组合数据', [
                    'count' => count($skuCombinationsData)
                ]);
                
                foreach ($skuCombinationsData as $index => $combination) {
                    // 检查必要字段是否存在
                    $hasError = false;
                    $errorFields = [];
                    
                    if (empty($combination['sku'])) {
                        $hasError = true;
                        $errorFields[] = 'sku';
                        \Illuminate\Support\Facades\Log::warning('SKU组合中缺少sku字段', [
                            'index' => $index,
                            'combination' => $combination
                        ]);
                    }
                    
                    // name字段使用名称，不需要验证
                    // jan字段可以为空，不需要验证
                    
                    // 记录当前处理的组合详情
                    \Illuminate\Support\Facades\Log::info('处理SKU组合', [
                            'index' => $index,
                        'combination' => $combination,
                        'has_attr_values' => isset($combination['attr_values']),
                        'attr_values_type' => isset($combination['attr_values']) ? gettype($combination['attr_values']) : 'undefined'
                        ]);
                    
                    // 如果有错误，记录并跳过
                    if ($hasError) {
                        $errorRecords[] = [
                            'index' => $index,
                            'missing_fields' => $errorFields,
                            'data' => $combination
                        ];
                        continue;
                    }
                    
                    // 验证通过，添加到待保存记录
                    $skuRecords[] = [
                        'sku' => $combination['sku'],
                        'name' => $commonName ?: ($combination['name'] ?? ''), // 优先使用名称
                        'pid' => $commonPid ?: ($combination['pid'] ?? ''), // 优先使用共通PID
                        'jan' => $combination['jan'] ?? '', // jan可以为空
                        'price' => $combination['price'] ?? 0,
                        'image' => $imagePath ?? '',
                        'category_id' => $categoryId,
                        'unit_id' => $unitId,
                        'attr_value_ids' => $combination['attr_value_ids'] ?? [],
                        'attr_values' => $combination['attr_values'] ?? []
                    ];
                }
            } else {
                \Illuminate\Support\Facades\Log::warning('没有有效的SKU记录可保存', [
                    'skuCombinationsData_type' => gettype($skuCombinationsData),
                    'skuCombinationsData_empty' => empty($skuCombinationsData),
                    'is_array' => is_array($skuCombinationsData),
                    'raw_data_type' => gettype($rawData),
                    'raw_data_empty' => empty($rawData)
                ]);
                
                // 如果没有SKU记录，返回明确的提示信息
                return response()->json([
                    'status' => false,
                    'message' => '没有有效的SKU记录可保存，请先生成SKU组合'
                ]);
            }
            
            // 批量插入SKU记录
            $successCount = 0;
            if (!empty($skuRecords)) {
                // 使用事务处理
                \Illuminate\Support\Facades\DB::beginTransaction();
                
                try {
                    // 跟踪跳过的SKU数量
                    $skippedDuplicates = 0;
                    
                    // 逐个插入记录，确保每个记录都有必要的字段
                    foreach ($skuRecords as $record) {
                        // 再次验证必要字段
                        if (empty($record['sku'])) {
                            \Illuminate\Support\Facades\Log::error('插入前发现记录缺少必要字段', $record);
                            continue;
                        }
                        
                        // 创建新的SKU模型对象前先记录日志
                        \Illuminate\Support\Facades\Log::info('准备创建SKU模型对象', [
                            'record_data' => $record
                        ]);
                        
                        // 不使用fill方法，直接逐个设置属性
                        $sku = new TecBanhuaProductSkuModel();
                        
                        // 手动设置所有必要字段，确保每个字段都被正确赋值
                        $sku->sku = $record['sku']; // 确保sku字段被设置
                        $sku->name = $record['name'];
                        $sku->pid = $record['pid'];
                        $sku->jan = $record['jan'];
                        $sku->category_id = $record['category_id'];
                        $sku->unit_id = $record['unit_id'];
                        $sku->price = $record['price'] ?? 0;
                        $sku->image = $record['image'] ?? '';

                        // 处理属性值IDs
                        \Illuminate\Support\Facades\Log::info('属性值处理前', [
                            'record' => $record
                        ]);
                        if (isset($record['attr_values']) && is_array($record['attr_values'])) {
                            $sku->attr_value_ids = $record['attr_values'];
                        } else if (isset($record['attr_value_ids'])) {
                            // 确保不是字符串JSON
                            if (is_string($record['attr_value_ids']) && (trim($record['attr_value_ids']) == '[]' || 
                                 substr($record['attr_value_ids'], 0, 1) == '[')) {
                                $decoded = json_decode($record['attr_value_ids'], true);
                                $sku->attr_value_ids = is_array($decoded) ? $decoded : [];
                            } else {
                                $sku->attr_value_ids = $record['attr_value_ids'];
                            }
                        } else {
                            $sku->attr_value_ids = [];
                        }
                        
                        // 再次确认sku字段有值
                        if (empty($sku->sku)) {
                            \Illuminate\Support\Facades\Log::error('手动设置后SKU字段仍为空', [
                                'record' => $record,
                                'sku_object' => $sku->getAttributes()
                            ]);
                            continue;
                        }
                        
                        // 生成并检查SKU和属性值组合的唯一哈希
                        $attrValueIds = $sku->attr_value_ids ?? [];
                        if (is_string($attrValueIds)) {
                            $attrValueIds = json_decode($attrValueIds, true) ?: [];
                        }
                        $skuAttrHash = TecBanhuaProductSkuModel::generateSkuAttrHash($sku->sku, $attrValueIds);
                        
                        // 检查是否已存在相同的SKU和属性值组合
                        if (TecBanhuaProductSkuModel::where('sku_attr_hash', $skuAttrHash)->exists()) {
                            \Illuminate\Support\Facades\Log::warning('SKU和属性值组合已存在，跳过保存', [
                                'sku' => $sku->sku,
                                'attr_value_ids' => $attrValueIds,
                                'sku_attr_hash' => $skuAttrHash
                            ]);
                            $skippedDuplicates++;
                            continue; // 跳过保存
                        }
                        
                        // 设置SKU和属性值组合的哈希值
                        $sku->sku_attr_hash = $skuAttrHash;
                        
                        // 记录最终将要保存的SKU对象的所有属性
                        \Illuminate\Support\Facades\Log::info('最终将要保存的SKU对象属性', [
                            'all_attributes' => $sku->getAttributes(),
                            'sku_value' => $sku->sku
                        ]);
                        
                        \Illuminate\Support\Facades\Log::info('保存前的SKU对象', [
                            'sku' => $sku->sku,
                            'pid' => $sku->pid,
                            'jan' => $sku->jan,
                            'attributes' => $sku->getAttributes()
                        ]);
                        
                        $sku->save();
                        $successCount++;
                        
                        \Illuminate\Support\Facades\Log::info('成功保存SKU', [
                            'id' => $sku->id,
                            'sku' => $sku->sku
                        ]);
                    }
                    
                    \Illuminate\Support\Facades\DB::commit();
                    \Illuminate\Support\Facades\Log::info('事务提交成功，保存了 ' . $successCount . ' 个SKU');
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\DB::rollBack();
                    \Illuminate\Support\Facades\Log::error('保存SKU过程中出错，事务回滚', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }
            
            // 返回成功响应
            return response()->json([
                'status' => true,
                'message' => '成功保存 ' . $successCount . ' 个SKU记录' . 
                             (count($errorRecords) > 0 ? '，' . count($errorRecords) . ' 个记录因缺少必要字段而跳过' : '') .
                             ($skippedDuplicates > 0 ? '，' . $skippedDuplicates . ' 个记录因SKU和属性值组合已存在而跳过' : '')
            ]);
        } catch (\Exception $e) {
            // 记录错误
            \Illuminate\Support\Facades\Log::error('保存SKU数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回错误响应
            return response()->json([
                'status' => false,
                'message' => '保存SKU数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content): Content
    {
        return $content
            ->header($this->title)
            ->description('产品详情')
            ->body($this->detail($id));
    }

    /**
     * 构建详情
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id): Show
    {
        return Show::make($id, new TecBanhuaProductSkuRepo(), function (Show $show) {
            $show->field('id');
            $show->field('category.title', '分类');
            $show->field('unit.unit_name', '单位');
            
            // SKU信息
            $show->field('sku', 'SKU');
            $show->field('name', '名称');
            $show->field('pid', 'PID');
            $show->field('jan', 'JAN');
            $show->field('price', '价格');
            
            // 属性值
            $show->field('attr_values_text', '属性组合');
            
            // 图片
            $show->field('image', '图片路径');
            
            // 时间
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * 批量生成SKU
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateSku(Request $request)
    {
        // 记录请求数据
        Log::info('生成SKU请求', [
            'input' => $request->all()
        ]);
        
        $categoryId = $request->input('category_id');
        $unitId = $request->input('unit_id');
        $attributes = $request->input('attributes', []);
        
        // 验证必填参数
        if (!$categoryId || !$unitId || empty($attributes)) {
            Log::error('缺少必要参数', [
                'category_id' => $categoryId,
                'unit_id' => $unitId,
                'attributes' => $attributes
            ]);
            return response()->json([
                'status' => false,
                'message' => '缺少必要参数'
            ]);
        }
        
        try {
            // 解析属性值
            $parsedAttrs = [];
            foreach ($attributes as $attrId => $valueIds) {
                if (empty($valueIds)) {
                    continue;
                }
                
                $attr = TecBanhuaAttrModel::find($attrId);
                if (!$attr) {
                    Log::warning('属性不存在', ['attr_id' => $attrId]);
                    continue;
                }
                
                // 确保valueIds是数组
                if (!is_array($valueIds)) {
                    $valueIds = [$valueIds];
                }
                
                $values = TecBanhuaAttrValueModel::whereIn('id', $valueIds)->get();
                if ($values->isEmpty()) {
                    Log::warning('属性值不存在', ['attr_id' => $attrId, 'value_ids' => $valueIds]);
                    continue;
                }
                
                $parsedAttrs[] = [
                    'attr_id' => $attrId,
                    'attr_name' => $attr->name,
                    'values' => $values->map(function ($value) {
                        return [
                            'id' => $value->id,
                            'name' => $value->name
                        ];
                    })->toArray()
                ];
            }
            
            // 记录解析后的属性数据
            Log::info('解析后的属性', [
                'parsed_attrs' => $parsedAttrs
            ]);
            
            // 如果没有有效的属性值，返回错误
            if (empty($parsedAttrs)) {
                return response()->json([
                    'status' => false,
                    'message' => '没有有效的属性值'
                ]);
            }
            
            // 生成笛卡尔积
            $combinations = $this->generateCartesianProduct($parsedAttrs);
            
            // 记录生成的组合
            Log::info('生成的组合', [
                'count' => count($combinations),
                'sample' => array_slice($combinations, 0, 2)
            ]);
            
            // 返回结果
            return response()->json([
                'status' => true,
                'data' => [
                    'combinations' => $combinations,
                    'count' => count($combinations)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('生成SKU异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '生成SKU失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 生成笛卡尔积
     *
     * @param array $attributes
     * @return array
     */
    protected function generateCartesianProduct(array $attributes): array
    {
        if (empty($attributes)) {
            return [];
        }
        
        // 初始化结果数组
        $result = [];
        foreach ($attributes[0]['values'] as $value) {
            $result[] = [
                [
                    'attr_id' => $attributes[0]['attr_id'],
                    'attr_name' => $attributes[0]['attr_name'],
                    'value_id' => $value['id'],
                    'value_name' => $value['name']
                ]
            ];
        }
        
        // 从第二个属性开始，依次计算笛卡尔积
        for ($i = 1; $i < count($attributes); $i++) {
            $currentAttr = $attributes[$i];
            $newResult = [];
            
            foreach ($result as $combo) {
                foreach ($currentAttr['values'] as $value) {
                    $newCombo = $combo;
                    $newCombo[] = [
                        'attr_id' => $currentAttr['attr_id'],
                        'attr_name' => $currentAttr['attr_name'],
                        'value_id' => $value['id'],
                        'value_name' => $value['name']
                    ];
                    $newResult[] = $newCombo;
                }
            }
            
            $result = $newResult;
        }
        
        return $result;
    }

    /**
     * 获取属性值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAttrValues(Request $request)
    {
        $attrId = $request->input('attr_id');
        
        if (!$attrId) {
            return response()->json([
                'status' => false,
                'message' => '属性ID不能为空'
            ]);
        }
        
        $attr = TecBanhuaAttrModel::with('values')->find($attrId);
        if (!$attr) {
            return response()->json([
                'status' => false,
                'message' => '属性不存在'
            ]);
        }
        
        return response()->json([
            'status' => true,
            'data' => [
                'attr' => [
                    'id' => $attr->id,
                    'name' => $attr->name
                ],
                'values' => $attr->values->map(function ($value) {
                    return [
                        'id' => $value->id,
                        'name' => $value->name
                    ];
                })
            ]
        ]);
    }

    /**
     * 覆盖父类的update方法，确保sku字段在更新前被正确设置
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update($id)
    {
        \Illuminate\Support\Facades\Log::info('产品更新请求', [
            'id' => $id,
            'all_inputs' => request()->all()
        ]);
        
        // 获取SKU组合数据
        $rawData = request()->input('_sku_combinations_data');
        
        if (!empty($rawData)) {
            try {
                // 获取基本信息
                $categoryId = request()->input('category_id');
                $unitId = request()->input('unit_id');
                $imagePath = request()->input('image');
                $attrValueIds = request()->input('attr_value_ids');
                $name = request()->input('common_name');
                $pid = request()->input('common_pid');
                
                // 确保attr_value_ids是有效数组，过滤掉null值
                if (is_array($attrValueIds)) {
                    $attrValueIds = array_filter($attrValueIds, function($value) {
                        return $value !== null;
                    });
                }
                
                \Illuminate\Support\Facades\Log::info('产品基本信息', [
                    'category_id' => $categoryId,
                    'unit_id' => $unitId,
                    'image' => $imagePath,
                    'attr_value_ids' => $attrValueIds,
                    'name' => $name,
                    'pid' => $pid
                ]);
                
                // 首先删除相关的旧SKU
                $currentSku = TecBanhuaProductSkuModel::find($id);
                if ($currentSku) {
                    $oldName = $currentSku->name;
                    $oldPid = $currentSku->pid;
                    
                    // 获取相同名称和PID的所有SKU（包括当前正在编辑的SKU）
                    $relatedSkus = TecBanhuaProductSkuModel::where('name', $oldName)
                        ->where('pid', $oldPid)
                        ->get();
                    
                    // 解析SKU组合数据以更新各个SKU的信息
                    try {
                        $skuData = json_decode($rawData, true);
                        
                        if (is_array($skuData) && !empty($skuData)) {
                            // 创建SKU代码到数据的映射
                            $skuMap = [];
                            foreach ($skuData as $data) {
                                if (isset($data['sku'])) {
                                    $skuMap[$data['sku']] = $data;
                                }
                            }
                            
                            // 更新每个相关的SKU
                            foreach ($relatedSkus as $sku) {
                                // 更新基本信息
                                $sku->category_id = $categoryId ?: $sku->category_id;
                                $sku->unit_id = $unitId ?: $sku->unit_id;
                                $sku->name = $name;
                                $sku->pid = $pid;
                                $sku->image = $imagePath ?: $sku->image;
                                
                                // 如果在提交的数据中找到匹配的SKU，更新JAN和价格
                                if (isset($skuMap[$sku->sku])) {
                                    $data = $skuMap[$sku->sku];
                                    $sku->jan = $data['jan'] ?? $sku->jan;
                                    $sku->price = $data['price'] ?? $sku->price;
                                }
                                
                                $sku->save();
                                
                                \Illuminate\Support\Facades\Log::info('更新SKU记录', [
                                    'id' => $sku->id,
                                    'sku' => $sku->sku,
                                    'name' => $sku->name,
                                    'pid' => $sku->pid,
                                    'jan' => $sku->jan,
                                    'price' => $sku->price
                                ]);
                            }
                            
                            // 更新成功后重定向到列表页面
                            return \Dcat\Admin\Admin::json([
                                'status'  => true,
                                'message' => '更新成功',
                                'redirect' => admin_url('banhua/skus'),
                            ]);
                        } else {
                            admin_toastr('SKU数据解析失败', 'error');
                            return redirect()->back()->withErrors('SKU数据解析失败');
                        }
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('更新SKU记录失败', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        throw $e;
                    }
                } else {
                    admin_toastr('未找到要编辑的SKU记录', 'error');
                    return redirect()->back()->withErrors('未找到要编辑的SKU记录');
                }
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('更新产品数据失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                admin_toastr('更新失败: ' . $e->getMessage(), 'error');
                return redirect()->back()->withErrors($e->getMessage());
            }
        } else {
            // 如果没有SKU组合数据，提示错误
            admin_toastr('没有SKU组合数据', 'error');
            return redirect()->back()->withErrors('请生成SKU组合数据');
        }
    }

    /**
     * 获取属性值文本
     *
     * @return string
     */
    public function getAttrValuesTextAttribute()
    {
        try {
            if (empty($this->attr_value_ids)) {
                return '';
            }
            
            // 确保attr_value_ids是数组
            $attrValueIds = $this->attr_value_ids;
            if (is_string($attrValueIds)) {
                $attrValueIds = json_decode($attrValueIds, true);
            }
            
            if (empty($attrValueIds) || !is_array($attrValueIds)) {
                return '';
            }
            
            // 获取所有属性值
            $attrValues = \App\Models\Banhua\TecBanhuaAttrValueModel::whereIn('id', $attrValueIds)
                ->with('attr')
                ->get();
                
            if ($attrValues->isEmpty()) {
                return '';
            }
            
            // 只显示属性值，不显示属性名
            $formattedValues = [];
            foreach ($attrValues as $attrValue) {
                $formattedValues[] = $attrValue->name;
            }
            
            return implode(', ', $formattedValues);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取属性值文本异常', [
                'error' => $e->getMessage(),
                'sku_id' => $this->id
            ]);
            return '';
        }
    }

    /**
     * 清除产品列表缓存
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearProductCache()
    {
        try {
            // 这里可以添加清除缓存的代码
            // 由于我们已经移除了缓存逻辑，这里只需要记录日志并返回
            \Illuminate\Support\Facades\Log::info('手动刷新产品列表');
            
            admin_toastr('刷新成功', 'success');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('刷新操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            admin_toastr('刷新失败: ' . $e->getMessage(), 'error');
        }
        
        return redirect()->back();
    }
    
    /**
     * 导出SKU数据
     *
     * @param Request $request
     * @return mixed
     */
    public function exportSku(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaProductSkuExportAction();
        return $action->handle($request);
    }
    
    /**
     * 导入SKU数据
     *
     * @param Request $request
     * @return mixed
     */
    public function importSku(Request $request)
    {
        $action = new \App\Admin\Actions\Grid\Banhua\TecBanhuaProductSkuImportAction();
        return $action->handle($request);
    }
    
    /**
     * 下载SKU导入模板
     *
     * @return \Illuminate\Http\Response
     */
    public function downloadImportTemplate()
    {
        try {
            // 禁用Pjax
            if (request()->pjax()) {
                return response()->json([
                    'status' => false,
                    'message' => '请刷新页面后重试'
                ]);
            }
            
            // 创建新的Excel文档
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            
            // ===== 第一个工作表：导入模板 =====
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('导入模板');
            
            // 添加说明文字
            $sheet->setCellValue('A1', '阪画产品SKU导入模板');
            $sheet->mergeCells('A1:I1');
            $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            $sheet->setCellValue('A2', '填写说明：');
            $sheet->mergeCells('A2:I2');
            $sheet->getStyle('A2')->getFont()->setBold(true);
            
            $sheet->setCellValue('A3', '1. SKU、名称、图库ID、分类、单位为必填项');
            $sheet->mergeCells('A3:I3');
            
            $sheet->setCellValue('A4', '2. 分类必须填写系统中已存在的分类名称，请参考"分类列表"工作表');
            $sheet->mergeCells('A4:I4');
            
            $sheet->setCellValue('A5', '3. 单位必须填写系统中已存在的单位名称，请参考"单位列表"工作表');
            $sheet->mergeCells('A5:I5');
            
            $sheet->setCellValue('A6', '4. 属性组合必须填写系统中已存在的属性值名称，多个属性值用英文逗号分隔，请参考"属性值列表"工作表');
            $sheet->mergeCells('A6:I6');
            
            // 设置表头
            $headers = [
                'SKU', '名称', '图库ID', 'JAN', '价格', '分类', '单位', '属性组合', '图片URL'
            ];
            
            $row = 8;
            foreach ($headers as $col => $header) {
                $sheet->setCellValueByColumnAndRow($col + 1, $row, $header);
                $sheet->getStyleByColumnAndRow($col + 1, $row)->getFont()->setBold(true);
                $sheet->getStyleByColumnAndRow($col + 1, $row)->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('CCCCCC');
            }
            
            // 从数据库获取实际的分类、单位和属性值
            $realCategories = \App\Models\Banhua\TecBanhuaProductCategoryModel::take(3)->pluck('title')->toArray();
            $realUnits = \App\Models\Banhua\TecBanhuaProductUnitModel::take(3)->pluck('unit_name')->toArray();
            
            // 获取属性和属性值
            $attrValuesByAttr = [];
            $attributes = \App\Models\Banhua\TecBanhuaAttrModel::with(['values' => function($query) {
                $query->orderBy('sort', 'asc');
            }])->orderBy('sort', 'asc')->get();
            
            foreach ($attributes as $attr) {
                $attrValues = $attr->values->pluck('name')->toArray();
                if (!empty($attrValues)) {
                    $attrValuesByAttr[$attr->name] = $attrValues;
                }
            }
            
            // 确保我们有足够的数据来创建示例
            $defaultCategory = !empty($realCategories) ? $realCategories[0] : '画材';
            $defaultUnit = !empty($realUnits) ? $realUnits[0] : '件';
            
            // 创建属性值组合示例
            $attrCombinations = [];
            
            // 单一属性值示例
            if (!empty($attrValuesByAttr)) {
                $firstAttrName = array_key_first($attrValuesByAttr);
                if (!empty($attrValuesByAttr[$firstAttrName])) {
                    $attrCombinations[] = $attrValuesByAttr[$firstAttrName][0];
                }
            }
            
            // 两个属性值组合示例
            if (count($attrValuesByAttr) >= 2) {
                $attrNames = array_keys($attrValuesByAttr);
                $firstAttr = $attrNames[0];
                $secondAttr = $attrNames[1];
                
                if (!empty($attrValuesByAttr[$firstAttr]) && !empty($attrValuesByAttr[$secondAttr])) {
                    $attrCombinations[] = $attrValuesByAttr[$firstAttr][0] . ', ' . $attrValuesByAttr[$secondAttr][0];
                }
            }
            
            // 三个属性值组合示例
            if (count($attrValuesByAttr) >= 3) {
                $attrNames = array_keys($attrValuesByAttr);
                $firstAttr = $attrNames[0];
                $secondAttr = $attrNames[1];
                $thirdAttr = $attrNames[2];
                
                if (!empty($attrValuesByAttr[$firstAttr]) && !empty($attrValuesByAttr[$secondAttr]) && !empty($attrValuesByAttr[$thirdAttr])) {
                    $attrCombinations[] = $attrValuesByAttr[$firstAttr][0] . ', ' . $attrValuesByAttr[$secondAttr][0] . ', ' . $attrValuesByAttr[$thirdAttr][0];
                }
            }
            
            // 确保我们有足够的属性组合
            while (count($attrCombinations) < 5) {
                $attrCombinations[] = count($attrCombinations) == 0 ? 'PS' : 'PS, 油画布, 40*60';
            }
            
            // 示例数据
            $exampleData = [
                ['BH-EXAMPLE-001', 'PS画材', 'PS001', 'JAN123456', '100', $defaultCategory, $defaultUnit, $attrCombinations[0], 'https://example.com/ps-material.jpg'],
                ['BH-EXAMPLE-002', '油画布', 'OIL002', 'JAN234567', '150', $defaultCategory, $defaultUnit, $attrCombinations[1], 'https://example.com/oil-canvas.jpg'],
                ['BH-EXAMPLE-003', '高级油画布', 'OIL003', 'JAN345678', '200', $defaultCategory, $defaultUnit, $attrCombinations[2], 'https://example.com/premium-oil-canvas.jpg'],
            ];
            
            // 如果有多个分类和单位，添加更多示例
            if (count($realCategories) > 1 && count($realUnits) > 1) {
                $exampleData[] = [
                    'BH-EXAMPLE-004', 
                    '水彩纸', 
                    'WC004', 
                    'JAN456789', 
                    '120', 
                    $realCategories[1], 
                    $realUnits[1], 
                    $attrCombinations[3], 
                    'https://example.com/watercolor-paper.jpg'
                ];
            }
            
            if (count($realCategories) > 2 && count($realUnits) > 2) {
                $exampleData[] = [
                    'BH-EXAMPLE-005', 
                    '专业丙烯颜料', 
                    'ACR005', 
                    'JAN567890', 
                    '80', 
                    $realCategories[2], 
                    $realUnits[2], 
                    $attrCombinations[4], 
                    'https://example.com/acrylic-paint.jpg'
                ];
            }
            
            foreach ($exampleData as $rowIndex => $rowData) {
                foreach ($rowData as $colIndex => $value) {
                    $sheet->setCellValueByColumnAndRow($colIndex + 1, $row + $rowIndex + 1, $value);
                }
            }
            
            // 调整列宽
            foreach (range('A', 'I') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
            
            // ===== 第二个工作表：分类列表 =====
            $categorySheet = $spreadsheet->createSheet();
            $categorySheet->setTitle('分类列表');
            
            // 设置表头
            $categorySheet->setCellValue('A1', '分类ID');
            $categorySheet->setCellValue('B1', '分类名称');
            $categorySheet->getStyle('A1:B1')->getFont()->setBold(true);
            $categorySheet->getStyle('A1:B1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            
            // 获取所有分类
            $categories = \App\Models\Banhua\TecBanhuaProductCategoryModel::orderBy('id')->get();
            
            $row = 2;
            foreach ($categories as $category) {
                $categorySheet->setCellValue('A' . $row, $category->id);
                $categorySheet->setCellValue('B' . $row, $category->title);
                $row++;
            }
            
            // 调整列宽
            $categorySheet->getColumnDimension('A')->setAutoSize(true);
            $categorySheet->getColumnDimension('B')->setAutoSize(true);
            
            // ===== 第三个工作表：单位列表 =====
            $unitSheet = $spreadsheet->createSheet();
            $unitSheet->setTitle('单位列表');
            
            // 设置表头
            $unitSheet->setCellValue('A1', '单位ID');
            $unitSheet->setCellValue('B1', '单位名称');
            $unitSheet->getStyle('A1:B1')->getFont()->setBold(true);
            $unitSheet->getStyle('A1:B1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            
            // 获取所有单位
            $units = \App\Models\Banhua\TecBanhuaProductUnitModel::orderBy('sort')->get();
            
            $row = 2;
            foreach ($units as $unit) {
                $unitSheet->setCellValue('A' . $row, $unit->id);
                $unitSheet->setCellValue('B' . $row, $unit->unit_name);
                $row++;
            }
            
            // 调整列宽
            $unitSheet->getColumnDimension('A')->setAutoSize(true);
            $unitSheet->getColumnDimension('B')->setAutoSize(true);
            
            // ===== 第四个工作表：属性值列表 =====
            $attrValueSheet = $spreadsheet->createSheet();
            $attrValueSheet->setTitle('属性值列表');
            
            // 设置表头
            $attrValueSheet->setCellValue('A1', '属性名称');
            $attrValueSheet->setCellValue('B1', '属性值名称');
            $attrValueSheet->setCellValue('C1', '属性值ID');
            $attrValueSheet->getStyle('A1:C1')->getFont()->setBold(true);
            $attrValueSheet->getStyle('A1:C1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');
            
            // 获取所有属性和属性值
            $attributes = \App\Models\Banhua\TecBanhuaAttrModel::with(['values' => function($query) {
                $query->orderBy('sort', 'asc');
            }])->orderBy('sort', 'asc')->get();
            
            $row = 2;
            foreach ($attributes as $attr) {
                foreach ($attr->values as $value) {
                    $attrValueSheet->setCellValue('A' . $row, $attr->name);
                    $attrValueSheet->setCellValue('B' . $row, $value->name);
                    $attrValueSheet->setCellValue('C' . $row, $value->id);
                    $row++;
                }
            }
            
            // 调整列宽
            $attrValueSheet->getColumnDimension('A')->setAutoSize(true);
            $attrValueSheet->getColumnDimension('B')->setAutoSize(true);
            $attrValueSheet->getColumnDimension('C')->setAutoSize(true);
            
            // ===== 第五个工作表：填写指南 =====
            $guideSheet = $spreadsheet->createSheet();
            $guideSheet->setTitle('填写指南');
            
            // 设置标题
            $guideSheet->setCellValue('A1', '阪画产品SKU导入指南');
            $guideSheet->mergeCells('A1:B1');
            $guideSheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
            $guideSheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            // 添加指南内容
            $guideContent = [
                ['字段', '说明'],
                ['SKU', '产品的唯一编码，必填，不能与系统中已有SKU重复'],
                ['名称', '产品名称，必填'],
                ['图库ID', '产品在图库中的ID，必填'],
                ['JAN', '日本商品编码，选填'],
                ['价格', '产品价格，选填，必须为数字'],
                ['分类', '产品分类，必填，必须是系统中已存在的分类名称，参见"分类列表"工作表'],
                ['单位', '产品单位，必填，必须是系统中已存在的单位名称，参见"单位列表"工作表'],
                ['属性组合', '产品属性值，选填，多个属性值用英文逗号分隔，必须是系统中已存在的属性值名称，参见"属性值列表"工作表'],
                ['图片URL', '产品图片的URL地址，选填'],
            ];
            
            $row = 3;
            foreach ($guideContent as $rowIndex => $rowData) {
                foreach ($rowData as $colIndex => $value) {
                    $guideSheet->setCellValueByColumnAndRow($colIndex + 1, $row + $rowIndex, $value);
                    if ($rowIndex === 0) {
                        $guideSheet->getStyleByColumnAndRow($colIndex + 1, $row + $rowIndex)->getFont()->setBold(true);
                        $guideSheet->getStyleByColumnAndRow($colIndex + 1, $row + $rowIndex)->getFill()
                            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                            ->getStartColor()->setRGB('CCCCCC');
                    }
                }
            }
            
            // 添加常见问题
            $row = 15;
            $guideSheet->setCellValue('A' . $row, '常见问题');
            $guideSheet->mergeCells('A' . $row . ':B' . $row);
            $guideSheet->getStyle('A' . $row)->getFont()->setBold(true);
            
            $faqContent = [
                ['问题', '解决方案'],
                ['导入失败：分类不存在', '请检查分类名称是否与"分类列表"工作表中的名称完全一致'],
                ['导入失败：单位不存在', '请检查单位名称是否与"单位列表"工作表中的名称完全一致'],
                ['导入失败：SKU已存在', '请修改SKU编码，确保其在系统中唯一'],
                ['属性值未被识别', '请检查属性值名称是否与"属性值列表"工作表中的名称完全一致，多个属性值之间使用英文逗号分隔'],
            ];
            
            $row = 17;
            foreach ($faqContent as $rowIndex => $rowData) {
                foreach ($rowData as $colIndex => $value) {
                    $guideSheet->setCellValueByColumnAndRow($colIndex + 1, $row + $rowIndex, $value);
                    if ($rowIndex === 0) {
                        $guideSheet->getStyleByColumnAndRow($colIndex + 1, $row + $rowIndex)->getFont()->setBold(true);
                        $guideSheet->getStyleByColumnAndRow($colIndex + 1, $row + $rowIndex)->getFill()
                            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                            ->getStartColor()->setRGB('CCCCCC');
                    }
                }
            }
            
            // 调整列宽
            $guideSheet->getColumnDimension('A')->setWidth(15);
            $guideSheet->getColumnDimension('B')->setWidth(80);
            
            // 设置第一个工作表为活动工作表
            $spreadsheet->setActiveSheetIndex(0);
            
            // 记录下载操作
            \Illuminate\Support\Facades\Log::info('下载SKU导入模板(Excel格式)');
            
            // 创建Excel写入器
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'sku_import_template');
            $writer->save($tempFile);
            
            // 返回文件下载响应
            return response()->download($tempFile, 'banhua_sku_import_template.xlsx', [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="banhua_sku_import_template.xlsx"',
            ])->deleteFileAfterSend(true);
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('下载SKU导入模板失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '下载模板失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 通过图库ID获取图片信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGalleryImage(Request $request)
    {
        try {
            $galleryId = $request->input('gallery_id');
            
            // 查询图库信息
            $gallery = \App\Models\Banhua\TecBanhuaImageGalleryModel::where('image_id', $galleryId)->first();
            
            if (!$gallery) {
                return response()->json([
                    'status' => false,
                    'message' => '图库ID不存在',
                    'data' => []
                ]);
            }
            
            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => [
                    'id' => $gallery->id,
                    'image_id' => $gallery->image_id,
                    'name' => $gallery->name,
                    'path' => $gallery->path,
                    'remark' => $gallery->remark
                ]
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取图库图片失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取图片信息失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
} 