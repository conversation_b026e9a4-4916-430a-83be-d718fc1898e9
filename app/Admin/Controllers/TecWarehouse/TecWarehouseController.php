<?php

namespace App\Admin\Controllers\TecWarehouse;

use App\Admin\Repositories\TecWarehouseRepo;
use App\Models\TecWarehouseModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\Post\Restore;

class TecWarehouseController extends AdminController
{
    protected $title = '仓库管理';
    /**
     * 构建Grid网格.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecWarehouseRepo(), function (Grid $grid) {
            $grid->scrollbarX();
            
            // 定义列
            $grid->column('id')->sortable();
            $grid->column('name')
                ->width('100px')
                ->style('word-break: break-all; white-space: normal; ');
            $grid->column('code');
            $grid->column('area')->display(function ($value) {
                // 使用sprintf确保显示完整的小数位数，并支持排序
                return sprintf('%.2f', $value) . ' ㎡';
            })->sortable()
            ->style('word-break: break-all; white-space: normal;color: blue; ') 
            ->width('100px');
            $grid->column('address_jp');
            $grid->column('address_en');
            $grid->column('manager');
            $grid->column('contact_phone');
            $grid->column('landlord_name');
            $grid->column('landlord_phone');
            $grid->column('status')->using(config('purchase_config.warehouse_status'))->label([
                'idle' => 'primary',
                'normal' => 'success', 
                'full' => 'danger',
                'closed' => 'warning'
            ]); // 使用配置的仓库状态，并为不同状态设置不同的标签颜色

            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            // 设置操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableView(false); // 启用查看按钮
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 启用快速编辑按钮
                $actions->disableDelete(false); // 启用删除按钮
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(TecWarehouseModel::class));
                }
            });

            // 设置数据模型的排序
            $grid->model()->orderBy('id', 'asc'); // 按照id升序排序
        });
    }

    /**
     * 构建Show展示.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TecWarehouseRepo(), function (Show $show) {
            $show->field('id'); // 显示id字段
            $show->field('name'); // 显示name字段
            $show->field('code'); // 显示code字段
            $show->field('area')->as(function ($value) {
                return sprintf('%.2f', $value) . ' 平方米';
            }); // 使用sprintf确保显示完整的小数位数
            $show->field('address_jp'); // 显示address_jp字段
            $show->field('address_en'); // 显示address_en字段
            $show->field('manager'); // 显示manager字段
            $show->field('contact_phone'); // 显示contact_phone字段
            $show->field('landlord_name'); // 显示landlord_name字段
            $show->field('landlord_phone'); // 显示landlord_phone字段
            $show->field('status')->using(config('purchase_config.warehouse_status')); // 使用配置的状态
        });
    }

    /**
     * 构建Form表单.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecWarehouseRepo(), function (Form $form) {
            $form->display('id'); // 显示id字段（只读）

            // 使用两个列来分组表单字段
            $form->column(6, function (Form $form) {
                $form->text('name')->required(); // 输入name字段，必填
                $form->text('code')->required(); // 输入code字段，必填
                $form->decimal('area')->default(0)->help('仓库面积（平方米）'); // 使用decimal输入，默认值为0
                $form->text('address_jp')->required(); // 输入address_jp字段，必填
                $form->text('address_en')->required(); // 输入address_en字段，必填
            });

            $form->column(6, function (Form $form) {
                $form->text('manager'); // 输入manager字段
                $form->text('contact_phone'); // 输入contact_phone字段
                $form->text('landlord_name'); // 输入landlord_name字段
                $form->text('landlord_phone'); // 输入landlord_phone字段
                $form->select('status')->options(config('purchase_config.warehouse_status'))->required(); // 使用配置的仓库状态
            });

            // 隐藏不需要的按钮和导航
            $form->disableResetButton(); // 隐藏重置按钮
            $form->disableViewCheck(); // 隐藏查看按钮
            $form->disableEditingCheck(); // 隐藏继续编辑按钮
        });
    }

    /**
     * 获取所有仓库列表 API
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function warehouseList()
    {
        return response()->json(
            TecWarehouseModel::select('id', 'name')->get()
        );
    }
}
