<?php

namespace App\Admin\Controllers\TecWarehouse;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\BatchActions;
use App\Models\TecWarehouseModel;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\TecWarehouseLocationModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Dcat\Admin\Show;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use App\Admin\Actions\Grid\WarehouseLocation\TecWarehouseLocationBulkDeleteAction;
use App\Admin\Actions\Grid\WarehouseLocation\TecWarehouseLocationBulkRestoreAction;
use App\Admin\Actions\Grid\WarehouseLocation\TecWarehouseLocationBulkSoftDeleteAction;
use App\Admin\Actions\Grid\WarehouseLocation\TecWarehouseLocationBulkImportAction;

/**
 * 仓库位置管理控制器
 * 
 * 负责仓库位置的增删改查操作，包括软删除和批量操作
 * 
 * @package App\Admin\Controllers
 */
class TecWarehouseLocationController extends AdminController
{
    protected $title = '仓库货位管理';
    /**
     * 构建仓库位置列表网格
     * 
     * 根据不同作用域（正常/回收站）展示仓库位置数据
     * 支持复杂的筛选、排序和批量操作
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TecWarehouseLocationModel(), function (Grid $grid) {
            // 根据当前作用域决定查询范围
            $isTrashedScope = request('_scope_') === 'trashed';
            if ($isTrashedScope) {
                // 仅显示软删除的记录
                $grid->model()->onlyTrashed();
            } else {
                // 默认不显示软删除的记录
                $grid->model()->whereNull('deleted_at');
            }

            // 预加载仓库关系，避免N+1查询
            $grid->model()->with(['warehouse']);

            // 仓库选择
            $grid->column('warehouse_id', '仓库')
                ->display(function($warehouseId) {
                    return $this->warehouse->name ?? '-';
                })
                ->sortable();
            
            $grid->column('area_number', '区域号')->sortable();
            $grid->column('shelf_number', '货架号')->sortable();
            $grid->column('level_number', '层号')->sortable();
            
            // 状态显示
            $grid->column('status', '状态')
                ->using(config('purchase_config.location_status'))
                ->label([
                    'available' => 'success',
                    'occupied' => 'warning',
                    'disabled' => 'danger',
                ]);

            // 默认排序
            $grid->model()->orderBy('warehouse_id')
                 ->orderBy('area_number')
                 ->orderBy('shelf_number')
                 ->orderBy('level_number');

            // 筛选
            $grid->filter(function (Grid\Filter $filter) {
                // 仓库选择，支持精确匹配（优化：只查询需要的字段）
                $filter->equal('warehouse_id', '仓库')
                    ->select(TecWarehouseModel::select('id', 'name')->whereNull('deleted_at')->pluck('name', 'id'))
                    ->load('area_number', '/api/warehouse-areas')
                    ->width(3);

                // 区域选择，依赖于仓库选择
                $filter->equal('area_number', '区域')
                    ->select([])
                    ->load('shelf_number', '/api/shelves-by-area')
                    ->width(3);
                
                // 货架号过滤  
                $filter->equal('shelf_number', '货架号')
                    ->select([])
                    ->width(3);
                
                // 添加软删除的筛选范围
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            // 工具栏操作
            $grid->tools(function (Grid\Tools $tools) use ($isTrashedScope) {
                if (!$isTrashedScope) {
                    $tools->append(new TecWarehouseLocationBulkImportAction());
                } else {
                    $tools->append(new TecWarehouseLocationBulkRestoreAction());
                }
            });

            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batchActions) use ($isTrashedScope) {
                if ($isTrashedScope) {
                    $batchActions->add(new TecWarehouseLocationBulkRestoreAction());
                    $batchActions->add(new TecWarehouseLocationBulkDeleteAction());
                } else {
                    $batchActions->add(new TecWarehouseLocationBulkSoftDeleteAction());
                    $batchActions->add(new TecWarehouseLocationBulkDeleteAction());
                }
            });

                // 操作列
            $grid->actions(function (Grid\Displayers\Actions $actions) use ($isTrashedScope) {
                if ($isTrashedScope) {
                    // 回收站视图：禁用所有操作
                    $actions->disableEdit();
                    $actions->disableQuickEdit();
                    $actions->disableDelete();
                } else {
                    // 正常视图：允许删除，禁用编辑
                    $actions->disableEdit();
                    $actions->disableQuickEdit();
                    $actions->disableDelete(false);
                }
            });

            // 移除可能导致问题的脚本
            // $grid->script = null; // 删除这一行
        });
    }

    /**
     * 构建仓库位置表单
     * 
     * 支持创建和编辑仓库位置
     * 包含严格的输入验证
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TecWarehouseLocationModel(), function (Form $form) {
            // 仓库选择
            $form->select('warehouse_id', '仓库')
                ->options(TecWarehouseModel::pluck('name', 'id'))
                ->required();
    
            $form->text('area_number', '区域号')
                ->required()
                ->creationRules([
                    function ($attribute, $value, $fail) {
                        $exists = TecWarehouseLocationModel::where('warehouse_id', request('warehouse_id'))
                            ->where('area_number', $value)
                            ->where('shelf_number', request('shelf_number'))
                            ->where('level_number', request('level_number'))
                            ->exists();
                        
                        if ($exists) {
                            $warehouseName = TecWarehouseModel::find(request('warehouse_id'))->name ?? '未知仓库';
                            $fail(sprintf(
                                "仓库 %s (区域号: %s, 货架号: %s, 层号: %s) 已存在", 
                                $warehouseName, 
                                $value, 
                                request('shelf_number'), 
                                request('level_number')
                            ));
                        }
                    }
                ])
                ->updateRules([
                    function ($attribute, $value, $fail) use ($form) {
                        $exists = TecWarehouseLocationModel::where('warehouse_id', $form->warehouse_id)
                            ->where('area_number', $value)
                            ->where('shelf_number', $form->shelf_number)
                            ->where('level_number', $form->level_number)
                            // 排除当前记录的所有情况
                            ->where('id', '!=', $form->model()->id)
                            ->exists();
                        
                        if ($exists) {
                            $warehouseName = TecWarehouseModel::find($form->warehouse_id)->name ?? '未知仓库';
                            $fail(sprintf(
                                "仓库 %s (区域号: %s, 货架号: %s, 层号: %s) 已存在", 
                                $warehouseName, 
                                $value, 
                                $form->shelf_number, 
                                $form->level_number
                            ));
                        }
                    }
                ]);
    
            $form->text('shelf_number', '货架号')->required();
            $form->text('level_number', '层号')->required();
            
            // 状态选择
            $form->select('status', '状态')
                ->options(config('purchase_config.location_status'))
                ->default('available')
                ->required();
        });
    }

    /**
     * 批量创建仓库位置
     * 
     * 支持批量创建区域、货架和层级位置
     * 包含严格的输入验证
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkCreate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'warehouse_id' => 'required|exists:t_warehouses,id',
            'type' => 'required|in:area,shelf,level',
            'quantity' => 'required|integer|min:1|max:100',
            'shelf_area_id' => 'nullable|required_if:type,shelf',
            'level_area_id' => 'nullable|required_if:type,level',
            'level_shelf_id' => 'nullable|required_if:type,level',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }

        $type = $request->input('type');
        $warehouseId = $request->input('warehouse_id');
        $quantity = $request->input('quantity');
        $prefix = $request->input('prefix', '');
        $generateShelves = $request->input('generate_shelves', '0') === '1';

        $createdLocations = 0;
        $skippedLocations = 0;

        try {
            switch ($type) {
                case 'area':
                    $startNumber = $request->input('start_number', 1);
                    for ($i = 0; $i < $quantity; $i++) {
                        $areaNumber = $prefix . ($startNumber + $i);
                        $location = $this->createOrSkipLocation([
                            'warehouse_id' => $warehouseId,
                            'area_number' => $areaNumber,
                            'shelf_number' => '1',
                            'level_number' => '1',
                            'status' => 'available'
                        ]);
                        $location ? $createdLocations++ : $skippedLocations++;
                    }
                    break;

                case 'shelf':
                    $startNumber = $request->input('shelf_number', 1);
                    $areaId = $request->input('shelf_area_id');
                    
                    for ($i = 0; $i < $quantity; $i++) {
                        $shelfNumber = $generateShelves ? ($startNumber + $i) : $startNumber;
                        $location = $this->createOrSkipLocation([
                            'warehouse_id' => $warehouseId,
                            'area_number' => $areaId,
                            'shelf_number' => $prefix . $shelfNumber,
                            'level_number' => '1',
                            'status' => 'available'
                        ]);
                        $location ? $createdLocations++ : $skippedLocations++;
                    }
                    break;

                case 'level':
                    $startNumber = $request->input('level_number', 1);
                    $areaId = $request->input('level_area_id');
                    $shelfId = $request->input('level_shelf_id');
                    
                    for ($i = 0; $i < $quantity; $i++) {
                        $levelNumber = $startNumber + $i;
                        $location = $this->createOrSkipLocation([
                            'warehouse_id' => $warehouseId,
                            'area_number' => $areaId,
                            'shelf_number' => $shelfId,
                            'level_number' => $prefix . $levelNumber,
                            'status' => 'available'
                        ]);
                        $location ? $createdLocations++ : $skippedLocations++;
                    }
                    break;
            }

            return response()->json([
                'status' => true,
                'message' => sprintf(
                    '批量创建成功：共创建 %d 个位置，跳过 %d 个重复位置', 
                    $createdLocations, 
                    $skippedLocations
                )
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '批量创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建或跳过仓库位置
     * 
     * 如果位置已经存在，则跳过创建
     *
     * @param array $locationData
     * @return TecWarehouseLocationModel|null
     */
    private function createOrSkipLocation($locationData)
    {
        $existingLocation = TecWarehouseLocationModel::where([
            'warehouse_id' => $locationData['warehouse_id'],
            'area_number' => $locationData['area_number'],
            'shelf_number' => $locationData['shelf_number'],
            'level_number' => $locationData['level_number']
        ])->first();

        if ($existingLocation) {
            return null;
        }

        return TecWarehouseLocationModel::create($locationData);
    }

    /**
     * 批量导入模态框渲染方法
     * 
     * 渲染批量导入模态框的视图
     *
     * @return View
     */
    public function bulkImportModal()
    {
        $warehouses = TecWarehouseModel::select('id', 'name')->get();
        $areas = TecWarehouseLocationModel::distinct()
            ->select('area_number')
            ->where('area_number', '!=', '0')
            ->where('area_number', '!=', '')
            ->get();
        
        if (request()->ajax()) {
            return response()->json([
                'areas_count' => $areas->count(),
                'areas' => $areas->toArray()
            ]);
        } else {
            if ($areas->isEmpty()) {
                $areas = collect();
            }
            
            return view('admin.warehouse-location-import', compact('warehouses', 'areas'));
        }
    }

    /**
     * 根据区域号获取货架列表 API
     * 
     * 返回指定区域号的货架列表
     *
     * @param string $areaNumber
     * @return JsonResponse
     */
    public function shelvesByAreaApi($areaNumber)
    {
        $shelves = TecWarehouseLocationModel::where('area_number', $areaNumber)
            ->select('id', 'shelf_number')
            ->distinct('shelf_number')
            ->get()
            ->map(function($item) {
                return [
                    'id' => $item->id,
                    'shelf_number' => $item->shelf_number
                ];
            });

        return response()->json($shelves);
    }

    /**
     * 根据区域获取货架列表
     * 
     * 返回指定区域的货架列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function shelvesByArea(Request $request)
    {
        $areaNumber = $request->input('area_number', $request->input('q'));
        
        // 记录请求参数
        Log::info('shelvesByArea 请求参数', [
            'area_number' => $areaNumber,
            'all_params' => $request->all()
        ]);
        
        if (!$areaNumber) {
            return response()->json([
                'status' => false,
                'message' => '区域ID不能为空',
                'data' => ['shelves' => []]
            ]);
        }
        
        try {
            $shelves = TecWarehouseLocationModel::where('area_number', $areaNumber)
                ->whereNull('deleted_at')
                ->distinct()
                ->pluck('shelf_number')
                ->toArray();
            
            Log::info('shelvesByArea 查询结果', [
                'area_id' => $areaNumber,
                'shelves_count' => count($shelves)
            ]);
            
            return response()->json([
                'status' => true,
                'data' => [
                    'shelves' => $shelves
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('shelvesByArea 错误', [
                'area_id' => $areaNumber,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取货架列表失败: ' . $e->getMessage(),
                'data' => ['shelves' => []]
            ], 500);
        }
    }

    /**
     * 根据仓库获取区域号列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAreasByWarehouse(Request $request)
    {
        $warehouseId = $request->input('warehouse_id') ?? $request->input('q');
        
        if (!$warehouseId) {
            return response()->json([]);
        }
        
        // 查询指定仓库的所有区域
        $areas = TecWarehouseLocationModel::where('warehouse_id', $warehouseId)
            ->distinct()
            ->select('area_number')
            ->get()
            ->map(function($area) {
                return [
                    'id' => $area->area_number,
                    'text' => $area->area_number
                ];
            });
        
        Log::info('区域列表结果', [
            'warehouse_id' => $warehouseId, 
            'areas_count' => $areas->count()
        ]);
        
        return response()->json($areas);
    }

    /**
     * 区域列表的路由方法
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function areas(Request $request)
    {
        return $this->getAreasByWarehouse($request);
    }

    /**
     * 根据仓库和区域获取货架号
     * 
     * 返回指定仓库和区域的货架号列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function shelvesByWarehouseAndArea(Request $request)
    {
        $params = $request->all();
        $warehouseId = $params['warehouse_id'] ?? $params['q'] ?? null;
        $areaNumber = $params['area_number'] ?? $params['area'] ?? null;
        
        if (!$warehouseId) {
            return response()->json([]);
        }
        
        $query = TecWarehouseLocationModel::where('warehouse_id', $warehouseId);
        
        if ($areaNumber) {
            $query->where('area_number', $areaNumber);
        }
        
        $shelves = $query->distinct()
            ->pluck('shelf_number', 'shelf_number')
            ->map(function($shelf) {
                return ['id' => $shelf, 'text' => $shelf];
            })
            ->values()
            ->toArray();
        
        return response()->json($shelves);
    }

    /**
     * 批量彻底删除数据（无论是否在回收站）
     *
     * @return JsonResponse
     */
    public function bulkDelete(Request $request)
    {
        $ids = $request->get('ids');

        if (empty($ids)) {
            return response()->json([
                'status'  => false,
                'message' => '请选择要删除的记录'
            ]);
        }

        try {
            $deletedCount = TecWarehouseLocationModel::withTrashed()
                ->whereIn('id', $ids)
                ->forceDelete();

            return response()->json([
                'status'  => true,
                'message' => "成功删除 {$deletedCount} 条记录"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status'  => false,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 恢复软删除的数据
     * 
     * 根据 ID 恢复软删除的数据
     *
     * @param int $id
     * @return JsonResponse
     */
    public function restore($id)
    {
        try {
            $model = TecWarehouseLocationModel::withTrashed()->findOrFail($id);
            $model->restore();
            return response()->json([
                'status'  => true,
                'message' => '恢复成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status'  => false,
                'message' => '恢复失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量恢复软删除的数据
     * 
     * 根据 ID 列表批量恢复软删除的数据
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function batchRestore(Request $request)
    {
        $ids = $request->input('ids', []);
        
        if (empty($ids)) {
            return response()->json([
                'status' => false,
                'message' => '请选择要恢复的记录'
            ]);
        }

        $successCount = 0;
        $failedCount = 0;
        $errorMessages = [];

        foreach ($ids as $id) {
            try {
                TecWarehouseLocationModel::restoreById($id);
                $successCount++;
            } catch (\Exception $e) {
                $failedCount++;
                $errorMessages[] = "ID {$id}: " . $e->getMessage();
            }
        }

        $message = "成功恢复 {$successCount} 条记录";
        if ($failedCount > 0) {
            $message .= "，{$failedCount} 条记录恢复失败：" . implode('; ', $errorMessages);
        }

        return response()->json([
            'status' => $failedCount === 0,
            'message' => $message
        ]);
    }

    /**
     * 批量永久删除回收站中的数据
     *
     * @return JsonResponse
     */
    public function bulkForceDelete(Request $request)
    {
        $ids = $request->get('ids');

        if (empty($ids)) {
            return response()->json([
                'status'  => false,
                'message' => '请选择要删除的记录'
            ]);
        }

        try {
            $deletedCount = TecWarehouseLocationModel::onlyTrashed()
                ->whereIn('id', $ids)
                ->forceDelete();

            return response()->json([
                'status'  => true,
                'message' => "成功删除 {$deletedCount} 条记录"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status'  => false,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 显示仓库位置详情
     * 
     * 根据 ID 显示仓库位置的详细信息
     *
     * @param mixed $id
     * @return Content
     */
    public function detail($id)
    {
        return Show::make($id, new TecWarehouseLocationModel(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('warehouse.name', '仓库名称');
            $show->field('area_number', '区域号');
            $show->field('shelf_number', '货架号');
            $show->field('level_number', '层号');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            if (method_exists($this->model(), 'trashed')) {
                $show->field('deleted_at', '删除时间');
            }
        });
    }

    /**
     * 根据区域获取货架列表
     * 
     * 返回指定区域的货架列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getShelvesByArea(Request $request)
    {
        $areaNumber = $request->input('q');
        
        Log::info('获取货架列表参数', compact('areaNumber'));
        
        if (!$areaNumber) {
            return response()->json([]);
        }
        
        // 获取货架列表并构建键值对
        $shelves = TecWarehouseLocationModel::where('area_number', $areaNumber)
            ->distinct()
            ->pluck('shelf_number')
            ->mapWithKeys(function ($item) {
                return [$item => ['id' => $item, 'text' => $item]];
            })
            ->toArray();
        
        Log::info('货架列表结果', compact('shelves'));
        
        return response()->json($shelves);
    }

    /**
     * 根据仓库、区域和货架获取货位列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getLocationsByWarehouseAreaShelf(Request $request)
    {
        $params = $request->all();
        $warehouseId = $params['warehouse_id'] ?? $params['q'] ?? null;
        $areaNumber = $params['area_number'] ?? $params['area'] ?? null;
        $shelfNumber = $params['shelf_number'] ?? $params['shelf'] ?? null;
        
        // 记录请求参数
        Log::info('获取货位列表(按区域货架) - 请求参数', [
            'warehouse_id' => $warehouseId,
            'area_number' => $areaNumber,
            'shelf_number' => $shelfNumber,
            'all_params' => $params
        ]);
        
        if (!$warehouseId || !$areaNumber || !$shelfNumber) {
            Log::info('获取货位列表(按区域货架) - 参数不完整');
            return response()->json([]);
        }
        
        try {
            $query = TecWarehouseLocationModel::where('warehouse_id', $warehouseId)
                ->where('area_number', $areaNumber)
                ->where('shelf_number', $shelfNumber)
                ->where('status', 'available');  // 只返回可用的货位
            
            $locations = $query->select('id', 'area_number', 'shelf_number', 'level_number')
                ->get()
                ->map(function($location) {
                    return [
                        'id' => $location->id,
                        'text' => sprintf(
                            '%s区%s架%s层',
                            $location->area_number,
                            $location->shelf_number,
                            $location->level_number
                        ),
                        'area_number' => $location->area_number,
                        'shelf_number' => $location->shelf_number,
                        'level_number' => $location->level_number
                    ];
                });
            
            // 记录查询结果
            Log::info('获取货位列表(按区域货架) - 查询结果', [
                'warehouse_id' => $warehouseId,
                'locations_count' => $locations->count(),
                'first_location' => $locations->first()
            ]);
            
            return response()->json($locations);
            
        } catch (\Exception $e) {
            // 记录错误
            Log::error('获取货位列表(按区域货架) - 错误', [
                'warehouse_id' => $warehouseId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => '获取货位列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取货位列表的路由方法
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function locations(Request $request)
    {
        $params = $request->all();
        $warehouseId = $params['warehouse_id'] ?? $params['q'] ?? null;
        $areaNumber = $params['area_number'] ?? $params['area'] ?? null;
        $shelfNumber = $params['shelf_number'] ?? $params['shelf'] ?? null;
        $search = $params['search'] ?? null;
        
        // 记录请求参数
        Log::info('获取货位列表 - 请求参数', [
            'warehouse_id' => $warehouseId,
            'area_number' => $areaNumber,
            'shelf_number' => $shelfNumber,
            'search' => $search,
            'all_params' => $params
        ]);
        
        // 如果没有仓库ID，返回空数组
        if (!$warehouseId) {
            Log::info('获取货位列表 - 未提供仓库ID');
            return response()->json([
                'status' => true,
                'data' => []
            ]);
        }
        
        try {
            // 构建查询
            $query = TecWarehouseLocationModel::where('warehouse_id', $warehouseId)
                ->where('status', 'available');  // 只返回可用的货位
            
            // 如果有区域号，添加区域号条件
            if ($areaNumber) {
                $query->where('area_number', $areaNumber);
            }
            
            // 如果有货架号，添加货架号条件
            if ($shelfNumber) {
                $query->where('shelf_number', $shelfNumber);
            }

            // 如果有搜索关键字，添加搜索条件
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('area_number', 'like', "%{$search}%")
                      ->orWhere('shelf_number', 'like', "%{$search}%")
                      ->orWhere('level_number', 'like', "%{$search}%");
                });
            }
            
            // 获取货位
            $locations = $query->select('id', 'area_number', 'shelf_number', 'level_number')
                ->get()
                ->map(function($location) {
                    return [
                        'id' => $location->id,
                        'text' => sprintf(
                            '%s区%s架%s层',
                            $location->area_number,
                            $location->shelf_number,
                            $location->level_number
                        ),
                        'area_number' => $location->area_number,
                        'shelf_number' => $location->shelf_number,
                        'level_number' => $location->level_number
                    ];
                });
            
            // 记录查询结果
            Log::info('获取货位列表 - 查询结果', [
                'warehouse_id' => $warehouseId,
                'locations_count' => $locations->count(),
                'first_location' => $locations->first()
            ]);
            
            // 返回包含data字段的对象格式
            return response()->json([
                'status' => true,
                'data' => $locations->toArray()
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取货位列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => '获取货位列表失败：' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
}
