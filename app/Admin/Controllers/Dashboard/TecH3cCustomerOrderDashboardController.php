<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Dashboard;

use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\TecH3cCustomerPurchaseOrderModel;
use Illuminate\Support\Facades\Log;
use App\Services\TecH3cCustomerOrderReportService;

class TecH3cCustomerOrderDashboardController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '客户订单仪表盘';

    /**
     * 允许容器实例化：空构造
     */
    public function __construct()
    {
        // 空实现，避免调用父受保护构造
    }

    /**
     * 仪表盘主页
     */
    public function index(Content $content)
    {

        // 筛选参数
        $period    = request('period', '3m');
        $companyId = (int) request('company_id');
        $service   = app(TecH3cCustomerOrderReportService::class);
        // 客户列表
        $clients   = $service->getClients();

        return $content
            ->header('客户订单仪表盘')
            ->description('H3C 客户订单综合分析')
            ->body(function (Row $row) use ($period, $clients, $companyId) {
                // 筛选器（时间 + 客户）
                $row->column(12, function (Column $column) use ($period, $clients, $companyId) {
                    $column->row(view('admin.dashboard.customer-order.h3c-customer-order-filters', compact('period', 'clients', 'companyId')));
                });
                // 顶部统计卡片
                $row->column(12, function (Column $column) {
                    // h3c-customer-order-header-cards-finance  
                    $column->row($this->buildHeaderCardsFinance());
                    // h3c-customer-order-header-cards-status
                    $column->row($this->buildHeaderCardsStatus());
                });

                // 产品分布饼图 下单产品一览 客户订单分布
                $row->column(12, function (Column $column) {
                    // h3c-customer-order-products-row  
                    $column->row($this->buildProductDistribution());
                });

                // 第1行: 订单统计（订单列表 + 产品数量图表）最近客户订单
                $row->column(12, function (Column $column) {
                    // h3c-customer-order-product-lists
                    $column->row($this->buildOrderStatistics());
                });
                
                // 第3行图表：根据选中产品统计最近一年的下单数量趋势
                $row->column(12, function (Column $column) {
                    // h3c-customer-order-product-trend
                    $column->row($this->buildProductTrendChart());
                });

            });
    }

    /**
     * 构建顶部统计卡片，支持自定义时间范围
     */
    protected function buildHeaderCardsStatus()
    {
        // 获取时间范围参数，传给服务层统计
        $period = request('period', '3m');
        $companyId = (int) request('company_id');
        $today = now();
        $year = $today->year;
        $month = $today->month;
        $startDate = null;
        $endDate = $today->copy()->endOfMonth()->format('Y-m-d');
        switch ($period) {
            case '1m':
                $startDate = $today->copy()->startOfMonth()->format('Y-m-d');
                break;
            case '3m':
                $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
                break;
            case '6m':
                $startDate = $today->copy()->startOfMonth()->subMonths(5)->startOfMonth()->format('Y-m-d');
                break;
            case '12m':
                $startDate = $today->copy()->startOfMonth()->subMonths(11)->startOfMonth()->format('Y-m-d');
                break;
            case 'q1':
                $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 3, 31)->format('Y-m-d');
                break;
            case 'q2':
                $startDate = \Carbon\Carbon::create($year, 4, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 6, 30)->format('Y-m-d');
                break;
            case 'q3':
                $startDate = \Carbon\Carbon::create($year, 7, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 9, 30)->format('Y-m-d');
                break;
            case 'q4':
                $startDate = \Carbon\Carbon::create($year, 10, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 12, 31)->format('Y-m-d');
                break;
            case 'ytd':
                $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                break;
            case 'all':
                $startDate = null;
                $endDate = null;
                break;
        }

        // 记录请求参数
        Log::info('获取客户订单状态统计', [
            'period' => $period,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'companyId' => $companyId
        ]);

        // 添加对服务层方法的调用，传递客户ID
        $service = app(TecH3cCustomerOrderReportService::class);

        // 检查服务层方法是否已支持客户ID筛选
        // 如果不支持，添加直接查询逻辑
        if (!method_exists($service, 'getHeaderStatsWithCompany')) {
            // 使用自定义查询实现客户ID筛选
            // 默认统计全部
            $query = DB::table('t_customer_purchase_order_h3c');
            
            // 应用日期筛选
            if ($startDate && $endDate) {
                $query->whereBetween('business_date', [$startDate, $endDate]);
            } elseif ($startDate) {
                $query->where('business_date', '>=', $startDate);
            } elseif ($endDate) {
                $query->where('business_date', '<=', $endDate);
            }
            // period=all时，不应用任何日期筛选
            
            // 应用客户筛选
            if ($companyId) {
                $query->where('sndCompany_id', $companyId);
            }
            
            // 统计订单总数
            $totalOrders = $query->count();
            
            // 统计各纳品状态数量（待纳品/部分纳品/完全纳品）
            $deliveryCounts = [
                'pending'   => (clone $query)->where('delivery_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_DELIVERY_STATUS_PENDING)->count(),
                'partial'   => (clone $query)->where('delivery_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_DELIVERY_STATUS_PARTIAL)->count(),
                'completed' => (clone $query)->where('delivery_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_DELIVERY_STATUS_COMPLETED)->count(),
            ];
            
            // 统计请求状态（未请求/部分请求/完全请求）
            $fundRequestCounts = [
                'unrequested' => (clone $query)->where('request_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_FUND_REQUEST_STATUS_UNREQUESTED)->count(),
                'requested'   => (clone $query)->where('request_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_FUND_REQUEST_STATUS_REQUESTED)->count(),
                'full'        => (clone $query)->where('request_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_FUND_REQUEST_STATUS_FULL)->count(),
            ];
            
            // 收款状态（未收款/部分收款/已收款）
            $paymentStatusCounts = [
                'unpaid'    => (clone $query)->where('payment_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_PAYMENT_STATUS_UNPAID)->count(),
                'partial'   => (clone $query)->where('payment_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_PAYMENT_STATUS_PARTIAL)->count(),
                'paid'      => (clone $query)->where('payment_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_PAYMENT_STATUS_PAID)->count(),
            ];
            
            $stats = [
                'totalOrders' => $totalOrders,
                'deliveryCounts' => $deliveryCounts,
                'fundRequestCounts' => $fundRequestCounts,
                'paymentStatusCounts' => $paymentStatusCounts,
            ];
        } else {
            $stats = $service->getHeaderStatsWithCompany($startDate, $endDate, $companyId);
        }

        // 构建统计卡片
        $card = new Card('订单统计');

        return $card->content(view('admin.dashboard.customer-order.h3c-customer-order-header-cards-status', [
            'totalOrders' => $stats['totalOrders'] ?? 0,
            'deliveryCounts' => $stats['deliveryCounts'] ?? ['pending'=>0,'partial'=>0,'completed'=>0],
            'fundRequestCounts' => $stats['fundRequestCounts'] ?? ['unrequested'=>0,'requested'=>0,'full'=>0],
            'paymentStatusCounts' => $stats['paymentStatusCounts'] ?? ['unpaid'=>0,'partial'=>0,'paid'=>0],
        ]));
    }

    /**
     * 构建顶部统计卡片，支持自定义时间范围
     */
    protected function buildHeaderCardsFinance()
    {
        // 获取时间范围参数，传给服务层统计
        $period = request('period', '3m');
        $companyId = (int) request('company_id');
        $today = now();
        $year = $today->year;
        $month = $today->month;
        $startDate = null;
        $endDate = $today->copy()->endOfMonth()->format('Y-m-d');
        switch ($period) {
            case '1m':
                $startDate = $today->copy()->startOfMonth()->format('Y-m-d');
                break;
            case '3m':
                $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
                break;
            case '6m':
                $startDate = $today->copy()->startOfMonth()->subMonths(5)->startOfMonth()->format('Y-m-d');
                break;
            case '12m':
                $startDate = $today->copy()->startOfMonth()->subMonths(11)->startOfMonth()->format('Y-m-d');
                break;
            case 'q1':
                $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 3, 31)->format('Y-m-d');
                break;
            case 'q2':
                $startDate = \Carbon\Carbon::create($year, 4, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 6, 30)->format('Y-m-d');
                break;
            case 'q3':
                $startDate = \Carbon\Carbon::create($year, 7, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 9, 30)->format('Y-m-d');
                break;
            case 'q4':
                $startDate = \Carbon\Carbon::create($year, 10, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 12, 31)->format('Y-m-d');
                break;
            case 'ytd':
                $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                break;
            case 'all':
                $startDate = null;
                $endDate = null;
                break;
        }

        // 获取统计数据
        Log::info('准备调用财务统计服务', [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'period' => $period,
            'companyId' => $companyId
        ]);
        
        // 调用服务获取统计数据，增加对客户ID的支持
        $service = app(TecH3cCustomerOrderReportService::class);
        
        // 判断是否支持客户ID筛选的方法
        if (method_exists($service, 'getHeaderFinanceStatsWithCompany')) {
            $stats = $service->getHeaderFinanceStatsWithCompany($startDate, $endDate, $companyId);
        } else {
            // 使用现有方法，后续在服务层修改以支持客户ID
            $stats = $service->getHeaderFinanceStats($startDate, $endDate);
            
            // 如果有客户ID筛选，需手动调整结果
            if ($companyId) {
                Log::info('使用客户ID筛选财务统计数据', [
                    'companyId' => $companyId
                ]);
                
                // 直接在控制器中实现对客户ID的筛选
                $orderIds = DB::table('t_customer_purchase_order_h3c')
                    ->where('sndCompany_id', $companyId);
                    
                if ($startDate && $endDate) {
                    $orderIds->whereBetween('business_date', [$startDate, $endDate]);
                }
                
                $orderIds = $orderIds->pluck('id')->toArray();
                
                // 1. 统计总金额
                $totalAmount = '0';
                $totalAmountValue = 0;
                if (!empty($orderIds)) {
                    $totalAmountValue = DB::table('t_customer_purchase_order_items_h3c as items')
                        ->whereIn('items.order_id', $orderIds)
                        ->sum(DB::raw('items.quantity * items.standard_selling_price'));
                    $totalAmount = number_format((float)$totalAmountValue, 0);
                }
                
                // 2. 已请求金额
                $requestedAmountValue = 0;
                $requestedAmount = '0';
                
                if (!empty($orderIds)) {
                    $purchaseRequestQuery = DB::table('t_customer_purchase_requests_h3c as req')
                        ->join('t_customer_purchase_request_items_h3c as req_items', 'req.id', '=', 'req_items.request_id')
                        ->join('t_customer_purchase_order_items_h3c as order_items', 'req_items.order_item_id', '=', 'order_items.id')
                        ->whereIn('order_items.order_id', $orderIds)
                        ->where('req.request_status', '>=', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_FUND_REQUEST_STATUS_REQUESTED);
                    
                    $requestedAmountValue = $purchaseRequestQuery->sum(DB::raw('req_items.qty_requested * order_items.standard_selling_price'));
                    $requestedAmount = number_format((float)$requestedAmountValue, 0);
                }
                
                // 3. 已到账金额
                $paidAmountValue = 0;
                $paidAmount = '0';
                
                if (!empty($orderIds)) {
                    $paidRequestQuery = DB::table('t_customer_purchase_requests_h3c as req')
                        ->join('t_customer_purchase_request_items_h3c as req_items', 'req.id', '=', 'req_items.request_id')
                        ->join('t_customer_purchase_order_items_h3c as order_items', 'req_items.order_item_id', '=', 'order_items.id')
                        ->whereIn('order_items.order_id', $orderIds)
                        ->where('req.payment_status', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_PAYMENT_STATUS_PAID);
                    
                    $paidAmountValue = $paidRequestQuery->sum(DB::raw('req_items.qty_requested * order_items.standard_selling_price'));
                    $paidAmount = number_format((float)$paidAmountValue, 0);
                }
                
                // 4. 统计未到账金额（已请款金额 - 已到账金额）
                $unpaidAmountValue = $requestedAmountValue - $paidAmountValue;
                $unpaidAmount = number_format((float)$unpaidAmountValue, 0);
                
                // 未请求金额(总金额-已请款金额)
                $unrequestedAmountValue = $totalAmountValue - $requestedAmountValue;
                $unrequestedAmount = number_format((float)$unrequestedAmountValue, 0);
                
                // 更新统计结果
                $stats = [
                    'totalAmount' => $totalAmount,
                    'requestedAmount' => $requestedAmount,
                    'paidAmount' => $paidAmount,
                    'unpaidAmount' => $unpaidAmount,
                    'unrequestedAmount' => $unrequestedAmount,
                ];
            }
        }
        
        Log::info('财务统计结果', [
            'totalAmount' => $stats['totalAmount'] ?? '0',
            'requestedAmount' => $stats['requestedAmount'] ?? '0',
            'paidAmount' => $stats['paidAmount'] ?? '0',
            'unpaidAmount' => $stats['unpaidAmount'] ?? '0',
            'companyId' => $companyId
        ]);
        
        // 构建统计卡片
        $card = new Card('订单统计');

        return $card->content(view('admin.dashboard.customer-order.h3c-customer-order-header-cards-finance', [
            'totalOrders' => $stats['totalOrders'] ?? 0,
            'totalAmount' => $stats['totalAmount'] ?? '',
            'unpaidAmount' => $stats['unpaidAmount'] ?? '',
            'partialAmount' => $stats['partialAmount'] ?? [],
            'requestedAmount' => $stats['requestedAmount'] ?? '',
            'paidAmount' => $stats['paidAmount'] ?? '',
            'unrequestedAmount' => $stats['unrequestedAmount'] ?? '',
        ]));
    }

    /**
     * 构建订单统计视图（订单列表+产品数量图表）
     */
    protected function buildOrderStatistics()
    {
        // 获取时间范围参数，传给服务层统计
        $period = request('period', '3m');
        $companyId = (int) request('company_id');
        $service = app(TecH3cCustomerOrderReportService::class);
        
        $today = now();
        $year = $today->year;
        $month = $today->month;
        $startDate = null;
        $endDate = $today->copy()->endOfMonth()->format('Y-m-d');
        switch ($period) {
            case '1m':
                $startDate = $today->copy()->startOfMonth()->format('Y-m-d');
                break;
            case '3m':
                $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
                break;
            case '6m':
                $startDate = $today->copy()->startOfMonth()->subMonths(5)->startOfMonth()->format('Y-m-d');
                break;
            case '12m':
                $startDate = $today->copy()->startOfMonth()->subMonths(11)->startOfMonth()->format('Y-m-d');
                break;
            case 'q1':
                $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 3, 31)->format('Y-m-d');
                break;
            case 'q2':
                $startDate = \Carbon\Carbon::create($year, 4, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 6, 30)->format('Y-m-d');
                break;
            case 'q3':
                $startDate = \Carbon\Carbon::create($year, 7, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 9, 30)->format('Y-m-d');
                break;
            case 'q4':
                $startDate = \Carbon\Carbon::create($year, 10, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 12, 31)->format('Y-m-d');
                break;
            case 'ytd':
                $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                break;
            case 'all':
                $startDate = null;
                $endDate = null;
                break;
        }
        
        // 从服务层获取客户列表和近期订单（增加日期筛选）
        $clients = $service->getClients();
        
        // 日志记录筛选条件
        Log::info('构建客户订单统计视图 - 筛选条件', [
            'period' => $period,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'companyId' => $companyId
        ]);
        
        // 需要扩展getRecentOrders服务方法来支持日期筛选
        $orders = DB::table('t_customer_purchase_order_h3c as o')
            ->join('t_snd_companies as c', 'o.sndCompany_id', '=', 'c.id')
            ->where('c.snd_role', 'client');
            
        // 应用日期筛选
        if ($startDate && $endDate) {
            $orders->whereBetween('o.business_date', [$startDate, $endDate]);
        }
        
        // 应用客户筛选
        if ($companyId) {
            $orders->where('c.id', $companyId);
        }
        
        // 获取最终的订单列表
        $orders = $orders->select(
                'o.id',
                'o.order_no',
                'o.business_date',
                'c.name as company_name',
                'c.notes as company_notes',
                'c.company_color',
                'o.case_name',
                'o.delivery_status',
                'o.request_status',
                'o.payment_status'
            )
            ->orderBy('o.delivery_status', 'asc')
            ->orderBy('o.request_status', 'asc')
            ->orderBy('o.payment_status', 'asc')
            ->orderBy('o.business_date', 'desc')
            ->get();
            
        $first = $orders->first();
        
        // 获取产品分布数据
        list($sumData, $orderCounts) = $service->getProductDistribution($startDate, $endDate, $companyId);
        
        // 添加调试日志
        Log::info('客户产品数量分布数据', [
            'sumData_count' => count($sumData),
            'orderCounts_count' => count($orderCounts),
            'orders_count' => $orders->count(),
            'first_order_id' => $first->id ?? null
        ]);
        
        // 获取管理后台URL
        $adminUrl = admin_url();
        
        // 生成唯一图表ID,避免冲突
        $chartId = 'order-items-chart-' . substr(md5((string)microtime(true)), 0, 8);
        
        // 传递需要的数据给视图模板
        return view('admin.dashboard.customer-order.h3c-customer-order-product-lists', [
            'orders' => $orders, 
            'first' => $first, 
            'clients' => $clients, 
            'companyId' => $companyId, 
            'sumData' => $sumData,
            'orderCounts' => $orderCounts,
            'adminUrl' => $adminUrl,
            'chartId' => $chartId
        ]);
    }

    /**
     * 构建产品分布视图（包含饼图和条形图）
     */
    protected function buildProductDistribution()
    {
        try {
            // 解析 period/company_id，计算起止日期
            $period = request('period', '3m');
            $today = now();
            $year = $today->year;
            $startDate = null;
            $endDate = $today->copy()->endOfMonth()->format('Y-m-d');
            switch ($period) {
                case '1m':
                    $startDate = $today->copy()->startOfMonth()->format('Y-m-d');
                    break;
                case '3m':
                    $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
                    break;
                case '6m':
                    $startDate = $today->copy()->startOfMonth()->subMonths(5)->startOfMonth()->format('Y-m-d');
                    break;
                case '12m':
                    $startDate = $today->copy()->startOfMonth()->subMonths(11)->startOfMonth()->format('Y-m-d');
                    break;
                case 'q1':
                    $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                    $endDate = \Carbon\Carbon::create($year, 3, 31)->format('Y-m-d');
                    break;
                case 'q2':
                    $startDate = \Carbon\Carbon::create($year, 4, 1)->format('Y-m-d');
                    $endDate = \Carbon\Carbon::create($year, 6, 30)->format('Y-m-d');
                    break;
                case 'q3':
                    $startDate = \Carbon\Carbon::create($year, 7, 1)->format('Y-m-d');
                    $endDate = \Carbon\Carbon::create($year, 9, 30)->format('Y-m-d');
                    break;
                case 'q4':
                    $startDate = \Carbon\Carbon::create($year, 10, 1)->format('Y-m-d');
                    $endDate = \Carbon\Carbon::create($year, 12, 31)->format('Y-m-d');
                    break;
                case 'ytd':
                    $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                    break;
                case 'all':
                    $startDate = null;
                    $endDate = null;
                    break;
            }
            $companyId = request('company_id', '');
            // 服务层方法需支持时间区间和客户过滤
            $service = app(\App\Services\TecH3cCustomerOrderReportService::class);
            list($sum, $orderCounts) = $service->getProductDistribution($startDate, $endDate, $companyId);
            $productOrderBar = $service->getProductOrderBar($startDate, $endDate, $companyId);
            Log::info('客户下单产品一览', [
                'sum' => $sum,
                'orderCounts' => $orderCounts,
                'productOrderBar_count' => count($productOrderBar),
                'startDate' => $startDate,
                'endDate' => $endDate,
                'companyId' => $companyId
            ]);
            
            // 创建包含两个图表的行视图
            return view('admin.dashboard.customer-order.h3c-customer-order-products-row', [
                'sumData' => json_encode($sum, JSON_UNESCAPED_UNICODE),
                'orderData' => json_encode($orderCounts, JSON_UNESCAPED_UNICODE),
                'customerOrderBar' => $productOrderBar,
                'id1' => 'product-distribution-sum',
                'id2' => 'product-distribution-orders',
                'height' => 300
            ]);
        } catch (\Exception $e) {
            Log::error('获取客户下单产品一览数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回空数据视图
            return view('admin.dashboard.customer-order.h3c-customer-order-products-row', [
                'sumData' => json_encode([]),
                'orderData' => json_encode([]),
                'customerOrderBar' => [],
                'id1' => 'product-distribution-sum',
                'id2' => 'product-distribution-orders',
                'height' => 300
            ]);
        }
    }

    /**
     * 构建产品趋势图表
     */
    protected function buildProductTrendChart()
    {
        // 只获取客户ID筛选，忽略period筛选（趋势图始终显示过去12个月）
        $companyId = (int) request('company_id');
        
        // 获取产品分布数据，使用过去12个月数据
        $today = now();
        $startDate = $today->copy()->subYear()->startOfMonth()->format('Y-m-d');
        $endDate = $today->copy()->endOfMonth()->format('Y-m-d');
        
        // 日志记录
        Log::info('构建产品趋势图表', [
            'companyId' => $companyId,
            'timeRange' => '固定为过去12个月',
            'startDate' => $startDate,
            'endDate' => $endDate
        ]);
        
        // 获取产品分布数据
        $service = app(TecH3cCustomerOrderReportService::class);
        list($sum, $orderCounts) = $service->getProductDistribution($startDate, $endDate, $companyId);
        
        // 确保数据包含产品ID
        $safeSum = [];
        foreach($sum as $item) {
            // 获取产品ID
            $productId = DB::table('t_product_lists_h3c')
                        ->where('product', $item['name'] ?? '')
                        ->value('id');
            
            if($productId) {
                $safeSum[] = [
                    'id' => $productId,
                    'name' => $item['name'] ?? '',
                    'value' => $item['value'] ?? 0
                ];
            }
        }
        
        // 直接传递数组，不进行JSON编码
        $card = new Card('产品年度下单趋势');
        return $card->content(view('admin.dashboard.customer-order.h3c-customer-order-product-trend', [
            'sumData' => $safeSum,
            'companyId' => $companyId,
            'adminUrl' => admin_url()
        ]));
    }

    /**
     * 产品下单数量图表数据接口
     */
    public function orderItemsChart(Request $request)
    {
        try {
            $orderId = (int) $request->input('order_id');
            
            // 记录请求参数
            Log::info('客户订单产品数据API请求', [
                'order_id' => $orderId,
                'client_ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);
            
            // 验证订单ID
            if (empty($orderId)) {
                Log::warning('客户订单产品数据API - 订单ID为空');
                return response()->json([
                    'categories' => [],
                    'series' => [],
                    'error' => '订单ID不能为空'
                ])->header('Access-Control-Allow-Origin', '*')
                  ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                  ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type')
                  ->header('Cache-Control', 'no-store, no-cache, must-revalidate');
            }
            
            // 获取订单号，用于显示
            $orderNo = '';
            try {
                $order = \App\Models\TecH3cCustomerPurchaseOrderModel::find($orderId);
                if ($order) {
                    $orderNo = $order->order_no;
                    Log::info('客户订单产品数据API - 订单信息', [
                        'order_id' => $orderId,
                        'order_no' => $orderNo
                    ]);
                } else {
                    Log::warning('客户订单产品数据API - 未找到订单', [
                        'order_id' => $orderId
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('获取订单号失败', [
                    'order_id' => $orderId,
                    'error' => $e->getMessage()
                ]);
            }
            
            // 获取图表数据
            $chartData = app(TecH3cCustomerOrderReportService::class)->getOrderItemsChart($orderId);
            
            // 记录原始数据以便调试
            Log::info('客户订单产品数据API响应', [
                'order_id' => $orderId,
                'categories_count' => count($chartData['categories'] ?? []),
                'series_count' => count($chartData['series'] ?? []),
                'categories_sample' => array_slice($chartData['categories'] ?? [], 0, 3),
                'series_sample' => array_slice($chartData['series'] ?? [], 0, 1)
            ]);
            
            // 确保数据格式正确
            $response = [
                'categories' => $chartData['categories'] ?? [],
                'series' => $chartData['series'] ?? [],
                'order_no' => $orderNo
            ];
            
            // 返回标准化的数据格式
            return response()->json($response)
                ->header('X-PJAX-URL', '')  // 防止PJAX自动处理
                ->header('Cache-Control', 'no-store, no-cache, must-revalidate')
                ->header('Content-Type', 'application/json; charset=utf-8')
                ->header('Access-Control-Allow-Origin', '*') // 允许跨域请求
                ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS') // 允许的HTTP方法
                ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type'); // 允许的请求头
        } catch (\Exception $e) {
            Log::error('客户订单产品数据API异常', [
                'order_id' => $request->input('order_id'),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'categories' => [],
                'series' => [],
                'error' => '数据获取失败: ' . $e->getMessage()
            ])->header('X-PJAX-URL', '')
              ->header('Access-Control-Allow-Origin', '*')
              ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
              ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type')
              ->header('Cache-Control', 'no-store, no-cache, must-revalidate');
        }
    }

    /**
     * 获取客户订单数据API，供前端刷新使用
     */
    public function getCustomerOrderData(Request $request)
    {
        $months = (int)$request->input('period', 6);
        // 获取统计数据
        $startDate = now()->subMonths($months)->startOfDay()->format('Y-m-d');
        $endDate = now()->format('Y-m-d');
        $stats = app(TecH3cCustomerOrderReportService::class)->getHeaderStats($startDate, $endDate);
        $bom    = app(TecH3cCustomerOrderReportService::class)->getBomUnits($months);
        $amount = app(TecH3cCustomerOrderReportService::class)->getAmountStats($months);
        $trend  = app(TecH3cCustomerOrderReportService::class)->getTrend($months);
        return response()->json([
            'status'  => $stats['status'] ?? [],
            'units'   => array_column($bom, 'total'), // 提取 BOM 单位数组
            'amount'  => ['ordered' => $amount['ordered'] ?? [], 'unpaid' => $amount['unpaid'] ?? []],
            'trend'   => ['labels' => $trend['labels'] ?? [], 'data' => $trend['data'] ?? []],
        ]);
    }

    /**
     * 产品年度下单趋势数据接口
     */
    public function productYearTrend(Request $request)
    {
        try {
            $product = $request->input('product');
            $companyId = (int)$request->input('company_id');
            
            // 记录请求参数
            Log::info('客户产品趋势数据API请求', [
                'product_id' => $product,
                'company_id' => $companyId,
                'client_ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);
            
            // 严格验证产品ID
            if (empty($product)) {
                Log::warning('客户产品趋势数据API - 产品ID为空');
                return response()->json([
                    'labels' => [],
                    'data' => [],
                    'error' => '请选择有效的产品',
                    'status' => 'error'
                ])->header('X-PJAX-URL', '')
                  ->header('Content-Type', 'application/json');
            }
            
            // 获取趋势图数据（过去12个月，带客户筛选）
            $service = app(TecH3cCustomerOrderReportService::class);
            $result = $service->getProductYearTrend($product, $companyId);
            
            // 记录查询结果
            Log::info('客户产品趋势数据API响应', [
                'product_id' => $product,
                'company_id' => $companyId,
                'labels_count' => count($result['labels']),
                'data_count' => count($result['data'])
            ]);
            
            return response()->json([
                'labels' => $result['labels'] ?? [],
                'data' => $result['data'] ?? [],
                'status' => 'success'
            ])->header('Content-Type', 'application/json');
            
        } catch (\Exception $e) {
            Log::error('客户产品趋势数据API异常', [
                'product_id' => $request->input('product'),
                'company_id' => $request->input('company_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'labels' => [],
                'data' => [],
                'error' => '获取数据异常: ' . $e->getMessage(),
                'status' => 'error'
            ])->header('Content-Type', 'application/json');
        }
    }
}
