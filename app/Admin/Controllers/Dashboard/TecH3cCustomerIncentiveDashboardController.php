<?php

namespace App\Admin\Controllers\Dashboard;

use App\Models\TecH3cCustomerPurchaseIncentiveModel;
use App\Models\TecH3cCustomerPurchaseIncentiveItemModel;
use App\Models\TecH3cCustomerPurchaseOrderModel;
use App\Models\TecSndCompanyModel;
use App\Models\TecH3cProductListModel;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Widgets\Card;
use App\Models\TecH3cCustomerIncentiveOutModel;
use App\Models\TecH3cCustomerIncentiveInModel;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Log;

class TecH3cCustomerIncentiveDashboardController extends AdminController
{
    protected $title = 'インセンティブダッシュボード';

    public function index(Content $content)
    {
        return $content
            ->header('インセンティブダッシュボード')
            ->description('インセンティブ相关数据分析')
            ->body(function (Row $row) {
                // 时间筛选
                $row->column(12, function (Column $column) {
                    $column->row($this->buildFilters());
                });
                // 头部统计卡片
                $row->column(12, function (Column $column) {
                    $column->row($this->buildHeaderCards());
                });
                // 图表区 - 第一行
                $row->column(12, function (Column $column) {
                    $column->row(function (Row $row) {
                        $row->column(6, $this->buildComparisonChart());
                        $row->column(6, $this->buildReasonPieChart());
                    });
                });

                // 图表区 - 第二行 - 事由/用途占比饼图
                $row->column(12, function (Column $column) {
                    $column->row(function (Row $row) {
                        $row->column(6, $this->buildRequestedChart());
                        $row->column(6, $this->buildOutChart());
                    });
                });
            });
    }

    protected function buildFilters()
    {
        // 检查用户是否通过period参数选择时间范围
        $period = request('period', '3m'); // 默认为最近三个月
        
        // 当前日期
        $today = \Carbon\Carbon::today();
        $year = $today->year;
        
        // 根据period获取对应的时间范围
        switch ($period) {
            case '1m': // 本月
                $start = $today->copy()->startOfMonth()->format('Y-m-d');
                $end = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case '3m': // 最近三个月
                $start = $today->copy()->startOfMonth()->subMonths(2)->format('Y-m-d');
                $end = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case '6m': // 最近半年
                $start = $today->copy()->startOfMonth()->subMonths(5)->format('Y-m-d');
                $end = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case '12m': // 最近一年
                $start = $today->copy()->startOfMonth()->subMonths(11)->format('Y-m-d');
                $end = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case 'q1': // 第一季度
                $start = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                $end = \Carbon\Carbon::create($year, 3, 31)->format('Y-m-d');
                break;
            case 'q2': // 第二季度
                $start = \Carbon\Carbon::create($year, 4, 1)->format('Y-m-d');
                $end = \Carbon\Carbon::create($year, 6, 30)->format('Y-m-d');
                break;
            case 'q3': // 第三季度
                $start = \Carbon\Carbon::create($year, 7, 1)->format('Y-m-d');
                $end = \Carbon\Carbon::create($year, 9, 30)->format('Y-m-d');
                break;
            case 'q4': // 第四季度
                $start = \Carbon\Carbon::create($year, 10, 1)->format('Y-m-d');
                $end = \Carbon\Carbon::create($year, 12, 31)->format('Y-m-d');
                break;
            case 'ytd': // 今年至今
                $start = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                $end = $today->format('Y-m-d');
                break;
            case 'all': // 全部时间
                $start = '1970-01-01';
                $end = '2099-12-31';
                break;
            default: // 自定义或其他
                // 尝试获取直接提供的日期范围
                $start = request('start_date');
                $end = request('end_date');
                // 如果没有提供，则使用最近三个月
                if (empty($start) || empty($end)) {
                    $start = $today->copy()->startOfMonth()->subMonths(2)->format('Y-m-d');
                    $end = $today->format('Y-m-d');
                }
        }
        
        // 记录当前使用的时间范围
        Log::info('当前选择的时间范围', [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end
        ]);
        
        // 将时间范围传递给视图
        return view('admin.dashboard.incentive.h3c-incentive-filters', compact('start', 'end', 'period'));
    }

    /**
     * 获取当前选择的时间范围
     * 
     * @return array 包含start和end的数组
     */
    protected function getTimeRange()
    {
        // 获取用户请求的时间范围
        $period = request('period', '3m'); // 默认为最近三个月
        
        // 当前日期
        $today = \Carbon\Carbon::today();
        $year = $today->year;
        
        // 未来时间范围
        $isFutureTime = false;
        
        // 根据period获取对应的时间范围
        switch ($period) {
            case '1m': // 本月
                $start = $today->copy()->startOfMonth()->format('Y-m-d');
                $end = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case '3m': // 最近三个月
                $start = $today->copy()->startOfMonth()->subMonths(2)->format('Y-m-d');
                $end = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case '6m': // 最近半年
                $start = $today->copy()->startOfMonth()->subMonths(5)->format('Y-m-d');
                $end = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case '12m': // 最近一年
                $start = $today->copy()->startOfMonth()->subMonths(11)->format('Y-m-d');
                $end = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case 'q1': // 第一季度
                $start = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                $end = \Carbon\Carbon::create($year, 3, 31)->format('Y-m-d');
                break;
            case 'q2': // 第二季度
                $start = \Carbon\Carbon::create($year, 4, 1)->format('Y-m-d');
                $end = \Carbon\Carbon::create($year, 6, 30)->format('Y-m-d');
                break;
            case 'q3': // 第三季度
                $start = \Carbon\Carbon::create($year, 7, 1)->format('Y-m-d');
                $end = \Carbon\Carbon::create($year, 9, 30)->format('Y-m-d');
                break;
            case 'q4': // 第四季度
                $start = \Carbon\Carbon::create($year, 10, 1)->format('Y-m-d');
                $end = \Carbon\Carbon::create($year, 12, 31)->format('Y-m-d');
                break;
            case 'ytd': // 今年至今
                $start = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                $end = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case 'all': // 全部时间
                $start = '1970-01-01';
                $end = '2099-12-31';
                break;
            default: // 自定义或其他
                // 尝试获取直接提供的日期范围
                $start = request('start_date');
                $end = request('end_date');
                // 如果没有提供，则使用最近三个月
                if (empty($start) || empty($end)) {
                    $start = $today->copy()->startOfMonth()->subMonths(2)->format('Y-m-d');
                    $end = $today->copy()->endOfMonth()->format('Y-m-d');
                } else {
                    // 检查是否是未来日期
                    $startDate = \Carbon\Carbon::parse($start);
                    $endDate = \Carbon\Carbon::parse($end);
                    
                    if ($startDate->isFuture() || $endDate->isFuture()) {
                        $isFutureTime = true;
                        Log::warning('用户查询了未来日期范围', [
                            'start' => $start,
                            'end' => $end,
                            'today' => $today->format('Y-m-d')
                        ]);
                    }
                }
        }
        
        // 替换用于测试的未来日期范围，使用正常的历史数据范围
        if ($isFutureTime) {
            Log::info('检测到未来日期范围，将使用当前时间替代', [
                'original_range' => [$start, $end],
                'current_time' => $today->format('Y-m-d')
            ]);
            
            // 对于未来日期范围，使用当前日期减去相同的月份数
            $testDate = \Carbon\Carbon::parse($start);
            $realDate = $today->copy();
            $monthsDiff = $today->diffInMonths($testDate);
            
            if ($monthsDiff > 0) {
                $realDate->subMonths($monthsDiff);
            }
            
            $start = $realDate->copy()->startOfMonth()->format('Y-m-d');
            $end = $today->copy()->endOfMonth()->format('Y-m-d');
            
            Log::info('调整后的日期范围', [
                'adjusted_range' => [$start, $end]
            ]);
        }
        
        // 记录最终使用的时间范围
        Log::info('getTimeRange返回的时间范围', [
            'start' => $start,
            'end' => $end,
            'period' => $period
        ]);
        
        return [$start, $end];
    }

    protected function buildHeaderCards()
    {
        // 使用getTimeRange获取时间范围
        list($start, $end) = $this->getTimeRange();
        
        // 记录正在使用的时间范围
        Log::info('卡片正在使用的时间范围', [
            'start' => $start,
            'end' => $end
        ]);
        
        // ==================== 已请求金额计算 ====================
        // 直接获取模型的所有记录，手动计算
        $incentives = TecH3cCustomerPurchaseIncentiveModel::with('incentiveItems')
            ->whereDate('created_at', '>=', $start)
            ->whereDate('created_at', '<=', $end)
            ->get();
        
        // 记录SQL查询语句用于调试
        $queryBuilder = TecH3cCustomerPurchaseIncentiveModel::with('incentiveItems')
            ->whereDate('created_at', '>=', $start)
            ->whereDate('created_at', '<=', $end);
        
        $sql = $queryBuilder->toSql();
        $bindings = $queryBuilder->getBindings();
        
        Log::info('インセンティブSQL查询', [
            'sql' => $sql,
            'bindings' => $bindings,
            'time_range' => [$start, $end],
            'count' => $incentives->count()
        ]);
        
        // 计算总金额
        $requested = 0;
        foreach ($incentives as $incentive) {
            // 计算该incentive的総インセンティブ金额
            $incentiveTotal = 0;
            
            foreach ($incentive->incentiveItems as $item) {
                // 对于已请求的インセンティブ，直接计算，不需要检查need_incentive_calc
                if ($item->qty_requested > 0) {
                    $values = TecH3cCustomerPurchaseIncentiveModel::calculateItemValues($item);
                    $incentiveTotal += $values['total']['incentive'];
                    
                    // 记录每个项目的计算详情
                    Log::info('インセンティブ计算详情', [
                        'incentive_id' => $incentive->id,
                        'item_id' => $item->id,
                        'product_id' => $item->product,
                        'qty_requested' => $item->qty_requested,
                        'values' => $values,
                        'profit_per' => $values['total']['profit'],
                        'incentive_per' => $values['total']['incentive'],
                        'item_incentive' => $values['total']['incentive']
                    ]);
                }
            }
            
            $requested += $incentiveTotal;
            
            // 记录各个インセンティブ的计算结果
            Log::info('インセンティブ记录总金额', [
                'id' => $incentive->id,
                'items_count' => $incentive->incentiveItems->count(),
                'incentive_total' => $incentiveTotal,
                'running_total' => $requested
            ]);
        }

        // 记录详细查询信息
        Log::info('インセンティブ详细查询信息', [
            'time_range' => [$start, $end],
            'incentive_count' => $incentives->count(),
            'items_count' => $incentives->sum(function($incentive) {
                return $incentive->incentiveItems->count();
            }),
            'total_amount' => $requested
        ]);

        // 计算待请求インセンティブ - 查找符合条件的订单
        $pending = 0;
        
        // 记录开始计算待请求インセンティブ
        Log::info('开始计算待请求インセンティブ', ['time_range' => [$start, $end]]);
        
        // 1. 获取所有 need_incentive_calc=true 的客户公司
        $eligibleCompanies = \App\Models\TecSndCompanyModel::where('snd_role', 'client')
            ->where('need_incentive_calc', true)
            ->pluck('id')
            ->toArray();
            
        // 记录找到的符合条件的公司数量
        Log::info('找到符合need_incentive_calc=true条件的客户公司', [
            'company_count' => count($eligibleCompanies),
            'companies' => $eligibleCompanies
        ]);
        
        if (!empty($eligibleCompanies)) {
            // 2. 获取这些公司的订单和订单项（排除已完全请求的订单）
            $orders = \App\Models\TecH3cCustomerPurchaseOrderModel::with(['items.product', 'sndCompany'])
                ->whereIn('sndCompany_id', $eligibleCompanies)
                ->whereDate('business_date', '>=', $start)
                ->whereDate('business_date', '<=', $end)
                ->where('request_status', '<', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_FUND_REQUEST_STATUS_FULL)
                ->get();
            
            foreach ($orders as $order) {
                foreach ($order->items as $item) {
                    if (empty($item->product)) continue;
                    
                    // 获取订单数量
                    $orderQty = floatval($item->quantity ?? 0);
                    
                    // 获取已请求数量
                    $requestedQty = DB::table('t_customer_purchase_request_items_h3c')
                        ->join('t_customer_purchase_requests_h3c', 't_customer_purchase_requests_h3c.id', '=', 't_customer_purchase_request_items_h3c.request_id')
                        ->where('t_customer_purchase_request_items_h3c.order_item_id', $item->id)
                        ->sum('t_customer_purchase_request_items_h3c.qty_requested');
                    
                    // 剩余可请求的数量 = 订单数量 - 已请求数量
                    $remainingQty = max(0, $orderQty - $requestedQty);
                    
                    if ($remainingQty > 0) {
                        // 直接从产品表获取数据，而不是依赖关联
                        $productData = DB::table('t_product_lists_h3c')
                            ->where('id', $item->product)
                            ->first();
                        
                        if (!$productData) {
                            Log::warning('计算インセンティブ时未找到产品', [
                                'product_id' => $item->product,
                                'order_no' => $order->order_no
                            ]);
                            continue;
                        }
                        
                        // 创建一个临时的Item模型实例
                        $tempItem = new TecH3cCustomerPurchaseIncentiveItemModel();
                        $tempItem->standard_price = floatval($productData->standard_price ?? 0);
                        // 优先使用订单汇率，如果没有则从公司中获取，如果都没有则使用默认值
                        if (isset($order->exchange_rate) && floatval($order->exchange_rate) > 0) {
                            $tempItem->exchange_rate = floatval($order->exchange_rate);
                        } else if ($order->sndCompany && isset($order->sndCompany->exchange_rate) && floatval($order->sndCompany->exchange_rate) > 0) {
                            $tempItem->exchange_rate = floatval($order->sndCompany->exchange_rate);
                        } else {
                            $tempItem->exchange_rate = 160; // 默认汇率
                        }
                        $tempItem->cip_cost = floatval($productData->cip_cost ?? 0);
                        $tempItem->standard_profit_rate = floatval($productData->standard_profit_rate ?? 15);
                        $tempItem->standard_selling_price = floatval($productData->standard_selling_price ?? 0);
                        $tempItem->qty_requested = $remainingQty;
                        $tempItem->need_incentive_calc = $productData->need_incentive_calc ?? true;
                        
                        // 使用Model的计算方法
                        $values = TecH3cCustomerPurchaseIncentiveModel::calculateItemValues($tempItem);
                        $itemIncentive = $values['total']['incentive'];
                        
                        // 记录该订单项的计算细节
                        Log::info('订单项インセンティブ计算', [
                            'order_no' => $order->order_no,
                            'product_id' => $item->product,
                            'product_bom' => $productData->product_bom ?? '-',
                            'product_name' => $productData->product ?? '-',
                            'order_qty' => $orderQty,
                            'requested_qty' => $requestedQty,
                            'remaining_qty' => $remainingQty,
                            'need_incentive_calc' => $productData->need_incentive_calc ?? true,
                            'values' => $values
                        ]);
                        
                        $pending += $itemIncentive;
                    }
                }
            }
        }
        
        // ==================== 手动调整金额计算 ====================
        // 从手动调整表中计算总金额
        $manualAdjustment = TecH3cCustomerIncentiveInModel::whereDate('date', '>=', $start)
            ->whereDate('date', '<=', $end)
            ->sum('total_amount');

        // ==================== 已支出金额计算 ====================
        // 从出库表中计算总金额
        $spent = TecH3cCustomerIncentiveOutModel::whereDate('date', '>=', $start)
            ->whereDate('date', '<=', $end)
            ->sum('total_amount');
        
        // 账户剩余金额 = 已请求 + 手动调整 - 已支出
        $balance = $requested + $manualAdjustment - $spent;
        
        // 记录日志，帮助调试
        Log::info('インセンティブ仪表盘计算结果(卡片)', [
            'requested' => $requested,
            'pending' => $pending,
            'manual_adjustment' => $manualAdjustment,
            'spent' => $spent,
            'balance' => $balance,
            'time_range' => [$start, $end]
        ]);
        
        return view('admin.dashboard.incentive.h3c-incentive-cards', [
            'requested' => $requested,
            'pending' => $pending,
            'manual_adjustment' => $manualAdjustment,
            'spent' => $spent,
            'balance' => $balance,
        ]);
    }

    protected function buildComparisonChart()
    {
        // 使用getTimeRange获取时间范围
        list($start, $end) = $this->getTimeRange();
        
        // 计算开始
        Log::info('构建インセンティブ对比图开始', ['time_range' => [$start, $end]]);
        
        // ==================== 已请求金额计算 ====================
        // 直接获取模型的所有记录，手动计算
        $incentives = TecH3cCustomerPurchaseIncentiveModel::with('incentiveItems')
            ->whereDate('created_at', '>=', $start)
            ->whereDate('created_at', '<=', $end)
            ->get();
        
        // 记录SQL查询语句用于调试
        $queryBuilder = TecH3cCustomerPurchaseIncentiveModel::with('incentiveItems')
            ->whereDate('created_at', '>=', $start)
            ->whereDate('created_at', '<=', $end);
        
        $sql = $queryBuilder->toSql();
        $bindings = $queryBuilder->getBindings();
        
        Log::info('対比图インセンティブSQL查询', [
            'sql' => $sql,
            'bindings' => $bindings,
            'time_range' => [$start, $end],
            'count' => $incentives->count()
        ]);
        
        // 计算总金额
        $requested = 0;
        foreach ($incentives as $incentive) {
            // 计算该incentive的総インセンティブ金额
            $incentiveTotal = 0;
            
            foreach ($incentive->incentiveItems as $item) {
                // 只统计qty_requested > 0的已请求インセンティブ
                if ($item->qty_requested > 0) {
                    $values = TecH3cCustomerPurchaseIncentiveModel::calculateItemValues($item);
                    $incentiveTotal += $values['total']['incentive'];
                }
            }
            
            $requested += $incentiveTotal;
            
            // 记录各个インセンティブ的计算结果
            Log::info('対比图インセンティブ记录总金额', [
                'id' => $incentive->id,
                'items_count' => $incentive->incentiveItems->count(),
                'incentive_total' => $incentiveTotal,
                'running_total' => $requested
            ]);
        }
        
        // 记录详细查询信息
        Log::info('対比图インセンティブ详细查询信息', [
            'time_range' => [$start, $end],
            'incentive_count' => $incentives->count(),
            'items_count' => $incentives->sum(function($incentive) {
                return $incentive->incentiveItems->count();
            }),
            'total_amount' => $requested
        ]);
        
        // 待请求インセンティブ - 与buildHeaderCards方法相同的计算逻辑
        $pending = 0;
        
        // 计算待请求インセンティブ，这里重用headerCards中相同的计算逻辑
        $eligibleCompanies = \App\Models\TecSndCompanyModel::where('snd_role', 'client')
            ->where('need_incentive_calc', true)
            ->pluck('id')
            ->toArray();
            
        if (!empty($eligibleCompanies)) {
            // 获取这些公司的订单和订单项（排除已完全请求的订单）
            $orders = \App\Models\TecH3cCustomerPurchaseOrderModel::with(['items.product', 'sndCompany'])
                ->whereIn('sndCompany_id', $eligibleCompanies)
                ->whereDate('business_date', '>=', $start)
                ->whereDate('business_date', '<=', $end)
                ->where('request_status', '<', \App\Models\TecH3cPurchaseBaseModel::CUSTOMER_ORDER_FUND_REQUEST_STATUS_FULL)
                ->get();
            
            foreach ($orders as $order) {
                foreach ($order->items as $item) {
                    if (empty($item->product)) continue;
                    
                    // 获取订单数量
                    $orderQty = floatval($item->quantity ?? 0);
                    
                    // 获取已请求数量
                    $requestedQty = DB::table('t_customer_purchase_request_items_h3c')
                        ->join('t_customer_purchase_requests_h3c', 't_customer_purchase_requests_h3c.id', '=', 't_customer_purchase_request_items_h3c.request_id')
                        ->where('t_customer_purchase_request_items_h3c.order_item_id', $item->id)
                        ->sum('t_customer_purchase_request_items_h3c.qty_requested');
                    
                    // 剩余可请求的数量 = 订单数量 - 已请求数量
                    $remainingQty = max(0, $orderQty - $requestedQty);
                    
                    if ($remainingQty > 0) {
                        // 直接从产品表获取数据，而不是依赖关联
                        $productData = DB::table('t_product_lists_h3c')
                            ->where('id', $item->product)
                            ->first();
                        
                        if (!$productData) continue;
                        
                        // 创建一个临时的Item模型实例
                        $tempItem = new TecH3cCustomerPurchaseIncentiveItemModel();
                        $tempItem->standard_price = floatval($productData->standard_price ?? 0);
                        // 优先使用订单汇率，如果没有则从公司中获取，如果都没有则使用默认值
                        if (isset($order->exchange_rate) && floatval($order->exchange_rate) > 0) {
                            $tempItem->exchange_rate = floatval($order->exchange_rate);
                        } else if ($order->sndCompany && isset($order->sndCompany->exchange_rate) && floatval($order->sndCompany->exchange_rate) > 0) {
                            $tempItem->exchange_rate = floatval($order->sndCompany->exchange_rate);
                        } else {
                            $tempItem->exchange_rate = 160; // 默认汇率
                        }
                        $tempItem->cip_cost = floatval($productData->cip_cost ?? 0);
                        $tempItem->standard_profit_rate = floatval($productData->standard_profit_rate ?? 15);
                        $tempItem->standard_selling_price = floatval($productData->standard_selling_price ?? 0);
                        $tempItem->qty_requested = $remainingQty;
                        $tempItem->need_incentive_calc = $productData->need_incentive_calc ?? true;
                        
                        // 只有当need_incentive_calc为true时才计算incentive
                        if ($tempItem->need_incentive_calc) {
                            // 使用Model的计算方法
                            $values = TecH3cCustomerPurchaseIncentiveModel::calculateItemValues($tempItem);
                            $itemIncentive = $values['total']['incentive'];
                            
                            // 记录计算详情
                            Log::info('待请求インセンティブ计算详情', [
                                'order_id' => $order->id,
                                'item_id' => $item->id,
                                'product_id' => $item->product,
                                'need_incentive_calc' => $tempItem->need_incentive_calc,
                                'remaining_qty' => $remainingQty,
                                'values' => $values,
                                'item_incentive' => $itemIncentive
                            ]);
                            
                            $pending += $itemIncentive;
                        }
                    }
                }
            }
        }
        
        // 手动调整金额 = 手动调整表 sum(total_amount)
        $manualAdjustment = TecH3cCustomerIncentiveInModel::whereDate('date', '>=', $start)
            ->whereDate('date', '<=', $end)
            ->sum('total_amount');
            
        // 花销金额 = 出库表 sum(total_amount)
        $spent = TecH3cCustomerIncentiveOutModel::whereDate('date', '>=', $start)
            ->whereDate('date', '<=', $end)
            ->sum('total_amount');
            
        // 记录计算结果
        Log::info('対比图计算结果', [
            'requested' => $requested,
            'pending' => $pending,
            'manual_adjustment' => $manualAdjustment,
            'spent' => $spent,
            'time_range' => [$start, $end]
        ]);
        
        return view('admin.dashboard.incentive.h3c-incentive-comparison', [
            'requested' => $requested,
            'pending' => $pending,
            'manual_adjustment' => $manualAdjustment,
            'spent' => $spent,
        ]);
    }

    protected function buildRequestedChart()
    {
        // 使用getTimeRange获取时间范围
        list($start, $end) = $this->getTimeRange();
        
        // 创建所有月份的数组
        $months = [];
        $startDate = Carbon::parse($start)->startOfMonth();
        $endDate = Carbon::parse($end)->startOfMonth();
        
        while ($startDate->lte($endDate)) {
            $months[$startDate->format('Y-m')] = 0;
            $startDate->addMonth();
        }
        
        // 获取时间范围内的所有请求记录
        $incentives = TecH3cCustomerPurchaseIncentiveModel::with('incentiveItems')
            ->whereDate('created_at', '>=', $start)
            ->whereDate('created_at', '<=', $end)
            ->get();
            
        // 按月分组计算总金额
        $monthlyData = [];
        foreach ($incentives as $incentive) {
            $month = Carbon::parse($incentive->created_at)->format('Y-m');
            if (!isset($monthlyData[$month])) {
                $monthlyData[$month] = 0;
            }
            
            // 计算该incentive的总金额
            $incentiveTotal = 0;
            foreach ($incentive->incentiveItems as $item) {
                // 只统计qty_requested > 0的已请求インセンティブ
                if ($item->qty_requested > 0) {
                    $values = TecH3cCustomerPurchaseIncentiveModel::calculateItemValues($item);
                    $incentiveTotal += $values['total']['incentive'];
                }
            }
            
            $monthlyData[$month] += $incentiveTotal;
        }
        
        // 记录查询结果
        Log::info('请求图表查询结果', [
            'incentive_count' => $incentives->count(),
            'monthly_data' => $monthlyData,
            'time_range' => [$start, $end],
            'calculation_method' => 'model_with_need_incentive_calc'
        ]);
            
        // 将查询结果填充到月份数组中
        foreach ($monthlyData as $month => $total) {
            if (isset($months[$month])) {
                $months[$month] = $total;
            }
        }
        
        // 将数组转换为所需的格式
        $data = collect();
        foreach ($months as $yearMonth => $total) {
            $date = Carbon::createFromFormat('Y-m', $yearMonth);
            $data->push([
                'date' => $date->format('Y年m月'),
                'total' => $total
            ]);
        }
        return view('admin.dashboard.incentive.h3c-incentive-requested-chart', ['data' => $data]);
    }

    protected function buildOutChart()
    {
        // 使用getTimeRange获取时间范围
        list($start, $end) = $this->getTimeRange();
        
        // 创建所有月份的数组
        $months = [];
        $monthsIn = [];
        $startDate = Carbon::parse($start)->startOfMonth();
        $endDate = Carbon::parse($end)->startOfMonth();
        
        while ($startDate->lte($endDate)) {
            $months[$startDate->format('Y-m')] = 0;
            $monthsIn[$startDate->format('Y-m')] = 0;
            $startDate->addMonth();
        }
        
        // 按月统计花销金额（出库表 total_amount）
        $results = TecH3cCustomerIncentiveOutModel::select(
                DB::raw('DATE_FORMAT(date, "%Y-%m") as date'),
                DB::raw('SUM(total_amount) as total')
            )
            ->whereBetween('date', [$start, $end])
            ->groupByRaw('DATE_FORMAT(date, "%Y-%m")')
            ->orderBy('date', 'asc')
            ->get();
            
        // 按月统计手动调整金额（手动调整表 total_amount）
        $resultsIn = TecH3cCustomerIncentiveInModel::select(
                DB::raw('DATE_FORMAT(date, "%Y-%m") as date'),
                DB::raw('SUM(total_amount) as total')
            )
            ->whereBetween('date', [$start, $end])
            ->groupByRaw('DATE_FORMAT(date, "%Y-%m")')
            ->orderBy('date', 'asc')
            ->get();
            
        // 将查询结果填充到月份数组中
        foreach ($results as $result) {
            $months[$result->date] = $result->total;
        }
        
        // 将手动调整查询结果填充到月份数组中
        foreach ($resultsIn as $result) {
            if (isset($result->date) && is_string($result->date)) {
                $monthsIn[$result->date] = $result->total;
            }
        }
        
        // 将数组转换为所需的格式
        $data = collect();
        foreach ($months as $yearMonth => $total) {
            $date = Carbon::createFromFormat('Y-m', $yearMonth);
            $inValue = 0;
            if (isset($monthsIn[$yearMonth]) && is_numeric($monthsIn[$yearMonth])) {
                $inValue = $monthsIn[$yearMonth];
            }
            $data->push([
                'date' => $date->format('Y年m月'),
                'total' => $total,
                'total_in' => $inValue
            ]);
        }

        return view('admin.dashboard.incentive.h3c-incentive-out-chart', ['data' => $data]);
    }

    /**
     * 构建事由/用途占比饼图
     */
    protected function buildReasonPieChart()
    {
        // 使用getTimeRange获取时间范围
        list($start, $end) = $this->getTimeRange();
        
        // 按事由/用途分组统计金额
        $reasonData = TecH3cCustomerIncentiveOutModel::whereDate('date', '>=', $start)
            ->whereDate('date', '<=', $end)
            ->select('reason', DB::raw('SUM(total_amount) as total'))
            ->groupBy('reason')
            ->orderBy('total', 'desc')
            ->get();
            
        // 记录查询结果
        Log::info('事由/用途占比饼图数据', [
            'data_count' => $reasonData->count(),
            'time_range' => [$start, $end]
        ]);
        
        return view('admin.dashboard.incentive.h3c-incentive-reason-pie', [
            'data' => $reasonData
        ]);
    }
}
