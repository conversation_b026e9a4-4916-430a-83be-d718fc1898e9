<?php

namespace App\Admin\Controllers\Dashboard;

use App\Models\TecH3cProductListModel;
use App\Models\TecH3cWarehouseInventoryModel;
use App\Models\TecWarehouseModel;
use App\Models\TecWarehouseLocationModel;
use App\Models\TecH3cWarehouseTransactionModel;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;

class TecH3cInventoryDashboardController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '库存仪表盘';

    /**
     * 库存仪表盘主页
     */
    public function index(Content $content)
    {
        // 判断是否为ajax排序请求
        if (request()->ajax() && request()->has('sort_field')) {
            $tbody = $this->renderInventoryProductListTbody();
            return response($tbody);
        }
        return $content
            ->header('库存仪表盘')
            ->description('实时库存数据分析')
            ->body(function (Row $row) {
                // 添加仓库和日期筛选
                $row->column(12, function (Column $column) {
                    $column->row($this->buildFilters());
                });
                
                // 顶部卡片
                $row->column(12, function (Column $column) {
                    $column->row(function (Row $row) {
                        $row->column(6, $this->buildHeaderCards());
                        $row->column(6, $this->buildTopProductsChart());
                    });
                });


                // 在库商品列表与年度趋势
                $row->column(12, function (Column $column) {
                    $column->row(function (Row $row) {
                        $row->column(6, $this->buildInventoryProductList());
                        $row->column(6, $this->buildInventoryTrendChart());
                    });
                });
                

            });
    }

    /**
     * 构建筛选器
     */
    protected function buildFilters()
    {
        $warehouses = TecWarehouseModel::pluck('name', 'id')->toArray();
        $warehouseId = request('warehouse_id', '');
        
        return view('admin.dashboard.inventory.h3c-inventory-filters', compact('warehouses', 'warehouseId'));
    }

    /**
     * 构建头部统计卡片
     */
    protected function buildHeaderCards()
    {
        // 获取选中的仓库ID
        $warehouseId = request('warehouse_id', '');
        
        // 构建基础查询
        $query = TecH3cWarehouseInventoryModel::query();
        
        // 如果选择了特定仓库，则添加筛选条件
        if (!empty($warehouseId)) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        // 计算总产品数和总数量（只统计库存大于0的）
        $totalProducts = $query->clone()
            ->select('product_id')
            ->groupBy('product_id')
            ->havingRaw('SUM(quantity) > 0')
            ->count();
        
        // 计算总数量（先按产品分组，只统计库存大于0的产品的总数量）
        $totalQuantity = $query->clone()
            ->select('product_id', DB::raw('SUM(quantity) as total_qty'))
            ->groupBy('product_id')
            ->havingRaw('SUM(quantity) > 0')
            ->get()
            ->sum('total_qty');
        
        // 计算库存总价值（只统计库存大于0的）
        try {
            $valueQuery = $query->clone()
                ->join('t_product_lists_h3c', 't_warehouse_inventory_h3c.product_id', '=', 't_product_lists_h3c.id')
                ->select('t_warehouse_inventory_h3c.product_id', 
                        DB::raw('SUM(t_warehouse_inventory_h3c.quantity) as total_qty'),
                        DB::raw('SUM(t_warehouse_inventory_h3c.quantity * IFNULL(t_product_lists_h3c.standard_selling_price, 0)) as total_value'))
                ->groupBy('t_warehouse_inventory_h3c.product_id')
                ->havingRaw('SUM(t_warehouse_inventory_h3c.quantity) > 0')
                ->get()
                ->sum('total_value');
            $totalValue = $valueQuery ?? 0;
        } catch (\Exception $e) {
            $totalValue = 0;
        }
        
        // 计算低库存产品数量（按仓库分组后单个产品库存低于10但大于0的）
        $lowStockProducts = $query->clone()
            ->select('product_id')
            ->groupBy('product_id')
            ->havingRaw('SUM(quantity) > 0 AND SUM(quantity) < 10')
            ->count();
        
        return view('admin.dashboard.inventory.h3c-inventory-cards', [
            'totalProducts' => $totalProducts,
            'totalQuantity' => number_format($totalQuantity),
            'totalValue' => number_format($totalValue, 0),
            'lowStockProducts' => $lowStockProducts,
        ]);
    }

    /**
     * 构建在库商品图表
     */
    protected function buildTopProductsChart()
    {
        // 获取选中的仓库ID
        $warehouseId = request('warehouse_id', '');
        
        // 构建查询
        $query = TecH3cWarehouseInventoryModel::select(
                'product_id',
                DB::raw('SUM(quantity) as total_quantity')
            );
            
        // 如果选择了特定仓库，则添加筛选条件
        if (!empty($warehouseId)) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        $data = $query->groupBy('product_id')
            ->havingRaw('SUM(quantity) > 0')
            ->orderByDesc('total_quantity')
            ->get()
            ->map(function ($item) {
                $product = TecH3cProductListModel::find($item->product_id);
                return [
                    'product' => $product ? ($product->product_name ?? $product->product) : '未知产品',
                    'quantity' => (float)$item->total_quantity,
                    'product_id' => $item->product_id,
                ];
            });
        
        $products = $data->pluck('product')->toJson();
        $quantities = $data->pluck('quantity')->toJson();
        $productIds = $data->pluck('product_id')->toJson();
        
        $card = new Card('库存分布');
        
        return $card->content(view('admin.dashboard.inventory.h3c-inventory-top-products', [
            'id' => 'top-products-chart',
            'products' => $products,
            'quantities' => $quantities,
            'productIds' => $productIds,
            'height' => 380,
        ]));
    }

    /**
     * 构建在库商品列表
     */
    protected function buildInventoryProductList()
    {
        $warehouseId = request('warehouse_id', '');
        $sortField = request('sort_field', 'bom');
        $sortDir = request('sort_dir', 'asc');
        $query = TecH3cWarehouseInventoryModel::select('product_id', DB::raw('SUM(quantity) as quantity'));
        if (!empty($warehouseId)) {
            $query->where('warehouse_id', $warehouseId);
        }
        $data = $query->groupBy('product_id')
            ->havingRaw('SUM(quantity) > 0')
            ->get()
            ->map(function ($item) {
                $product = TecH3cProductListModel::find($item->product_id);
                $zokusei = '';
                $zokuseiColor = 'inherit';
                $productName = '未知产品';
                $productNameColor = 'inherit';
                
                if ($product) {
                    $productName = $product->product_name ?? $product->product;
                    
                    // 获取产品属性颜色
                    $productZokusei = $product->product_zokusei ?? 'normal';
                    $isInspection = $product->is_inspection_machine ?? false;
                    $productNameColor = \App\Support\GridStyleHelper::getProductColorByAttribute($productZokusei, $isInspection);
                    
                    switch ($product->product_zokusei) {
                        case 'rr': 
                            $zokusei = 'RR製品'; 
                            $zokuseiColor = $productNameColor;
                            break;
                        case 'inspection': 
                            $zokusei = '検証機'; 
                            $zokuseiColor = $productNameColor;
                            break;
                        default: 
                            $zokusei = ''; 
                            $zokuseiColor = $productNameColor;
                            break;
                    }
                }
                
                return [
                    'id'       => $item->product_id,
                    'name'     => $productName,
                    'name_color' => $productNameColor,
                    'bom'      => $product ? $product->product_bom : '',
                    'zokusei'  => $zokusei,
                    'zokusei_color' => $zokuseiColor,
                    'quantity' => $item->quantity,
                ];
            });
        // 分组排序：先按BOM分组，组内再按指定字段排序
        $grouped = $data->groupBy('bom');
        $sortedGroups = collect();
        
        // 对每个BOM组内部排序
        foreach ($grouped as $bomGroup => $items) {
            $sortedGroup = $items->sortBy(function($item) use ($sortField) {
                if ($sortField === 'quantity') {
                    return (float)($item[$sortField] ?? 0);
                }
                return $item[$sortField] ?? '';
            }, SORT_REGULAR, $sortDir === 'desc');
            
            $sortedGroups->put($bomGroup, $sortedGroup);
        }
        
        // 根据每组第一个元素的排序字段值对组进行排序
        $sortedGroups = $sortedGroups->sortBy(function($group) use ($sortField) {
            $firstItem = $group->first();
            if ($sortField === 'quantity') {
                return (float)($firstItem[$sortField] ?? 0);
            }
            return $firstItem[$sortField] ?? '';
        }, SORT_REGULAR, $sortDir === 'desc');
        
        // 合并所有组
        $sortedData = collect();
        foreach ($sortedGroups as $group) {
            $sortedData = $sortedData->merge($group);
        }
        
        // 为数据添加分组标识
        $currentBom = null;
        $data = $sortedData->map(function($item) use (&$currentBom) {
            $item['bom_group_start'] = false;
            if ($currentBom !== $item['bom']) {
                $item['bom_group_start'] = true;
                $currentBom = $item['bom'];
            }
            return $item;
        });
        
        $card = new Card('在库商品一览');
        return $card->content(view('admin.dashboard.inventory.h3c-inventory-product-list', [
            'data' => $data,
            'height' => 380,
        ]));
    }

    /**
     * 渲染在库商品一览tbody片段（ajax排序用）
     */
    protected function renderInventoryProductListTbody()
    {
        $warehouseId = request('warehouse_id', '');
        $sortField = request('sort_field', 'bom');
        $sortDir = request('sort_dir', 'asc');
        $query = TecH3cWarehouseInventoryModel::select('product_id', DB::raw('SUM(quantity) as quantity'));
        if (!empty($warehouseId)) {
            $query->where('warehouse_id', $warehouseId);
        }
        $data = $query->groupBy('product_id')
            ->havingRaw('SUM(quantity) > 0')
            ->get()
            ->map(function ($item) {
                $product = TecH3cProductListModel::find($item->product_id);
                $zokusei = '';
                $zokuseiColor = 'inherit';
                $productName = '未知产品';
                $productNameColor = 'inherit';
                
                if ($product) {
                    $productName = $product->product_name ?? $product->product;
                    
                    // 获取产品属性颜色
                    $productZokusei = $product->product_zokusei ?? 'normal';
                    $isInspection = $product->is_inspection_machine ?? false;
                    $productNameColor = \App\Support\GridStyleHelper::getProductColorByAttribute($productZokusei, $isInspection);
                    
                    switch ($product->product_zokusei) {
                        case 'rr': 
                            $zokusei = 'RR製品'; 
                            $zokuseiColor = $productNameColor;
                            break;
                        case 'inspection': 
                            $zokusei = '検証機'; 
                            $zokuseiColor = $productNameColor;
                            break;
                        default: 
                            $zokusei = ''; 
                            $zokuseiColor = $productNameColor;
                            break;
                    }
                }
                
                return [
                    'id'       => $item->product_id,
                    'name'     => $productName,
                    'name_color' => $productNameColor,
                    'bom'      => $product ? $product->product_bom : '',
                    'zokusei'  => $zokusei,
                    'zokusei_color' => $zokuseiColor,
                    'quantity' => $item->quantity,
                ];
            });
        // 分组排序：先按BOM分组，组内再按指定字段排序
        $grouped = $data->groupBy('bom');
        $sortedGroups = collect();
        
        // 对每个BOM组内部排序
        foreach ($grouped as $bomGroup => $items) {
            $sortedGroup = $items->sortBy(function($item) use ($sortField) {
                if ($sortField === 'quantity') {
                    return (float)($item[$sortField] ?? 0);
                }
                return $item[$sortField] ?? '';
            }, SORT_REGULAR, $sortDir === 'desc');
            
            $sortedGroups->put($bomGroup, $sortedGroup);
        }
        
        // 根据每组第一个元素的排序字段值对组进行排序
        $sortedGroups = $sortedGroups->sortBy(function($group) use ($sortField) {
            $firstItem = $group->first();
            if ($sortField === 'quantity') {
                return (float)($firstItem[$sortField] ?? 0);
            }
            return $firstItem[$sortField] ?? '';
        }, SORT_REGULAR, $sortDir === 'desc');
        
        // 合并所有组
        $sortedData = collect();
        foreach ($sortedGroups as $group) {
            $sortedData = $sortedData->merge($group);
        }
        
        // 渲染tbody
        $html = '<tbody>';
        $currentBom = null;
        foreach ($sortedData as $item) {
            // 如果BOM变化，添加分组视觉标识
            $bomClass = '';
            if ($currentBom !== $item['bom']) {
                $bomClass = ' bom-group-start';
                $currentBom = $item['bom'];
            }
            $html .= '<tr data-id="'.$item['id'].'" class="'.$bomClass.'">'
                .'<td class="product-name"><span style="color:'.$item['name_color'].';">'.$item['name'].'</span></td>'
                .'<td class="product-bom">'.($item['bom'] ?? '-').'</td>'
                .'<td class="product-zokusei"><span style="color:'.$item['zokusei_color'].';">'.($item['zokusei'] ?? '-').'</span></td>'
                .'<td class="product-quantity">'.$item['quantity'].'</td>'
                .'</tr>';
        }
        $html .= '</tbody>';
        return $html;
    }

    /**
     * 构建库存年度趋势图表
     */
    protected function buildInventoryTrendChart()
    {
        // 获取选中的仓库ID
        $warehouseId = request('warehouse_id', '');
        
        // 获取过去12个月的日期
        $endDate = now()->endOfMonth();
        $startDate = now()->subMonths(11)->startOfMonth();
        $dates = [];
        
        // 生成日期数组
        for ($date = clone $startDate; $date->lte($endDate); $date->addMonth()) {
            $dates[] = $date->format('Y-m');
        }
        
        // 获取库存交易记录
        $query = TecH3cWarehouseTransactionModel::selectRaw('DATE_FORMAT(created_at, "%Y-%m") as date, type, SUM(quantity) as total_quantity')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('type', ['in', 'out']);
            
        // 如果选择了特定仓库，则添加筛选条件
        if (!empty($warehouseId)) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        $transactions = $query->groupBy('date', 'type')
            ->get()
            ->groupBy('date');
        
        // 准备数据
        $inData = [];
        $outData = [];
        
        foreach ($dates as $date) {
            $dayData = $transactions->get($date, collect([]));
            
            // 获取入库数量
            $inQty = $dayData->where('type', 'in')->sum('total_quantity');
            $inData[] = $inQty ?? 0;
            
            // 获取出库数量
            $outQty = $dayData->where('type', 'out')->sum('total_quantity');
            $outData[] = $outQty ?? 0;
        }
        
        $card = new Card('库存流动趋势(12个月)');

        // 添加粘性定位样式到Card
        $card->style('position: sticky; top: 80px; z-index: 100; box-shadow: 0 4px 12px rgba(0,0,0,0.15);');

        // 获取第一个产品的信息（如果有）
        $firstProduct = TecH3cProductListModel::first();

        return $card->content(view('admin.dashboard.inventory.h3c-inventory-trend', [
            'id' => 'inventory-trend-year-chart',
            'dates' => json_encode($dates),
            'inData' => json_encode($inData),
            'outData' => json_encode($outData),
            'height' => 320,
            'productName' => $firstProduct ? $firstProduct->product : '',
            'productBom' => $firstProduct ? $firstProduct->product_bom : '',
        ]));
    }

    /**
     * API：获取指定商品的年度出入库趋势
     */
    public function getInventoryProductTrend(Request $request)
    {
        $productId = $request->input('product_id');
        // 时间范围12个月
        $endDate = now()->endOfMonth();
        $startDate = now()->subMonths(11)->startOfMonth();
        
        // 获取产品信息
        $product = TecH3cProductListModel::findOrFail($productId);
        
        // 查询交易
        $transactions = TecH3cWarehouseTransactionModel::selectRaw('DATE_FORMAT(created_at, "%Y-%m") as date, type, SUM(quantity) as total_quantity')
            ->where('product_id', $productId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('type', ['in', 'out'])
            ->groupBy('date', 'type')
            ->get()
            ->groupBy('date');
        
        // 构建结果
        $dates = [];
        $inData = [];
        $outData = [];
        for ($date = clone $startDate; $date->lte($endDate); $date->addMonth()) {
            $d = $date->format('Y-m');
            $dates[] = $d;
            $group = $transactions->get($d, collect([]));
            $inData[] = $group->where('type', 'in')->sum('total_quantity');
            $outData[] = $group->where('type', 'out')->sum('total_quantity');
        }
        
        return response()->json([ 
            'dates' => $dates, 
            'in' => $inData, 
            'out' => $outData,
            'product_name' => $product->product,
            'product_bom' => $product->product_bom ?? ''
        ]);
    }

    /**
     * 获取库存数据API(用于图表刷新)
     */
    public function getInventoryData(Request $request)
    {
        $warehouseId = $request->input('warehouse_id');
        
        // 构建查询
        $query = TecH3cWarehouseInventoryModel::query();
        
        if ($warehouseId && $warehouseId != 'all') {
            $query->where('warehouse_id', $warehouseId);
        }
        
        // 库存分布数据
        $distribution = $query->clone()
            ->select('warehouse_id', DB::raw('SUM(quantity) as total_quantity'))
            ->groupBy('warehouse_id')
            ->get()
            ->map(function ($item) {
                $warehouse = TecWarehouseModel::find($item->warehouse_id);
                return [
                    'warehouse' => $warehouse ? $warehouse->name : '未知仓库',
                    'quantity' => (float)$item->total_quantity,
                ];
            });
        
        // TOP产品数据
        $topProducts = $query->clone()
            ->select('product_id', DB::raw('SUM(quantity) as total_quantity'))
            ->groupBy('product_id')
            ->orderByDesc('total_quantity')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                $product = TecH3cProductListModel::find($item->product_id);
                return [
                    'product' => $product ? ($product->product_name ?? $product->product) : '未知产品',
                    'quantity' => (float)$item->total_quantity,
                    'product_id' => $item->product_id,
                ];
            });
        
        return response()->json([
            'distribution' => [
                'categories' => $distribution->pluck('warehouse'),
                'data' => $distribution->pluck('quantity'),
            ],
            'topProducts' => [
                'categories' => $topProducts->pluck('product'),
                'data' => $topProducts->pluck('quantity'),
                'ids' => $topProducts->pluck('product_id'),
            ],
        ]);
    }
} 
