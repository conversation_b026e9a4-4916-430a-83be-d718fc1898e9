<?php

declare(strict_types=1);

namespace App\Admin\Controllers\Dashboard;

use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\TecH3cPurchaseOrderModel;
use App\Services\TecH3cPurchaseOrderReportService;
use Illuminate\Support\Facades\Log;

class TecH3cPurchaseOrderDashboardController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '采购订单仪表盘';

    /**
     * 允许容器实例化：空构造
     */
    public function __construct()
    {
        // 空实现，避免调用父受保护构造
    }

    /**
     * 仪表盘主页
     */
    public function index(Content $content)
    {
        // 详细日志记录
        Log::info('开始加载采购订单仪表盘');
        
        // 注意：不再注入JavaScript代码，由视图自行处理
        
        Log::info('仪表盘资源已准备 (控制器)');

        return $content
            ->header('采购订单仪表盘')
            ->description('H3C 采购订单综合分析')
            ->body(function (Row $row) {
                // 筛选器（时间 + 客户）
                $row->column(12, function (Column $column)  {
                    $column->row(view('admin.dashboard.purchase.h3c-purchase-order-filters'));
                });
                // 顶部统计卡片
                $row->column(12, function (Column $column) {
                    // h3c-purchase-order-header-cards
                    $column->row($this->buildHeaderCards());
                });
                // 产品分布饼图
                $row->column(12, function (Column $column) {
                    // h3c-purchase-order-products-row
                    $column->row($this->buildProductDistribution());
                });
                // 第1行: 订单统计（订单列表 + 产品数量图表）
                $row->column(12, function (Column $column) {
                    // h3c-purchase-order-product-lists
                    $column->row($this->buildOrderStatistics());
                });

                // 第3行图表：根据选中产品统计最近一年的下单数量趋势
                $row->column(12, function (Column $column) {
                    // h3c-purchase-order-product-trend
                    $column->row($this->buildProductTrendChart());
                });

                Log::info('仪表盘内容已构建完成 (控制器)');
            });
    }

    /**
     * 构建顶部统计卡片，支持自定义时间范围
     */
    protected function buildHeaderCards()
    {
        // 解析 period 参数，转换为起止日期
        $period = request('period', '3m');
        $today = now();
        $year = $today->year;
        $month = $today->month;
        $startDate = null;
        $endDate = $today->copy()->endOfMonth()->format('Y-m-d');
        switch ($period) {
            case '1m':
                $startDate = $today->copy()->startOfMonth()->format('Y-m-d');
                break;
            case '3m':
                $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
                break;
            case '6m':
                $startDate = $today->copy()->startOfMonth()->subMonths(5)->startOfMonth()->format('Y-m-d');
                break;
            case '12m':
                $startDate = $today->copy()->startOfMonth()->subMonths(11)->startOfMonth()->format('Y-m-d');
                break;
            case 'q1':
                $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 3, 31)->format('Y-m-d');
                break;
            case 'q2':
                $startDate = \Carbon\Carbon::create($year, 4, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 6, 30)->format('Y-m-d');
                break;
            case 'q3':
                $startDate = \Carbon\Carbon::create($year, 7, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 9, 30)->format('Y-m-d');
                break;
            case 'q4':
                $startDate = \Carbon\Carbon::create($year, 10, 1)->format('Y-m-d');
                $endDate = \Carbon\Carbon::create($year, 12, 31)->format('Y-m-d');
                break;
            case 'ytd':
                $startDate = \Carbon\Carbon::create($year, 1, 1)->format('Y-m-d');
                $endDate = $today->copy()->endOfMonth()->format('Y-m-d');
                break;
            case 'all':
                $startDate = null;
                $endDate = null;
                break;
            default:
                $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
        }
        // 调用服务，传递起止日期
        $service = app(TecH3cPurchaseOrderReportService::class);
        $stats   = $service->getHeaderStats($startDate, $endDate);
        // 获取应付款金额（基于已入库数量）
        $payableAmount = $service->getPayableAmountStats($startDate, $endDate);
        // 获取已付款金额（payment_status=2）
        $paidAmount = $service->getPaidAmountStats($startDate, $endDate);
        // 获取待下单数量和入库分布
        $pendingOrders             = $service->getPendingOrders($startDate, $endDate);
        // 入库分布也按时间范围过滤
        $warehouseDist             = $service->getWarehouseDistribution($startDate, $endDate);
        // 保证金额全部为数组格式，便于前端统一渲染
        $totalAmount = $stats['totalAmount'] ?? [];
        $cipAmount   = $stats['cipAmount'] ?? [];
        $ddpAmount   = $stats['ddpAmount'] ?? [];
        // 获取待付款金额（应付款-已付款）
        $pendingAmount = $service->getPendingAmountStats($startDate, $endDate);
        // 总金额为CIP和DDP合集
        $mergedAmount = [];
        foreach ([$cipAmount, $ddpAmount] as $arr) {
            foreach ($arr as $currency => $amount) {
                if (!isset($mergedAmount[$currency])) {
                    $mergedAmount[$currency] = 0;
                }
                $mergedAmount[$currency] += (float)$amount;
            }
        }
        return view('admin.dashboard.purchase.h3c-purchase-order-header-cards', [
            'totalOrders'                => $stats['totalOrders'],
            'pendingOrders'              => $pendingOrders,
            'totalAmount'                => $mergedAmount,
            'cipAmount'                  => $cipAmount,
            'ddpAmount'                  => $ddpAmount,
            'unpaidAmount'               => $stats['unpaidAmount'],
            'payableAmount'              => $payableAmount,
            'pendingAmount'              => $pendingAmount,
            'paidAmount'                 => $paidAmount,
            'pendingWarehouseOrders'     => $warehouseDist['pending'],
            'partialWarehouseOrders'     => $warehouseDist['partial'],
            'completedWarehouseOrders'   => $warehouseDist['completed'],
        ]);
    }


    /**
     * 构建订单统计视图（订单列表+产品数量图表）
     */
    protected function buildOrderStatistics()
    {
        // 时间过滤：根据 period 参数计算起止日期
        $period = request('period', '3m');
        $companyId = (int) request('company_id');
        $today  = now();
        $year   = $today->year;
        $endDate = $today->copy()->endOfMonth()->format('Y-m-d');
        switch ($period) {
            case '1m':
                $startDate = $today->copy()->startOfMonth()->format('Y-m-d');
                break;
            case '3m':
                $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
                break;
            case '6m':
                $startDate = $today->copy()->startOfMonth()->subMonths(5)->startOfMonth()->format('Y-m-d');
                break;
            case '12m':
                $startDate = $today->copy()->startOfMonth()->subMonths(11)->startOfMonth()->format('Y-m-d');
                break;
            case 'q1':
                $startDate = Carbon::create($year, 1, 1)->format('Y-m-d');
                $endDate   = Carbon::create($year, 3, 31)->format('Y-m-d');
                break;
            case 'q2':
                $startDate = Carbon::create($year, 4, 1)->format('Y-m-d');
                $endDate   = Carbon::create($year, 6, 30)->format('Y-m-d');
                break;
            case 'q3':
                $startDate = Carbon::create($year, 7, 1)->format('Y-m-d');
                $endDate   = Carbon::create($year, 9, 30)->format('Y-m-d');
                break;
            case 'q4':
                $startDate = Carbon::create($year, 10, 1)->format('Y-m-d');
                $endDate   = Carbon::create($year, 12, 31)->format('Y-m-d');
                break;
            case 'ytd':
                $startDate = Carbon::create($year, 1, 1)->format('Y-m-d');
                break;
            case 'all':
                $startDate = null;
                $endDate = null;
                break;
            default:
                $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
        }
        
        // 日志记录筛选条件
        Log::info('构建采购订单统计视图 - 筛选条件', [
            'period' => $period,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'companyId' => $companyId
        ]);
        
        // 查询订单列表
        $orders = DB::table('t_purchase_order_h3c as o')
            ->join('t_snd_companies as c', 'o.sndCompany_id', '=', 'c.id')
            ->where('o.order_status', '<>', 0);
            
        // 应用日期筛选
        if ($startDate && $endDate) {
            $orders->whereBetween('o.business_date', [$startDate, $endDate]);
        }
        
        // 应用公司筛选
        if ($companyId) {
            $orders->where('o.sndCompany_id', $companyId);
        }
        
        // 获取最终的订单列表
        $orders = $orders->select(
                'o.id',
                'o.order_no',
                'o.case_name',
                'o.business_date',
                'c.name as company_name',
                'o.warehousing_status',
                'o.fund_request_status',
                'o.payment_status'
            )
            ->orderBy('o.business_date', 'desc')
            ->get();
            
        $first = $orders->first();
        
        // 获取产品分布数据，用于图表初始化,传入公司ID
        $service = app(TecH3cPurchaseOrderReportService::class);
        list($sum, $orderCounts) = $service->getProductDistribution($startDate, $endDate, $companyId);
        
        // 添加调试日志
        Log::info('采购产品数量分布数据', [
            'sumData_count' => count($sum),
            'orderCounts_count' => count($orderCounts),
            'orders_count' => $orders->count(),
            'first_order_id' => $first->id ?? null,
            'companyId' => $companyId
        ]);
        
        // 获取管理后台URL
        $adminUrl = admin_url();
        
        // 生成唯一图表ID，避免冲突
        $chartId = 'order-items-chart-' . substr(md5((string)microtime(true)), 0, 8);
        
        // 获取供应商列表
        $suppliers = DB::table('t_snd_companies')
            ->where('snd_role', 'supplier')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();
        
        // 传递完整数据给视图
        return view('admin.dashboard.purchase.h3c-purchase-order-product-lists', [
            'orders' => $orders, 
            'first' => $first, 
            'sumData' => $sum,
            'orderCounts' => $orderCounts,
            'adminUrl' => $adminUrl,
            'chartId' => $chartId,
            'suppliers' => $suppliers,
            'companyId' => $companyId
        ]);
    }

    /**
     * 构建产品分布视图（饼图和柱状图）
     */
    protected function buildProductDistribution()
    {
        try {
            // 解析时间范围：period -> startDate/endDate
            $period = request('period', '3m');
            $today = now();
            $year = $today->year;
            $startDate = null;
            $endDate = $today->copy()->endOfMonth()->format('Y-m-d');
            switch ($period) {
                case '1m':
                    $startDate = $today->copy()->startOfMonth()->format('Y-m-d');
                    break;
                case '3m':
                    $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
                    break;
                case '6m':
                    $startDate = $today->copy()->startOfMonth()->subMonths(5)->startOfMonth()->format('Y-m-d');
                    break;
                case '12m':
                    $startDate = $today->copy()->startOfMonth()->subMonths(11)->startOfMonth()->format('Y-m-d');
                    break;
                case 'q1':
                    $startDate = Carbon::create($year, 1, 1)->format('Y-m-d');
                    $endDate = Carbon::create($year, 3, 31)->format('Y-m-d');
                    break;
                case 'q2':
                    $startDate = Carbon::create($year, 4, 1)->format('Y-m-d');
                    $endDate = Carbon::create($year, 6, 30)->format('Y-m-d');
                    break;
                case 'q3':
                    $startDate = Carbon::create($year, 7, 1)->format('Y-m-d');
                    $endDate = Carbon::create($year, 9, 30)->format('Y-m-d');
                    break;
                case 'q4':
                    $startDate = Carbon::create($year, 10, 1)->format('Y-m-d');
                    $endDate = Carbon::create($year, 12, 31)->format('Y-m-d');
                    break;
                case 'ytd':
                    $startDate = Carbon::create($year, 1, 1)->format('Y-m-d');
                    break;
                case 'all':
                    $startDate = null;
                    $endDate = null;
                    break;
                default:
                    $startDate = $today->copy()->startOfMonth()->subMonths(2)->startOfMonth()->format('Y-m-d');
            }
            
            // 添加日志记录
            Log::info('构建产品分布视图 - 筛选条件', [
                'period' => $period,
                'startDate' => $startDate,
                'endDate' => $endDate,
                'companyId' => (int) request('company_id')
            ]);
            
            // 调用服务获取产品分布及订单占比数据,传递公司ID
            $service = app(TecH3cPurchaseOrderReportService::class);
            list($sum, $orderCounts) = $service->getProductDistribution($startDate, $endDate, (int) request('company_id'));
            
            // 添加详细调试日志
            Log::info('产品分布详细数据 (控制器)', [
                'sum_count' => count($sum),
                'orderCounts_count' => count($orderCounts),
                'first_sum' => $sum[0] ?? null,
                'first_order' => $orderCounts[0] ?? null,
                'companyId' => (int) request('company_id')
            ]);
            
            // 查询商品订单分布数据，传递给右侧柱状图,传递公司ID
            $productOrderBar = $service->getProductOrderBar($startDate, $endDate, (int) request('company_id'));
            
            // 确保adminUrl被正确传递
            $adminUrl = admin_url();
            
            // 生成唯一的图表ID,防止多次实例化冲突
            $uniqueChartId = 'product-distribution-' . substr(md5((string)microtime(true)), 0, 8);
            
            // 返回更简化的视图数据，参照客户订单实现
            return view('admin.dashboard.purchase.h3c-purchase-order-products-row', [
                'sumData' => json_encode($sum, JSON_UNESCAPED_UNICODE),
                'orderData' => json_encode($orderCounts, JSON_UNESCAPED_UNICODE),
                'id1' => $uniqueChartId,
                'id2' => 'purchase-order-bar',
                'chartHeight' => 300,
                'height' => 300,
                'productOrderBar' => $productOrderBar, // 直接传递数组，不进行JSON编码
                'adminUrl' => $adminUrl,
                'companyId' => (int) request('company_id')
            ]);
        } catch (\Exception $e) {
            // 异常处理并返回空数据视图
            Log::error('获取产品分布数据失败 (控制器)', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'companyId' => (int) request('company_id')
            ]);
            
            // 确保即使发生错误也传递adminUrl和companyId
            $adminUrl = admin_url();
            $companyId = (int) request('company_id');
            
            // 使用更简化的空数据结构
            return view('admin.dashboard.purchase.h3c-purchase-order-products-row', [
                'sumData' => json_encode([]),
                'orderData' => json_encode([]),
                'id1' => 'product-distribution-error-' . substr(md5((string)microtime(true)), 0, 8),
                'id2' => 'purchase-order-bar',
                'chartHeight' => 300,
                'height' => 300,
                'productOrderBar' => [],
                'adminUrl' => $adminUrl,
                'companyId' => $companyId
            ]);
        }
    }


    /**
     * 构建产品年度下单趋势图表
     */
    protected function buildProductTrendChart()
    {
        // 只获取客户ID筛选，忽略period筛选（趋势图始终显示过去12个月）
        $companyId = (int) request('company_id');
        
        // 获取产品分布数据，使用过去12个月数据
        $today = now();
        $startDate = $today->copy()->subYear()->startOfMonth()->format('Y-m-d');
        $endDate = $today->format('Y-m-d');
        
        // 日志记录
        Log::info('构建采购产品趋势图表', [
            'companyId' => $companyId,
            'timeRange' => '固定为过去12个月',
            'startDate' => $startDate,
            'endDate' => $endDate
        ]);
        
        // 获取产品分布数据
        $service = app(TecH3cPurchaseOrderReportService::class);
        list($sum, $orderCounts) = $service->getProductDistribution($startDate, $endDate, $companyId);
        
        // 确保数据包含产品ID
        $safeSum = [];
        foreach($sum as $item) {
            // 获取产品ID
            $productId = DB::table('t_product_lists_h3c')
                        ->where('product', $item['name'] ?? '')
                        ->value('id');
            
            if($productId) {
                $safeSum[] = [
                    'id' => $productId,
                    'name' => $item['name'] ?? '',
                    'value' => $item['value'] ?? 0
                ];
            }
        }
        
        // 直接传递数组，不进行JSON编码
        $card = new Card('产品年度下单趋势');
        return $card->content(view('admin.dashboard.purchase.h3c-purchase-order-product-trend', [
            'sumData' => $safeSum,
            'companyId' => $companyId,
            'adminUrl' => admin_url()
        ]));
    }

    /**
     * 产品下单数量图表数据接口
     */
    public function orderItemsChart(Request $request)
    {
        try {
            $orderId = (int) $request->input('order_id');
            
            // 验证订单ID
            if (empty($orderId)) {
                return $this->safeJsonResponse([
                    'categories' => [],
                    'series' => [['name' => '数量', 'data' => []]]
                ]);
            }
            
            // 获取订单号
            $orderNo = '';
            $order = \App\Models\TecH3cPurchaseOrderModel::find($orderId);
            if ($order) {
                $orderNo = $order->order_no;
            }
            
            $chartData = app(TecH3cPurchaseOrderReportService::class)->getOrderItemsChart($orderId);
            
            // 确保数据格式正确
            if (!isset($chartData['categories']) || !isset($chartData['series'])) {
                $chartData = [
                    'categories' => ['无数据'],
                    'series' => [['name' => '数量', 'data' => [0]]]
                ];
            }
            
            // 更彻底的数据清洗
            $safeCategories = [];
            foreach ($chartData['categories'] as $category) {
                // 将非字符串值转为字符串
                $category = is_null($category) ? '' : (string)$category;
                // 过滤所有非打印字符和潜在问题字符
                $category = preg_replace('/[\x00-\x1F\x7F-\xFF\p{C}]/u', '', $category);
                // 移除所有HTML和JavaScript相关的特殊字符
                $category = htmlspecialchars($category, ENT_QUOTES | ENT_HTML5, 'UTF-8', false);
                $safeCategories[] = $category;
            }
            
            // 清洗series数据
            $safeSeries = [];
            foreach ($chartData['series'] as $series) {
                $safeSeries[] = [
                    'name' => htmlspecialchars((string)($series['name'] ?? '数量'), ENT_QUOTES | ENT_HTML5, 'UTF-8', false),
                    'data' => array_map(function($val) {
                        return is_numeric($val) ? (float)$val : 0;
                    }, $series['data'] ?? [])
                ];
            }
            
            // 安全的orderNo
            $safeOrderNo = htmlspecialchars((string)$orderNo, ENT_QUOTES | ENT_HTML5, 'UTF-8', false);
            
            return $this->safeJsonResponse([
                'categories' => $safeCategories,
                'series' => $safeSeries,
                'order_no' => $safeOrderNo
            ]);
            
        } catch (\Exception $e) {
            return $this->safeJsonResponse([
                'categories' => ['加载失败'],
                'series' => [['name' => '数量', 'data' => [0]]],
                'error' => '数据获取失败'
            ]);
        }
    }

    // 新增辅助方法确保安全的JSON响应
    private function safeJsonResponse(array $data)
    {
        // 转换为JSON并编码为UTF-8
        $content = json_encode($data, 
            JSON_UNESCAPED_UNICODE | 
            JSON_HEX_TAG | 
            JSON_HEX_APOS | 
            JSON_HEX_AMP | 
            JSON_HEX_QUOT | 
            JSON_PRESERVE_ZERO_FRACTION
        );
        
        // 如果JSON编码失败,返回安全的空数据
        if ($content === false) {
            $content = json_encode([
                'categories' => ['编码错误'],
                'series' => [['name' => '数量', 'data' => [0]]]
            ]);
        }
        
        return response($content, 200, [
            'Content-Type' => 'application/json; charset=utf-8',
            'X-PJAX-URL' => 'false',  // 阻止PJAX自动处理
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers' => 'X-Requested-With, Content-Type',
            'Cache-Control' => 'no-store, no-cache, must-revalidate'
        ]);
    }

    /**
     * 获取采购订单数据API，供前端刷新使用
     */
    public function getPurchaseOrderData(Request $request)
    {
        $months = (int)$request->input('period', 6);
        // 计算查询日期范围
        $today     = now();
        $endDate   = $today->format('Y-m-d');
        $startDate = $today->copy()->subMonths($months - 1)->startOfMonth()->format('Y-m-d');
        // 只统计已下单订单的金额
        $stats = app(TecH3cPurchaseOrderReportService::class)->getHeaderStats($startDate, $endDate);
        $bom    = app(TecH3cPurchaseOrderReportService::class)->getBomUnits($months);
        $amount = app(TecH3cPurchaseOrderReportService::class)->getAmountStats($months);
        $trend  = app(TecH3cPurchaseOrderReportService::class)->getTrend($months);
        return response()->json([
            'status'  => $stats['status'], // 假设getHeaderStats返回status?
            'units'   => array_column($bom, 'total'), // 提取 BOM 单位数组
            'amount'  => ['ordered' => $amount['ordered'], 'unpaid' => $amount['unpaid']],
            'trend'   => ['labels' => $trend['labels'], 'data' => $trend['data']],
        ]);
    }

    /**
     * 产品年度下单趋势数据接口
     */
    public function productYearTrend(Request $request)
    {
        try {
            $product = $request->input('product');
            $companyId = (int)$request->input('company_id');
            
            // 记录请求参数
            Log::info('采购产品趋势数据API请求', [
                'product_id' => $product,
                'company_id' => $companyId,
                'client_ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);
            
            // 严格验证产品ID
            if (empty($product)) {
                Log::warning('采购产品趋势数据API - 产品ID为空');
                return response()->json([
                    'labels' => [],
                    'data' => [],
                    'error' => '请选择有效的产品',
                    'status' => 'error'
                ])->header('Content-Type', 'application/json');
            }
            
            // 获取趋势图数据（过去12个月，带客户筛选）
            $service = app(TecH3cPurchaseOrderReportService::class);
            $result = $service->getProductYearTrend($product, $companyId);
            
            // 记录查询结果
            Log::info('采购产品趋势数据API响应', [
                'product_id' => $product,
                'company_id' => $companyId,
                'labels_count' => count($result['labels']),
                'data_count' => count($result['data'])
            ]);
            
            return response()->json([
                'labels' => $result['labels'] ?? [],
                'data' => $result['data'] ?? [],
                'status' => 'success'
            ])->header('Content-Type', 'application/json');
            
        } catch (\Exception $e) {
            Log::error('采购产品趋势数据API异常', [
                'product_id' => $request->input('product'),
                'company_id' => $request->input('company_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'labels' => [],
                'data' => [],
                'error' => '获取数据异常: ' . $e->getMessage(),
                'status' => 'error'
            ])->header('Content-Type', 'application/json');
        }
    }
}
