<?php

namespace App\Admin\Controllers;

use App\Models\TecH3cExcelTemplateModel;
use App\Constants\TecUploadPathConstant;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Grid;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class TecH3cExcelTemplateController extends AdminController
{
    protected $title = 'Excelテンプレート';

    protected function grid()
    {
        return Grid::make(new TecH3cExcelTemplateModel(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('name', '模板名称');
            $grid->column('description', '模板描述');
            $grid->column('category', '模板分类')
                ->using(TecUploadPathConstant::H3C_EXCEL_TEMPLATE_CATEGORY_MAP);
            
            $grid->column('file_path', '文件')->display(function ($path) {
                $displayName = $this->getAttribute('original_filename') ?: basename($path);
                $html = $path ? $displayName : '无';
                if ($path) {
                    $html .= ' <a href="javascript:void(0);" data-id="'.$this->getKey().'" class="grid-row-download">
                        <i class="fa fa-download"></i>
                    </a>';
                }
                return $html;
            });

            $grid->column('is_active', '状态')->switch('', true);

            $grid->column('created_at', '创建时间')
                ->display(function ($value) {
                    return $value ? date('Y-m-d', strtotime($value)) : '';
                })
                ->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();  // 使用面板布局
                $filter->equal('category', '模板分类')->select(TecUploadPathConstant::H3C_EXCEL_TEMPLATE_CATEGORY_MAP);
                $filter->like('name', '模板名称');
                $filter->equal('is_active', '状态')->radio([
                    1 => '启用',
                    0 => '禁用'
                ]);
            });           
            
            // 设置操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView(); // 禁用查看按钮
                $actions->disableEdit(); // 禁用编辑按钮
                $actions->disableQuickEdit(false); // 禁用快速编辑按钮
                $actions->disableDelete(); // 禁用默认删除按钮
                
                // 添加自定义删除按钮
                $actions->append('<a href="javascript:void(0);" data-id="'.$actions->row->id.'" class="grid-row-delete">
                    <i class="feather icon-trash"></i>
                </a>');
            });

            // 添加JS
            Admin::script(<<<JS
                // 删除操作
                $('.grid-row-delete').on('click', function() {
                    var id = $(this).data('id');
                    Dcat.confirm('确定要删除这条记录吗？', null, function() {
                        $.ajax({
                            method: 'DELETE',
                            url: 'r_h3c_excel_templates/' + id,
                            success: function(response) {
                                if (response.status) {
                                    Dcat.success(response.message);
                                    // 直接移除当前行
                                    $('.grid-row-delete[data-id="' + id + '"]').closest('tr').remove();
                                } else {
                                    Dcat.error(response.message);
                                }
                            },
                            error: function(xhr) {
                                Dcat.error('删除失败：' + (xhr.responseJSON.message || '未知错误'));
                            }
                        });
                    });
                });

                // 下载操作
                $('.grid-row-download').on('click', function() {
                    var id = $(this).data('id');
                    window.location.href = '/admin/r_h3c_excel_templates/' + id + '/download';
                });

                // 监听开关状态变化
                $('input[data-field="is_active"]').on('change', function () {
                    // 延迟执行刷新，等待后端处理完成
                    setTimeout(function() {
                        Dcat.reload();
                    }, 500);
                });
            JS);
        });
    }

    public function form()
    {
        return Form::make(new TecH3cExcelTemplateModel(), function (Form $form) {
            $form->disableCreatingCheck();
            $form->disableEditingCheck();
            $form->disableViewCheck();
            
            // 先输入模板名称
            $form->text('name', '模板名称')
                ->required()
                ->help('请输入模板名称');

            if ($form->isCreating()) {
                $form->select('category', '模板分类')
                    ->options(TecUploadPathConstant::H3C_EXCEL_TEMPLATE_CATEGORY_MAP)
                    ->default(TecUploadPathConstant::H3C_EXCEL_TEMPLATE_CATEGORY_PURCHASE)
                    ->required();
            } else {
                $form->display('category', '模板分类')
                    ->with(function ($value) {
                        return TecUploadPathConstant::H3C_EXCEL_TEMPLATE_CATEGORY_MAP[$value] ?? $value;
                    });
            }

            if ($form->isCreating()) {
                // 添加隐藏字段
                $form->hidden('original_filename');
                
                $form->file('file_path', 'Excel模板')
                    ->accept('xlsx,xls,csv')
                    ->maxSize(1024 * 5)
                    ->required()
                    ->autoUpload()
                    ->disk('local')
                    ->move('files')
                    ->name(function ($file) use ($form) {
                        // 获取原始文件名
                        $originalName = $file->getClientOriginalName();
                        
                        // 获取原始扩展名
                        $extension = strtolower($file->getClientOriginalExtension());
                        
                        // 使用更可靠的方式生成唯一文件名
                        $timestamp = microtime(true);
                        $random = mt_rand(10000, 99999);
                        $hashedName = md5($timestamp . $random . $originalName) . '_' . $random . '.' . $extension;
                        
                        // 记录详细日志
                        Log::info('Excel模板文件上传', [
                            'original_filename' => $originalName,
                            'hashed_filename' => $hashedName
                        ]);
                        
                        // 使用 session 保存值
                        session(['excel_template_original_filename' => $originalName]);
                        session(['excel_template_hashed_name' => $hashedName]);
                        
                        Log::info('设置 session 后', [
                            'session_values' => [
                                'original_filename' => session('excel_template_original_filename'),
                                'hashed_name' => session('excel_template_hashed_name')
                            ]
                        ]);
                        
                        return $hashedName;
                    })
                    ->help('仅允许上传 Excel 文件，大小不超过 5MB');

            } else {
                // 显示原始文件名
                $form->display('original_filename', 'Excel模板');

            }

            $form->text('description', '模板描述')
                ->help('描述此模板的用途和特点（可选）');

            $form->hidden('user_id')->default(Admin::user()->id);
            $form->switch('is_active', '是否启用')
                ->customFormat(function ($v) {
                    return $v == 1 ? 1 : 0;
                })
                ->saving(function ($v) {
                    return $v ? 1 : 0;
                });

            // 保存前的处理
            $form->saving(function (Form $form) {
                // 处理文件上传和重命名
                if ($form->isCreating()) {
                    // 从 session 获取值并设置到表单中
                    $originalFilename = session('excel_template_original_filename');
                    $hashedName = session('excel_template_hashed_name');
                    
                    $form->input('original_filename', $originalFilename);
                    
                    Log::info('表单保存前', [
                        'session_values' => [
                            'original_filename' => $originalFilename,
                            'hashed_name' => $hashedName
                        ],
                        'form_values' => [
                            'name' => $form->input('name'),
                            'file_path' => $form->input('file_path'),
                            'original_filename' => $form->input('original_filename')
                        ]
                    ]);

                    // 确保目标目录存在
                    Storage::disk('local')->makeDirectory(TecUploadPathConstant::H3C_EXCEL_TEMPLATE);

                    // 如果有文件上传
                    if ($form->input('file_path')) {
                        $currentPath = $form->input('file_path');
                        $hashedName = basename($currentPath);
                        $targetPath = TecUploadPathConstant::H3C_EXCEL_TEMPLATE . '/' . $hashedName;

                        // 移动文件到最终目录
                        if (Storage::disk('local')->exists($currentPath)) {
                            // 如果目标文件已存在，先删除
                            if (Storage::disk('local')->exists($targetPath)) {
                                Storage::disk('local')->delete($targetPath);
                            }
                            
                            // 移动文件
                            Storage::disk('local')->move($currentPath, $targetPath);
                            
                            // 更新文件路径
                            $form->input('file_path', $targetPath);
                            
                            Log::info('文件移动成功', [
                                'from' => $currentPath,
                                'to' => $targetPath
                            ]);
                        }
                    }
                }
            });
        });
    }

    public function download(TecH3cExcelTemplateModel $template)
    {
        if (!$template->file_path || !Storage::disk('local')->exists($template->file_path)) {
            abort(404, '文件不存在');
        }

        $filePath = storage_path('app/' . $template->file_path);
        $downloadName = $template->original_filename ?: basename($template->file_path);
        return response()->download($filePath, $downloadName);
    }

    public function preview(TecH3cExcelTemplateModel $template)
    {
        // 检查文件是否存在
        if (!$template->file_path || !Storage::disk('local')->exists($template->file_path)) {
            abort(404, '文件不存在');
        }

        try {
            // 获取文件的完整路径
            $excelPath = storage_path('app/' . $template->file_path);
            
            // 生成PDF文件路径
            $pdfPath = storage_path('app/temp/' . uniqid() . '.pdf');
            
            // 确保临时目录存在
            if (!file_exists(dirname($pdfPath))) {
                mkdir(dirname($pdfPath), 0755, true);
            }
            
            // 使用LibreOffice转换为PDF
            $command = "soffice --headless --convert-to pdf --outdir " . dirname($pdfPath) . " " . escapeshellarg($excelPath);
            exec($command, $output, $returnVar);
            
            if ($returnVar !== 0) {
                throw new \Exception('转换失败');
            }
            
            // 读取PDF内容
            $pdfContent = file_get_contents($pdfPath);
            
            // 删除临时文件
            unlink($pdfPath);
            
            // 返回PDF预览
            return response($pdfContent)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'inline; filename="' . $template->filename . '.pdf"');
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '预览失败：' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            Log::info('开始删除模板', ['id' => $id]);
            
            $template = TecH3cExcelTemplateModel::findOrFail($id);
            Log::info('找到模板记录', [
                'template_id' => $template->id,
                'file_path' => $template->file_path
            ]);
            
            // 删除关联的文件（如果有）
            if ($template->file_path && Storage::exists($template->file_path)) {
                Log::info('准备删除文件', ['file_path' => $template->file_path]);
                Storage::delete($template->file_path);
                Log::info('文件删除成功');
            }
            
            // 执行删除
            $template->delete();
            Log::info('数据库记录删除成功');
            
            return response()->json([
                'status'  => true,
                'message' => '删除成功',
                'refresh' => true
            ]);
        } catch (\Exception $e) {
            Log::error('删除失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status'  => false,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }
}
