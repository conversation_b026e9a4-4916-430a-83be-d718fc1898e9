---
description: 
globs: 
alwaysApply: true
---
# 项目技术规范和开发指南

## 1. 基础设置

- 默认使用简体中文回答
- 专注于 Laravel、PHP、Dcat Admin 框架的开发
- 具备物流 ERP 行业软件开发经验

## 2. 思维模式

### 2.1 多维度思考

- 系统思维：从整体到细节的立体分析
- 辩证思维：权衡多种方案的利弊
- 创造性思维：寻找创新解决方案
- 批判性思维：多角度验证和优化

### 2.2 思维平衡

- 分析与直觉平衡：结合逻辑分析和经验判断
- 细节与全局平衡：关注细节的同时把握整体
- 理论与实践平衡：理论指导下的实践应用

## 3. 工作流程

### 3.1 分析阶段

1. 理解用户需求
2. 评估技术可行性
3. 设计解决方案
4. 验证方案可靠性

### 3.2 实现阶段

1. 编写代码
2. 进行测试
3. 优化性能
4. 提供文档

### 3.3 项目初始化

1. 仔细阅读项目路径下的 README-TECH3C.md 文件
2. 理解项目目标、功能架构、技术栈和开发计划
3. 确保对项目的整体架构和功能有清晰的认识

## 4. 代码规范

### 4.1 基础要求

- 使用 Laravel 9.x 和 PHP 8.1+
- 遵循 PSR-12 编码标准
- 启用严格类型 `declare(strict_types=1)`
- 遵循 SOLID 设计原则

### 4.2 命名规范

- 控制器：`Tec` 前缀（如 `TecTagController`）
- 模型：`Tec` 前缀 + `Model` 后缀（如 `TecProductUnitModel`）
- 数据表：`t_` 前缀（如 `t_warehouses`）
- 路由：`r_` 前缀（如 `r_products`） 路由要放在app/Admin/routes下的具体机能里面

## 5. 数据库规范

### 5.1 外键约束策略

1. `RESTRICT` 策略
   - 用于核心业务数据的关联,防止误删除
   - 适用场景:
     - 采购订单与采购明细的关联
     - 产品与采购明细的关联
     - 仓库与入库单的关联
     - 货位与入库明细的关联

2. `CASCADE` 策略
   - 用于父子关系的数据,子数据随父数据一起删除
   - 适用场景:
     - 入库单与入库明细的关联
     - 主表与其日志表的关联

3. `SET NULL` 策略
   - 用于非核心关联数据,允许关联数据被删除
   - 适用场景:
     - 联系人与公司的关联
     - 订单与经办人的关联
     - 产品与分类的关联

### 5.2 外键命名规范

- 格式: `表名_字段名_foreign`
- 示例: `t_purchase_item_h3c_order_id_foreign`

### 5.3 字段类型匹配规范

1. 主键 ID 字段类型:
   - 使用 increments() 的表,对应的外键应使用 unsignedInteger
   - 使用 bigIncrements() 的表,对应的外键应使用 unsignedBigInteger
   - 使用 id() 的表(Laravel 8+),对应的外键应使用 unsignedBigInteger

2. 常见表的主键类型:
   - t_product_lists_h3c: increments (unsignedInteger)
   - t_product_units: id (unsignedBigInteger)
   - t_warehouse_entries_h3c: id (unsignedBigInteger)

## 6. 安全规范

### 6.1 数据安全

- 使用参数绑定防止 SQL 注入
- 使用 `e()` 或 `{{ }}` 防止 XSS
- 实施 Laravel Gate 和 Policy 权限控制

### 6.2 性能安全

- 合理使用数据库索引
- 实施缓存机制
- 采用队列处理耗时任务

## 7. 日志处理

### 7.1 日志规范

- 记录关键操作日志
- 按模块分类
- 支持按日期归档
- 异常日志需包含完整调用栈

### 7.2 日志命令

- `111`：分析 `/storage/logs` 下的日志
- `222`：执行 git 提交，并总结修改点
- `333`：检查代码规范
- `444`：运行单元测试
- `555`：性能分析
- 每次修改后清空 `laravel.log`

## 8. 代码质量

### 8.1 模块化要求

- 业务逻辑封装在 Service 层
- 数据访问封装在 Repository 层
- 控制器只负责请求处理和响应
- 遵循单一职责原则

### 8.2 性能优化

- 优化数据库查询
- 实施缓存策略
- 使用队列处理后台任务
- 代码性能分析和优化

## 9. Dcat Admin 开发规范

### 9.1 功能实现

- 支持异步表单
- 实现无限层级权限管理
- 支持树状结构展示
- 优化用户交互体验

### 9.2 表格功能

- 实现分页功能
- 支持筛选功能
- 支持数据导出
- 支持批量操作

## 10. 代码输出规范

### 10.1 代码展示

- 仅显示必要修改部分
- 减少冗余代码
- 使用 Markdown 格式化
- 指定代码语言和路径

### 10.2 注释要求

- 提供修改原因说明
- 添加关键逻辑注释
- 说明依赖关系
- 标注重要的业务规则

## 11. 测试与验证

### 11.1 测试要求

- 使用 PHPUnit 进行单元测试
- 使用 Laravel Dusk 进行功能测试
- 验证方案的完整性和可行性
- 确保向后兼容性

### 11.2 优化建议

- 提供多种可选方案
- 包含同步和异步处理选项
- 考虑性能和可维护性
- 权衡开发成本和收益

## 12. 响应规范

### 12.1 交互原则

- 提供简洁明确的解决方案
- 避免不必要的推测
- 确保代码可直接运行
- 提供必要的上下文说明

### 12.2 问题处理

- 优先解决核心问题
- 提供可行的替代方案
- 说明潜在的风险
- 建议最佳实践方案

## 13. 前端资源管理

### 13.1 源文件管理

- JavaScript 源文件存放在 `resources/js/` 目录下
- 编译后的文件存放在 `public/js/` 目录下
- 源文件使用现代 JavaScript 语法（ES6+）
- 使用构建工具（如 Webpack）进行编译和压缩
- 源文件应保持可读性和模块化
- 编译后的文件用于生产环境

### 13.2 前端资源编译

- 使用 Laravel Mix 进行资源编译
- 运行 `npm run dev` 进行开发环境编译
- 运行 `npm run prod` 进行生产环境编译
- 确保源文件和编译文件同步更新

### 13.3 入库管理 JS 文件说明

- 入库管理 JS 源文件：`resources/js/h3c-warehouse-entry.js`
- 主要功能：
  1. 处理入库单表单交互
  2. 管理订单明细加载
  3. 处理仓库和货位联动
  4. 实现表单验证逻辑
- 使用现代 Promise 异步处理技术
- 支持动态内容加载和事件绑定

## 14. 全局配置和常量管理

### 14.1 PurchaseConfig 常量类

`PurchaseConfig` 是项目的全局配置常量管理类，位于 `app/Admin/Config/PurchaseConfig.php`。

#### 主要功能

- 集中管理项目中的常量
- 提供静态方法快速获取配置映射
- 支持类型提示和代码补全

#### 常量类别

1. 角色配置
2. 贸易条件
3. 货币配置
4. 维护配置
5. 仓库位置状态
6. 仓库状态
7. 产品状态
8. 产品属性

### 14.2 TecH3cPurchaseBaseModel 基础模型

`TecH3cPurchaseBaseModel` 是采购订单相关模型的基础模型，位于 `app/Models/TecH3cPurchaseBaseModel.php`。

#### 主要功能

- 定义采购订单相关的状态常量
- 提供状态管理的基础方法
- 集中管理订单相关的枚举和常量

#### 状态常量类别

1. 审核状态
2. 交付状态
3. 入库状态
4. 付款状态
5. 请款状态

### 14.3 最佳实践

1. 优先使用 `PurchaseConfig` 和 `TecH3cPurchaseBaseModel` 中定义的常量
2. 避免在代码中使用硬编码的状态值
3. 通过常量提高代码的可读性和可维护性

## 15. 项目架构

### 15.1 目录结构

- `app/Admin/Controllers/`: 管理后台控制器
- `app/Models/`: 数据模型
- `app/Repositories/`: 数据仓库层
- `app/Services/`: 业务逻辑服务层
- `app/Admin/routes/`: 路由文件
- `resources/js/`: 前端资源文件
- `resources/css/`: 前端样式文件
- `resources/views/`: 视图文件
- `resources/lang/`: 语言文件
- `resources/public/`: 公共资源文件
- `resources/public/js/`: 公共JS文件
- `resources/public/css/`: 公共CSS文件
- `webpack.mix.js`: 前端资源编译配置

### 15.2 关键组件

- 控制器：处理 HTTP 请求和响应
- 模型：定义数据表结构和关系
- 仓库：封装数据库操作
- 服务：实现复杂的业务逻辑

## 16. 开发环境要求

### 16.1 基础环境

- PHP 8.1+
- Laravel 9.x
- MySQL 5.7+
- Redis 6.2+
- Composer 2.0+
- Node.js 16+ (用于前端资源管理)
- npm 8+

### 16.2 扩展要求

- PHP 扩展：
  - BCMath
  - Ctype
  - JSON
  - Mbstring
  - OpenSSL
  - PDO
  - Tokenizer
  - XML
  - Redis

### 16.3 推荐工具

- Git 2.30+
- Visual Studio Code
- PhpStorm
- Redis Desktop Manager
- MySQL Workbench

## 17. 数据库迁移顺序规范

### 17.1 迁移顺序

1. 基础表(无外键依赖)
   - 付款条款表
   - 产品主分类表
   - 仓库表
   - 单位表

2. 关联表(有外键依赖)
   - 供应商公司表(依赖付款条款)
   - 联系人表(依赖供应商公司)
   - 产品分类表(依赖主分类)
   - 产品表(依赖分类)
   - 采购订单表(依赖供应商、联系人)
   - 采购明细表(依赖订单、产品)

### 17.2 迁移文件命名示例

```
2024_07_28_100000_create_t_payment_terms_table.php
2024_07_28_110000_create_t_snd_companies_table.php
2024_07_28_120000_create_t_snd_contacts_table.php
2024_07_28_130000_create_t_contacts_table.php
2024_07_28_140000_create_t_product_major_category_table.php
2024_07_28_150000_create_t_product_categories_h3c_table.php
2024_07_28_160000_create_t_product_lists_h3c_table.php
2024_07_28_170000_create_t_purchase_order_h3c_table.php
2024_07_28_180000_create_t_purchase_item_h3c_table.php
```
